<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry excluding="**/*.java" including="**/*.xml" kind="src" output="target/test-classes" path="src/main/webapp"/>
	<classpathentry kind="src" path="src/main/java"/>
	<classpathentry excluding="applicationContext-resources.xml|**/*.java" kind="src" path="src/main/resources"/>
	<classpathentry excluding="src/main/webapp/|src/main/java/|src/main/resources/" kind="src" path=""/>
	<classpathentry kind="var" path="M2_REPO/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1.jar"/>
	<classpathentry kind="var" path="M2_REPO/javax/servlet/servlet-api/2.5/servlet-api-2.5.jar"/>
	<classpathentry kind="var" path="M2_REPO/javax/xml/bind/jaxb-api/2.1/jaxb-api-2.1.jar"/>
	<classpathentry kind="var" path="M2_REPO/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2.jar"/>
	<classpathentry kind="var" path="M2_REPO/javax/activation/activation/1.1/activation-1.1.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.6"/>
	<classpathentry kind="var" path="M2_REPO/com/lanmosoft/lanmobase/1.0.0/lanmobase-1.0.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/com/sun/xml/bind/jaxb-impl/2.1.13/jaxb-impl-2.1.13.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-frontend-jaxws/2.7.0/cxf-rt-frontend-jaxws-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/xml-resolver/xml-resolver/1.2/xml-resolver-1.2.jar"/>
	<classpathentry kind="var" path="M2_REPO/asm/asm/3.3.1/asm-3.3.1.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-api/2.7.0/cxf-api-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/codehaus/woodstox/woodstox-core-asl/4.1.4/woodstox-core-asl-4.1.4.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/codehaus/woodstox/stax2-api/3.1.1/stax2-api-3.1.1.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/ws/xmlschema/xmlschema-core/2.0.3/xmlschema-core-2.0.3.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/geronimo/specs/geronimo-javamail_1.4_spec/1.7.1/geronimo-javamail_1.4_spec-1.7.1.jar"/>
	<classpathentry kind="var" path="M2_REPO/wsdl4j/wsdl4j/1.5.1/wsdl4j-1.5.1.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-core/2.7.0/cxf-rt-core-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-bindings-soap/2.7.0/cxf-rt-bindings-soap-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-databinding-jaxb/2.7.0/cxf-rt-databinding-jaxb-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-bindings-xml/2.7.0/cxf-rt-bindings-xml-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-frontend-simple/2.7.0/cxf-rt-frontend-simple-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-ws-addr/2.7.0/cxf-rt-ws-addr-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-ws-policy/2.7.0/cxf-rt-ws-policy-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/neethi/neethi/3.0.2/neethi-3.0.2.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-databinding-xmlbeans/2.7.0/cxf-rt-databinding-xmlbeans-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/xmlbeans/xmlbeans/2.5.0/xmlbeans-2.5.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/org/apache/cxf/cxf-rt-transports-http/2.7.0/cxf-rt-transports-http-2.7.0.jar"/>
	<classpathentry kind="var" path="M2_REPO/com/google/code/gson/gson/2.3.1/gson-2.3.1.jar"/>
	<classpathentry kind="var" path="M2_REPO/axis/axis/1.3/axis-1.3.jar"/>
	<classpathentry kind="var" path="M2_REPO/axis/axis-jaxrpc/1.3/axis-jaxrpc-1.3.jar"/>
	<classpathentry kind="var" path="M2_REPO/axis/axis-saaj/1.3/axis-saaj-1.3.jar"/>
	<classpathentry kind="var" path="M2_REPO/commons-logging/commons-logging/1.0.4/commons-logging-1.0.4.jar"/>
	<classpathentry kind="var" path="M2_REPO/commons-discovery/commons-discovery/0.2/commons-discovery-0.2.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.junit.JUNIT_CONTAINER/4"/>
	<classpathentry kind="output" path="target/classes"/>
</classpath>
