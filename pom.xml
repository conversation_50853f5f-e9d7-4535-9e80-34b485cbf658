<?xml version="1.0" encoding="UTF-8"?><!-- bak the file by lanmosoft.com -->
<!--
  copyright by lanmosoft.com
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.lanmosoft</groupId>
    <artifactId>tesystem</artifactId>
    <packaging>war</packaging>
    <version>1.0.0</version>
    <name>Lanmosoft Module</name>

    <build>
        <defaultGoal>install</defaultGoal>
        <finalName>tesystem</finalName>
        <plugins>
            <plugin>
                <groupId>org.appfuse.plugins</groupId>
                <artifactId>appfuse-maven-plugin</artifactId>
                <version>${appfuse.version}</version>
                <configuration>
                    <!-- Fix annotation detection issue for Java 7. Thanks Shred! -->
                    <!-- http://www.shredzone.de/cilla/page/352/hibernate3-maven-plugin-fails-with-java-17.html -->
                    <componentProperties>
                        <implementation>annotationconfiguration</implementation>
                    </componentProperties>
                    <genericCore>${amp.genericCore}</genericCore>
                    <fullSource>${amp.fullSource}</fullSource>
                </configuration>
                <!-- Dependency needed by appfuse:gen-model to connect to database. -->
                <!-- See http://issues.appfuse.org/browse/APF-868 to learn more.    -->
                <dependencies>
                    <dependency>
                        <groupId>${jdbc.groupId}</groupId>
                        <artifactId>${jdbc.artifactId}</artifactId>
                        <version>${jdbc.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.7.2</version>
                <configuration>
                    <skip>${maven.test.skip}</skip>
                    <testFailureIgnore>${maven.test.failure.ignore}</testFailureIgnore>
                    <excludes>
                        <exclude>**/*WebTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>${jetty.version}</version>
                <configuration>
                    <webApp>
                        <contextPath>/tesystem</contextPath>
                    </webApp>
                    <!--     <connectors>
                         <connector implementation="org.eclipse.jetty.server.bio.SocketConnector">
                             <port>80</port>
                         </connector>
                     </connectors>
                     <webDefaultXml>webdefault.xml</webDefaultXml> -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <port>8080</port>
                    <path>/tesystem</path>
                    <uriEncoding>UTF-8</uriEncoding>
                    <finalName>tesystem</finalName>
                    <server>tomcat7</server>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.zeroturnaround</groupId>
                <artifactId>javarebel-maven-plugin</artifactId>
                <version>1.0.5</version>
                <executions>
                    <execution>
                        <id>generate-rebel-xml</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-java2ws-plugin</artifactId>
                <version>${cxf.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.cxf</groupId>
                        <artifactId>cxf-rt-frontend-simple</artifactId>
                        <version>${cxf.version}</version>
                    </dependency>
                </dependencies>

                <executions>
                    <execution>
                        <id>process-classes</id>
                        <phase>process-classes</phase>
                        <configuration>
                            <className>com.lanmosoft.web.IStudentService</className>
                            <genWsdl>true</genWsdl>
                            <verbose>true</verbose>
                            <serviceName>StudentService</serviceName>
                            <address>http://**************:8080/tesystem/services/studentManager</address>
                        </configuration>
                        <goals>
                            <goal>java2ws</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Jreble 插件-->
            <plugin>
                <groupId>org.zeroturnaround</groupId>
                <artifactId>jrebel-maven-plugin</artifactId>
                <version>1.1.5</version>
                <executions>
                    <execution>
                        <id>generate-rebel-xml</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>applicationContext-resources.xml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>applicationContext-resources.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/main/webapp</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </testResource>
        </testResources>
        <!-- Read the contents of the file below to see how to change databases -->
    </build>

    <!-- 项目分发信息，在执行mvn deploy后表示要发布的位置。有了这些信息就可以把网站部署到远程服务器或者把构件部署到远程仓库。 -->
    <distributionManagement>
        <repository>
            <id>appfuse-releases</id>
            <name>AppFuse Release Repository</name>
            <url>http://oss.sonatype.org/service/local/staging/deploy/maven2</url>
        </repository>
        <snapshotRepository>
            <id>appfuse-snapshots</id>
            <name>AppFuse Snapshot Repository</name>
            <url>http://oss.sonatype.org/content/repositories/appfuse-snapshots</url>
        </snapshotRepository>
        <site>
            <id>appfuse-light-site</id>
            <name>AppFuse Light Maven Site</name>
            <url>scp://static.appfuse.org/var/www/appfuse-site/light</url>
        </site>
    </distributionManagement>

    <!-- 发现依赖和扩展的远程仓库列表。 -->
    <repositories>
        <repository>
            <id>appfuse-snapshots</id>
            <url>http://oss.sonatype.org/content/repositories/appfuse-snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
    </repositories>
    <!-- 发现插件的远程仓库列表，这些插件用于构建和报表 -->
    <pluginRepositories>
        <pluginRepository>
            <id>appfuse-snapshots</id>
            <url>http://oss.sonatype.org/content/repositories/appfuse-snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!--依赖jar-->
    <dependencies>


        <!--        <dependency>-->
        <!--            <groupId>com.mangofactory</groupId>-->
        <!--            <artifactId>swagger-springmvc</artifactId>-->
        <!--            <version>1.0.2</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.fasterxml.jackson.core</groupId>-->
        <!--            <artifactId>jackson-annotations</artifactId>-->
        <!--            <version>2.4.4</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.fasterxml.jackson.core</groupId>-->
        <!--            <artifactId>jackson-databind</artifactId>-->
        <!--            <version>2.4.4</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.fasterxml.jackson.core</groupId>-->
        <!--            <artifactId>jackson-core</artifactId>-->
        <!--            <version>2.4.4</version>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.20</version>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>org.csource</groupId>
            <artifactId>fastdfs-client-java</artifactId>
            <version>1.27-SNAPSHOT</version>
        </dependency>
        <!-- lanmobase dependency -->
        <dependency>
            <groupId>com.lanmosoft</groupId>
            <artifactId>lanmobase</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.0.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>${servlet.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.1.13</version>
        </dependency>
        <!--      <dependency>
	       <groupId>com.lanmosoft.ueditor</groupId>
	            <artifactId>ueditor</artifactId>
	            <version>1.1.1</version>
	        </dependency>-->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-frontend-jaxws</artifactId>
            <version>${cxf.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-databinding-xmlbeans</artifactId>
            <version>${cxf.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http</artifactId>
            <version>${cxf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>
        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis</artifactId>
            <version>${axis.version}</version>
        </dependency>
        <!-- <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>1.9.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>3.1.3.RELEASE</version>
            <scope>test</scope>
        </dependency> -->
        <!-- cxf 进行rs开发 必须导入  -->
        <!--<dependency>-->
        <!--<groupId>org.apache.cxf</groupId>-->
        <!--<artifactId>cxf-rt-frontend-jaxrs</artifactId>-->
        <!--<version>3.0.1</version>-->
        <!--</dependency>-->

        <!-- 客户端 -->
        <!--<dependency>-->
        <!--<groupId>org.apache.cxf</groupId>-->
        <!--<artifactId>cxf-rt-rs-client</artifactId>-->
        <!--<version>3.0.1</version>-->
        <!--</dependency>-->

        <!--&lt;!&ndash; 扩展json提供者 &ndash;&gt;-->
        <!--<dependency>-->
        <!--<groupId>org.apache.cxf</groupId>-->
        <!--<artifactId>cxf-rt-rs-extension-providers</artifactId>-->
        <!--<version>3.0.1</version>-->
        <!--</dependency>-->

        <!-- 转换json工具包，被extension providers 依赖 -->
        <dependency>
            <groupId>org.codehaus.jettison</groupId>
            <artifactId>jettison</artifactId>
            <version>1.3.7</version>
        </dependency>
        <!--cglib-->
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>2.2.2</version>
        </dependency>
    </dependencies>


    <properties>
        <maven.test.skip>true</maven.test.skip>
        <maven.test.failure.ignore>true</maven.test.failure.ignore>
        <!-- Application settings -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <dao.framework>hibernate</dao.framework>
        <web.framework>spring-freemarker</web.framework>
        <amp.genericCore>true</amp.genericCore>
        <amp.fullSource>true</amp.fullSource>

        <!-- Framework dependency versions -->
        <appfuse.version>2.2.1</appfuse.version>
        <cargo.version>1.2.4</cargo.version>
        <freemarker.version>2.3.16</freemarker.version>
        <jetty.version>8.1.3.v20120416</jetty.version>
        <jmock.version>2.5.1</jmock.version>
        <junit.version>4.10</junit.version>
        <jwebunit.version>2.5</jwebunit.version>
        <spring.version>3.1.3.RELEASE</spring.version>

        <!-- Database settings, also defined in src/main/jdbc.properties for easy IDE testing
        <dbunit.dataTypeFactoryName>org.dbunit.ext.h2.H2DataTypeFactory</dbunit.dataTypeFactoryName>
        <dbunit.operation.type>CLEAN_INSERT</dbunit.operation.type>
        <hibernate.dialect>org.hibernate.dialect.H2Dialect</hibernate.dialect>
        <jdbc.groupId>com.h2database</jdbc.groupId>
        <jdbc.artifactId>h2</jdbc.artifactId>
        <jdbc.version>1.3.170</jdbc.version>
        <jdbc.driverClassName>org.h2.Driver</jdbc.driverClassName>
        <jdbc.url>jdbc:h2:/tmp/appfuse_light;MVCC=TRUE</jdbc.url>
        <jdbc.username>sa</jdbc.username>
        <jdbc.password/>-->

        <!-- mysql -->
        <dbunit.dataTypeFactoryName>org.dbunit.ext.mysql.MySqlDataTypeFactory</dbunit.dataTypeFactoryName>
        <dbunit.operation.type>CLEAN_INSERT</dbunit.operation.type>
        <hibernate.dialect>org.hibernate.dialect.MySQL5InnoDBDialect</hibernate.dialect>
        <jdbc.groupId>mysql</jdbc.groupId>
        <jdbc.artifactId>mysql-connector-java</jdbc.artifactId>
        <jdbc.version>5.1.18</jdbc.version>
        <jdbc.driverClassName>com.mysql.jdbc.Driver</jdbc.driverClassName>
        <jdbc.url>*********************************************************************************************************************</jdbc.url>
        <jdbc.username>root</jdbc.username>
        <jdbc.password>kaike1</jdbc.password>

        <!-- Properties calculated by AppFuse when running full-source plugin -->
        <aspectj.version>1.6.10</aspectj.version>
        <commons.beanutils.version>1.8.3</commons.beanutils.version>
        <commons.collections.version>3.2.1</commons.collections.version>
        <commons.dbcp.version>1.3</commons.dbcp.version>
        <commons.lang.version>2.6</commons.lang.version>
        <cxf.version>2.7.0</cxf.version>
        <axis.version>1.3</axis.version>
        <dwr.version>2.0.3</dwr.version>
        <ehcache.version>2.6.2</ehcache.version>
        <ehcache.web.version>2.0.4</ehcache.web.version>
        <hibernate.search.version>4.1.1.Final</hibernate.search.version>
        <hibernate.version>4.1.8.Final</hibernate.version>
        <javamail.version>1.4.1</javamail.version>
        <jpa.version>2.0-cr-1</jpa.version>
        <jstl.version>1.2</jstl.version>
        <log4j.version>1.2.17</log4j.version>
        <servlet.version>2.5</servlet.version>
        <sitemesh.version>2.4.2</sitemesh.version>
        <slf4j.version>1.6.1</slf4j.version>
        <spring.security.version>3.1.3.RELEASE</spring.security.version>
        <struts.menu.version>2.4.3</struts.menu.version>
        <urlrewrite.version>3.1.0</urlrewrite.version>
        <velocity.version>1.4</velocity.version>
        <wiser.version>1.2</wiser.version>
        <gson.version>2.3.1</gson.version>


        <!-- Cargo settings -->
        <!--        <cargo.container>tomcat7x</cargo.container>
                <cargo.container.home>${env.CATALINA_HOME}</cargo.container.home>
                <cargo.container.url>http://archive.apache.org/dist/tomcat/tomcat-7/v7.0.34/bin/apache-tomcat-7.0.34.zip</cargo.container.url>
                <cargo.host>localhost</cargo.host>
                <cargo.port>25888</cargo.port>
                <cargo.wait>false</cargo.wait>-->

        <!-- Search index settings -->
        <search.index.basedir>${project.build.testOutputDirectory}</search.index.basedir>
    </properties>
</project>
