<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

    <display-name>Lanmosoft project</display-name>

    <!-- [INSERT FRAGMENT HERE] -->
    <!-- Define the basename for a resource bundle for I18N -->

    <filter>
        <filter-name>rewriteFilter</filter-name>
        <filter-class>org.tuckey.web.filters.urlrewrite.UrlRewriteFilter</filter-class>
    </filter>
    
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>


    <!--<filter>
        <filter-name>lazyLoadingFilter</filter-name>
        <filter-class>org.springframework.orm.hibernate3.support.OpenSessionInViewFilter</filter-class>
    </filter>-->
    <!-- Use "org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter" if you're using JPA -->

    <filter>
        <filter-name>sitemesh</filter-name>
        <filter-class>com.opensymphony.module.sitemesh.filter.PageFilter</filter-class>
    </filter>

    <!-- Context Configuration locations for Spring XML files -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
        	<!-- CXF server -->
            classpath:/applicationContext-server.xml 
            classpath:/applicationContext-resources.xml
            classpath:/applicationContext-service.xml
            /WEB-INF/applicationContext*.xml
            /WEB-INF/i18n.xml
           <!--  /WEB-INF/*.properties -->
           
        </param-value>
    </context-param>

    <filter-mapping>
        <filter-name>sitemesh</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
    </filter-mapping>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!--<filter-mapping>
        <filter-name>lazyLoadingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>-->
    <filter-mapping>
        <filter-name>rewriteFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    <listener>
        <listener-class>com.lanmosoft.webapp.controller.MyHttpSessionListener</listener-class>
    </listener>

    <servlet>
        <servlet-name>dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    
    <servlet>
        <servlet-name>sitemesh-freemarker</servlet-name>
        <servlet-class>com.opensymphony.module.sitemesh.freemarker.FreemarkerDecoratorServlet</servlet-class>
          <init-param>
              <param-name>TemplatePath</param-name>
              <param-value>/</param-value>
          </init-param>
          <init-param>
              <param-name>default_encoding</param-name>
              <param-value>UTF-8</param-value>
          </init-param>
    </servlet>

    <servlet-mapping>
        <servlet-name>dispatcher</servlet-name>
        <url-pattern>/app/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>sitemesh-freemarker</servlet-name>
        <url-pattern>*.ftl</url-pattern>
    </servlet-mapping>
    
    <!-- cxf服务启动servlet start-->
    <servlet>
    	<servlet-name>CXFServlet</servlet-name>
    	<servlet-class>org.apache.cxf.transport.servlet.CXFServlet</servlet-class>
    </servlet>
    
    <servlet-mapping>  
        <servlet-name>CXFServlet</servlet-name>  
        <url-pattern>/services/*</url-pattern>  
    </servlet-mapping>
    <!-- cxf服务启动servlet end-->


    <welcome-file-list>
        <welcome-file>index.ftl</welcome-file>
    </welcome-file-list>

    <error-page>
        <error-code>404</error-code>
        <location>/404.jsp</location>
    </error-page>

    <error-page>
        <error-code>500</error-code>
        <location>/error.jsp</location>
    </error-page>

    <session-config>
        <session-timeout>120</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <!--<secure>true</secure>-->
        </cookie-config>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>
</web-app>
