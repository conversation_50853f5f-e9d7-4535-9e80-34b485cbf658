/* BEDIN REVOLUTION SLIDER */
.revolution-slider {
	z-index: 1;
	position: relative;
	border-bottom: solid 1px #eee;
	overflow: hidden;
	margin-bottom: 10px;
}

.fullwidthbanner-container ul {
	list-style: none;
	margin: 0;
	padding: 0;
	height: 0;
	overflow: hidden;
}
.tp-bannershadow {
	display: none;
}
.tp-bullets.simplebullets.round .bullet {
	width: 12px;
	height: 12px;
	border-radius: 50% !important;
	background: #d8d8d8;
}
.tp-bullets.simplebullets.round .bullet:hover,
.tp-bullets.simplebullets.round .bullet.selected {
	background: #919191;
}

.slide_title_white {
	color: #fff;
	text-align: center;
	text-transform: uppercase;
	font: 300 47px/66px "Open Sans", sans-serif;
}
.slide_title_white_bold {
	font-weight: 400;
}
.slide_subtitle_white {
	border-top: 1px solid #fff !important;
	border-bottom: 1px solid #fff !important;
	font: 300 18px/25px "Open Sans", sans-serif;
	padding: 5px 10px;
	color: #fff;
	text-align: center;
	text-transform: uppercase;
}
.slider-colored {
	z-index: 1;
	height: 495px !important;
	position: relative;
	border-top: solid 1px #eee;
	border-bottom: solid 1px #eee;
}
.slide_title, .slide_subtitle, .slide_desc, .slide_btn{
	-moz-box-sizing: content-box; 
	-webkit-box-sizing: content-box;
    box-sizing: content-box;
}
.slide_title {
	font: 300 47px/66px "Open Sans", sans-serif;
	color: #3f5862;
	text-transform: uppercase;
}
.slide_subtitle {
	font: 400 18px/25px "Open Sans", sans-serif;
	background: #E84D1C;
	padding: 5px;
	color: #fafafa;
	text-transform: uppercase;
}
.slide_list_item {
	font: 400 14px/20px "Open Sans", sans-serif;
	background: #E84D1C;
	padding: 5px;
	color: #fafafa;
	text-transform: uppercase;
}
.slide_desc {
	color: #7b8793;
	font-size: 14px;
	line-height: 25px;
}
.slide_desc_bordered {
	border-left: 3px solid #ddd !important; 
	padding-left: 5px;
}
.slide_btn {
	font: 300 14px/14px "Open Sans", sans-serif;
	background: #6fc561;
	padding: 7px 15px;
	color: #fff;
	text-transform: uppercase;
	cursor: pointer;
}
.tp-leftarrow.default,
.tp-rightarrow.default{
	opacity: 0.7;
}
.tp-leftarrow.default:hover,
.tp-rightarrow.default:hover {
	opacity: 1;
}
.revolution-slider .hidearrows {
	opacity: 0;
}