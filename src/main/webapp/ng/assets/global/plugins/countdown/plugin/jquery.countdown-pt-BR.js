/* http://keith-wood.name/countdown.html
   Brazilian initialisation for the jQuery countdown extension
   Translated by <PERSON><PERSON> (<EMAIL>) Feb 2008.
   and <PERSON> (juan.roldan[at]relayweb.com.br) Mar 2012. */
(function($) {
	$.countdown.regional['pt-BR'] = {
		labels: ['<PERSON><PERSON>', '<PERSON><PERSON>', 'Se<PERSON><PERSON>', '<PERSON><PERSON>', 'Horas', 'Minuto<PERSON>', 'Segundos'],
		labels1: ['An<PERSON>', 'M�s', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Min<PERSON>', '<PERSON><PERSON><PERSON>'],
		compactLabels: ['a', 'm', 's', 'd'],
		whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regional['pt-BR']);
})(jQuery);
