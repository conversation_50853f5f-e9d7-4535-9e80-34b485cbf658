/* LayerSlider Full Width Dark Skin */



.ls-fullwidthdark .ls-ct-half {
	background: #444;
}

.ls-fullwidthdark .ls-ct-center {
	background: white;
}

.ls-fullwidthdark .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-fullwidthdark .ls-playvideo,
.ls-fullwidthdark .ls-nav-sides,
.ls-fullwidthdark .ls-bottom-slidebuttons a,
.ls-fullwidthdark .ls-nav-prev,
.ls-fullwidthdark .ls-nav-next,
.ls-fullwidthdark .ls-nav-start,
.ls-fullwidthdark .ls-nav-stop,
.ls-fullwidthdark .ls-fullscreen,
.ls-fullwidthdark .ls-loading-container {
	background-image: url(skin.png);	
}

.ls-fullwidthdark .ls-playvideo {
	background-position: -300px -150px;
}

.ls-fullwidthdark .ls-playvideo:hover,
.ls-fullwidthdark .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-fullwidthdark .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-fullwidthdark .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-fullwidthdark .ls-nav-next {
	background-position: -150px 0px;
}

.ls-fullwidthdark .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-fullwidthdark .ls-nav-start {
	background-position: -300px 0px;
}

.ls-fullwidthdark .ls-nav-start:hover,
.ls-fullwidthdark .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-fullwidthdark .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-fullwidthdark .ls-nav-stop:hover,
.ls-fullwidthdark .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-fullwidthdark .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-fullwidthdark .ls-bottom-slidebuttons a.ls-nav-active,
.ls-fullwidthdark .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-fullwidthdark .ls-nav-sideleft {
	background-position: -150px -150px;	
}

.ls-fullwidthdark .ls-nav-sideright {
	background-position: -225px -150px;	
}



.ls-fullwidthdark .ls-nav-prev,
.ls-fullwidthdark .ls-nav-next {
	width: 40px;
	height: 40px;
	z-index: 10000;
	top: 50%;
	margin-top: -20px;	
	position: absolute;
}

.ls-fullwidthdark .ls-nav-prev {
	left: 10px;	
}

.ls-fullwidthdark .ls-nav-next {
	right: 10px;	
}



.ls-fullwidthdark .ls-bottom-slidebuttons,
.ls-fullwidthdark .ls-bottom-slidebuttons a,
.ls-fullwidthdark .ls-nav-start,
.ls-fullwidthdark .ls-nav-stop,
.ls-fullwidthdark .ls-nav-sides {
	height: 20px;
}

.ls-fullwidthdark .ls-bottom-slidebuttons,
.ls-fullwidthdark .ls-bottom-slidebuttons a,
.ls-fullwidthdark .ls-nav-start,
.ls-fullwidthdark .ls-nav-stop,
.ls-fullwidthdark .ls-nav-sides {
	display: inline-block;
}

.ls-fullwidthdark .ls-bottom-slidebuttons,
.ls-fullwidthdark .ls-nav-start,
.ls-fullwidthdark .ls-nav-stop,
.ls-fullwidthdark .ls-nav-sides {
	top: -30px;
}

.ls-fullwidthdark .ls-nav-start,
.ls-fullwidthdark .ls-nav-stop {
	width: 25px;
}

.ls-fullwidthdark .ls-bottom-slidebuttons a {
	width: 20px;
}

.ls-fullwidthdark .ls-nav-sides {
	width: 0px;	
}



.ls-fullwidthdark .ls-thumbnail-hover {
	bottom: 30px;
	padding: 2px;
	margin-left: 1px;
}

.ls-fullwidthdark .ls-thumbnail-hover-bg {
	background: #222;
}

.ls-fullwidthdark .ls-thumbnail-hover span {
	border: 5px solid #222;
	margin-left: -5px;
}



.ls-fullwidthdark .ls-thumbnail {
	top: 10px;
}

.ls-fullwidthdark .ls-thumbnail-inner {
	padding: 2px;
	margin-left: -2px;
	background: #222;
}

.ls-fullwidthdark .ls-thumbnail-slide a {
	margin-right: 2px;
}

.ls-fullwidthdark .ls-nothumb {
	background: #333;
}



.ls-fullwidthdark .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;	
}

.ls-fullwidthdark .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);	
}



.ls-fullwidthdark .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-fullwidthdark .ls-fullscreen:hover {
	background-position: -525px -190px;
}