/* Layer<PERSON><PERSON>r Default Skin */



.ls-defaultskin {
	padding: 5px;
	background: #f7f7f7;
	box-shadow: 0px 3px 15px -5px #000;
	-moz-box-shadow: 0px 3px 15px -5px #000;
	-webkit-box-shadow: 0px 3px 15px -5px #000;
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
}

.ls-defaultskin .ls-bottom-nav-wrapper {
	margin: 15px auto 0px auto;
}

.ls-defaultskin .ls-playvideo {
	width: 50px;
	height: 50px;
	margin-left: -25px;
	margin-top: -25px;
}

.ls-defaultskin .ls-playvideo,
.ls-defaultskin .ls-nav-sides,
.ls-defaultskin .ls-bottom-slidebuttons a,
.ls-defaultskin .ls-nav-prev,
.ls-defaultskin .ls-nav-next,
.ls-defaultskin .ls-nav-start,
.ls-defaultskin .ls-nav-stop,
.ls-defaultskin .ls-fullscreen,
.ls-defaultskin .ls-loading-container {
	background-image: url(skin.png);	
}

.ls-defaultskin .ls-playvideo {
	background-position: -300px -150px;
}

.ls-defaultskin .ls-playvideo:hover,
.ls-defaultskin .ls-vpcontainer:hover .ls-playvideo {
	background-position: -375px -150px;
}

.ls-defaultskin .ls-nav-prev {
	background-position: 0px 0px;
}

.ls-defaultskin .ls-nav-prev:hover {
	background-position: 0px -75px;
}

.ls-defaultskin .ls-nav-next {
	background-position: -150px 0px;
}

.ls-defaultskin .ls-nav-next:hover {
	background-position: -150px -75px;
}

.ls-defaultskin .ls-nav-start {
	background-position: -300px 0px;
}

.ls-defaultskin .ls-nav-start:hover,
.ls-defaultskin .ls-nav-start-active {
	background-position: -300px -75px;
}

.ls-defaultskin .ls-nav-stop {
	background-position: -450px 0px;
}

.ls-defaultskin .ls-nav-stop:hover,
.ls-defaultskin .ls-nav-stop-active {
	background-position: -450px -75px;
}

.ls-defaultskin .ls-bottom-slidebuttons a {
	background-position: 0px -150px;
}

.ls-defaultskin .ls-bottom-slidebuttons a.ls-nav-active,
.ls-defaultskin .ls-bottom-slidebuttons a:hover {
	background-position: -75px -150px;
}

.ls-defaultskin .ls-nav-sideleft {
	background-position: -150px -150px;	
}

.ls-defaultskin .ls-nav-sideright {
	background-position: -225px -150px;	
}



.ls-defaultskin .ls-nav-prev,
.ls-defaultskin .ls-nav-next {
	width: 45px;
	height: 55px;
	z-index: 10000;
	top: 50%;
	margin-top: -27px;	
	position: absolute;
}

.ls-defaultskin .ls-nav-prev {
	left: 5px;	
}

.ls-defaultskin .ls-nav-next {
	right: 5px;	
}



.ls-defaultskin .ls-bottom-slidebuttons,
.ls-defaultskin .ls-bottom-slidebuttons a,
.ls-defaultskin .ls-nav-start,
.ls-defaultskin .ls-nav-stop,
.ls-defaultskin .ls-nav-sides {
	height: 28px;
}

.ls-defaultskin .ls-bottom-slidebuttons,
.ls-defaultskin .ls-bottom-slidebuttons a,
.ls-defaultskin .ls-nav-start,
.ls-defaultskin .ls-nav-stop,
.ls-defaultskin .ls-nav-sides {
	display: inline-block;
}

.ls-defaultskin .ls-nav-start,
.ls-defaultskin .ls-nav-stop {
	width: 24px;
}

.ls-defaultskin .ls-bottom-slidebuttons a {
	width: 20px;
}

.ls-defaultskin .ls-nav-sides {
	width: 6px;	
}



.ls-defaultskin .ls-thumbnail-hover {
	bottom: 55px;
	padding: 4px;
	margin-left: 1px;
}

.ls-defaultskin .ls-thumbnail-hover-bg {
	background: white;
	box-shadow: 0px 2px 12px -4px black;
	border-radius: 4px;
}

.ls-defaultskin .ls-thumbnail-hover span {
	border: 5px solid white;
	margin-left: -5px;
}



.ls-defaultskin .ls-thumbnail {
	top: -20px;
}

.ls-defaultskin .ls-thumbnail-inner {
	padding: 5px;
	margin-left: -5px;
	background: white;
	box-shadow: 0px 3px 35px -10px black;
	border-radius: 4px;
}

.ls-defaultskin .ls-thumbnail-slide a {
	margin-right: 5px;
}

.ls-defaultskin .ls-nothumb {
	background: #eee;
}



.ls-defaultskin .ls-above-thumbnails {
	display: none;
}

.ls-defaultskin .ls-below-thumbnails {
	display: block;
	margin-top: -20px;
}



.ls-defaultskin .ls-loading-container {
	width: 40px;
	height: 40px;
	margin-left: -20px;
	margin-top: -20px;
	background-position: -450px -150px;	
}

.ls-defaultskin .ls-loading-indicator {
	width: 22px;
	height: 22px;
	margin-top: 9px;
	background-image: url(loading.gif);	
}



.ls-defaultskin .ls-fullscreen {
	width: 30px;
	height: 30px;
	right: 10px;
	top: 10px;
	background-position: -525px -150px;
}

.ls-defaultskin .ls-fullscreen:hover {
	background-position: -525px -190px;
}