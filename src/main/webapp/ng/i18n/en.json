{"menu_01": "Home", "static_words_0101": "Campus", "static_words_0102": "Welcome", "static_words_0103": "Welcome to TE Management System!", "static_words_0104": "Message", "static_words_0105": "Student Leave Request", "static_words_0106": "Tutor Leave Request", "static_words_0107": "Remind me of student's insufficient balance", "static_words_0108": "Student Follow-ups", "static_words_0109": "Abnormal sign-in", "static_words_0110": "Student Refund", "static_words_0111": "Save before uploading, etc.", "static_words_0112": "Time zone", "menu_02": "Students", "menu_0201": "Student List", "menu_0202": "Student Follow-up", "menu_0203": "Student Refund", "menu_0204": "Reminder of follow-ups", "tab_0201": "Students Info", "tab_0202": "Contract", "tab_0203": "Requirements", "tab_0204": "Available Time", "tab_0205": "Timetable", "tab_0206": "Refund History", "tab_0207": "Leave Request", "tab_0208": "Follow-up", "static_words_0201": "Basic Information", "static_words_0202": "Contact Information", "static_words_0203": "Other Information", "static_words_0204": "Contract", "static_words_0205": "Subjects and Tutors", "static_words_0206": "Requirements", "static_words_0207": "Recur", "static_words_0208": "To", "static_words_0209": "City and Campus", "static_words_0210": "Schedule Timezone Table", "static_words_0211": "Additional Info", "btn_0201": "Previous Week", "btn_0202": "Next Week", "menu_03": "Tutors", "menu_0301": "Tutor List", "menu_0302": "Tutor Leave Request", "tab_0301": "Tutor Information", "tab_0302": "Available Time", "tab_0303": "Subjects and Hourly Rate", "tab_0304": "Change to Timezone", "static_words_0301": "Tutor List", "static_words_0302": "Basic information ", "static_words_0303": "Attachment", "static_words_0304": "Campus", "static_words_0305": "Tutor Contract", "static_words_0306": "Subjects", "msg_0301": "Shortname can be used!", "msg_0301_1": "passport can be used!", "msg_0302": "Shortname exists!", "msg_0302_1": "The teacher who has the same passport number exists, please send e-<NAME_EMAIL>!", "menu_04": "Contracts", "menu_0401": "Contract List", "static_words_0401": "Contract List", "btn_0401": "Refund", "menu_05": "Scheduling", "menu_0501": "Scheduled Sessions", "menu_0502": "Scheduling - Student", "static_words_0501": "Scheduled Sessions List", "static_words_0502": "Scheduling-Student", "static_words_0503": "Subjects and Tutors", "static_words_0504": "Student Requirements", "static_words_0505": "Skip Holidays", "btn_0501": "Show/Hide Details", "btn_0502": "Update student available time", "msg_0601": "Sign in completed", "msg_0602": "Sign in failed", "menu_06": "Attendance", "menu_0601": "Sign-in", "menu_0602": "Attendance List", "static_words_0601": "Sign-in List", "btn_0601": "Bulk Sign In", "btn_0602": "Sign in", "btn_0603": "Abnormal Sign-in", "btn_0604": "Cancel SI", "btn_0605": "Cancel Actived SI", "msg_0603": "Batch sign-in completed", "msg_0604": "<PERSON><PERSON> sign-in failed", "msg_0605": "Cancel sign-in completed", "msg_0606": "Cancel sign-in failed", "menu_07": "Config", "menu_0701": "Basic Data", "menu_0702": "Product", "static_words_0701": "Basic Data Maintenance", "static_words_0702": "Product Management", "tab_0701": "City", "tab_0702": "Campus", "tab_0703": "Classroom", "tab_0704": "Session time", "tab_0705": "Holidays  ", "tab_0706": "Level Classification", "tab_0707": "<PERSON><PERSON><PERSON><PERSON>", "menu_08": "Settings", "menu_0801": "Organizational Structure", "menu_0802": "Authorization Management", "tab_0801": "Organizational Structure", "tab_0802": "User Management", "btn_0801": "New Departments", "btn_0802": "New Position", "btn_0803": "New Users", "btn_0804": "Add Class", "msg_0801": "Authorization has been set successfully!", "msg_0802": "Failed to set user authorization!", "menu_09": "Report", "menu_0901": "Timetables", "menu_0902": "Student Class Taking Report", "menu_0903": "TE Teacher's Salary", "menu_0904": "Students to renew lesson time", "menu_0905": "Performance-<PERSON><PERSON>", "menu_0906": "Performance-Student's recommendation", "menu_0907": "User-defined Report", "menu_10": "Tutor <PERSON>", "menu_1001": "Timetable", "tab_1001": "List View", "tab_1002": "Calendar View", "tab_1002_1": "Calendar lesson schedule", "menu_11": "language", "menu_12": "Center", "menu_1201": "Personal Inforamation", "menu_1202": "logout", "menu_1203": "Support", "global_static_words_0001": "Monday", "global_static_words_0002": "Tuesday", "global_static_words_0003": "Wednesday", "global_static_words_0004": "Thursday", "global_static_words_0005": "Friday", "global_static_words_0006": "Saturday", "global_static_words_0007": "Sunday", "global_btn_0001": "Add New", "global_btn_0002": "View  ", "global_btn_0003": "Edit", "global_btn_0004": "Delete", "global_btn_0005": "Find", "global_btn_0006": "Back", "global_btn_0007": "Save", "global_btn_0008": "Upload", "global_btn_0009": "Download", "global_btn_0010": "Input", "global_btn_0011": "Output", "global_btn_0012": "Submit", "global_btn_0013": "Close", "global_btn_0014": "<PERSON><PERSON>", "global_btn_0015": "Verified and Approved", "global_btn_0016": "Verified but not Approved", "global_btn_0017": "Reset", "global_btn_0018": "Verified", "global_btn_0019": "Upload in bulk", "global_msg_0001": "The information has been saved successfully!", "global_msg_0002": "ERROR: The information can not be saved!", "global_msg_0003": "Deleted!", "global_msg_0004": "Can not be deleted!", "global_msg_0005": "Failed to access data!", "global_msg_0006": "Password has been changed successfully!", "global_msg_0007": "Original password isn't correct!", "global_msg_0008": "Password cannot be changed!", "global_msg_0009": "Get List", "global_msg_0010": "My query scheme", "global_msg_0011": "Save query scheme", "table_filed_00001": "", "table_filed_00002_1": "Scheme Name", "table_filed_00002": "Chinese Name", "table_filed_00003": "English Name", "table_filed_00004": "Gender", "table_filed_00005": "Age", "table_filed_00006": "Birth Date", "table_filed_00007": "Nationality", "table_filed_00008": "Mobile Phone", "table_filed_00008_1": "Secondary Mobile Phone", "table_filed_00009": "Email", "table_filed_00009_1": "Secondary email", "table_filed_00010": "WeChat", "table_filed_00011": "Year", "table_filed_00012": "City", "table_filed_00013": "Campus", "table_filed_00014": "Current School", "table_filed_00015": "Home Address", "table_filed_00016": "", "table_filed_00017": "Name(P)", "table_filed_00018": "Name(S)", "table_filed_00019": "Relation(P)", "table_filed_00020": "Relation(S)", "table_filed_00021": "Mobile Phone(P)", "table_filed_00022": "Mobile Phone(S)", "table_filed_00023": "Company(P)", "table_filed_00024": "Company(S)", "table_filed_00025": "Occupation(P)", "table_filed_00026": "Occupation(s)", "table_filed_00027": "WeChat(P)", "table_filed_00028": "WeChat(S)", "table_filed_00029": "Passport/ID(P)", "table_filed_00030": "Passport/ID(S)", "table_filed_00031": "Email(P)", "table_filed_00032": "Email(S)", "table_filed_00033": "", "table_filed_00034": "Source", "table_filed_00035": "Referee", "table_filed_00036": "Consultant", "table_filed_00037": "tppId", "table_filed_00038": "Student Type", "table_filed_00038-2": "Contract Type", "table_filed_00038-3": "Contract Classification", "table_filed_00038-4": "Teaching Classification", "table_filed_00039": "Study Advisor", "table_filed_00040": "OC Class", "table_filed_00041": "OC Class Time", "table_filed_00042": "Status", "table_filed_00043": "Frozen Date", "table_filed_00044": "Remarks", "table_filed_00045": "", "table_filed_00046": "Contract No.", "table_filed_00047": "Company Name", "table_filed_00048": "Student Name", "table_filed_00049": "Student Number", "table_filed_00050": "Tuition", "table_filed_00050a": "Tuition Without VAT", "table_filed_00050b": "VAT Rate", "table_filed_00050c": "VAT", "table_filed_00051": "Unit", "table_filed_00052": "Level", "table_filed_00053": "Start Date", "table_filed_00054": "End Date", "table_filed_00055": "Student Type", "table_filed_00056": "Contract Status", "table_filed_00057": "", "table_filed_00058": "Subjects", "table_filed_00059": "Teaching Method", "table_filed_00060": "Level Classification", "table_filed_00061": "Level", "table_filed_00062": "Tutors", "table_filed_00063": "Unit", "table_filed_00064": "Start Date", "table_filed_00065": "End Date", "table_filed_00065_1": "Teacher BUType", "table_filed_00066": "Status", "table_filed_00067": "Subject", "table_filed_00068": "Classification", "table_filed_00069": "Start Date", "table_filed_00070": "End Date", "table_filed_00071": "Lesson Interval", "table_filed_00072": "Min Hrs per Day", "table_filed_00073": "Max Hrs per Day", "table_filed_00074": "Min Hrs per Week", "table_filed_00075": "Max Hrs per Week", "table_filed_00076": "Min Hrs per Month", "table_filed_00077": "Max Hrs per Month", "table_filed_00078": "Min Total Hrs", "table_filed_00079": "Max Total Hrs", "table_filed_00080": "", "table_filed_00081": "Date", "table_filed_00082": "Tutor", "table_filed_00083": "Classroom", "table_filed_00084": "Teaching Method", "table_filed_00085": "Attendance", "table_filed_00086": "", "table_filed_00087": "Refund Hours", "table_filed_00088": "Refund Amount", "table_filed_00089": "Refund Date", "table_filed_00090": "Reason", "table_filed_00091": "Status", "table_filed_00092": "", "table_filed_00093": "Submit Time", "table_filed_00094": "24 hours+", "table_filed_00095": "", "table_filed_00096": "Follow up date", "table_filed_00097": "Method", "table_filed_00098": "Follow up activities", "table_filed_00099": "Time for next follow-up", "table_filed_00100": "", "table_filed_00101": "Student Number", "table_filed_00102": "Student Name", "table_filed_00103": "Refund Units", "table_filed_00104": "Refund Amount", "table_filed_00105": "Refund Time", "table_filed_00106": "", "table_filed_00107": "Student Number", "table_filed_00108": "", "table_filed_00109": "ERP ID", "table_filed_00110": "Chinese Name", "table_filed_00111": "English Name", "table_filed_00112": "Short Name", "table_filed_00113": "ID No.", "table_filed_00114": "Passport No.", "table_filed_00115": "Tutor Type", "table_filed_00116": "Address", "table_filed_00117": "Attachment", "table_filed_00118": "Created Time", "table_filed_00119": "Attachment Name", "table_filed_00120": "Campus", "table_filed_00121": "Start Date", "table_filed_00122": "End Date", "table_filed_00123": "Teacher's Contract", "table_filed_00124": "Contract Effective Date", "table_filed_00125": "Contract End Date", "table_filed_00126": "Position Type", "table_filed_00127": "Select file", "table_filed_00128": "Drag files here to upload", "table_filed_00129": "Mass upload", "table_filed_00130": "Upload", "table_filed_00131": "Upload Progress", "table_filed_00132": "To be Uploaded    Time needed to upload", "table_filed_00133": "", "table_filed_00134": "Interval Lower Limit", "table_filed_00135": "Imterval Upper Limit", "table_filed_00136": "Hourly Rate Cycle", "table_filed_00137": "Academic Level", "table_filed_00138": "Teaching Method", "table_filed_00139": "Start Time", "table_filed_00140": "End Time", "table_filed_00141": "Hourly Rate", "table_filed_001488": "Unconfirmed classes on the day", "table_filed_00142": "", "table_filed_00143": "Type", "table_filed_00144": "Student ZH Name", "table_filed_00145": "Student EN Name", "table_filed_00146": "Session Time", "table_filed_00147": "Tutor <PERSON>", "table_filed_00148": "Tutor <PERSON>", "table_filed_00149": "Teaching Method", "table_filed_00149_1": "End of class date", "table_filed_00150": "Classroom", "table_filed_00151": "Leave Request", "table_filed_00151a": "Scheduled By", "table_filed_00152": "Application Time", "table_filed_00153": "24 hours+", "table_filed_00154": "Deduct student    hour(s)", "table_filed_00155": "Add   hour(s) to tutor salary", "table_filed_00156": "Reason", "table_filed_00157": "Created by", "table_filed_00158": "Time", "table_filed_00159": "Free Hours to Student", "table_filed_00160": "Deduct Tutor   <PERSON>", "table_filed_00161": "", "table_filed_00162": "Total Hours", "table_filed_00163": "Scheduled Hours", "table_filed_00164": "Not Scheduled Hours", "table_filed_00165": "Hours Taken", "table_filed_00165_1": "Consume class hours", "table_filed_00667": "已经重新排课", "table_filed_00668": "不需要重新排课", "table_filed_00669": "offline Scheduled Hours", "table_filed_00670": "online Scheduled Hours", "table_filed_00166": "Reason for Cancel", "table_filed_00167": "Auto Scheduling", "table_filed_00168": "Created Time", "table_filed_00169": "Actual Start Date", "table_filed_00170": "Actual Start Time", "table_filed_00171": "Actual End Time", "table_filed_00172": "Skip Holidays", "table_filed_00173": "", "table_filed_00174": "Date", "table_filed_00175": "Hours", "table_filed_00176": "Attendance", "table_filed_00176_1": "Whether absence is displayed", "table_filed_00177": "Approval", "table_filed_00178": "Reason for Change", "table_filed_00179": "<PERSON><PERSON>", "table_filed_00180": "Tutor <PERSON>", "table_filed_00181": "Student Late", "table_filed_00182": "Student No Show", "table_filed_00183": "Half Unit", "table_filed_00184": "One Unit", "table_filed_00185": "Student Class Taken", "table_filed_00186": "Free Hours to Student", "table_filed_00187": "<PERSON><PERSON>", "table_filed_00188": "Deducted Tutor", "table_filed_00189": "Miscellaneous Expenses", "table_filed_00190": "", "table_filed_00191": "Basic Data Maintenance", "table_filed_00192": "City Name", "table_filed_00193": "City Code", "table_filed_00194": "Campus Name", "table_filed_00195": "Campus Code", "table_filed_00196": "Location", "table_filed_00197": "Classroom", "table_filed_00197_1": "Campus mailbox", "table_filed_00197_2": "Campus mailbox(Multiple mailboxes separated by commas)", "table_filed_00198": "Campus", "table_filed_00199": "Reset", "table_filed_00200": "Name of Holiday(s) ", "table_filed_00201": "Date of Holiday(s)", "table_filed_00202": "Classification", "table_filed_00203": "Subject", "table_filed_00204": "", "table_filed_00205": "Product ID", "table_filed_00206": "Product Name", "table_filed_00207": "Price  ", "table_filed_00208": "pricebookId", "table_filed_00209": "Product Type", "table_filed_00210": "productCategory", "table_filed_00211": "Disable", "table_filed_00212": "", "table_filed_00213": "User Name", "table_filed_00214": "Password", "table_filed_00215": "User Type", "table_filed_00216": "Teach lessons or not", "table_filed_00217": "Department", "table_filed_00218": "Position  ", "table_filed_00219": "Campus", "table_filed_00220": "Access (Campus)", "table_filed_00221": "Remark: The user can only access the campus he/she belongs to if no campus is chosen. ", "table_filed_00221_1": "Lock Data ", "table_filed_00222": "Add Departments", "table_filed_00223": "Department Name", "table_filed_00224": "Report to", "table_filed_00225": "Description", "table_filed_00226": "Add Position", "table_filed_00227": "Title", "table_filed_00228": "Belong to (Department)", "table_filed_00229": "Report to", "table_filed_00230": "", "table_filed_00231": "Campus", "table_filed_00232": "Student Name(ZH)", "table_filed_00233": "Tutor <PERSON>(ZH)", "table_filed_00234": "Student Name(EN)", "table_filed_00235": "Teacher Name(EN)", "table_filed_00236": "Week", "table_filed_00237": "", "table_filed_00238": "Remaining Hours", "table_filed_00239": "Hours Taken", "table_filed_00240": "Total Hours", "table_filed_00241": "Late (Hours)", "table_filed_00242": "Absent (Hours)", "table_filed_00243": "", "table_filed_00244": "Tutor Name", "table_filed_00245": "Start Date", "table_filed_00246": "End Date", "table_filed_00247": "Hourly Rate", "table_filed_00248": "Hour(s)", "table_filed_00249": "Salary", "table_filed_00250": "", "table_filed_00251": "Consultant", "table_filed_00252": "Remaining Hours", "table_filed_00253": "Hours Taken", "table_filed_00254": "Hours Scheduled", "table_filed_00255": "", "table_filed_00256": "Renew Students", "table_filed_00257": "Renew Hours", "table_filed_00258": "<PERSON>w Amount", "table_filed_00259": "", "table_filed_00260": "Referee", "table_filed_00261": "Referral", "table_filed_00262": "Referral Amount", "table_filed_00263": "Study Report", "table_filed_00264": "Please select", "table_filed_00265": "Save tutor information", "table_filed_00266": "Save tutor information before uploading.", "table_filed_00266_1": "After saving, please leave the teacher campus and exit, otherwise you may not see the new teacher in the teacher list", "table_filed_00266_2": "Please save and then maintain the student area", "table_filed_00267": " Save tutor information", "table_filed_00268": "This information will be deleted forever. Do you want to continue?", "table_filed_00269": " Please choose records to be deleted", "table_filed_00270": "Your information was deleted", "table_filed_00271": "Delete all", "table_filed_00272": "Bulk Delete", "table_filed_00273": "Note: The file can only be seen by who upload it. Please share to others,if other user wants to see it.", "table_filed_00274": "The file can be seen by others, however, deleted only by the user", "table_filed_00275": "Upload Time", "table_filed_00276": "Upload File", "table_filed_00277": "Upload All", "table_filed_00278": "Choose files to import", "table_filed_00279": "Add files to import", "table_filed_00280": "Importing! Please wait.", "table_filed_00281": "Reminder", "table_filed_00282": "Please click the link to download", "table_filed_00283": "Export complete", "table_filed_00284": "Please delete the largest interval", "table_filed_00285": "Start Time cannot be later than End Time", "table_filed_00286": "Please select the campus for student", "table_filed_00287": "Select tutor", "table_filed_00288": "Select student", "table_filed_00289": "Selected by", "table_filed_00290": "Choose files imported", "table_filed_00291": "EXCEL files must be strictly in the specified format. Pay attention to the selectable ", "table_filed_00292": "Select All", "table_filed_00293": "SA Chinese Name", "table_filed_00294": "SA English Name", "table_filed_00295": "Free Hour for referee", "table_filed_00296": "Referee's Name", "table_filed_00297": "Refree's Contract No.", "table_filed_00298": "OC Course Status", "table_filed_00299": "Regular Session", "table_filed_00300": "Session ID", "table_filed_00301": "Session Type", "table_filed_00302": "Level", "table_filed_00303": "<PERSON><PERSON> Timetable", "table_filed_00304": "Virtual Session.Pay tutor and deduct student hours.", "table_filed_00305": "Real Session. No payment to tutor, and no deduction of student hours.", "table_filed_00306": "Real session. Pay tutor and deduct student hours.", "table_filed_00307": "Session Start Time", "table_filed_00308": "Session End Time", "table_filed_00309": "Leave Start Time", "table_filed_00310": "Leave End Time", "table_filed_00310_1": "Leave End Date", "table_filed_00311": "Tutor No.", "table_filed_00312": "Tutor profiles: name+space+active (%)+space+match (%)+space+number of active students", "table_filed_00313": "Tutor Subjects", "table_filed_00314": "Tutor Leave Request", "table_filed_00315": "Cancel teacher LR", "table_filed_00315_1": "Cancel actived teacher LR", "table_filed_00316": "Details", "table_filed_00317": "Choose tutor", "table_filed_00318": "Deduct tutor   hour(s)", "table_filed_00319": "Lock Status", "table_filed_00320": "Current Status", "table_filed_00321": "Status Management", "table_filed_00322": "Status Name", "table_filed_00323": "Status Value", "table_filed_00324": "Dropout Date", "table_filed_00325": "Follow-up Date", "table_filed_00326": "Thawing Date", "table_filed_00327": "Leave Date", "table_filed_00328": "Normal Attendance", "table_filed_00329": "Absent", "table_filed_00330": "Late", "table_filed_00331": "Refund Reason", "table_filed_00332": "1:1", "table_filed_00333": "Free TE Type", "table_filed_00334": "Refund Type", "table_filed_00335": "Choose a type to maintain", "table_filed_00336": "Supported Type: XLS (not exceed 20MB)", "table_filed_00337": "24 hours+", "table_filed_00338": "Creator ID", "table_filed_00339": "Paid but not scheduled", "table_filed_00340": "Auto scheduled or not", "table_filed_00341": "Classes Taken", "table_filed_00342": "Houly Rate", "table_filed_00343": "Sold Hours", "table_filed_00344": "Free hours for referee", "table_filed_00345": "Free hours", "table_filed_00346": "Miscellaneous expenses", "table_filed_00347": "Chart of Department Positions", "table_filed_00348": "Change department", "table_filed_00349": "Top department", "table_filed_00350": "Change Position", "table_filed_00351": "Hour", "table_filed_00352": "Money", "table_filed_00353": "TE Service Report", "table_filed_00353_1": "Student Class Report", "table_filed_00353_2": "Teacher Class Report", "table_filed_00353_3": "Student Consumption Report", "table_filed_00354": "Sessions for ", "table_filed_00355": "Performance", "table_filed_00356": "New Students of Regional Office Report", "table_filed_00357": "Students Leave Request", "table_filed_00358": "Original password", "table_filed_00359": "New password", "table_filed_00360": "Confirm new password", "table_filed_00361": "Cancel", "table_filed_00362": "Authorization Management", "table_filed_00363": "Authorization", "table_filed_00364": "File cabinet", "table_filed_00365": "File name", "table_filed_00366": "Shared By", "table_filed_00367": "Created By", "table_filed_00368": "No.", "table_filed_00369": "Operation", "table_filed_00370": "Start", "table_filed_00371": "End", "table_filed_00372": "Shared file", "table_filed_00373": "File name", "table_filed_00374": "Share", "table_filed_00375": "Confirm", "table_filed_00376": "To All", "table_filed_00377": "Shared with", "table_filed_00378": "Sessions Scheduled and Taken Report", "table_filed_00379": "Not Scheduled Hours", "table_filed_00380": "Hours Taken", "table_filed_00381": "Hours Scheduled", "table_filed_00382": "Total Hours", "table_filed_00383": "Timetable", "table_filed_00384": "Session Details", "table_filed_00385": "", "table_filed_00386": " Page", "table_filed_00387": "Total ", "table_filed_00388": "", "table_filed_00389": "Total ", "table_filed_00390": " Records", "table_filed_00391": "New", "table_filed_00392": "Quantity", "table_filed_00393": "Related contract No.", "table_filed_00394": "Dropout?", "table_filed_00395": "Virtual contract?", "table_filed_00396": "Payment", "table_filed_00397": "Refund by Contract Change", "table_filed_00398": "Refund by Actual Unit Price", "table_filed_00399": "Product", "table_filed_00400": "Price", "table_filed_00401": "Pending", "table_filed_00402": "In Progress", "table_filed_00403": "Completed", "table_filed_00404": "Contract Details", "table_filed_00405": "Dropout", "table_filed_00406": "Date of birth", "table_filed_00407": "Start", "table_filed_00408": "Yes", "table_filed_00409": "No", "table_filed_00410": "Sessions Scheduled for ", "table_filed_00411": "Tutor Name", "table_filed_00412": "Utilization", "table_filed_00413": "All", "table_filed_00414": "<PERSON><PERSON>", "table_filed_00415": "Original name", "table_filed_00416": "Shared name", "table_filed_00417": "Outstanding", "table_filed_00418": "Frozen", "table_filed_00419": "Phone", "table_filed_00420": "Email", "table_filed_00421": "File size", "table_filed_00422": "No. of students", "table_filed_00423": "OC Session", "table_filed_00424": "1 ", "table_filed_00425": "2 ", "table_filed_00425_1": "3 ", "table_filed_00426": "Choose subject", "table_filed_00427": "No data", "table_filed_00428": "Data format example: 08:00 (24 hours)", "table_filed_00429": " to ", "table_filed_00430": "Related subjects", "table_filed_00431": "<PERSON><PERSON> Amount", "table_filed_00432": "Month", "table_filed_00433": "Consumption rate", "table_filed_00434": "Total Amount", "table_filed_00435": "Part-time Salary", "table_filed_00436": "Full-time Sal<PERSON>", "table_filed_00437": "Current position", "table_filed_00438": "organization", "table_filed_00439": "Organization details", "table_filed_00440": "Organization Tree", "table_filed_00441": "On-site", "table_filed_00442": "<PERSON><PERSON>", "table_filed_00443": "Invalid", "table_filed_00444": "Male", "table_filed_00445": "Female", "table_filed_00446": "Session Details", "table_filed_00447": "Classroom", "table_filed_00448": "On-site", "table_filed_00449": "Remotely", "table_filed_00450": "Special I", "table_filed_00451": "Special II", "table_filed_00452": "Special III", "table_filed_00453": "Initial", "table_filed_00454": "Confirmed", "table_filed_00455": "Document requirement", "table_filed_00456": "New/Edit", "table_filed_00457": "SF ID", "table_filed_00458": "Receive SMS", "table_filed_00459": "Primary Contact", "table_filed_00459_1": "Secondary contact", "table_filed_00460": "Print", "table_filed_00461": "OC session Time", "table_filed_00462": "OC session", "table_filed_00463": "Pending for 1st-level approval", "table_filed_00464": "Reject", "table_filed_00465": "Pending for 2nd-level approval", "table_filed_00466": "1st-level Approve", "table_filed_00467": "2nd-level Approve", "table_filed_00468": "1st-level Reject", "table_filed_00469": "2nd-level Reject", "table_filed_00470": "Not taken", "table_filed_00471": "Taken", "table_filed_00472": "N/A", "table_filed_00473": "Study Advisor", "table_filed_00474": "SA - Free TE", "table_filed_00475": "SC - Free TE", "table_filed_00476": "TE - Referral", "table_filed_00477": "TE - others", "table_filed_00478": "Full-time", "table_filed_00479": "Part-time", "table_filed_00480": "Semi-full-time", "table_filed_00481": "In-service", "table_filed_00482": "Dimission", "table_filed_00483": "Frozen", "table_filed_00484": "Subjects", "table_filed_00485": "Month", "table_filed_00486": "Week", "table_filed_00487": "Study period", "table_filed_00488": "Student ID", "table_filed_00489": "Administrator", "table_filed_00490": "Staff", "table_filed_00491": "User details", "table_filed_00492": "It won't change if you do not fill", "table_filed_00493": "To be approved", "table_filed_00494": "Approver", "table_filed_00495": "Last Modified By", "table_filed_00496": "Last Modified At", "table_filed_00497": "Contract", "table_filed_00498": "Bulk Confirm", "table_filed_00499": "Deleted", "table_filed_00500": "N/A", "table_filed_00501": "Confirm", "table_filed_00502": "<PERSON><PERSON>", "table_filed_00503": "Cancel student LR", "table_filed_00503_1": "Cancel actived student LR", "table_filed_00504": "Student LR List", "table_filed_00505": "Tutor <PERSON>", "table_filed_00506": "Leave Request Details", "table_filed_00507": "English", "table_filed_00508": "Exam", "table_filed_00509": "New Student", "table_filed_00510": "Classes Taken", "table_filed_00511": "Special session", "table_filed_00512": "Scheduling - <PERSON><PERSON>", "table_filed_00513": "Scheduling - Student", "table_filed_00514": "Tutor <PERSON> - Summary", "table_filed_00515": "System Log", "table_filed_00516": "Log Details", "table_filed_00517": "Teacher confirmation", "table_filed_00518": "Student confirmation", "table_filed_00519": "TE-结转课时报表", "table_filed_00520": "Carry-over time", "table_filed_00521": "Transfer rate", "table_filed_00522": "结转收入", "table_filed_00523": "The number of new students", "table_filed_00524": "续报率", "table_filed_00525": "The number of seniors", "table_filed_00526": "Salary details", "table_filed_00527": "Payment date", "table_filed_00528": "Class time fee(RMB)", "table_filed_00529": "Professor hours", "table_filed_00530": "other fee(RMB)", "table_filed_00531": "Handle(RMB)", "table_filed_00531_1": "<PERSON><PERSON>", "table_filed_00532": "Export teacher class schedule", "table_filed_00533": "Export student class schedule", "table_filed_00534": "Refund details", "table_filed_00535": "Teach remotely(Need a classroom)", "table_filed_00536": "Student attachments", "table_filed_00537": "Freeze begins", "table_filed_00538": "No sign", "table_filed_00539": "The first payment date", "table_filed_00551": "<PERSON>ail Te<PERSON>late", "table_filed_00540": "Campus Type", "table_filed_00541": "online", "table_filed_00542": "offline", "table_filed_00543": "国家/地区", "table_filed_00544": "display", "table_filed_00545": "timezone", "table_filed_00546": "Currency Code", "table_filed_00546_1": "<PERSON><PERSON><PERSON><PERSON>", "table_filed_00547": "Name", "table_filed_00548": "Receive Email", "table_filed_00549": "OC Start Time", "table_filed_00550": "OC End Time", "table_filed_00552": "Schedule Timezone", "table_filed_00553": "Campus", "table_filed_00554": "Main Campus", "table_filed_00555": "Main Campus Start Time", "table_filed_00556": "Main Campus End Time", "table_filed_00557": "URL", "table_filed_00557-1": "Meeting ID", "table_filed_00558": "Invalid Time", "table_filed_00559": "Scheme No", "table_filed_00600": "<PERSON><PERSON>", "table_filed_00601": "Invalid", "table_filed_00602": "Hourly Rate Scheme", "table_filed_00603": "Defined Houly Rate", "table_filed_00604": "interval tracing", "table_filed_00605": "lower limit", "table_filed_00606": "upper limit", "table_filed_00852": "Jump To", "table_filed_00607": "Hourly Rate Scheme detail", "table_filed_00608": "Start Time", "table_filed_00634": "End Time", "table_filed_00609": "Need Classroom", "table_filed_00610": "Need Classroom", "table_filed_00611": "Scheduling - Student-Basic Version", "table_filed_00612": "Scheduling-Upgrade", "table_filed_00613": "hybrid", "table_filed_00615": "fullName", "table_filed_00616": "show Name", "table_filed_00617": "email acount", "table_filed_00618": "email Template", "table_filed_00619": "acoountName", "table_filed_00620": "host", "table_filed_00621": "protocol", "table_filed_00622": "port", "table_filed_00623": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "table_filed_00624": "password", "table_filed_00625": "tempalateName", "table_filed_00626": "type", "table_filed_00627": "subject", "table_filed_00628": "emailAccount", "table_filed_00629": "content", "table_filed_00630": "Student", "table_filed_00631": "Tutor", "table_filed_00632": "Label", "table_filed_00633": "Payment Method", "table_filed_00635": "Bank", "table_filed_00636": "Cash", "table_filed_00637": "B2B", "table_filed_00638": "<PERSON><PERSON><PERSON>", "table_filed_00639": "Special Case", "table_filed_00640": "Most recent class date", "table_filed_00641": "3rd party partner", "table_filed_00642": "University", "table_filed_00643": "Settlement", "table_filed_00644": "Monthly", "table_filed_00645": "Half monthly", "table_filed_00646": "Scheduling-SmallGroup", "table_filed_00647": "Small groups", "table_filed_00648": "Class name", "table_filed_00649": "Class code", "table_filed_00650": "Tutor", "table_filed_00651": "Assis. <PERSON>", "table_filed_00652": "月度上课时间", "table_filed_00653": "排课频率及科目比例", "table_filed_00654": "级别", "table_filed_00655": "科目+老师", "table_filed_00656": "备注/课表更改具体诉求", "table_filed_00657": "常规", "table_filed_00658": "非常规", "table_filed_00659": "协调沟通", "table_filed_00660": "排课诉求", "table_filed_00661": "排课类型", "table_filed_00662": "日常排课", "table_filed_00663": "月度排课", "table_filed_00664": "超课时排课", "table_filed_00665": "Reschedule status", "table_filed_00666": "需要重新排课", "table_filed_00968": "Specialist", "table_filed_00969": "Specialist已耗", "table_filed_00970": "Specialist已排", "table_filed_00971": "Specialist总课时", "table_filed_00972": "紧急排课", "table_filed_00973": "Sync OK", "table_filed_00977": "Online TE", "new_alert_0032": "Please fill in time", "new_msg_0001": "Please delete the largest interval data", "new_msg_0002": "Please edit the largest interval data", "new_alert_0001": "Start time cannot be later than End Time", "new_alert_0002": "Please assign campus for student", "new_alert_0003": "Campus is not assigned for student!", "new_alert_0004": "Tutor or student is not available in this period", "new_alert_0005": "Tutor is not available", "new_alert_0006": "Student is not available", "new_alert_0007": "Choose session time", "new_alert_0008": "Please select the record to be deleted", "new_alert_0009": "Choose user(s) to share", "new_alert_0010": "Failed to share", "new_alert_0011": "Please choose date within two weeks", "new_alert_0012": "Sort in the same level!", "new_alert_0013": "Submit completed", "new_alert_0014": "Failed to submit", "new_alert_0015": "Start Time cannot be blank", "new_alert_0016": "End Time cannot be blank", "new_alert_0017": "End Time shall be later than Start Time", "new_alert_0018": "The referee cannot be found", "new_alert_0019": "Please enter shortname!", "new_alert_0019_1": "Please enter passport!", "new_alert_0020": "Your information will be deleted forever. Do you want to continue?", "new_alert_0021": "The lower limit must be larger than 0", "new_alert_0022": "Cannt sign in twice!", "new_alert_0023": "Please choose sign-in records!", "new_alert_0024": "Please fill in the reason!", "new_alert_0025": "Approved recrod cannot be cancelled!", "new_alert_0025_1": "NO Approved recrod cannot be cancelled!", "new_alert_0026": "Cannot cancel sign-in! ", "new_alert_0027": "Can<PERSON> cannot be reverted. Do you want to continue?", "new_alert_0028": "Please select records need to be approved!", "new_alert_0029": "Please select records to be deleted", "new_alert_0030": "Please select record to be confirmed.", "new_alert_0031": "This session is locked and cannot be cancelled!", "new_alert_0033": "Scheduled sessions exceed contract 15 hours", "new_alert_0034": "In progress...", "new_alert_0035": "Do not navigate away.", "new_alert_0036": "Wrong date.", "new_alert_0037": "Too many recurrent days.", "new_toastr_0001": "Add completed", "new_toastr_0002": "Failed to add", "new_toastr_0003": "Deleted", "new_toastr_0004": "Failed to delete", "new_toastr_0005": "Query failed", "new_toastr_0006": "Failed to retrieve data", "new_toastr_0007": "Failed to display", "new_toastr_0008": "Level must be unique within the same level group!", "new_toastr_0009": "Two passwords are different. Please enter again!", "new_toastr_0010": "The password has been changed successfully", "new_toastr_0011": "Original password isn't correct", "new_toastr_0012": "Sort completed!", "new_toastr_0013": "Failed to sort!", "new_toastr_0014": "User authority has been set successfully!", "new_toastr_0015": "Failed to set user authority!", "new_toastr_0016": "There is an overlop between Start Time and End Time!", "new_toastr_0017": "This period has been arranged!", "new_toastr_0018": "Failed to load!", "new_toastr_0019": "Time has been repeated!", "new_toastr_0099": "Appeal has been repeated!", "new_toastr_0020": "Verified and Approved！", "new_toastr_0021": "Import completed!", "new_toastr_0022": "Failed to import!", "new_toastr_0023": "You did not start with the initial value!", "new_toastr_0024": "Time interval arises!", "new_toastr_0025": "There is an overlop between teacher's lessons!", "new_toastr_0026": "The user already exists！", "new_toastr_0027": "Sign in completed！", "new_toastr_0028": "Sign in failed!", "new_toastr_0029": "Batch Sign-in completed！", "new_toastr_0030": "Cancel Sign-in completed！", "new_toastr_0031": "Cancel Sign-in failed!", "new_toastr_0032": "Verified and Approved！", "new_toastr_0033": "Verified but not Approved！", "new_toastr_0034": "Comfirm completed！", "new_toastr_0035": "Failed to confirm!", "new_toastr_0036": "Resource for this lesson schedule has been occupied, cannot recover!", "new_toastr_0037": "Recover completed！", "new_toastr_0038": "Failed to recover!", "new_toastr_0039": "Leave Request is approved！", "new_toastr_0040": "Leave Request is not approved！", "new_toastr_0041": "Failed to cancel Leave Request!", "new_toastr_0041_1": "Failed to cancel Leave Request ,Students or teachers are occupied!", "new_toastr_0041_2": "Failed to cancel Sign Request ,Students or teachers are occupied!", "new_toastr_0042": "Leave Request has been cancelled!", "new_toastr_0043": "Failed to schedule lessons automatically", "new_toastr_0044_1": "ERROR: No data, can't be saved!", "new_toastr_0044": "ERROR: The information can not be saved!", "new_toastr_0045": "ERROR: You cannot access the product information!", "new_toastr_0046": "ERROR: <PERSON><PERSON> cannot re-search!", "new_toastr_0047": "You cannot fill in the data", "new_toastr_0047_1": "The length of abbreviation is not greater than 20", "new_toastr_0047_2": "Referred  to as cannot contain spaces", "new_toastr_0048": "ERROR: Teacher's campus  information cannot be saved!", "new_toastr_0049": "Please fill in and complete the data, and then save", "new_toastr_0050": "Sign in failed!", "new_toastr_0051": "<PERSON><PERSON> Sign-in failed", "new_toastr_0052": "Verified but not Approved", "new_toastr_0053": "Please choose the city", "new_toastr_0054": "You did not choose the campus", "new_toastr_0055": "Start Time is incorrect", "new_toastr_0056": "End Time is incorrect", "new_toastr_0057": "Time shall bot be blank", "new_toastr_0058": "Start Time shall not be later than End Time", "new_toastr_0059": "ERROR: Your information can not be saved!", "new_toastr_0060": "Holiday name shall bot be blank", "new_toastr_0061": "Your information has been saved successfully!", "new_toastr_0062": "Your information has been deleted successfully!", "new_toastr_0063": "Sign in completed!", "new_toastr_0064": "Batch Sign-in completed!", "new_toastr_0065": "Share completed！", "new_toastr_0067": "Start time is effective after setting, please delete before you set", "new_toastr_0068": "Synchronous success", "new_toastr_0069": "Synchronization failure", "new_toastr_0070": "Please choose records!", "new_toastr_0074": "Campus has been repeated!!", "new_toastr_0071": "Timezone shall not be blank", "new_toastr_0072": "Region shall not be blank", "new_toastr_0073": "ERROR: Student's campus  information cannot be saved!", "index_login_001": "Welcome to TE Management System", "index_login_002": "<PERSON><PERSON>", "index_login_003": "Remember me", "index_login_004": "Chinese", "index_login_005": "English", "new_alert_0075": "Please fill in OC time", "new_alert_0076": "OC Start time cannot be later than OC End Time", "new_toastr_0077": "ERROR: Student's timezone  information cannot be saved!", "new_alert_0078": "Please fill in main zone time", "new_alert_0079": "Main zone Start time cannot be later than main zone End Time", "new_toastr_0080": "ERROR: Teacher's timezone  information cannot be saved!", "new_toastr_0081": "ERROR:Main zone is haven", "new_toastr_0082": "ERROR:online information cannot be saved!", "new_toastr_0083": "ERROR:Subject has been repeated!", "new_toastr_0084": "ERROR:Subject grade has been repeated!", "new_toastr_0085": "Please select type", "new_toastr_0086": "ERROR:Hourlyrate  teaching way has been repeated!", "new_alert_0087": "Please select a zone", "new_alert_0088": "Please select timezone", "new_alert_0089": "Please set timezone in class schedule", "new_toastr_0090": "Please fill in displayName", "new_toastr_0091": "ERROR:displayName has been repeated!", "new_toastr_0092": "Student already added."}