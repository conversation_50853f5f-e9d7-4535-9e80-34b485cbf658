/***
 lanmosoft AngularJS App Main Script
 ***/

var isdebug = false;
var baseUrl = '../ngres';
/* Metronic App */
var MetronicApp = angular.module("MetronicApp", [
	"ui.router",
	"ui.bootstrap",
	"ui.select",
	'angularFileUpload',
	"oc.lazyLoad",
	"ngSanitize",
	'pascalprecht.translate',
	'flow',
	'ui'
]);


/**
 * 文件上传
 * @param files
 * @param res
 */
function initUploadFiles(files, res) {
	for (var i = 0; i < files.length; i++) {
		var file = files[i];
		var r = { "uniqueIdentifier": file.uniqueIdentifier, "flowFilename": file.name };
		res.push(r);
	}
}

/* Configure ocLazyLoader(refer: https://github.com/ocombe/ocLazyLoad) */
MetronicApp.config(['$ocLazyLoadProvider', function ($ocLazyLoadProvider) {
	$ocLazyLoadProvider.config({
		cssFilesInsertBefore: 'ng_load_plugins_before' // load the above css files before a LINK element with this ID. Dynamic CSS files must be loaded between core and theme css files
	});
}]);

/*语言切换*/
MetronicApp.config(['$translateProvider', function ($translateProvider) {
	var lang = window.localStorage.lang;
	$translateProvider.preferredLanguage(lang);
	$translateProvider.useStaticFilesLoader({
		prefix: '../ng/i18n/',
		suffix: '.json'
	});
}]);

//长度
//{{d.c_name| textLengthSet:true:9:' ...'}}
MetronicApp.filter('textLengthSet', function () {
	return function (value, wordwise, max, tail) {
		if (!value) return '';

		max = parseInt(max, 10);
		if (!max) return value;
		if (value.length <= max) return value;

		value = value.substr(0, max);
		if (wordwise) {
			var lastspace = value.lastIndexOf(' ');
			if (lastspace != -1) {
				value = value.substr(0, lastspace);
			}
		}

		return value + (tail || ' …');//'...'可以换成其它文字
	};
});

//上传文件
MetronicApp.config(['flowFactoryProvider', function (flowFactoryProvider) {
	flowFactoryProvider.defaults = {
		target: '../ngres/uploadFile',
		permanentErrors: [404, 500, 501],
		maxChunkRetries: 1,
		chunkRetryInterval: 5000,
		simultaneousUploads: 4,
		singleFile: true,
		chunkSize: 20 * 1024 * 1024
	};
	flowFactoryProvider.on('catchAll', function (event) {
		console.log('catchAll', arguments);
	});
	// Can be used with different implementations of Flow.js
	// flowFactoryProvider.factory = fustyFlowFactory;
}]);

//消息过滤器（语言切换）
MetronicApp.filter("T", ['$translate', function ($translate) {
	return function (key) {
		if (key) {
			return $translate.instant(key);
		}
	};
}]);

// MetronicApp.config(['$provide', function ($provide) {
//     $provide.decorator('ngClickDirective',['$delegate','$timeout', function ($delegate,$timeout) {
//         var original = $delegate[0].compile;
//         var delay = 500;
//         $delegate[0].compile = function (element, attrs, transclude) {
//
//             var disabled = false;
//             function onClick(evt) {
//                 if (disabled) {
//                     evt.preventDefault();
//                     evt.stopImmediatePropagation();
//                 } else {
//                     disabled = true;
//                     $timeout(function () { disabled = false; }, delay, false);
//                 }
//             }
//             //   scope.$on('$destroy', function () { iElement.off('click', onClick); });
//             element.on('click', onClick);
//
//             return original(element, attrs, transclude);
//         };
//         return $delegate;
//     }]);
// }]);

/**
 * 百分数过滤器
 * 保留4位小数
 */
MetronicApp.filter("percentage", function () {
	return function (old_number) {
		var new_number = Math.round(old_number * 1000000) / 10000;
		if (new_number == 0) return 0;
		return new_number + "%";
	}
})


/* Setup global settings */
MetronicApp.factory('settings', ['$rootScope', '$http', function ($rootScope, $http) {
	// supported languages
	var settings = {
		chang: [],
		menu: 1,
		utils: {},
		layout: {
			pageSidebarClosed: false, // sidebar menu state
			pageBodySolid: false, // solid body color state
			pageAutoScrollOnLoad: 1000 // auto scroll to top on page load
		},
		layoutImgPath: Metronic.getAssetsPath() + 'admin/layout/img/',
		layoutCssPath: Metronic.getAssetsPath() + 'admin/layout/css/'
	};
	/*settings.getchangliang=function(leibie){
	 if (settings.chang[leibie] == undefined) {
	 var promise = changliang_service.getchangliang(leibie);
	 promise.then(function(d){
	 settings.chang[leibie] = d;
	 });
	 }
	 }*/

	settings.utils.back = function () {
		window.history.back()
	}
	settings.utils.initpicker = function () {
		ComponentsPickers.init();
	}
	settings.utils.initdropdown = function () {
		ComponentsDropdowns.init();
	}

	$rootScope.settings = settings;

	return settings;
}]);

/* Setup App Main Controller */
MetronicApp.controller('AppController', ['$scope', '$rootScope', function ($scope, $rootScope) {
	$scope.$on('$viewContentLoaded', function () {
		Metronic.initComponents(); // init core components
		//Layout.init(); //  Init entire layout(header, footer, sidebar, etc) on page load if the partials included in server side instead of loading with ng-include directive
	});
	$scope.menu1 = false;
	$scope.menu2 = false;
	$scope.menu3 = false;
	$scope.menu4 = false;
	$scope.menu5 = false;
	$scope.menu6 = false;
	$scope.menu7 = false;
	$scope.menu8 = false;
	$scope.menu9 = false;
	$scope.menu10 = false;
	$scope.menu11 = false;
	$scope.menu12 = false;
	$scope.menu13 = false;
	$scope.menu14 = false;
	$scope.menu15 = false;
	$scope.menu16 = false;

	$scope.subMenu1 = false;
	$scope.subMenu2 = false;
	$scope.subMenu3 = false;
	$scope.subMenu4 = false;
	$scope.subMenu5 = false;
	$scope.subMenu6 = false;
	$scope.subMenu7 = false;
	$scope.subMenu8 = false;
	$scope.subMenu9 = false;
	$scope.subMenu10 = false;
	$scope.subMenu11 = false;
	$scope.subMenu12 = false;
	$scope.subMenu13 = false;
	$scope.subMenu14 = false;
	$scope.subMenu15 = false;
	$scope.subMenu16 = false;
	$scope.subMenu17 = false;
	$scope.subMenu18 = false;
	$scope.subMenu19 = false;
	$scope.subMenu20 = false;
	$scope.subMenu21 = false;
	$scope.subMenu22 = false;
	$scope.subMenu23 = false;
	function clearAllMenu() {
		$scope.menu1 = false;
		$scope.menu2 = false;
		$scope.menu3 = false;
		$scope.menu4 = false;
		$scope.menu5 = false;
		$scope.menu6 = false;
		$scope.menu7 = false;
		$scope.menu8 = false;
		$scope.menu9 = false;
		$scope.menu10 = false;
		$scope.menu11 = false;
		$scope.menu12 = false;
		$scope.menu13 = false;
		$scope.menu14 = false;
		$scope.menu15 = false;
		$scope.menu16 = false;
		$scope.subMenu19 = false;
		$scope.subMenu20 = false;
		$scope.subMenu21 = false;
		$scope.subMenu22 = false;
		$scope.subMenu23 = false;
	}
	function clearAllSubMenu() {
		$scope.subMenu1 = false;
		$scope.subMenu2 = false;
		$scope.subMenu3 = false;
		$scope.subMenu4 = false;
		$scope.subMenu5 = false;
		$scope.subMenu6 = false;
		$scope.subMenu7 = false;
		$scope.subMenu8 = false;
		$scope.subMenu9 = false;
		$scope.subMenu10 = false;
		$scope.subMenu11 = false;
		$scope.subMenu12 = false;
		$scope.subMenu13 = false;
		$scope.subMenu14 = false;
		$scope.subMenu15 = false;
		$scope.subMenu16 = false;
		$scope.subMenu17 = false;
		$scope.subMenu18 = false;
		$scope.subMenu19 = false;
		$scope.subMenu20 = false;
		$scope.subMenu21 = false;
		$scope.subMenu22 = false;
		$scope.subMenu23 = false;
	}
	$scope.clearMenu = function () {
		clearAllMenu();
	}
	$scope.clearSubMenu = function () {
		clearAllSubMenu();
	}
	$scope.activeSubMenu = function (subMenu) {
		clearAllSubMenu();
		if (subMenu == 'subMenu1') {
			$scope.subMenu1 = true;
		} else if (subMenu == 'subMenu2') {
			$scope.subMenu2 = true;
		} else if (subMenu == 'subMenu3') {
			$scope.subMenu3 = true;
		} else if (subMenu == 'subMenu4') {
			$scope.subMenu4 = true;
		} else if (subMenu == 'subMenu5') {
			$scope.subMenu5 = true;
		} else if (subMenu == 'subMenu6') {
			$scope.subMenu6 = true;
		} else if (subMenu == 'subMenu7') {
			$scope.subMenu7 = true;
		} else if (subMenu == 'subMenu8') {
			$scope.subMenu8 = true;
		} else if (subMenu == 'subMenu9') {
			$scope.subMenu9 = true;
		} else if (subMenu == 'subMenu10') {
			$scope.subMenu10 = true;
		} else if (subMenu == 'subMenu11') {
			$scope.subMenu11 = true;
		} else if (subMenu == 'subMenu12') {
			$scope.subMenu12 = true;
		} else if (subMenu == 'subMenu13') {
			$scope.subMenu13 = true;
		} else if (subMenu == 'subMenu14') {
			$scope.subMenu14 = true;
		} else if (subMenu == 'subMenu15') {
			$scope.subMenu15 = true;
		} else if (subMenu == 'subMenu16') {
			$scope.subMenu16 = true;
		} else if (subMenu == 'subMenu17') {
			$scope.subMenu17 = true;
		} else if (subMenu == 'subMenu18') {
			$scope.subMenu18 = true;
		} else if (subMenu == 'subMenu19') {
			$scope.subMenu19 = true;
		} else if (subMenu == 'subMenu20') {
			$scope.subMenu20 = true;
		} else if (subMenu == 'subMenu21') {
			$scope.subMenu21 = true;
		} else if (subMenu == 'subMenu22') {
			$scope.subMenu22 = true;
		} else if (subMenu == 'subMenu23') {
			$scope.subMenu23 = true;
		} else {

		}
	}
	$scope.menu1Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu1) {
			return;
		} else {
			clearAllMenu();
			$scope.menu1 = true;
		}
	}
	$scope.menu2Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu2) {
			return;
		} else {
			clearAllMenu();
			$scope.menu2 = true;
		}
	}

	$scope.menu3Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu3) {
			return;
		} else {
			clearAllMenu();
			$scope.menu3 = true;
		}
	}
	$scope.menu4Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu4) {
			return;
		} else {
			clearAllMenu();
			$scope.menu4 = true;
		}
	}
	$scope.menu5Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu5) {
			return;
		} else {
			clearAllMenu();
			$scope.menu5 = true;
		}
	}
	$scope.menu6Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu6) {
			return;
		} else {
			clearAllMenu();
			$scope.menu6 = true;
		}
	}
	$scope.menu7Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu7) {
			return;
		} else {
			clearAllMenu();
			$scope.menu7 = true;
		}
	}
	$scope.menu8Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu8) {
			return;
		} else {
			clearAllMenu();
			$scope.menu8 = true;
		}
	}
	$scope.menu9Click = function (subMenu) {
		$scope.activeSubMenu(subMenu);
		if ($scope.menu9) {
			return;
		} else {
			clearAllMenu();
			$scope.menu9 = true;
		}
	}
}]);

/***
 Layout Partials.
 By default the partials are loaded through AngularJS ng-include directive. In case they loaded in server side(e.g: PHP include function) then below partial
 initialization can be disabled and Layout.init() should be called on page load complete as explained above.
 ***/

/* Setup Layout Part - Header */
MetronicApp.controller('HeaderController', ['$scope', '$rootScope', '$location', '$modal', '$http', function ($scope, $rootScope, $location, $modal, $http, $filter) {
	$scope.$on('$includeContentLoaded', function () {
		Layout.initHeader(); // init header
		//Layout.initSidebar(); // init sidebar

	});
	$scope.menuclick = function (value, path) {
		$rootScope.settings.menu = value
		$location.path(path)
	}
	//修改密码
	$scope.updatePassword = function (size) {
		openwindow($modal, 'passwd-update.html', size, function ($scope, $modal, $modalInstance, $filter) {
			var password = {};
			$scope.password = password;

			$scope.submit = function () {
				if (password.newPassword != password.confirmNewPassword) {
					alert("两次密码输入不一致，请重新输入");
					return;
				}
				var submitForm = {};
				submitForm.oldPassword = password.oldPassword;
				submitForm.newPassword = password.newPassword;
				submitForm.confirmNewPassword = password.confirmNewPassword;
				var promise = $http.post(baseUrl + '/passwd', submitForm);
				promise.then(function (data) {
					var res = data.data;
					if (res.status == 'success') {
						toastr['success']($filter('T')(res.message), "");
						/*toastr['success'](res.message, "");*/
						$modalInstance.dismiss('cancel');
					} else {
						alert(res.message);
					}
				})
			};
			$scope.cancel = function () {
				$modalInstance.dismiss('cancel');
			};
		})
	}

}]);

/* Setup Layout Part - Sidebar */
MetronicApp.controller('SidebarController', ['$scope', function ($scope) {
	$scope.$on('$includeContentLoaded', function () {
		Layout.initSidebar(); // init sidebar
	});
}]);

/* Setup Layout Part - Quick Sidebar */
MetronicApp.controller('QuickSidebarController', ['$scope', function ($scope) {
	$scope.$on('$includeContentLoaded', function () {
		setTimeout(function () {
			QuickSidebar.init(); // init quick sidebar
		}, 2000)
	});
}]);

/* Setup Layout Part - Theme Panel */
MetronicApp.controller('ThemePanelController', ['$scope', function ($scope) {
	$scope.$on('$includeContentLoaded', function () {
		Demo.init(); // init theme panel
	});
}]);

/* Setup Layout Part - Footer */
MetronicApp.controller('FooterController', ['$scope', function ($scope) {
	$scope.$on('$includeContentLoaded', function () {
		Layout.initFooter(); // init footer
	});
}]);


/* Setup Rounting For All Pages */
MetronicApp.config(['$stateProvider', '$urlRouterProvider', function ($stateProvider, $urlRouterProvider) {
	// Redirect any unmatched url
	$urlRouterProvider.otherwise("/dashboard.html");

	$stateProvider

		// Dashboard
		.state('dashboard', {
			url: "/dashboard.html",
			templateUrl: "views/dashboard.html",
			data: { pageTitle: '控制台' },
			controller: "DashboardController",
			resolve: {
				deps: ['$ocLazyLoad', function ($ocLazyLoad) {
					return $ocLazyLoad.load({
						name: 'MetronicApp',
						files: [
							'assets/global/plugins/morris/morris.css',
							'assets/admin/pages/css/tasks.css',

							'assets/global/plugins/morris/morris.min.js',
							'assets/global/plugins/morris/raphael-min.js',
							'assets/global/plugins/jquery.sparkline.min.js',

							'assets/admin/pages/scripts/index3.js',
							'assets/admin/pages/scripts/tasks.js',

							'js/controllers/DashboardController.js'
						]
					});
				}]
			}
		})

		.state('org-list', {
			url: "/org-list/{type:.*}",
			templateUrl: "views/org-list.html",
			data: { pageTitle: '授权管理' },
			controller: "org_controller"
		})
		.state('org-role-list', {
			url: "/org-role-list/{type:.*}",
			templateUrl: "views/org-role-list.html",
			data: { pageTitle: '授权管理' },
			controller: "org_role_controller"
		})
		.state('user-list', {
			url: "/user-list/{type:.*}",
			templateUrl: "views/user-list.html",
			data: { pageTitle: '用户管理' },
			controller: "user_controller"
		})
		.state('user-add', {
			url: "/user-add/{id:.*}",
			templateUrl: "views/user-add.html",
			data: { pageTitle: '用户管理' },
			controller: "user_edit_controller"
		})
		.state('user-edit', {
			url: "/user-edit/{id:.*}",
			templateUrl: "views/user-edit.html",
			data: { pageTitle: '用户管理' },
			controller: "user_edit_controller"
		})
		.state('user-detail', {
			url: "/user-detail/{id:.*}",
			templateUrl: "views/user-detail.html",
			data: { pageTitle: '用户管理' },
			controller: "user_edit_controller"
		})

		//基础数据
		.state('jichushuju', {
			url: "/jichushuju.html",
			templateUrl: "views/jichushuju.html",
			data: { pageTitle: '基础数据' },
			controller: "jichushuju_controller"
		})

		.state('product', {
			url: "/product.html",
			templateUrl: "views/product.html",
			data: { pageTitle: '产品管理' },
			controller: "product_controller"
		})
		.state('productedit', {
			url: "/productedit.html/{id:.*}",
			templateUrl: "views/productedit.html",
			data: { pageTitle: '产品管理' },
			controller: "productedit_controller"
		})
		//学生管理
		.state('student', {
			url: "/student.html",
			templateUrl: "views/student.html",
			data: { pageTitle: '学生管理' },
			controller: "student_controller"
		})
		.state('studentedit', {
			url: "/studentedit.html/{id:.*}",
			templateUrl: "views/studentedit.html",
			data: { pageTitle: '学生管理' },
			controller: "studentedit_controller"
		})
		.state('studentxiangxi', {
			url: "/studentxiangxi.html/{id:.*}/{tab:.*}",
			templateUrl: "views/studentxiangxi.html",
			data: { pageTitle: '学生详情' },
			controller: "studentxiangxi_controller"
		})
		.state('followupLog', {
			url: "/followupLog.html",
			templateUrl: "views/followupLog.html",
			data: { pageTitle: '学员跟进' },
			controller: "followupLog_controller"
		})
		.state('suqiu', {
			url: "/suqiu.html",
			templateUrl: "views/suqiu.html",
			data: { pageTitle: '学员诉求' },
			controller: "suqiu_controller"
		})
		.state('studentreport', {
			url: "/studentreport.html",
			templateUrl: "views/studentreport.html",
			data: { pageTitle: '学员报告' },
			controller: "studentxiangxi_controller"
		})
		.state('followupTask', {
			url: "/followupTask.html",
			templateUrl: "views/followupTask.html",
			data: { pageTitle: '跟进提醒' },
			controller: "followupTask_controller"
		})


		.state('studentReturns', {
			url: "/studentReturns.html",
			templateUrl: "views/studentReturns.html",
			data: { pageTitle: '学员退费' },
			controller: "studentReturns_controller"
		})

		.state('followupLogedit', {
			url: "/followupLogedit.html/{id:.*}",
			templateUrl: "views/followupLogedit.html",
			data: { pageTitle: '学员跟进' },
			controller: "followupLogedit_controller"
		})
		.state('suqiueditNew', {
			url: "/suqiueditNew.html/{id:.*}",
			templateUrl: "views/suqiueditNew.html",
			data: { pageTitle: '学员诉求' },
			controller: "suqiueditNew_controller"
		})
		.state('followupLogeditNew', {
			url: "/followupLogeditNew.html/{id:.*}",
			templateUrl: "views/followupLogeditNew.html",
			data: { pageTitle: '学员跟进' },
			controller: "followupLogeditNew_controller"
		})
		.state('suqiuedit', {
			url: "/suqiuedit.html/{id:.*}",
			templateUrl: "views/suqiuedit.html",
			data: { pageTitle: '学员诉求' },
			controller: "suqiuedit_controller"
		})
		.state('followupLogdetail', {
			url: "/followupLogdetail.html/{id:.*}",
			templateUrl: "views/followupLogdetail.html",
			data: { pageTitle: '学员跟进' },
			controller: "followupLogdetail_controller"
		})
		.state('suqiudetail', {
			url: "/suqiudetail.html/{id:.*}",
			templateUrl: "views/suqiudetail.html",
			data: { pageTitle: '学员诉求' },
			controller: "suqiudetail_controller"
		})
		.state('student_leave', {
			url: "/student_leave.html",
			templateUrl: "views/view_student_leave.html",
			data: { pageTitle: '学员请假' },
			controller: "view_student_leave_controller"
		})
		.state('student_leavedetail', {
			url: "/student_leavedetail.html/{id:.*}",
			templateUrl: "views/view_student_leavedetail.html",
			data: { pageTitle: '学员请假' },
			controller: "view_student_leavedetail_controller"
		})

		/*合同管理*/
		.state('contract', {
			url: "/contract.html",
			templateUrl: "views/contract.html",
			data: { pageTitle: '合同' },
			controller: "contract_controller"
		})
		.state('contractedit', {
			url: "/contractedit.html/",
			templateUrl: "views/contractedit.html",
			data: { pageTitle: '合同' },
			controller: "contractedit_controller",
			params: { id: null, type: null }
		})

		.state('contracttuifei', {
			url: "/contracttuifei.html/{id:.*}",
			templateUrl: "views/contracttuifei.html",
			data: { pageTitle: '退费' },
			controller: "contracttuifei_controller"
		})

		.state('contractview', {
			url: "/contractview.html",
			templateUrl: "views/contractview.html",
			data: { pageTitle: '合同列表' },
			controller: "contractview_controller"
		})
		.state('contractviewcheck', {
			url: "/contractviewcheck.html/{id:.*}",
			templateUrl: "views/contractviewcheck.html",
			data: { pageTitle: '合同详情' },
			controller: "contractviewcheck_controller"
		})
		/*教师管理*/
		.state('teacherview', {
			url: "/teacherview.html",
			templateUrl: "views/teacherview.html",
			data: { pageTitle: '教师列表' },
			controller: "teacherview_controller"
		})
		.state('teacherviewedit', {
			url: "/teacherviewedit.html/{id:.*}",
			templateUrl: "views/teacherviewedit.html",
			data: { pageTitle: '教师编辑' },
			controller: "teacherviewedit_controller"
		})
		.state('teacherxiangxi', {
			url: "/teacherxiangxi.html/{id:.*}/{tab:.*}",
			templateUrl: "views/teacherxiangxi.html",
			data: { pageTitle: '教师详情' },
			controller: "teacherxiangxi_controller"
		})
		.state('teacher_leave', {
			url: "/view_teacher_leave.html",
			templateUrl: "views/view_teacher_leave.html",
			data: { pageTitle: '教师请假' },
			controller: "view_teacher_leave_controller"
		})
		.state('teacher_leavedetail', {
			url: "/teacher_leavedetail.html/{id:.*}",
			templateUrl: "views/view_teacher_leavedetail.html",
			data: { pageTitle: '教师请假' },
			controller: "view_teacher_leavedetail_controller"
		})

		//今日签到
		.state('attendanceBook', {
			url: "/attendanceBook.html/{flagStatus:.*}",
			templateUrl: "views/view_attendanceBook.html",
			data: { pageTitle: '今日签到' },
			controller: "view_attendanceBook_controller"
		})
		//今日签到(手机) 首页去了
		// .state('attendanceBook',{
		//     url: "/attendanceBook.html/{flagStatus:.*}",
		//     templateUrl: "views/view_attendanceBookPhone.html",
		//     data: {pageTitle: '今日签到'},
		//     controller: "view_attendanceBookPhone_controller"
		// })


		//签到列表
		.state('attendanceBookHis', {
			url: "/attendanceBookHis.html",
			templateUrl: "views/view_attendanceBookHis.html",
			data: { pageTitle: '签到列表' },
			controller: "view_attendanceBookHis_controller"
		})
		//异常签到详情
		.state('attendanceBook_detail', {
			url: "/attendanceBook_detail.html/{id:.*}",
			templateUrl: "views/view_attendanceBook_detail.html",
			data: { pageTitle: '异常签到详情' },
			controller: "view_attendanceBook_detail_controller"
		})

		//同步ERP
		.state('lockData', {
			url: "/lockData.html",
			templateUrl: "views/lockData.html",
			data: { pageTitle: '同步ERP' },
			controller: "lockData_controller"
		})

		/**排课**/
		.state('classSchedule', {
			url: "/classSchedule.html",
			templateUrl: "views/classSchedule.html",
			data: { pageTitle: '排课' },
			controller: "classSchedule_controller"
		})
		.state('view_paike_class', {
			url: "/view_paike_class.html/{path:.*}",
			templateUrl: "views/view_paike_class.html",
			data: { pageTitle: '排课列表' },
			controller: "view_paike_class_controller"
		})

		.state('paike_basic', {
			url: "/paike_basic.html",
			templateUrl: "views/paike_basic.html",
			data: { pageTitle: '排课' },
			controller: "paike_basic_controller"
		})

		.state('paike', {
			url: "/paike.html",
			templateUrl: "views/paike.html",
			data: { pageTitle: '排课' },
			controller: "paike_controller"
		})

		.state('paike_group', {
			url: "/paike_group.html",
			templateUrl: "views/paike_group.html",
			data: { pageTitle: '排课' },
			controller: "smallgroup_paike_controller"
		})

		.state('small_group', {
			url: "/small_group/{type:.*}",
			templateUrl: "views/small_group.html",
			data: { pageTitle: '排课' },
			controller: "smallgroup_controller"
		})

		.state('smallgroup-add', {
			url: "/small_group-add/{id:.*}",
			templateUrl: "views/small_group-add.html",
			data: { pageTitle: '用户管理' },
			controller: "smallgroup_edit_controller"
		})
		.state('smallgroup-edit', {
			url: "/small_group-edit/{id:.*}",
			templateUrl: "views/small_group-edit.html",
			data: { pageTitle: '用户管理' },
			controller: "smallgroup_edit_controller"
		})
		.state('smallgroup-detail', {
			url: "/small_group-detail/{id:.*}",
			templateUrl: "views/small_group-detail.html",
			data: { pageTitle: '用户管理' },
			controller: "smallgroup_edit_controller"
		})



		.state('laoshipaike', {
			url: "/laoshipaike.html",
			templateUrl: "views/laoshipaike.html",
			data: { pageTitle: '老师排课' },
			controller: "laoshipaike_controller"
		})

		//自定义报表
		.state('zidingyi', {
			url: "/zidingyi.html",
			templateUrl: "views/zidingyi.html",
			data: { pageTitle: '自定义报表' },
			controller: "zidingyi_controller"
		})

		.state('zongkebiao', {
			url: "/zongkebiao.html",
			templateUrl: "views/zongkebiao.html",
			data: { pageTitle: '总课表' },
			controller: "kebiao_controller"
		})

		.state('zongkebiaoPhone', {
			url: "/zongkebiaoPhone.html",
			templateUrl: "views/zongkebiaoPhone.html",
			data: { pageTitle: '总课表(手机)' },
			controller: "kebiaoPhone_controller"
		})

		.state('rilikebiao', {
			url: "/rilikebiao.html",
			templateUrl: "views/rilikebiao.html",
			data: { pageTitle: '日历课表' },
			controller: "rilikebiao_controller"
		})

		.state('daixufeixueyuan', {
			url: "/daixufeixueyuan.html",
			templateUrl: "views/daixufeixueyuan.html",
			data: { pageTitle: '待续费学员' },
			controller: "daixufeixueyuan_controller"
		})

		.state('shengyukeshi', {
			url: "/shengyukeshi.html",
			templateUrl: "views/shengyukeshi.html",
			data: { pageTitle: '学员剩余课时' },
			controller: "shengyukeshi_controller"
		})

		.state('laoshigongzi', {
			url: "/laoshigongzi.html",
			templateUrl: "views/laoshigongzi.html",
			data: { pageTitle: 'TE-老师工资' },
			controller: "laoshigongzi_controller"
		})

		.state('huizong', {
			url: "/huizong.html",
			templateUrl: "views/huizong.html",
			data: { pageTitle: '老师工资汇总' },
			controller: "huizong_controller"
		})

		.state('xubao', {
			url: "/xubao.html",
			templateUrl: "views/xubao.html",
			data: { pageTitle: '续报学员统计' },
			controller: "xubao_controller"
		})

		.state('zhuanjieshao', {
			url: "/zhuanjieshao.html",
			templateUrl: "views/zhuanjieshao.html",
			data: { pageTitle: '转介绍学员统计' },
			controller: "zhuanjieshao_controller"
		})

		.state('keshi', {
			url: "/keshi.html",
			templateUrl: "views/keshi.html",
			data: { pageTitle: 'TE-课时统计' },
			controller: "keshi_controller"
		})

		.state('fuwuliucheng', {
			url: "/fuwuliucheng.html",
			templateUrl: "views/fuwuliucheng.html",
			data: { pageTitle: '服务流程监控' },
			controller: "fuwuliucheng_controller"
		})

		.state('studentClassReport', {
			url: "/studentClassReport.html",
			templateUrl: "views/studentClassReport.html",
			data: { pageTitle: '学员上课报表' },
			controller: "studentClassReport_controller"
		})

		.state('teacherClassReport', {
			url: "/teacherClassReport.html",
			templateUrl: "views/teacherClassReport.html",
			data: { pageTitle: '教师上课报表' },
			controller: "teacherClassReport_controller"
		})
		.state('studentConsumptionReport', {
			url: "/studentConsumptionReport.html",
			templateUrl: "views/studentConsumptionReport.html",
			data: { pageTitle: '学员排耗课报表' },
			controller: "studentConsumptionReport_controller"
		})

		.state('dangyuepaike', {
			url: "/dangyuepaike.html",
			templateUrl: "views/dangyuepaike.html",
			data: { pageTitle: '当月排课Tutor' },
			controller: "dangyuepaike_controller"
		})

		.state('jixiao', {
			url: "/jixiao.html",
			templateUrl: "views/jixiao.html",
			data: { pageTitle: 'EM绩效' },
			controller: "jixiao_controller"
		})

		.state('xinzhao', {
			url: "/xinzhao.html",
			templateUrl: "views/xinzhao.html",
			data: { pageTitle: '各办公室新招生学员情况' },
			controller: "xinzhao_controller"
		})

		.state('keshishengyu', {
			url: "/keshishengyu.html",
			templateUrl: "views/keshishengyu.html",
			data: { pageTitle: '排耗课报表' },
			controller: "keshishengyu_controller"
		})

		.state('jiezhuan', {
			url: "/jiezhuan.html",
			templateUrl: "views/jiezhuan.html",
			data: { pageTitle: 'TE-结转课时报表' },
			controller: "jiezhuan_controller"
		})

		.state('gongzimingxi', {
			url: "/gongzimingxi.html",
			templateUrl: "views/gongzimingxi.html",
			data: { pageTitle: '工资明细' },
			controller: "gongzimingxi_controller"
		})

		.state('gongzimingxionline', {
			url: "/gongzimingxionline.html",
			templateUrl: "views/gongzimingxionline.html",
			data: { pageTitle: '工资明细online' },
			controller: "gongzimingxionline_controller"
		})
		.state('gongzimingxioffline', {
			url: "/gongzimingxioffline.html",
			templateUrl: "views/gongzimingxioffline.html",
			data: { pageTitle: '工资明细offline' },
			controller: "gongzimingxioffline_controller"
		})

		//老师页面
		.state('chakankebiao', {
			url: "/chakankebiao.html",
			templateUrl: "views/chakankebiao.html",
			data: { pageTitle: '查看课表' },
			controller: "chakankebiao_controller"
		})
		//打印
		.state('dayin', {
			url: "/dayin.html/:serchfrom",
			templateUrl: "views/dayin.html",
			data: { pageTitle: '打印课表' },
			controller: "dayin_controller"
		})

		//日志
		.state('rizhi', {
			url: "/rizhi.html",
			templateUrl: "views/rizhi.html",
			data: { pageTitle: '查看日志' },
			controller: "rizhi_controller"
		})

		//自定义报表2 - iframe
		.state('zidingyi_iframe', {
			url: "/zidingyi_iframe.html",
			templateUrl: "views/zidingyi_iframe.html",
			data: { pageTitle: '自定义报表2' }
		})

		//Class Schedule Calendar
		.state('classschedulecalendar', {
			url: "/classschedulecalendar.html",
			templateUrl: "views/classschedulecalendar.html",
			data: { pageTitle: '自定义报表2' }
		})
}]);

/* Init global settings and run the app */
MetronicApp.run(["$rootScope", "settings", "$state", function ($rootScope, settings, $state) {
	$rootScope.$state = $state; // state to be accessed from view
}]);
MetronicApp.config(['$httpProvider', function ($httpProvider) {
	$httpProvider.responseInterceptors.push(function ($q) {
		return function (promise) {
			var deferred = $q.defer();
			promise.then(
				function (response) {
					if (response.data == undefined || response.data.loginstatus == 'error') {
						window.location.href = '../login';
						return false;
					}
					//                  alert('success:'+JSON.stringify(response)); return response;
				},
				function (error) {
					try {
						$q.reject(error);
					} catch (e) {

					}

				}
			);
			return promise;
		};
	});
}]);

//保留两位小数
//功能：将浮点数四舍五入，取小数点后2位
function toDecimal(x) {
	var f = parseFloat(x);
	if (isNaN(f)) {
		return;
	}
	f = Math.round(x * 100) / 100;
	return f;
}
//当前日期
function currentDate() {
	var now = new Date();
	var year = now.getFullYear();       //年
	var month = now.getMonth() + 1;     //月
	var day = now.getDate();            //日
	var nowDate = year + "-";
	if (month < 10)
		nowDate += "0";

	nowDate += month + "-";

	if (day < 10)
		nowDate += "0";

	nowDate += day;

	return (nowDate);
}
/**
 *
 * @param date
 * @returns {string} yyyy-mm-dd
 */
function dateToString(date) {
	var year = date.getFullYear();       //年
	var month = date.getMonth() + 1;     //月
	var day = date.getDate();            //日
	var dateStr = year + "-";
	if (month < 10)
		dateStr += "0";

	dateStr += month + "-";

	if (day < 10)
		dateStr += "0";

	dateStr += day;

	return (dateStr);
}
/**
 * 获取当前时间
 *  format yyyy-MM-dd HH:mm:ss
 */
function currentTime() {
	var d = new Date();
	var vYear = d.getFullYear();         //年
	var vMon = d.getMonth() + 1;         //月
	var vDay = d.getDate();              //日
	var h = d.getHours();                //时
	var m = d.getMinutes();              //分
	var se = d.getSeconds();             //秒
	var dateTimeStr = vYear + "-" + (vMon < 10 ? "0" + vMon : vMon) + "-" + (vDay < 10 ? "0" + vDay : vDay) + " " +
		(h < 10 ? "0" + h : h) + ":" + (m < 10 ? "0" + m : m) + ":" + (se < 10 ? "0" + se : se);
	return dateTimeStr;
}

/**
 * 获取当前时间的HH:MM
 */
function currentHM() {
	var d = new Date();
	var h = d.getHours();                //时
	var m = d.getMinutes();              //分
	return (h < 10 ? "0" + h : h) + ":" + (m < 10 ? "0" + m : m);
}


//时间先后比较
//格式12:00
function TimeCompare(start, end) {
	if (isNull(start) || isNull(end)) {
		return -1;
	}
	var starts = start.replace("：", "");
	starts = starts.replace(":", "");
	var ends = end.replace("：", "");
	ends = ends.replace(":", "");
	if (parseInt(starts) > parseInt(ends)) {
		return 0;
	} else {
		return 1;
	}

};

//日期先后比较
function DateCompare(start, end) {
	if (isNull(start) || isNull(end)) {
		return -1;
	}

	start = start.replace("-", "");
	start = start.replace("-", "");
	console.log(start);
	end = end.replace("-", "");
	end = end.replace("-", "");
	console.log(end);
	if (parseInt(start) > parseInt(end)) {
		return 0;
	} else {
		return 1;
	}
}

/*获得一周的所有"yyyy-mm-dd"格式的日期字符串*/
//参数：填写数字 例如：weekNum = 1（获得下一周的所有日期）；weekNum = 0（获得本周的所有日期）；weekNum = -1（获得上一周的所有日期）
//auther-zsc
var getWeeks = function (weekNum) {

	var temp = weekNum * 7;
	var now = new Date();
	var dayone = now.getDay();
	var week = "7123456";
	/*  console.info(dayone);*/
	/*获得本周的所有日期*/
	var weeksForm = [];
	for (var j = 1; j <= 7; j++) {
		var one = j + temp - week.charAt(dayone);
		var weekone = new Date();
		weekone.setDate(weekone.getDate() + one);

		var year = weekone.getFullYear();       //年
		var month = weekone.getMonth() + 1;     //月
		var day = weekone.getDate();            //日
		var nowDate = year + "-";
		if (month < 10) {
			nowDate += "0";
		}
		nowDate += month + "-";
		if (day < 10) {
			nowDate += "0";
		}
		nowDate += day;
		weeksForm.push(nowDate);
	}
	return weeksForm;
};

/*获得指定一周的所有"yyyy-mm-dd"格式的日期字符串*/
//参数：填写数字 例如：weekNum = 1（获得下一周的所有日期）；weekNum = 0（获得本周的所有日期）；weekNum = -1（获得上一周的所有日期）
var getWeeksToday = function (weekNum, today) {

	var temp = weekNum * 7;
	var now = new Date(today);
	var dayone = now.getDay();
	var week = "7123456";
	/*获得本周的所有日期*/
	var weeksForm = [];
	for (var j = 1; j <= 7; j++) {
		var one = j + temp - week.charAt(dayone);
		var weekone = new Date(today);
		weekone.setDate(weekone.getDate() + one);
		var year = weekone.getFullYear();       //年
		var month = weekone.getMonth() + 1;     //月
		var day = weekone.getDate();            //日
		var nowDate = year + "-";
		if (month < 10) {
			nowDate += "0";
		}
		nowDate += month + "-";
		if (day < 10) {
			nowDate += "0";
		}
		nowDate += day;
		weeksForm.push(nowDate);
	}
	return weeksForm;
};
/**
 * 获取两个日期获取它们之间的所有日期
 * @param startDateStr
 * @param endDateStr
 * @returns {Array}
 */
function getDate(startDateStr, endDateStr) {
	var dateForm = [];
	/**
	 * 获取指定的日期
	 * @param dateStr yyyy-mm-dd
	 * @returns {Date}
	 */
	var getDesignatedDate = function (dateStr) {
		var tempDate = new Date();
		var list = dateStr.split("-");
		tempDate.setFullYear(list[0]);
		tempDate.setMonth(list[1] - 1);
		tempDate.setDate(list[2]);
		return tempDate;
	}
	var startDate = getDesignatedDate(startDateStr);
	var endDate = getDesignatedDate(endDateStr);
	if (startDate > endDate) return dateForm;
	while (startDate <= endDate) {
		var vYear = startDate.getFullYear();         //年
		var vMon = startDate.getMonth() + 1;         //月
		var vDay = startDate.getDate();              //日
		var dateStr = vYear + "-" + (vMon < 10 ? "0" + vMon : vMon) + "-" + (vDay < 10 ? "0" + vDay : vDay);
		dateForm.push(dateStr);
		startDate.setDate(startDate.getDate() + 1);
	}
	return dateForm;
}
/**
 * 根据生日获取年龄
 * @param str 生日yyyy-mm-dd
 * @returns {*}
 */
function getAge(str) {
	if (isNull(str)) {
		return "";
	}
	var r = str.match(/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/);
	if (r == null)
		return "";
	var d = new Date(r[1], r[3] - 1, r[4]);
	if (d.getFullYear() == r[1] && (d.getMonth() + 1) == r[3] && d.getDate() == r[4]) {
		var year = new Date().getFullYear();
		return (year - r[1]);
	}
	return "";
}
var timeItems = [
	{ "name": "T1", "startTime": "08:00", "endTime": "09:00" },
	{ "name": "T2", "startTime": "09:00", "endTime": "10:00" },
	{ "name": "T3", "startTime": "10:00", "endTime": "11:00" },
	{ "name": "T4", "startTime": "11:00", "endTime": "12:00" },
	{ "name": "T5", "startTime": "12:00", "endTime": "13:00" },
	{ "name": "T6", "startTime": "13:00", "endTime": "14:00" },
	{ "name": "T7", "startTime": "14:00", "endTime": "15:00" },
	{ "name": "T8", "startTime": "15:00", "endTime": "16:00" },
	{ "name": "T9", "startTime": "16:00", "endTime": "17:00" },
	{ "name": "T10", "startTime": "17:00", "endTime": "18:00" },
	{ "name": "T11", "startTime": "18:00", "endTime": "19:00" },
	{ "name": "T12", "startTime": "19:00", "endTime": "20:00" },
	{ "name": "T13", "startTime": "20:00", "endTime": "21:00" },
	{ "name": "T14", "startTime": "21:00", "endTime": "22:00" },
	{ "name": "T15", "startTime": "22:00", "endTime": "23:00" },
	{ "name": "T16", "startTime": "23:00", "endTime": "00:00" },
	{ "name": "T17", "startTime": "00:00", "endTime": "01:00" },
	{ "name": "T18", "startTime": "01:00", "endTime": "02:00" },
	{ "name": "T19", "startTime": "02:00", "endTime": "03:00" },
	{ "name": "T20", "startTime": "03:00", "endTime": "04:00" },
	{ "name": "T21", "startTime": "04:00", "endTime": "05:00" },
	{ "name": "T22", "startTime": "05:00", "endTime": "06:00" },
	{ "name": "T23", "startTime": "06:00", "endTime": "07:00" },
	{ "name": "T24", "startTime": "07:00", "endTime": "08:00" }
];
var timeItem = [
	{ "name": "T1", "startTime": "00:00", "endTime": "02:00" },
	{ "name": "T2", "startTime": "02:00", "endTime": "04:00" },
	{ "name": "T3", "startTime": "04:00", "endTime": "06:00" },
	{ "name": "T4", "startTime": "06:00", "endTime": "08:00" },
	{ "name": "T5", "startTime": "08:00", "endTime": "10:00" },
	{ "name": "T6", "startTime": "10:00", "endTime": "12:00" },
	{ "name": "T7", "startTime": "12:00", "endTime": "14:00" },
	{ "name": "T8", "startTime": "14:00", "endTime": "16:00" },
	{ "name": "T9", "startTime": "16:00", "endTime": "18:00" },
	{ "name": "T10", "startTime": "18:00", "endTime": "20:00" },
	{ "name": "T11", "startTime": "20:00", "endTime": "22:00" },
	{ "name": "T12", "startTime": "22:00", "endTime": "24:00" }
];

var timeItemBasic = [
	{ "name": "T1", "startTime": "08:00", "endTime": "10:00" },
	{ "name": "T2", "startTime": "10:00", "endTime": "12:00" },
	{ "name": "T3", "startTime": "12:00", "endTime": "13:00" },
	{ "name": "T4", "startTime": "13:00", "endTime": "15:00" },
	{ "name": "T5", "startTime": "15:00", "endTime": "17:00" },
	{ "name": "T6", "startTime": "17:00", "endTime": "19:00" },
	{ "name": "T7", "startTime": "19:00", "endTime": "21:00" },
	{ "name": "T8", "startTime": "21:00", "endTime": "23:00" }
];
/**
 * 国家常量
 * @type {Array}
 */
var countries = [
	{ "name_cn": "美国", "name_en": "America", "value": "America" },
	{ "name_cn": "澳大利亚", "name_en": "Australia", "value": "Australia" },
	{ "name_cn": "巴西", "name_en": "Brazil", "value": "Brazil" },
	{ "name_cn": "加拿大", "name_en": "Canada", "value": "Canada" },
	{ "name_cn": "中国", "name_en": "China", "value": "China" },
	{ "name_cn": "英国", "name_en": "England", "value": "England" },
	{ "name_cn": "法国", "name_en": "France", "value": "France" },
	{ "name_cn": "德国", "name_en": "Germany", "value": "Germany" },
	{ "name_cn": "荷兰", "name_en": "Holland", "value": "Holland" },
	{ "name_cn": "匈牙利", "name_en": "Hungary", "value": "Hungary" },
	{ "name_cn": "印度", "name_en": "India", "value": "India" },
	{ "name_cn": "爱尔兰", "name_en": "Ireland", "value": "Thailand" },
	{ "name_cn": "意大利", "name_en": "Italy", "value": "Italy" },
	{ "name_cn": "日本", "name_en": "Japan", "value": "Japan" },
	{ "name_cn": "韩国", "name_en": "Korea", "value": "Korea" },
	{ "name_cn": "波兰", "name_en": "Poland", "value": "Poland" },
	{ "name_cn": "葡萄牙", "name_en": "Portugal", "value": "Portugal" },
	{ "name_cn": "俄罗斯", "name_en": "Russia", "value": "Russia" },
	{ "name_cn": "西班牙", "name_en": "Spain", "value": "Spain" },
	{ "name_cn": "瑞典", "name_en": "Sweden", "value": "Sweden" },
	{ "name_cn": "泰国", "name_en": "Thailand", "value": "Thailand" },
	{ "name_cn": "南非", "name_en": "South Africa", "value": "South Africa" }


]


/**
 *
 * @param str
 * @returns {boolean}
 */
String.prototype.endWith = function (str) {
	if (str == null || str == "" || this.length == 0 || str.length > this.length)
		return false;
	if (this.substring(this.length - str.length) == str)
		return true;
	else
		return false;
	return true;
}
/**
 *
 * @param str
 * @returns {boolean}
 */
String.prototype.startWith = function (str) {
	if (str == null || str == "" || this.length == 0 || str.length > this.length)
		return false;
	if (this.substr(0, str.length) == str)
		return true;
	else
		return false;
	return true;
}



var dataRoles = [
	{ "name": "自己和下属", "value": "2" },
	{ "name": "所有人", "value": "1" },
	{ "name": "仅自己", "value": "4" },
	{ "name": "部门所有人", "value": "3" }
];

var IDMark_Switch = "_switch",
	IDMark_Icon = "_ico",
	IDMark_Span = "_span",
	IDMark_Input = "_input",
	IDMark_Check = "_check",
	IDMark_Edit = "_edit",
	IDMark_Remove = "_remove",
	IDMark_Ul = "_ul",
	IDMark_A = "_a";

function initZtrees(treeid, nodes, addHoverDom, removeHoverDom, addDiyDom, onClick, beforeDrag, beforeDrop, zTreeOnDrop) {
	var setting = {
		view: {
			dblClickExpand: true,
			showLine: false,
			selectedMulti: false,
			addDiyDom: addDiyDom,
			addHoverDom: addHoverDom,
			removeHoverDom: removeHoverDom
		},
		edit: {
			enable: true,
			editNameSelectAll: false,
			renameTitle: "编辑",
			removeTitle: "删除",
			showRenameBtn: false,
			showRemoveBtn: false,
			drag: {
				prev: true,
				next: true,
				inner: false,
				isCopy: false,
				isMove: true
			}
		},
		data: {
			simpleData: {
				enable: true,
				idKey: "id",
				pIdKey: "pid",
				rootPId: ""
			}
		},
		callback: {
			onClick: onClick,
			beforeDrag: beforeDrag,
			beforeDrop: beforeDrop,
			onDrop: zTreeOnDrop
		}
	};
	return $.fn.zTree.init($("#" + treeid), setting, nodes);
}