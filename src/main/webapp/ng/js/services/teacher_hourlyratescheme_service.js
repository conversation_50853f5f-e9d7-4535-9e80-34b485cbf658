'use strict';
MetronicApp.factory('teacher_hourlyratescheme_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.createOrUpdate = function(item){
        var promise;
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/edit',item);
    	}
    	return promise;
       
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/delete',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/softdelete',items);
        }
        return promise;
    };
    
    service.checkList = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=$http.post(baseUrl+'/jiaoshiguanli/teacher_hourlyratescheme/checkList',items);
        }
        return promise;
    };

    return service;
}]);
