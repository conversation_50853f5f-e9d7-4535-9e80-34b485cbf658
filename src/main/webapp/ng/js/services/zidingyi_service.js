/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2015/12/7.
 */
MetronicApp.factory('zidingyi_service',['$http', '$q', function ($http,$q) {
    var service={};

    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/tongjibaobiao/zidingyi/list', searchForm).then(function (response) {
            return response.data;
        });
        return res;
    };

    service.queryAll = function(){
        searchForm = {}
        searchForm.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        var res=  $http.post(baseUrl+'/tongjibaobiao/zidingyi/all',searchForm).then(function (response) {
            return response.data;
        });
        return res;
    };

    service.export = function(searchForm){
        var res=  $http.post(baseUrl+'/tongjibaobiao/zidingyi/export', searchForm).then(function (response) {
            return response.data;
        });
        return res;
    };

    return service;
}]);
