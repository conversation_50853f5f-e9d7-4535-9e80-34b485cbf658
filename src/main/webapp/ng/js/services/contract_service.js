'use strict';
MetronicApp.factory('contract_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/hetongguanli/contract/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/hetongguanli/contract/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.createOrUpdate = function(item){
        var promise;
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/hetongguanli/contract/edit',item);
    	}
    	return promise;
       
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contract/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contract/delete',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contract/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contract/softdelete',items);
        }
        return promise;
    };

    return service;
}]);
