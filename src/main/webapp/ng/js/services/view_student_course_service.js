'use strict';
MetronicApp.factory('student_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        searchForm.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        var res=  $http.post(baseUrl+'/xueshengguanli/student/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/xueshengguanli/student/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.findKeshi = function(id){
        var res;
        res=  $http.post(baseUrl+'/xueshengguanli/student/findKeshi',id).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.daoru = function(item){
        //防止重复提交参数设置
        item.loadingStatus = true;
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/xueshengguanli/student/import',item);
        }
        return promise;
    };


    service.createOrUpdate = function(item){
        var promise;
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/xueshengguanli/student/edit',item);
    	}
    	return promise;
       
    };

    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/xueshengguanli/student/delete',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/xueshengguanli/student/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/xueshengguanli/student/softdelete',items);
        }
        return promise;
    };

    service.createContract = function(submitForm){
        var promise;
        if(isdebug){
        }else{
            promise= $http.post(baseUrl+'/xueshengguanli/student/createContract',submitForm).then(function(response){
                return response.data;
            });
        }
        return promise;
    }

    service.getConsumption = function(student_id){
        var res;
        res=  $http.post(baseUrl+'/xueshengguanli/student/list/consumption', {"student_id":student_id}).then(function (response) {
            return response.data;
        })
        return res;
    }

    return service;
}]);
