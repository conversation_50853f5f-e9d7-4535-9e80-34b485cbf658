/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/1/5.
 */
MetronicApp.factory('studentClassReport_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/tongjibaobiao/studentClassReport/list', searchForm).then(function (response) {
            return response.data;
        });
        return res;
    };
    return service;
}]);