'use strict';
MetronicApp.factory('teacher_course_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/jiaoshiguanli/teacher_course/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/jiaoshiguanli/teacher_course/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.findAllBystudent_id = function(id){
        var res;
        res=  $http.post(baseUrl+'/jiaoshiguanli/teacher_course/list/findAllBystudent_id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.checkTeacher = function(item){
        var res;
        item.BUType = sessionStorage.getItem("BUType");
        res=  $http.post(baseUrl+'/jiaoshiguanli/teacher_course/list/checkTeacher',item).then(function (response) {
            return response.data;
        })
        return res;
    };


    service.createOrUpdate = function(item){
        var promise;
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_course/edit',item);
    	}
    	return promise;
       
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_course/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_course/delete',items);
        }
        return promise;
    };

    service.update = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_course/update',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_course/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/teacher_course/softdelete',items);
        }
        return promise;
    };

    return service;
}]);
