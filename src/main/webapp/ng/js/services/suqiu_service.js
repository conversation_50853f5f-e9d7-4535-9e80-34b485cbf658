'use strict';
MetronicApp.factory('suqiu_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        searchForm.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        searchForm.timezone_id = sessionStorage.getItem("timezone_id"); //bohuang

        var res=  $http.post(baseUrl+'/paikeguanli/suqiu/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
	 service.loadById = function(id){
	     var res;
	     var timezone_id = sessionStorage.getItem("timezone_id");
        res=  $http.post(baseUrl+'/paikeguanli/suqiu/list/id', {"id":id,"timezone_id": timezone_id}).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.createOrUpdate = function(item){
        var promise;
        item.timezone_id = sessionStorage.getItem("timezone_id");
        item.zone_id = sessionStorage.getItem("zone_id");
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/paikeguanli/suqiu/edit',item).then(function(response){
                return response.data;
            });
    	}
    	return promise;
       
    };
    service.clone = function(item){
        var promise;
        item.timezone_id = sessionStorage.getItem("timezone_id");
        item.zone_id = sessionStorage.getItem("zone_id");
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/paikeguanli/suqiu/clone',item).then(function(response){
                return response.data;
            });
    	}
    	return promise;
       
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/paikeguanli/suqiu/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/paikeguanli/suqiu/delete',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/paikeguanli/suqiu/softdelete',items);
        }
        return promise;
    };

    return service;
}]);

'use strict';
MetronicApp.factory('followupTask_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        searchForm.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        var res=  $http.post(baseUrl+'/paikeguanli/suqiu/listTask', searchForm).then(function (response) {
            return response.data;
        });
        return res;
    };

    return service;
}]);
