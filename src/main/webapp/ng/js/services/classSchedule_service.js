'use strict';
MetronicApp.factory('classSchedule_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/paikeguanli/classSchedule/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };

    service.checktecherAndstudent = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id"); //bohuang
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/checktecherAndstudent',item).then(function (response) {
            return response.data;
        })
        return res;
    };
    
    service.checktecherAndstudent_basic = function(item){
        var res;
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/checktecherAndstudent_basic',item).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.findAllteacherDay = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id"); //bohuang
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/findAllteacherDay',item).then(function (response) {
            return response.data;
        })
        return res;
    };
    
    service.findAllteacherDay_basic = function(item){
        var res;
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/findAllteacherDay_basic',item).then(function (response) {
            return response.data;
        })
        return res;
    };
    
    service.paikeBystudnet = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id");
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/paikeBystudnet1',item).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.paikeBystudnetfreedatecode = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id");
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/paikeBystudnetfreedatecode',item).then(function (response) {
            return response.data;
        })
        return res;
    };
    
    service.paikeBystudnet_basic = function(item){
        var res;
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/paikeBystudnet1__basic',item).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.paikeByteacher = function(item){
        var res;
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/paikeByteacher1',item).then(function (response) {
            return response.data;
        })
        return res;
    };
    
    service.findStudentNextDay = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id"); //bohuang
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/findStudentNextDay',item).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.findTeacherNextDay = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id"); //bohuang
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/findTeacherNextDay',item).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.teacherkebiao = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id");//bohuang
        item.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        item.BUType = sessionStorage.getItem("BUType"); //bohuang
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/teacherkebiao',item).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.zongkebiao = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id");
        item.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        item.BUType = sessionStorage.getItem("BUType"); //bohuang
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/zongkebiao',item).then(function (response) {
            return response.data;
        })
        return res;
    };

   //查询教室
    service.checkclassroom = function(item){
        var res;
        item.timezoneId = sessionStorage.getItem("timezone_id"); //bohuang
        item.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        item.BUType = sessionStorage.getItem("BUType"); //bohuang
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/checkclassroom',item).then(function (response) {
            return response.data;
        })
        return res;
    };
  //查询教室
    service.checkclassroom_basic = function(item){
        item.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        item.BUType = sessionStorage.getItem("BUType"); //bohuang
        item.timezone_id = sessionStorage.getItem("timezone_id"); //bohuang
        var res;
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/checkclassroom_basic',item).then(function (response) {
            return response.data;
        })
        return res;
    };


	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/paikeguanli/classSchedule/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.createOrUpdate = function(item){
        var promise;
        item.timezoneId = sessionStorage.getItem("timezone_id");
        item.zone_id = sessionStorage.getItem("zone_id");
        item.BUType = sessionStorage.getItem("BUType");
    	if(isdebug){
    	}else{
            promise=$http.post(baseUrl+'/paikeguanli/classSchedule/edit',item);
    	}
    	return promise;
       
    };
    
    service.createOrUpdate_basic = function(item){
        var promise;
        item.zone_id = sessionStorage.getItem("zone_id"); //bohuang
        item.BUType = sessionStorage.getItem("BUType"); //bohuang
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/paikeguanli/classSchedule/edit_basic',item);
    	}
    	return promise;
       
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/paikeguanli/classSchedule/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/paikeguanli/classSchedule/delete',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/paikeguanli/classSchedule/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/paikeguanli/classSchedule/softdelete',items);
        }
        return promise;
    };

    return service;
}]);
