'use strict';
MetronicApp.factory('classroom_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/jichushuju/classroom/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
    service.findAll = function(id){
        var res;
        res=  $http.post(baseUrl+'/jichushuju/classroom/findAll', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };
	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/jichushuju/classroom/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.createOrUpdate = function(item){
        var promise;
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/jichushuju/classroom/edit',item);
    	}
    	return promise;
       
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jichushuju/classroom/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jichushuju/classroom/delete',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jichushuju/classroom/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jichushuju/classroom/softdelete',items);
        }
        return promise;
    };

    return service;
}]);
