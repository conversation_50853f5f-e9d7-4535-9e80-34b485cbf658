'use strict';
MetronicApp.factory('view_teacher_course_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
    service.query1 = function(searchForm){
        var res=  $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/list1', searchForm).then(function (response) {
            return response.data;
        });
        return res;
    };
    service.findAllBykechengAndjibie = function(item){
        var res;
        res=  $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/list/findAllBykechengAndjibie',item).then(function (response) {
            return response.data;
        })
        return res;
    };
	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };
    service.createOrUpdate = function(item){
        var promise;
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/edit',item);
    	}
    	return promise;
       
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/delete',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/jiaoshiguanli/view_teacher_course/softdelete',items);
        }
        return promise;
    };

    return service;
}]);
