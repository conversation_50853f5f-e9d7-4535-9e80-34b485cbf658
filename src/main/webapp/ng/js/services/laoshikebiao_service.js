/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2015/12/23.
 */
MetronicApp.factory('laoshikebiao_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        var res=  $http.post(baseUrl+'/tongjibaobiao/laoshikebiao/list', searchForm).then(function (response) {
            return response.data;
        });
        return res;
    };
    return service;
}]);
