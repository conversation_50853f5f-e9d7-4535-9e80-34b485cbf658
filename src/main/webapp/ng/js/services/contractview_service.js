'use strict';
MetronicApp.factory('contractview_service',['$http', '$q', function ($http,$q) {
    var service={};
    service.query = function(searchForm){
        //bohuang start
        searchForm.zone_id = sessionStorage.getItem("zone_id");
        searchForm.BUType = sessionStorage.getItem("BUType");
        //bohuang end
        var res=  $http.post(baseUrl+'/hetongguanli/contractview/list', searchForm).then(function (response) {
                return response.data;
            });
        return res;
    };
	 service.loadById = function(id){
	     var res;
        res=  $http.post(baseUrl+'/hetongguanli/contractview/list/id', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.tuifeiById = function(id){
        var res;
        res=  $http.post(baseUrl+'/hetongguanli/contractview/list/tuifei', {"id":id }).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.getchanpin = function(item){
        var res;
        res=  $http.post(baseUrl+'/hetongguanli/contractview/list/getchanpin',item).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.getTuifei = function(item){
        var res;
        res=  $http.post(baseUrl+'/hetongguanli/contractview/list/getTuifei',item).then(function (response) {
            return response.data;
        })
        return res;
    };

    service.createOrUpdate = function(item){
        var promise;
    	if(isdebug){
    	}else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/edit',item);
    	}
    	return promise;
       
    };

    service.createOrUpdate2 = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/edit2',item);
        }
        return promise;
       
    };

    service.createOrUpdate3 = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/edit3',item);
        }
        return promise;
       
    };

    service.saveTuifei = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/tuifei',item);
        }
        return promise;
    };
	  service.daoru = function(item){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/daoru',item);
        }
        return promise;

    };
    service.delete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/delete',items);
        }
        return promise;
    };
	
	 service.deletestatus = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/deletestatus',items);
        }
        return promise;
    };
	
	 service.softdelete = function(items){
        var promise;
        if(isdebug){
        }else{
            promise=   $http.post(baseUrl+'/hetongguanli/contractview/softdelete',items);
        }
        return promise;
    };

    service.getByContractNo = function(contractNo){
        var res;
        res= $http.post(baseUrl+'/hetongguanli/contractview/list/contractNo', {"contractNo":contractNo }).then(function (response) {
            return response.data;
        })
        return res;
    }

    return service;
}]);
