'use strict';

MetronicApp.controller('smallgroup_controller', function($scope,$rootScope,$state, $modal, $stateParams, student_service,smallgroup_service, org_service, alertService, $location, filterFilter, $filter) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
        //初始化二级菜单
        $scope.menu7Click('subMenu1');
    });
    //begin-本业务的全局变量
    var smallgroup = {};
    $scope.smallgroup = smallgroup;

    $scope.count = $rootScope.count;

    //初始化controller
    initController($scope, smallgroup, 'smallgroup',smallgroup_service, filterFilter, $filter);
    smallgroup.searchForm.orderGuize="createTime desc";
    if($scope.zone_id!="all"){
    	//student.searchForm.searchItems.BUType_eq=$scope.BUType;
    	smallgroup.searchForm.searchItems.zone_id_eq=$scope.zone_id;
	}

    smallgroup.smallgroupAdd=function(){
        $state.go('smallgroup-add',{id: '0'});
    }

    smallgroup.deleteData=function(item){
        //if(!$scope.authbutton(fnid, e)) return false;
        var selectedItems = [];
        selectedItems.push(item);
        var res = confirm("删除是不可恢复的，你确认要删除吗？");
        if (res) {
            Metronic.blockUI();
            var promise = smallgroup_service.delete(selectedItems);
            promise.then(function (data) {
                Metronic.unblockUI();
                if(data.data.status=='success'){
                    toastr['success']($filter('T')(data.data.message), "Success");
                    /* toastr['success'](data.data.message, "Success");*/
                    smallgroup.searchN();
                } else{
                    toastr['error']($filter('T')(data.data.message), "Sorry");
                    /*toastr[data.data.status](data.data.message, "Sorry");*/
                }
            },function(){
                Metronic.unblockUI();
            });
        }
    }
    //        执行数据初始化
    var type = $stateParams.type;
    if(type==0){
        smallgroup.search1();
    }
    //        执行数据初始化结束
    //-end功能结束
    $("body").keyup(function(event) {
        if (event.keyCode == 13){
            if(type==0){
                smallgroup.search1();
            }
        }
    });
});



MetronicApp.controller('smallgroup_edit_controller', function($scope,$rootScope, $state, $modal, $stateParams, smallgroup_service, student_service, smallgroup_student_service, org_service, zone_service, $location, teacherview_service, filterFilter, student_zone_service, student_timezone_service, contractview_service, view_coures_teacher_service, student_freeTime_service, shangke_service, studentReturns_service,qingjia_service,followupLog_service, tutor_service, suqiu_service, file_service, city_service, course_service, gradeCategory_service, teachingway_service, grade_service, student_course_service, $filter) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
    });

    var teacher={};
    $scope.teacher=teacher;
    var student1={};
    $scope.student1=student1;

    var id =  $stateParams.id;
    var smallgroup = {};
    $scope.smallgroup = smallgroup;
    zone_service.getZoneInfo({}).then(function(data){
        smallgroup.zoneList = data.zoneList;
    },function (data) {  // 处理错误 .reject
    });
    if(id != undefined && id != '0'){
        var promise=  smallgroup_service.loadById(id);
        promise.then(function(data){
            smallgroup.item=data;
            teacher.item = {};
            teacher.item.name=data.tutor_englishName;
        });
    }else{
        smallgroup.item={};


        //获取校区信息
        
    }


    smallgroup.getCheckedItems = function (dataList) {
        var res = filterFilter(dataList, function (record) {
            return record.checked;
        });
        return res;
    }
    // 增加修改保存
    smallgroup.save = function(action) {
        Metronic.blockUI();
        var submitForm={};
        submitForm.item = smallgroup.item;
        submitForm.zoneList  = smallgroup.getCheckedItems(smallgroup.zoneList);//可访问校区

        var promise = smallgroup_service.createOrUpdate(submitForm);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            if(data.data.status=="success"){
                toastr['success']($filter('T')(data.data.message), "Success");
                /*  toastr['success'](data.data.message, "Success");*/
            }else{
                toastr['error']($filter('T')(data.data.message), "Sorry");
                /*   toastr[data.data.status](data.data.message, "Sorry");*/
            }
            $state.go('small_group',{type: '0'});
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']("您的信息保存出错！", "Sorry");
        });
    }


    //学员的选择
    
    
    $scope.xuanzeStudent=function(){
        openwindow($modal,"views/window/student-list.html","lg",function($scope,$modalInstance){
            var student = {};
            $scope.student = student;
            //初始化controller
            initController($scope, student, 'student',  student_service, filterFilter, $filter);
            student.searchForm.orderGuize="id desc";
            student.search1();
            $scope.selectRow=function(d){
                student1.item={};
                student1.item.name="";
                if(!isNull(d.chineseName)){
                    student1.item.name+=d.chineseName;
                }
                if(!isNull(d.englishName)){
                    student1.item.name+="-"+d.englishName;
                }
                student1.item.name += "-" + d.id;
                $modalInstance.dismiss("cancel");

                // insert into database
                smallgroup_student.item.student_id = d.id;
                save_student();
            }
            $scope.cancel=function(){
                $modalInstance.dismiss("cancel");
            }
        });
    }


    
    $scope.xuanzeTeacher=function(){
       openwindow($modal,"views/window/teacher-list.html","lg",function($scope,$modalInstance){
           var teacherview = {};
           $scope.teacherview = teacherview;
           //初始化controller
           initController($scope, teacherview, 'teacherview',  teacherview_service, filterFilter, $filter);
           teacherview.searchForm.orderGuize="id desc";
           teacherview.searchForm.searchItems.delstatus_eq="0";
           teacherview.search1();

           $scope.selectRow=function(d){
               teacher.item={};
               teacher.item.name="";
               if(!isNull(d.englishName)){
                   teacher.item.name+=d.englishName;
               }
               if(!isNull(d.chineseName)){
                   teacher.item.name+="-"+d.chineseName;
               }
               smallgroup.item.tutorId = d.id;

               //smallgroup.searchForm.searchItems.teacher_id_eq= d.id;
               $modalInstance.dismiss("cancel");
           }
           $scope.cancel=function(){
               $modalInstance.dismiss("cancel");
           }
       })
   }

   /*第三方合作controller初始化------------------------------------------------------------------------------------------------*/
    var smallgroup_student = {};
    $scope.smallgroup_student = smallgroup_student;
    smallgroup_student.item = {};
    initController($scope, smallgroup_student, 'smallgroup_student',  smallgroup_student_service, filterFilter, $filter);
    smallgroup_student.searchForm.orderGuize="id desc";
    smallgroup_student.searchForm.searchItems.delStatus_eq="0";
    smallgroup_student.searchForm.searchItems.smallgroup_id_eq = id;

    var save_student=function(){
        var submit = {};
        smallgroup_student.item.smallgroup_id = id;
        if(!isNull(smallgroup_student.item.smallgroup_id)){
            Metronic.blockUI({
                boxed: true
            });

            submit.item = smallgroup_student.item;
            smallgroup_student_service.createOrUpdate(submit).then(function (data) {
                Metronic.unblockUI();
                if(data.data.status=="error"){
                    toastr['error']($filter('T')(data.data.message), "Sorry！");
               /*     toastr['error'](data.data.message, "Sorry！");*/
                } else if(data.data.status=="warning") {
                    toastr['warning']($filter('T')(data.data.message), "Sorry！");
                } else{
                    toastr['success']($filter('T')("new_toastr_0061"), "Success");
                  /*  toastr['success']("您的信息已保存成功！", "Success")*/
                    smallgroup_student.item={};
                    smallgroup_student.search1();
                }

            }, function (data) {  // 处理错误 .reject
                Metronic.unblockUI();
                toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
            /*    toastr['error']("您的信息保存出错！", "Sorry！")*/
            });
        }
    };

    smallgroup_student.search1();
    smallgroup_student.edit=function(d){
        if(!$rootScope.authbutton('604')) return;//验证按钮权限
        smallgroup_student.item = angular.copy(d);
    };

    smallgroup_student.reset = function(){
        smallgroup_student.item = {};
    };

    // add contracts
    $scope.add=function(){
        $state.go('contractedit',{id: smallgroup.item.id, type: 's'});
    }
    $scope.edit=function(d){
        $state.go('contractedit',{id: d.id});
    }
    $scope.detail=function(d){
        $state.go('contractdetail',{id: d.id});
    }

    // copied from studentxiangxi controller
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
        //初始化二级菜单
        $scope.menu1Click('subMenu1');
    });

    //添加校区
    zone_service.findAllZoneid().then(function (data) {
        $scope.zone_list = data;
    });

    //菜单切换
    $rootScope.$on("SetTab", function(){
        var tab_index = 0;
        if ($location.absUrl().indexOf('studentxiangxi.html/') > 0) {
            if ($scope.tab == 1) {
                tab_index = 1;
            } else if ($scope.tab == 2) {
                tab_index = 3;
            } else if ($scope.tab == 3) {
                tab_index = 2;
            } else if ($scope.tab == 4) {
                tab_index = 4;
            } else if ($scope.tab == 5) {
                tab_index = 5;
            } else if ($scope.tab == 6) {
                tab_index = 6;
            } else if ($scope.tab == 7) {
                tab_index = 7;
            } else if ($scope.tab == 8) {
                tab_index = 8;
            } else if ($scope.tab == 9) {
                tab_index = 9;
            } else if ($scope.tab == 10) {
                tab_index = 10;
            } else if ($scope.tab == 0) {
                tab_index = 0;
            }
            $timeout(function () {
                $('.nav-tabs li a').eq(tab_index).click();
            });
            student.chooseSetTable(tab_index);
        }
        
    });
    
    $scope.edit=function(d){
        if(!$rootScope.authbutton('104')) return;//验证按钮权限
        $state.go('studentedit',{id: d.id});
    }


    /*学生信息初始化------------------------------------------------------------------------------------------------------*/
    var student = {};
    $scope.student = student;
    student.item = {};
    var zoneBUType;
    $scope.zoneBUType = zoneBUType;
    
    /*城市和校区controller-------------------------------------------------------------------------------------------------*/
    var student_zone = {};
    $scope.student_zone = student_zone;
    //初始化controller
    initController($scope, student_zone, 'student_zone',  student_zone_service, filterFilter, $filter);
    student_zone.searchForm.orderGuize="id desc";
    student_zone.searchForm.searchItems.student_id_eq=$stateParams.id;
    student_zone.search1();
    
    /*课表时区controller-------------------------------------------------------------------------------------------------*/
    var student_timezone = {};
    $scope.student_timezone = student_timezone;
    //初始化controller
    initController($scope, student_timezone, 'student_timezone',  student_timezone_service, filterFilter, $filter);
    student_timezone.searchForm.orderGuize="id desc";
    student_timezone.searchForm.searchItems.student_id_eq=$stateParams.id;
    student_timezone.search1();
    
    /*合同信息controller-------------------------------------------------------------------------------------------------*/
    var contractview = {};
    $scope.contractview = contractview;
    //初始化controller
    initController($scope, contractview, 'contractview',  contractview_service, filterFilter, $filter);
    contractview.searchForm.orderGuize="id desc";
    contractview.searchForm.searchItems.delstatus_eq="0";

    //=========================================学员诉求
    var requirement={};
    
    //========================================科目及老师
    var view_coures_teacher = {};
    $scope.view_coures_teacher = view_coures_teacher;
    //初始化controller
    initController($scope, view_coures_teacher, 'view_coures_teacher',  view_coures_teacher_service, filterFilter, $filter);
    view_coures_teacher.searchForm.orderGuize="id desc";
    //view_coures_teacher.searchForm.status_eq="1";
    var zoneId_sesrch=$scope.zone_id;
    /*if(zoneId_sesrch=="all"){
    	zoneBUType="";
    }
    else{
    	zone_service.loadById(zoneId_sesrch).then(function(res){
    	view_coures_teacher.searchForm.searchItems.BUType_eq=res.BUtype;
    	zoneBUType=res.BUtype;
    });
    }*/
    

    /*---学生可排课时间初始化--------------------------------------------------------------------------------------------------*/
    var student_freeTime = {};
    $scope.student_freeTime = student_freeTime;
    initController($scope, student_freeTime, 'student_freeTime',student_freeTime_service, filterFilter, $filter);
    student_freeTime.searchForm.orderGuize="id desc";
    student_freeTime.searchForm.BUType_eq=$scope.BUType;
    /*初始化日期*/
    student_freeTime.timeItems = timeItems;
    student_freeTime.weekTitle = [];

    var shangke = {};
    $scope.shangke = shangke;
    //初始化controller
    initController($scope, shangke, 'shangke',  shangke_service, filterFilter, $filter);
    shangke.searchForm.orderGuize="id desc";

    var studentReturns = {};
    $scope.studentReturns = studentReturns;
    //初始化controller
    initController($scope, studentReturns, 'studentReturns',  studentReturns_service, filterFilter, $filter);
    studentReturns.searchForm.orderGuize="id desc";

    var qingjia = {};
    $scope.qingjia = qingjia;
    //初始化controller
    initController($scope, qingjia, 'qingjia',  qingjia_service, filterFilter, $filter);
    qingjia.searchForm.orderGuize="id desc";

    var followupLog = {};
    $scope.followupLog = followupLog;
    //初始化controller
    initController($scope, followupLog, 'followupLog',  followupLog_service, filterFilter, $filter);
    followupLog.searchForm.orderGuize="id desc";

    //助教
    var tutor = {};
    $scope.tutor = tutor;
    initController($scope, tutor, 'tutor',  tutor_service, filterFilter, $filter);
    tutor.searchForm.orderGuize="startDate desc";

    //文档
    var file = {};
    $scope.file = file;
    initController($scope, file, 'file',  file_service, filterFilter, $filter);
    file.searchForm.orderGuize="id desc";

    //诉求
    var suqiu = {};
    $scope.suqiu = suqiu;
    initController($scope, suqiu, 'suqiu',  suqiu_service, filterFilter, $filter);
    suqiu.searchForm.orderGuize="id desc";

    var weekbtnStart  = 2;
    /*表头封装方法*/
    var makeTitle =  function(weekDates){
        var zhongwen = ["周一","周二","周三","周四","周五","周六","周日"]
        student_freeTime.weekTitle=[];
        for(var i=0;i<7;i++){
            var title = {};
            title.name = zhongwen[i];
            title.date = weekDates[i];
            student_freeTime.weekTitle.push(title);
        }
    };
    /*---学生可排课时间初始化--------------------------------------------------------------------------------------------------*/
    var init_student_freeTime = function(weekbtnStart){
        Metronic.blockUI({
            boxed: true
        });
        student_freeTime.isCycle = false;
        student_freeTime.cycleEndDay = '';
        if(!isNull(id)){
            student_freeTime.searchForm.student_id=id;
            student_freeTime.searchForm.BUType=$scope.BUType;
            student_freeTime.searchForm.timezone_id=$scope.timezone_id;
            /*student_freeTime.search1();*/
            /*student_freeTime.dataList = [{status:"0",id:"01"}, {status:"0",id:"02"}, {status:"0",id:"03"}, {status:"0",id:"04"}, {status:"0",id:"05"}, {status:"0",id:"06"}, {status:"0",id:"07"}];*/
            /*初始化当前周*/
            var weekDates = getWeeks(weekbtnStart);
            makeTitle(weekDates);
            student_freeTime.searchForm.weeks = weekDates;//一周时间
            student_freeTime.searchForm.timeItems= student_freeTime.timeItems;// 每天对应的时间段
            student_freeTime_service.query(student_freeTime.searchForm).then(function(data){
                student_freeTime.list = data.list;
                Metronic.unblockUI();
            },function(data){
                Metronic.unblockUI();
            });
        }else{
            Metronic.unblockUI();
        }
    };

    /*页面控制----------------------------------------------------------------------------------------------------------*/
    student.chooseSetTable  = function(setPage){
        if(setPage==1){//合同信息
            $scope.tab = 1;
            contractview.searchForm.searchItems.studentId_eq = id;
            contractview.searchN();
        }else if(setPage==2){
            $scope.tab = 2;
            view_coures_teacher.searchForm.searchItems.student_id_eq = id;
            //view_coures_teacher.searchForm.searchItems.status_eq="1";
            view_coures_teacher.searchN();
        }else if(setPage==3){
            $scope.tab = 3;
            $scope.paikeFlag=false;
    		init_student_freeTime(weekbtnStart);
        	/*var zoneId_select=$scope.zone_id;
        	 if(zoneId_select=="all"||zoneId_select=="null"||zoneId_select==null||zoneId_select==undefined){
        	    	zoneBUType="";
        	    	$scope.paikeFlag=true;
            		alert($filter("T")('new_alert_0087'));
                    return;
        	 }
        	
        	else{
        		$scope.paikeFlag=false;
        		init_student_freeTime(weekbtnStart);
        	}*/
        	
            
        }else if(setPage==4){
            $scope.tab = 4;
            shangke.searchForm.searchItems.studentId = id;
            shangke.searchN();
        }else if(setPage==5){
            $scope.tab = 5;
            studentReturns.searchForm.searchItems.studentId = id;
            studentReturns.searchN();
        }else if(setPage==6){
            $scope.tab = 6;
            qingjia.searchForm.searchItems.studentId_eq = id;
            qingjia.searchN();
        }else if(setPage==7){
            $scope.tab = 7;
            followupLog.searchForm.searchItems.student_id_eq = id;
            followupLog.searchN();
        }else if(setPage==8){
            $scope.tab = 8;
            student.consumption={};
            init_consumption(id);
        }else if(setPage==9){
            $scope.tab = 9;
            tutor.searchForm.searchItems.student_id_eq = id;
            tutor.searchN();
        }else if(setPage==10){
            $scope.tab = 10;
            file.searchForm.searchItems.student_id_eq = id;
            file.searchForm.orderGuize="createTime desc";
            file.searchN();
        }else if(setPage==0){
            $scope.tab = 0;
        }else if(setPage==11){
            $scope.tab = 11;
            suqiu.searchForm.searchItems.student_id_eq = id;
            suqiu.searchForm.orderGuize="createTime desc";
            suqiu.searchN();
        }
    };

    var id= $stateParams.id;
    var tab = $stateParams.tab;
    $scope.tab = tab;
    console.info(tab);
    student.chooseSetTable(tab);
    if(!isNull(id)){
        student_service.loadById(id).then(function (res) {
            //初始化变量
            student.item = res;
            if(student.item.status=='5'){
                student.item.isThawed='1';
            }else{
                student.item.isThawed='0';
            }
            student.item.age =getAge(student.item.birthday);
        });
    }
    $scope.tab = 0;
    /*城市查询*/
    var searchForm = {};
    searchForm.orderGuize="id desc";
    searchForm.searchItems = {};
    searchForm.searchItems.delStatus_eq="0";
    student.city=[];
    city_service.query(searchForm).then(function(data){
        student.city = data.list;
    });

    /*校区监听*/
    student.zone = [];
    $scope.$watch("student.item.city_id",function(newValue,oldValue,scope){
        if(!isNull(student.item.city_id)){
            var searchForm = {};
            searchForm.searchItems ={};
            searchForm.searchItems.city_id_eq = student.item.city_id;
            zone_service.query(searchForm).then(function(res){
                student.zone = res.list;
            });
        }else{
            student.zone = [];
        }
    });

    $scope.followupLog_edit=function(d){
        console.log('here');
        if(!$rootScope.authbutton('109')) return;//验证按钮权限
        $state.go('followupLogedit',{id: d.id});
    }
    $scope.followupLog_add=function(d){
        if(!$rootScope.authbutton('108')) return;//验证按钮权限
        $state.go('followupLogeditNew',{id: d.item.id});
    }
    $scope.suqiu_add=function(d){
        if(!$rootScope.authbutton('142')) return;//验证按钮权限
        var url = $state.href('suqiueditNew',{id: d.item.id});
        window.open(url, '_blank');
        //$state.go('suqiueditNew',{id: d.item.id});
    }
    $scope.suqiu_edit=function(d){
        if(!$rootScope.authbutton('143')) return;//验证按钮权限
        var url = $state.href('suqiuedit',{id: d.id});
        window.open(url, '_blank');
        //$state.go('suqiuedit',{id: d.id});
    }
    $scope.suqiu_clone=function(d){
        if(!$rootScope.authbutton('143')) return;//验证按钮权限
        Metronic.blockUI({
            boxed: true
        });

        var counter = 0;
        var suqiuId = '';
        for(var i = 0; i < suqiu.dataList.length; i++) {
            if (suqiu.dataList[i].checked) {
                counter++;
                suqiuId = suqiu.dataList[i].id;
            }
        }
        if (counter == 0) {
            Metronic.unblockUI();
            toastr['error']($filter('T')("Please select at least one record to clone."), "Sorry！");
        } else if (counter > 1) {
            Metronic.unblockUI();
            toastr['error']($filter('T')("More than one selected."), "Sorry！");
        } else if (counter == 1) {
            var submitform={};
            submitform.item = suqiuId;

            //submitform.item.zone_id = $scope.zone_id;
            var promise = suqiu_service.clone(submitform);
            promise.then(function (data) {  // 调用承诺API获取数据 .resolve
                suqiu.searchN();
                Metronic.unblockUI();
                toastr['success']($filter('T')(data.message), "Success");
            }, function (data) {  // 处理错误 .reject
                Metronic.unblockUI();
                toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
             /*   toastr['error']("您的信息保存出错！", "Sorry！")*/
            });
        }

        //var suqiu = {};
        // suqiu.searchForm.searchItems.student_id_eq = id;
        // suqiu.searchForm.orderGuize="createTime desc";
        // suqiu.searchN();

    }
    $scope.detail=function(d){
        if(!$rootScope.authbutton('144')) return;//验证按钮权限
        $state.go('followupLogdetail',{id: d.id});
    }

    view_coures_teacher.createOrUpdate=function(d){
        // if(isNull(student_zone.dataList)){
        //     alert($filter("T")('new_alert_0002'));
        //     alert("new_alert_0002"|T);
        //     return;
        // }
        openwindow($modal,"student_course.html","lg",function($scope,$modalInstance){
        	var student_courses={};
            $scope.student_courses=student_courses;
            //级别大类
            gradeCategory_service.findAll("").then(function(data){
                student_courses.gradeCategory_idList=data.list;
            })
            var searchForm = {};
            searchForm.orderGuize="id desc";
            searchForm.searchItems = {};
            searchForm.searchItems.BUType_eq=$scope.BUType;
            teachingway_service.query(searchForm).then(function(data){
            	student_courses.item.teachingWayList=data.list;
            });
            //get课程
            $scope.$watch("student_courses.item.grade_id",function(newvalue,oldvalue){
            	if(!isNull(newvalue)){
                    course_service.findAllByGrad(student_courses.item.grade_id).then(function(data){
                        student_courses.courseList=data.list;
                    });
                }
            })

            //课程 change
            student_courses.change2=function(){
                student_courses.item.teacher_id="";
                check();
            }

            $scope.$watch("student_courses.item.gradeCategory_id",function(newvalue,oldvalue){
            	if(!isNull(newvalue)){
                    grade_service.findAllbygrade(student_courses.item.gradeCategory_id).then(function(data){
                        student_courses.gradeList=data.list;
                    })
                }
            })

            //级别分类  change
            student_courses.change=function(){
                student_courses.item.grade_id="";
                student_courses.item.teacher_id="";
                if(!isNull(student_courses.item.gradeCategory_id)){
                    grade_service.findAllbygrade(student_courses.item.gradeCategory_id).then(function(data){
                        student_courses.gradeList=data.list;
                    })
                }
                check();
            }
            //级别  change student_courses.item.teachingWay_id
            student_courses.change1=function(){
                student_courses.item.teacher_id="";
                check();
            }

            //上课方式
            student_courses.teachingWayChange=function(){
                student_courses.item.teacher_id="";
                check();
            }

            //查询老师   by课程  级别分类  级别 上课分哪敢时
            var check=function(){
                if(!isNull(student_courses.item.course_id)&&!isNull(student_courses.item.grade_id)&&!isNull(student_courses.item.gradeCategory_id)&&!isNull(student_courses.item.teachingWay_id)){
                    var su={};
                    su.grade_id=student_courses.item.grade_id;
                    su.course_id=student_courses.item.course_id;
                    su.gradeCategory_id=student_courses.item.gradeCategory_id;
                    su.student_id =student.item.id;
                    //su.zone_id =student.item.zone_id;
                    su.zone_id = sessionStorage.getItem("zone_id");
                    su.teachingWay_id = student_courses.item.teachingWay_id;
                    console.info(student.item.zone_id);

                    //查询老师
                    view_teacher_course_service.findAllBykechengAndjibie(su).then(function(data){
                        student_courses.userList=data.list;
                    });
                }
            }


            if(d!=undefined){
                student_courses.item=angular.copy(d);
                student_courses.item.teachingWay_id=d.teachingWay;
                
                //分类级别  change
                grade_service.findAllbygrade(student_courses.item.gradeCategory_id,student_courses.item.course_id).then(function(data){
                    student_courses.gradeList=data.list;
                })
                check();
                //console.info("11111111"+student_courses.item.course_id);
            }else{
                student_courses.item={};
               // student_courses.item.teachingWay = "1";
                student_courses.item.status = "1";
                student_courses.item.BUType=$scope.BUType;
                //console.info("2222222222"+student_courses.item.course_id);
            }
            var zoneId=$scope.zone_id;

            $scope.submit=function(){
                Metronic.blockUI();

                if(student_courses.item.endDate != "" && student_courses.item.startDate>=student_courses.item.endDate){
                    alert($filter("T")('new_alert_0001'));
                 /*   alert("new_alert_0001"|T);*/
                    Metronic.unblockUI();
                    return false;
                }
                var sub={};
                student_courses.item.student_id=student.item.id;
                sub.item=student_courses.item;
                student_course_service.createOrUpdate(sub).then(function(data){
                    Metronic.unblockUI();
                    if(data.data.status=="error"){
                        toastr['error']($filter('T')(data.data.message), "Sorry！");
                    }else{
                        view_coures_teacher.searchN();
                        toastr['success']($filter('T')(data.data.message), "Success");
                        $modalInstance.dismiss("cancel");
                    }
                },function(){
                    Metronic.unblockUI();
                });
            }
            $scope.cancel=function(){
                $modalInstance.dismiss("cancel");
            }
        });
    }

    //get课程
    course_service.findAll("").then(function(data){
        requirement.courseList=data.list;
        shangke.courseList=data.list;
    });

    //诉求的新增and修改
    $scope.shuqiu=function(d){
        openwindow($modal,"xueyuansuqiu.html","lg",function($scope,$modalInstance){
            var requi={};
            $scope.requi=requi;
            //查询所有的 科目信息
            if(d!=undefined){
                requi.item=angular.copy(d);
            }else{
                requi.item={};
            }

            //get课程
            course_service.findAllBystudent(student.item.id).then(function(data){
                requi.courseList=data.list;
            });
            

            $scope.submit=function(){
                Metronic.blockUI();
                if(requi.item.startDate>=requi.item.endDate){
                    alert($filter("T")('new_alert_0001'));
                 
                    Metronic.unblockUI();
                    return false;
                }
                var sbmit={};
                requi.item.student_id=student.item.id;
                sbmit.item=requi.item;
                requirement_service.createOrUpdate(sbmit).then(function(data){
                    Metronic.unblockUI();
                    if(data.data.status=="error"){
                         toastr['error']($filter('T')(data.data.message), "Sorry！");
                    }else{
                        requirement.searchN();
                        toastr['success']($filter('T')(data.data.message), "Success");
                        $modalInstance.dismiss("cancel");
                    }

                },function(){
                    Metronic.unblockUI();
                });
            }
            $scope.cancel=function(){
                $modalInstance.dismiss("cancel");
            }
        })
    }


    $scope.$watch("student_freeTime.toDay",function(newvalue,oldvalue){
    	/*if(zoneBUType==undefined||zoneBUType==""){
    		$scope.paikeFlag=true;
            return;
    	}*/
    	
    	if(!isNull(newvalue)){
            Metronic.blockUI({
                boxed: true
            });
            /*保存本周可排课时间*/
            var submitForm = {};
            submitForm.student_id = student.item.id;
            submitForm.isCycle = student_freeTime.isCycle;
            submitForm.cycleEndDay  = student_freeTime.cycleEndDay;
            submitForm.weekTitle = student_freeTime.weekTitle;
            submitForm.list = student_freeTime.list;
            submitForm.toDay=student_freeTime.toDay;
            submitForm.BUType = $scope.BUType;
            submitForm.timezone_id = $scope.timezone_id;
            student_freeTime_service.batchSave(submitForm).then(function(data){
                /*初始化上周可排课时间*/
                console.info(data.count);
                weekbtnStart=parseInt(data.count);
                student_freeTime.searchForm.student_id=student.item.id;
                var weekDates = getWeeks(weekbtnStart);
                makeTitle(weekDates);
                student_freeTime.searchForm.weeks = weekDates;//一周时间
                student_freeTime.searchForm.timeItems= student_freeTime.timeItems;// 每天对应的时间段
                student_freeTime.searchForm.timezone_id = $scope.timezone_id;
                student_freeTime_service.query(student_freeTime.searchForm).then(function(data){
                    student_freeTime.list = data.list;
                    Metronic.unblockUI();
                },function(data){
                    Metronic.unblockUI();
                });
                toastr['success']($filter('T')("new_toastr_0061"), "Success");
                /*  toastr['success']("您的信息已保存成功！", "");*/
            },function(data){
                Metronic.unblockUI();
                toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
                /*   toastr['error']("您的信息保存出错！", "");*/
            })
        }
    })


    /*上一周*/
    student_freeTime.preWeek = function(){
    	var zone_id=$scope.zone_id;
    	/*if(zone_id==undefined||zone_id==""||zone_id=="all"){
    		alert($filter("T")('new_alert_0087'));
            alert("new_alert_0087"|T);
            return;
    	}*/
    	Metronic.blockUI({
            boxed: true
        });
        
        /*保存本周可排课时间*/
        var submitForm = {};
        submitForm.student_id = student.item.id;
        submitForm.isCycle = student_freeTime.isCycle;
        submitForm.cycleEndDay  = student_freeTime.cycleEndDay;
        submitForm.weekTitle = student_freeTime.weekTitle;
        submitForm.list = student_freeTime.list;
        submitForm.BUType = $scope.BUType;
        submitForm.timezone_id = $scope.timezone_id;
        student_freeTime_service.batchSave(submitForm).then(function(data){
            /*初始化上周可排课时间*/
            weekbtnStart-=1;
            student_freeTime.searchForm.student_id=student.item.id;
            var weekDates = getWeeks(weekbtnStart);
            makeTitle(weekDates);
            student_freeTime.searchForm.weeks = weekDates;//一周时间
            student_freeTime.searchForm.timeItems= student_freeTime.timeItems;// 每天对应的时间段
            student_freeTime.searchForm.timezone_id = $scope.timezone_id;
            student_freeTime_service.query(student_freeTime.searchForm).then(function(data){
                student_freeTime.list = data.list;
                Metronic.unblockUI();
            },function(data){
                Metronic.unblockUI();
            });
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
          /*  toastr['success']("您的信息已保存成功！", "");*/
        },function(data){
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
         /*   toastr['error']("您的信息保存出错！", "");*/
        })
    };

    /*下一周*/
    student_freeTime.nextWeek = function(){
    	var zone_id=$scope.zone_id;
    	/*if(zone_id==undefined||zone_id==""||zone_id=="all"){
    		alert($filter("T")('new_alert_0087'));
            alert("new_alert_0087"|T);
            return;
    	}*/
        Metronic.blockUI({
            boxed: true
        });
        /*保存本周可排课时间*/
        var submitForm = {};
        submitForm.student_id = student.item.id;
        submitForm.isCycle = student_freeTime.isCycle;
        submitForm.cycleEndDay  = student_freeTime.cycleEndDay;
        submitForm.weekTitle = student_freeTime.weekTitle;
        submitForm.list = student_freeTime.list;
        submitForm.BUType = $scope.BUType;
        submitForm.timezone_id = $scope.timezone_id;
        student_freeTime_service.batchSave(submitForm).then(function(data){
            /*初始化下周可排课时间*/
            weekbtnStart+=1;
            student_freeTime.isCycle = false;
            student_freeTime.cycleEndDay = '';
            student_freeTime.searchForm.student_id=student.item.id;
            var weekDates = getWeeks(weekbtnStart);
            makeTitle(weekDates);
            student_freeTime.searchForm.weeks = weekDates;//一周时间
            student_freeTime.searchForm.timeItems= student_freeTime.timeItems;// 每天对应的时间段
            student_freeTime.searchForm.timezone_id = $scope.timezone_id;
            student_freeTime_service.query(student_freeTime.searchForm).then(function(data){
                student_freeTime.list = data.list;
                Metronic.unblockUI();
            },function(data){
                Metronic.unblockUI();
            });
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
           /* toastr['success']("您的信息已保存成功！", "");*/
        },function(data){
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
          /*  toastr['error']("您的信息保存出错！", "");*/
        })

    };

    /*单击事件*/
    var intervalTimer = null;//设置定时器
    student_freeTime.toCheck = function(d){
        clearTimeout(intervalTimer); //取消上次延时未执行的方法
        intervalTimer = setTimeout(function() {
            // click 事件的处理
            if(d.checked==undefined){
                if(!isNull(d.freeTimeCode)){
                    d.checked =false;
                    d.isFree = '0';
                    d.freeTimeCode = '';
                }else{
                    d.checked = true;
                    d.isFree = '1';
                    d.freeTimeCode = '1234';
                }
            }else{
                if(d.checked){
                    d.checked =false;
                    d.isFree = '0';
                    d.freeTimeCode = '';
                }else{
                    d.checked = true;
                    d.isFree = '1';
                    d.freeTimeCode = '1234';
                }
            }
            $scope.$digest();
        }, 300);


    };
    student_freeTime.changeTimezone = function(d){
		       	 openwindow($modal,"student_freeTime_timezone.html","sm",function($scope,$modalInstance){   		
		       		var searchForm = {};
		        	var stu_timezoneList=[];
		    	 	var stu_timezone={};
		    	 	var submitForm = {};
		    	 	$scope.stu_timezone=stu_timezone;
		        	 searchForm.orderGuize="id desc";
		    	        searchForm.searchItems = {};
		    	        searchForm.student_id=student.item.id;
		    	        searchForm.weeks=student_freeTime.weekTitle;
		    	        Metronic.blockUI({
    	        			boxed: true
		    	        });
		    	        student_timezone_service.findAll(searchForm).then(function(data){
		    	        	if(data.list.length==0){
		    	        		$modalInstance.dismiss("cancel");
		    	        		alert($filter("T")('new_alert_0089'));
		    	        		var submitForm_t = {};
		    	        		submitForm_t.student_id = student.item.id;
				                var weekSearchtz_t=[];
				                for(var i=0;i<student_freeTime.weekTitle.length;i++){
				                	var date=student_freeTime.weekTitle[i].date;
				                	weekSearchtz_t.push(date);
				                	
				                }
				                
				                submitForm_t.weeks= weekSearchtz_t;
				                submitForm_t.BUType = $scope.BUType;
				                submitForm_t.timezone_id = $scope.timezone_id;
				                submitForm_t.timeItems= student_freeTime.timeItems;
				                Metronic.blockUI({
				    	        	boxed: true
				    	        });
				                student_freeTime_service.query(submitForm_t).then(function(data){
				                    student_freeTime.list = data.list;
				                    Metronic.unblockUI();
				                },function(data){
				                   
				                });
				                $modalInstance.dismiss("cancel");
		                        return false;
		    	        		
		    	        	}
		    	        	else{
		    	        		stu_timezoneList= data.list;
		    		        	$scope.stu_timezoneList=stu_timezoneList;

		    		        	//如果只有一个课表时区,直接跳转时区,不需要显示列表了
                                if(stu_timezoneList != undefined && stu_timezoneList.length == 1){
                                    stu_timezone = stu_timezoneList[0];
                                    $scope.submit();
                                }
		    		        	
		    	        	}
		                    Metronic.unblockUI();
		                },function(data){
		                    Metronic.unblockUI();
		                });
		    	        $scope.cancel=function(){
			                 $modalInstance.dismiss("cancel");
			             }

		    	        $scope.submit = function(){
			                if(isNull(stu_timezone.timezone_id)){
			                	alert($filter("T")('new_alert_0088'));
			                            return false;
			                }
			                Metronic.blockUI();
			                $rootScope.timezone_id = stu_timezone.timezone_id;
			                $scope.timezone_id = stu_timezone.timezone_id;
			                $rootScope.$emit('CallParentMethod', {});
			                submitForm.student_id = student.item.id;
			                var weekSearchtz=[];
			                for(var i=0;i<student_freeTime.weekTitle.length;i++){
			                	var date=student_freeTime.weekTitle[i].date;
			                	weekSearchtz.push(date);
			                	
			                }
			                
			                submitForm.weeks= weekSearchtz;
			                submitForm.BUType = $scope.BUType;
			                submitForm.timezone_id = stu_timezone.timezone_id;
			                submitForm.timeItems= student_freeTime.timeItems;
			                Metronic.blockUI({
			    	        	boxed: true
			    	        });
			                student_freeTime_service.query(submitForm).then(function(data){
			                    student_freeTime.list = data.list;
			                    Metronic.unblockUI();
			                },function(data){
			                   
			                });
			                 $modalInstance.dismiss("cancel");
			             }   
	        	//}
	        	
	        });
    	
    
    	
    	
    }
    /*双击事件*/
    student_freeTime.todelete = function(d){
        clearTimeout(intervalTimer);
        openwindow($modal,"student_freeTimeDel.html","sm",function($scope,$modalInstance){
            var s_freeTime = d;
            s_freeTime.startOptions = getStartOptions(s_freeTime.startTime);
            s_freeTime.endOptions = getEndOptions(s_freeTime.startTime);
            function getStartOptions(startTime){
                var startOptions = [];
                startOptions.push({"label":startTime.split(":")[0]+":00","value":startTime.split(":")[0]+":00"});
                startOptions.push({"label":startTime.split(":")[0]+":15","value":startTime.split(":")[0]+":15"});
                startOptions.push({"label":startTime.split(":")[0]+":30","value":startTime.split(":")[0]+":30"});
                startOptions.push({"label":startTime.split(":")[0]+":45","value":startTime.split(":")[0]+":45"});
                return startOptions;
            }
            function getEndOptions(startTime){
                var endOptions = [];
                var hour = parseInt(startTime.split(":")[0])+1;
                if(hour==24){
                	hour=0;
                }
                if(hour<10){
                    hour = "0"+hour;
                }else {
                    hour = ""+hour;
                }
                endOptions.push({"label":startTime.split(":")[0]+":15","value":startTime.split(":")[0]+":15"});
                endOptions.push({"label":startTime.split(":")[0]+":30","value":startTime.split(":")[0]+":30"});
                endOptions.push({"label":startTime.split(":")[0]+":45","value":startTime.split(":")[0]+":45"});
                endOptions.push({"label":hour+":00","value":hour+":00"});
                return endOptions;
            }
            $scope.s_freeTime = s_freeTime;
            $scope.cancel=function(){
                $modalInstance.dismiss("cancel");
            }
            $scope.submit = function(){
                if(!validateFreeTime(s_freeTime.startTime, s_freeTime.endTime)) return;
                s_freeTime.isFree = '1';
                s_freeTime.checked = false;
                s_freeTime.freeTimeCode = getFreeTimeCode(s_freeTime.startTime, s_freeTime.endTime);
                $modalInstance.dismiss("cancel");
            }
            function validateFreeTime(startTime, endTime){
                if(isNull(startTime)){
                    alert($filter("T")('new_alert_0015'));
            /*        alert("开始时间不能为空！"); //TODO*/
                    return false;
                }
                if(isNull(endTime)){
                    alert($filter("T")('new_alert_0016'));
                  /*  alert("结束时间不能为空！"); //TODO*/
                    return false;
                }
                var endTimeInt=parseInt(endTime.replace(":", ""));
                if(endTimeInt==0){
                	endTimeInt=2400;
                }
                if(endTimeInt <= parseInt(startTime.replace(":", ""))){
                	alert($filter("T")('new_alert_0017'));
                  /*  alert("结束时间必须大于开始时间！");*/
                    return false;
                }
                return true;
            }
            function getFreeTimeCode(startTime, endTime){
                var freeTimeCode = "";
                if(startTime.endWith("00")){
                    if(endTime.endWith("15")){
                        freeTimeCode="1";
                    }else if(endTime.endWith("30")){
                        freeTimeCode="12";
                    }else if(endTime.endWith("45")){
                        freeTimeCode="123";
                    }else if(endTime.endWith("00")){
                        freeTimeCode="1234";
                    }
                }else if(startTime.endWith("15")){
                    if(endTime.endWith("30")){
                        freeTimeCode="2";
                    }else if(endTime.endWith("45")){
                        freeTimeCode="23";
                    }else if(endTime.endWith("00")){
                        freeTimeCode="234";
                    }
                }else if(startTime.endWith("30")){
                    if(endTime.endWith("45")){
                        freeTimeCode="3";
                    }else if(endTime.endWith("00")){
                        freeTimeCode="34";
                    }
                }else if(startTime.endWith("45")){
                    if(endTime.endWith("00")){
                        freeTimeCode="4";
                    }
                }
                return freeTimeCode;
            }
        });
    };
    /*选中/取消选中全天*/
    student_freeTime.selectDay = function(title){
        if(student_freeTime.list==undefined) return;
        
        if(title.checked){
            for(var i=0; i<student_freeTime.list.length; i++){
                var timeItem = student_freeTime.list[i];
                for(var j=0; j<timeItem.s_freeTimeList.length; j++){
                    var s_freeTime = timeItem.s_freeTimeList[j];
                    //alert(i+"--"+s_freeTime.freeDate+"--"+title.date);
                    if(s_freeTime.freeDate == title.date||s_freeTime.freeDateStr == title.date){
                       
                    	s_freeTime.checked = false;
                        s_freeTime.isFree = '0';
                        s_freeTime.freeTimeCode='';
                        break;
                    }
                }
            }
            title.checked =false;
        }else{
            for(var i=0; i<student_freeTime.list.length; i++){
                var timeItem = student_freeTime.list[i];
                for(var j=0; j<timeItem.s_freeTimeList.length; j++){
                    var s_freeTime = timeItem.s_freeTimeList[j];
                    
                    if(s_freeTime.freeDate== title.date||s_freeTime.freeDateStr == title.date){
                    	
                    	s_freeTime.checked = true;
                        s_freeTime.isFree = '1';
                        s_freeTime.freeTimeCode='1234';
                        //alert(i+"--"+s_freeTime.freeDate+"--"+title.date+"--"+s_freeTime.freeDateStr);
                        break;
                    }
                }
            }
            title.checked =true;
        }
       

    }

    /*选中/取消选中全周的某小时*/
    student_freeTime.selectTime = function(timeItem){
        if(student_freeTime.list==undefined) return;
        if(timeItem.checked){
            for(var i=0; i<student_freeTime.list.length; i++){
                var freeTime = student_freeTime.list[i];
                for(var j=0; j<freeTime.s_freeTimeList.length; j++){
                    var s_freeTime = freeTime.s_freeTimeList[j];
                    if(s_freeTime.startTime == timeItem.startTime&&s_freeTime.endTime == timeItem.endTime){
                        s_freeTime.checked = false;
                        s_freeTime.isFree = '0';
                        s_freeTime.freeTimeCode='';
                        // break;
                    }
                }
            }
            timeItem.checked =false;
        }else{
            for(var i=0; i<student_freeTime.list.length; i++){
                var freeTime = student_freeTime.list[i];
                for(var j=0; j<freeTime.s_freeTimeList.length; j++){
                    var s_freeTime = freeTime.s_freeTimeList[j];
                    if(s_freeTime.startTime == timeItem.startTime&&s_freeTime.endTime == timeItem.endTime){
                        s_freeTime.checked = true;
                        s_freeTime.isFree = '1';
                        s_freeTime.freeTimeCode='1234';
                        // break;
                    }
                }
            }
            timeItem.checked =true;
        }
    }

    var allTime={};

    /*选中/取消选中全周*/
    student_freeTime.all = function(){
        if(student_freeTime.list==undefined) return;
        if(allTime.checked){
            for(var i=0; i<student_freeTime.list.length; i++){
                var freeTime = student_freeTime.list[i];
                for(var j=0; j<freeTime.s_freeTimeList.length; j++){
                    var s_freeTime = freeTime.s_freeTimeList[j];
                    s_freeTime.checked = false;
                    s_freeTime.isFree = '0';
                    s_freeTime.freeTimeCode='';
                }
            }
            allTime.checked =false;
        }else{
            for(var i=0; i<student_freeTime.list.length; i++){
                var freeTime = student_freeTime.list[i];
                for(var j=0; j<freeTime.s_freeTimeList.length; j++){
                    var s_freeTime = freeTime.s_freeTimeList[j];
                    s_freeTime.checked = true;
                    s_freeTime.isFree = '1';
                    s_freeTime.freeTimeCode='1234';
                }
            }
            allTime.checked =true;
        }
    }

    student_freeTime.isSelect_1= function(s_freeTime){
        if(s_freeTime.checked){
            return true;
        }else{
            if(isNull(s_freeTime.freeTimeCode)){
                return false;
            }else{
                if(s_freeTime.freeTimeCode.indexOf('1')>=0){
                    return true;
                }else{
                    return false;
                }
            }
        }
    }
    student_freeTime.isSelect_2= function(s_freeTime){
        if(s_freeTime.checked){
            return true;
        }else{
            if(isNull(s_freeTime.freeTimeCode)){
                return false;
            }else{
                if(s_freeTime.freeTimeCode.indexOf('2')>=0){
                    return true;
                }else{
                    return false;
                }
            }
        }
    }
    student_freeTime.isSelect_3= function(s_freeTime){
        if(s_freeTime.checked){
            return true;
        }else{
            if(isNull(s_freeTime.freeTimeCode)){
                return false;
            }else{
                if(s_freeTime.freeTimeCode.indexOf('3')>=0){
                    return true;
                }else{
                    return false;
                }
            }
        }
    }
    student_freeTime.isSelect_4= function(s_freeTime){
        if(s_freeTime.checked){
            return true;
        }else{
            if(isNull(s_freeTime.freeTimeCode)){
                return false;
            }else{
                if(s_freeTime.freeTimeCode.indexOf('4')>=0){
                    return true;
                }else{
                    return false;
                }
            }
        }
    }
    student_freeTime.save = function(){
    	var zone_id=$scope.zone_id;
    	/*if(zone_id==undefined||zone_id==""||zone_id=="all"){
    		alert($filter("T")('new_alert_0087'));
            alert("new_alert_0087"|T);
            return;
    	}*/
    	
        Metronic.blockUI({
            boxed: true
        });
        var submitForm = {};
        submitForm.student_id = student.item.id;
        submitForm.isCycle = student_freeTime.isCycle;
        submitForm.cycleEndDay  = student_freeTime.cycleEndDay;
        submitForm.weekTitle = student_freeTime.weekTitle;
        submitForm.list = student_freeTime.list;
        submitForm.BUType = $scope.BUType;
        submitForm.timezone_id = $scope.timezone_id;
        student_freeTime_service.batchSave(submitForm).then(function(data){
            init_student_freeTime(weekbtnStart);
            Metronic.unblockUI();
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
           /* toastr['success']("您的信息已保存成功！", "");*/
        },function(data){
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
     /*       toastr['error']("您的信息保存出错！", "");*/
        })

    }

    student.givenClass=function(){
        //获取推荐人
        contractview_service.getByContractNo(student.item.referral).then(function(data){
            var contracts = data.contracts;
            if(contracts==undefined||contracts.length==0){
                alert($filter("T")('new_alert_0018'));
              /*  alert("TE系统中暂无此推荐人");*/
                return;
            }
            openwindow($modal,"contract.html","",function($scope, $modalInstance){
                var contract = {};
                $scope.contract = contract;
                var reference = contracts[0];
                var studentName = "";
                if(!isNull(reference.chineseName)){
                    studentName+=reference.chineseName;
                }
                if(!isNull(reference.englishName)){
                    if(!isNull(studentName)){
                        studentName+="-";
                    }
                    studentName+=reference.englishName;
                }
                contract.studentName = studentName;
                contract.contractNo = reference.contractNo;
                contract.persentType = "3";//TE-推荐赠送
                contract.reference_id = reference.studentId;//介绍学员Id
                contract.student_id = student.item.id;//被介绍学员Id

                $scope.submit=function(){
                    Metronic.blockUI();
                    var submitForm ={};
                    submitForm.item = contract;
                    student_service.createContract(submitForm).then(function(res){
                        Metronic.unblockUI();
                        $modalInstance.dismiss("cancel");
                        if(res.status=='success'){
                            student.item.isGiven='1';
                            toastr['success']($filter('T')(res.message), "Success");
                            $modalInstance.dismiss("cancel");
                        }else{
                            toastr["error"]($filter('T')(res.message),"error");
                        }
                    },function(res){
                        Metronic.unblockUI();
                    })
                }
                $scope.cancel=function(){
                    $modalInstance.dismiss("cancel");
                }
            })
        })

    }

    //初始化耗课信息
    function init_consumption(student_id){
        Metronic.blockUI();
        student_service.getConsumption(student_id).then(function(data){
            Metronic.unblockUI();
            student.consumption.dataList = data.list;
            student.consumption.item = data.item;
        },function(data){
            Metronic.unblockUI();
        })
    }


    file.down=function(d){
        var submit={};
        submit.item= d.id;

        if (d.logicName.substr(0, 2) == 'h-') {
            window.location.href= "https://te2.beacademy.org:8443/tesystem/ngres/downloadFile/"+d.id;
        } else {
            window.location.href= "../ngres/downloadFile/"+d.id;
        }
    };
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;

});

