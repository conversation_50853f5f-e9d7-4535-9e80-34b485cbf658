'use strict';
MetronicApp.controller('gradeCategory_controller', function($rootScope, $filter,$scope,$state,$stateParams,gradeCategory_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
    });
    var gradeCategory = {};
    $scope.gradeCategory = gradeCategory;
    //初始化controller
    initController($scope, gradeCategory, 'gradeCategory',  gradeCategory_service, filterFilter, $filter);
    gradeCategory.searchForm.orderGuize="id desc";
	gradeCategory.searchForm.searchItems.delstatus_eq="0";
    gradeCategory.search1();

    $scope.add=function(){
        $state.go('gradeCategoryedit',{id: ''});
    }
    $scope.edit=function(d){
        $state.go('gradeCategoryedit',{id: d.id});
    }
    $scope.detail=function(d){
        $state.go('gradeCategorydetail',{id: d.id});
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
});


MetronicApp.controller('gradeCategoryedit_controller', function($rootScope,$filter, $scope,$state,$stateParams,gradeCategory_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
       
    });
    var gradeCategory = {};
    $scope.gradeCategory = gradeCategory;
    var id= $stateParams.id;
    if(!isNull(id)){
       gradeCategory_service.loadById(id).then(function (res) {
            //初始化变量
           gradeCategory.item = res
        });
    }


    // 增加修改保存
    // 增加修改保存
    gradeCategory.save = function(action) {
        Metronic.blockUI({
            boxed: true
        });
        var submitform={};
        submitform.item = gradeCategory.item;
        var promise = gradeCategory_service.createOrUpdate(submitform);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
          /*  toastr['success']("您的信息已保存成功！", "Success")*/
            gradeCategory.item={}
            if(action==1){
                $state.go('gradeCategory');
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
     /*       toastr['error']("您的信息保存出错！", "Sorry！")*/
        });
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})
