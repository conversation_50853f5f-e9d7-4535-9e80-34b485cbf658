'use strict';
MetronicApp.controller('contractview_controller', function($rootScope,$filter, $scope,$state,$stateParams,contractview_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
        //初始化二级菜单
        $scope.menu3Click('subMenu1');
    });
    var contractview = {};
    $scope.contractview = contractview;
    //初始化controller
    initController($scope, contractview, 'contractview',  contractview_service, filterFilter, $filter);
    contractview.searchForm.orderGuize="id2";
	contractview.searchForm.searchItems.delstatus_eq="0";
    if($rootScope.searchFormContractview!=undefined){
        contractview.searchForm=$rootScope.searchFormContractview;
    }
    contractview.search1();
    $("body").keyup(function(event) {
        if (event.keyCode == 13){
            contractview.search1();
        }
    });

    var flag="flag";
    $scope.flag = flag;
    //排序
    $scope.paixu=function(s,f){
        if(f=='flag'){
            flag=s+"1";
        }else{
            if(f.substring(0,f.length-1)==s){
                if(f.substring(f.length-1,f.length)=='1'){
                    flag=s+"2";
                }else{
                    flag=s+"1";
                }
            }else{
                flag=s+"1";
            }
        }
        $scope.flag = flag;
        contractview.searchForm.orderGuize=flag;
        contractview.search1();
    }

    /*退费弹窗*/
    contractview.checkone=function(d){
        if(!$rootScope.authbutton('306')) return;//验证按钮权限
        openwindow($modal,"tuifei.html","lg",function($scope,$modalInstance){

            var contractviewone={};
            $scope.contractviewone=contractviewone;
            contractviewone.item={};
            if(!isNull(d)){
                contractviewone.item = angular.copy(d) ;
            }


            $scope.cancel=function(){
                $modalInstance.dismiss("cancel");
            }
        })
    };


    // 弃学
    contractview.qixue = function(d) {
        Metronic.blockUI({
            boxed: true
        });
        var submitform={};
        submitform.item = angular.copy(d) ;
        if(isNull(submitform.item.isAbandoned)||submitform.item.isAbandoned!="1"){
            submitform.item.isAbandoned = "1";
            submitform.item.abandonedDate = currentDate();
        }else{
            submitform.item.isAbandoned = "0";
            submitform.item.abandonedDate = "1000-01-01";
        }

        var promise = contractview_service.createOrUpdate(submitform);
        promise.then(function (data) {
            Metronic.unblockUI();
            if(data.data.status=="error"){
                toastr['error']($filter('T')(data.data.message), "Sorry！");
              /*  toastr['error'](data.data.message, "Sorry！");*/
            }else{
                toastr['success']($filter('T')("new_toastr_0061"), "Success");
      /*          toastr['success']("您的信息修改成功！", "Success");*/
                contractview.searchN();
            }

        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
         /*   toastr['error']("您的信息保存出错！", "Sorry！")*/
        });

    };

    //退费
    $scope.tuifei=function(d){
        if(!$rootScope.authbutton('306')) return;//验证按钮权限
        $state.go('contracttuifei',{id: d.id});
    }






    $scope.add=function(){
        if(!$rootScope.authbutton('303')) return;//验证按钮权限
        $state.go('contractedit',{id: ''});
    }
    $scope.edit=function(d){
        if(!$rootScope.authbutton('304')) return;//验证按钮权限
        $state.go('contractviewedit',{id: d.id});
    }
    $scope.checkone=function(d){
        if(!$rootScope.authbutton('302')) return;//验证按钮权限
        $rootScope.searchFormContractview=contractview.searchForm;
        $state.go('contractviewcheck',{id: d.id});
    }
    $scope.detail=function(d){
        if(!$rootScope.authbutton('302')) return;//验证按钮权限
        $rootScope.searchFormContractview=contractview.searchForm;
        $state.go('contractviewdetail',{id: d.id});
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
});


MetronicApp.controller('contractviewcheck_controller', function($rootScope,$filter, $scope,$state,$stateParams,contractview_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
       
    });
    var contractview = {};
    $scope.contractview = contractview;
    var id= $stateParams.id;
    if(!isNull(id)){
       contractview_service.loadById(id).then(function (res) {
            //初始化变量
           contractview.item = res;
        });
    }


    contractview.save = function(action) {
        Metronic.blockUI();
        if(isNull(contractview.item)){
            toastr['error']($filter('T')("new_toastr_0047"), "Sorry！");
            /*toastr['error']("您没有填数据！" ,"Sorry！");*/
            Metronic.unblockUI();
            return ;
        }
        $scope.isDisabled = true;
        var submitform={};
        submitform.item = contractview.item;
        var promise = contractview_service.createOrUpdate3(submitform);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            console.log(data);
            if(data.data.status=='error'){
                toastr['error']($filter('T')(data.data.message), "Sorry！");
                /*  toastr['error'](data.data.message, "Sorry！")*/
            }else{
                toastr['success']($filter('T')(data.data.message), "Success");
                /*  toastr['success'](data.data.message, "Success");*/
                contractview.item = {};
                contractview.item = data.data.item;
                
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
            /*         toastr['error']("您的信息保存出错！", "Sorry！")*/
        });

    };
    $scope.$watch('contractview.item.allowchangetype',function(oldValue,newValue){
        if (contractview.item != undefined) {
            if (contractview.item.allowchangetype == "0" || contractview.item.contractType2 != ""){
                $scope.isDisabled = true;
            } else {
                $scope.isDisabled = false;
            }
        }
        
    },false);


    // 弃学
    contractview.qixue = function() {
        Metronic.blockUI({
            boxed: true
        });
        var submitform={};
        submitform.item = angular.copy(contractview.item) ;

        if(isNull(contractview.item.isAbandoned)||contractview.item.isAbandoned!="1"){
            submitform.item.isAbandoned = "1";
            submitform.item.abandonedDate = currentDate();
        }else{
            submitform.item.isAbandoned = "0";
            submitform.item.abandonedDate = "1000-01-01";
        }

        var promise = contractview_service.createOrUpdate(submitform);
        promise.then(function (data) {
                Metronic.unblockUI();
            if(data.data.status=="error"){
                toastr['error']($filter('T')(data.data.message), "Sorry！");
             /*   toastr['error'](data.data.message, "Sorry！");*/
            }else{
                toastr['success']($filter('T')("new_toastr_0061"), "Success");
              /*  toastr['success']("您的信息修改成功！", "Success")*/
                contractview_service.loadById(id).then(function (res) {
                    contractview.item = res;
                });
            }

        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
        /*    toastr['error']("您的信息保存出错！", "Sorry！")*/
        });

    };

    /**/


    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})


MetronicApp.controller('contracttuifei_controller', function($rootScope,$filter, $scope,$state,$stateParams,contractview_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();

    });
    var returns = {};
    $scope.returns = returns;
    var id= $stateParams.id;
    if(!isNull(id)){
        contractview_service.tuifeiById(id).then(function (res) {
            //初始化变量
            returns.item = res;
        });
    }

    returns.saveTuifei= function(d) {
        Metronic.blockUI({
            boxed: true
        });
        var submitform={};
        submitform.item = angular.copy(d) ;
        var promise = contractview_service.saveTuifei(submitform);
        promise.then(function (data) {
            Metronic.unblockUI();
            if(data.data.status=="error"){
                toastr['error']($filter('T')(data.data.message), "Sorry！");
           /*     toastr['error'](data.data.message, "Sorry！");*/
            }else{
                $state.go('contractview');
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
           /* toastr['error']("您的信息保存出错！", "Sorry！")*/
        });
    };

    returns.xuanzeType= function(d) {
        Metronic.blockUI({
            boxed: true
        });
        var type= d.type;
        if(type!=undefined){
            if(type=='1'){
                var submitform={};
                submitform.item = angular.copy(d) ;
                var promise = contractview_service.getchanpin(submitform);
                promise.then(function (data) {
                    Metronic.unblockUI();
                    if(data.status=="error"){
                        toastr['error']($filter('T')(data.message), "Sorry！");
                        /*toastr['error'](data.message, "Sorry！");*/
                    }else{
                        returns.list=data.list;
                    }
                }, function (data) {  // 处理错误 .reject
                    Metronic.unblockUI();
                    toastr['error']("您的产品信息获取出错！", "Sorry！")
                });
            }else{
                Metronic.unblockUI();
            }
        }
    }

    returns.xuanzeChapin= function(d) {
        Metronic.blockUI({
            boxed: true
        });
        var submitform={};
        submitform.item = angular.copy(d) ;
        var promise = contractview_service.getTuifei(submitform);
        promise.then(function (data) {
            Metronic.unblockUI();
            if(data.status=="error"){
                toastr['error']($filter('T')(data.message), "Sorry！");
              /*  toastr['error'](data.message, "Sorry！");*/
            }else{
                returns.item.tuifeijine=data.jine;
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0045"), "Sorry！");
           /* toastr['error']("您的产品信息获取出错！", "Sorry！")*/
        });
    }

    returns.tuifeijine= function(d) {


    }

    $scope.$watch('returns.item.danjia',function(oldValue,newValue){
        if(returns.item!=undefined&&returns.item.danjia!=undefined){
            Metronic.blockUI();
            var submitform={};
            submitform.item = angular.copy(returns.item) ;
            var promise = contractview_service.getTuifei(submitform);
            promise.then(function (data) {
                Metronic.unblockUI();
                if(data.status=="error"){
                    toastr['error']($filter('T')(data.message), "Sorry！");
                /*    toastr['error'](data.message, "Sorry！");*/
                }else{
                    returns.item.tuifeijine=data.jine;
                }
            }, function (data) {  // 处理错误 .reject
                Metronic.unblockUI();
                toastr['error']($filter('T')("new_toastr_0045"), "Sorry！");
            });
        }
    },false);

    /**/


    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})
