'use strict';
MetronicApp.controller('teacher_course_controller', function($rootScope,$filter, $scope,$state,$stateParams,teacher_course_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
    });
    var teacher_course = {};
    $scope.teacher_course = teacher_course;
    //初始化controller
    initController($scope, teacher_course, 'teacher_course',  teacher_course_service, filterFilter, $filter);
    teacher_course.searchForm.orderGuize="id desc";
	teacher_course.searchForm.searchItems.delstatus_eq="0";
    teacher_course.search1();

    $scope.add=function(){
        $state.go('teacher_courseedit',{id: ''});
    }
    $scope.edit=function(d){
        $state.go('teacher_courseedit',{id: d.id});
    }
    $scope.detail=function(d){
        $state.go('teacher_coursedetail',{id: d.id});
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
});


MetronicApp.controller('teacher_courseedit_controller', function($rootScope,$filter, $scope,$state,$stateParams,teacher_course_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
       
    });
    var teacher_course = {};
    $scope.teacher_course = teacher_course;
    var id= $stateParams.id;
    if(!isNull(id)){
       teacher_course_service.loadById(id).then(function (res) {
            //初始化变量
           teacher_course.item = res
        });
    }


    // 增加修改保存
    // 增加修改保存
    teacher_course.save = function(action) {
        Metronic.blockUI();
        var submitform={};
        submitform.item = teacher_course.item;
        var promise = teacher_course_service.createOrUpdate(submitform);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
         /*   toastr['success']("您的信息已保存成功！", "Success")*/
            teacher_course.item={}
            if(action==1){
                $state.go('teacher_course');
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
 /*           toastr['error']("您的信息保存出错！", "Sorry！")*/
        });
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})
