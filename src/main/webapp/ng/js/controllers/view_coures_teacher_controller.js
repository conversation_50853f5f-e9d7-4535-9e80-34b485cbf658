'use strict';
MetronicApp.controller('view_coures_teacher_controller', function($rootScope,$filter, $scope,$state,$stateParams,view_coures_teacher_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
    });
    var view_coures_teacher = {};
    $scope.view_coures_teacher = view_coures_teacher;
    //初始化controller
    initController($scope, view_coures_teacher, 'view_coures_teacher',  view_coures_teacher_service, filterFilter, $filter);
    view_coures_teacher.searchForm.orderGuize="id desc";
	view_coures_teacher.searchForm.searchItems.delstatus_eq="0";
    view_coures_teacher.search1();

    $scope.add=function(){
        $state.go('view_coures_teacheredit',{id: ''});
    }
    $scope.edit=function(d){
        $state.go('view_coures_teacheredit',{id: d.id});
    }
    $scope.detail=function(d){
        $state.go('view_coures_teacherdetail',{id: d.id});
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
});


MetronicApp.controller('view_coures_teacheredit_controller', function($rootScope,$filter, $scope,$state,$stateParams,view_coures_teacher_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
       
    });
    var view_coures_teacher = {};
    $scope.view_coures_teacher = view_coures_teacher;
    var id= $stateParams.id;
    if(!isNull(id)){
       view_coures_teacher_service.loadById(id).then(function (res) {
            //初始化变量
           view_coures_teacher.item = res
        });
    }


    // 增加修改保存
    // 增加修改保存
    view_coures_teacher.save = function(action) {
        Metronic.blockUI();
        var submitform={};
        submitform.item = view_coures_teacher.item;
        var promise = view_coures_teacher_service.createOrUpdate(submitform);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
            /*toastr['success']("您的信息已保存成功！", "Success")*/
            view_coures_teacher.item={}
            if(action==1){
                $state.go('view_coures_teacher');
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
          /*  toastr['error']("您的信息保存出错！", "Sorry！")*/
        });
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})
