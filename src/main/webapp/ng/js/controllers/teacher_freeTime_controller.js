'use strict';
MetronicApp.controller('teacher_freeTime_controller', function($rootScope,$filter, $scope,$state,$stateParams,teacher_freeTime_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
    });
    var teacher_freeTime = {};
    $scope.teacher_freeTime = teacher_freeTime;
    //初始化controller
    initController($scope, teacher_freeTime, 'teacher_freeTime',  teacher_freeTime_service, filterFilter, $filter);
    teacher_freeTime.searchForm.orderGuize="id desc";
	teacher_freeTime.searchForm.searchItems.delstatus_eq="0";
    teacher_freeTime.search1();

    $scope.add=function(){
        $state.go('teacher_freeTimeedit',{id: ''});
    }
    $scope.edit=function(d){
        $state.go('teacher_freeTimeedit',{id: d.id});
    }
    $scope.detail=function(d){
        $state.go('teacher_freeTimedetail',{id: d.id});
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
});


MetronicApp.controller('teacher_freeTimeedit_controller', function($rootScope,$filter, $scope,$state,$stateParams,teacher_freeTime_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
       
    });
    var teacher_freeTime = {};
    $scope.teacher_freeTime = teacher_freeTime;
    var id= $stateParams.id;
    if(!isNull(id)){
       teacher_freeTime_service.loadById(id).then(function (res) {
            //初始化变量
           teacher_freeTime.item = res
        });
    }


    // 增加修改保存
    // 增加修改保存
    teacher_freeTime.save = function(action) {
        Metronic.blockUI();
        var submitform={};
        submitform.item = teacher_freeTime.item;
        var promise = teacher_freeTime_service.createOrUpdate(submitform);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
         /*   toastr['success']("您的信息已保存成功！", "Success")*/
            teacher_freeTime.item={}
            if(action==1){
                $state.go('teacher_freeTime');
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
           /* toastr['error']("您的信息保存出错！", "Sorry！")*/
        });
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})
