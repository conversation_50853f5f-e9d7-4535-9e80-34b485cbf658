'use strict';
MetronicApp.controller('requirement_controller', function($rootScope,$filter, $scope,$state,$stateParams,requirement_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
    });
    var requirement = {};
    $scope.requirement = requirement;
    //初始化controller
    initController($scope, requirement, 'requirement',  requirement_service, filterFilter, $filter);
    requirement.searchForm.orderGuize="id desc";
	requirement.searchForm.searchItems.delstatus_eq="0";
    requirement.search1();

    $scope.add=function(){
        $state.go('requirementedit',{id: ''});
    }
    $scope.edit=function(d){
        $state.go('requirementedit',{id: d.id});
    }
    $scope.detail=function(d){
        $state.go('requirementdetail',{id: d.id});
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
});


MetronicApp.controller('requirementedit_controller', function($rootScope,$filter, $scope,$state,$stateParams,requirement_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
       
    });
    var requirement = {};
    $scope.requirement = requirement;
    var id= $stateParams.id;
    if(!isNull(id)){
       requirement_service.loadById(id).then(function (res) {
            //初始化变量
           requirement.item = res
        });
    }


    // 增加修改保存
    // 增加修改保存
    requirement.save = function(action) {
        Metronic.blockUI();
        var submitform={};
        submitform.item = requirement.item;
        var promise = requirement_service.createOrUpdate(submitform);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
          /*  toastr['success']("您的信息已保存成功！", "Success")*/
            requirement.item={}
            if(action==1){
                $state.go('requirement');
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
           /* toastr['error']("您的信息保存出错！", "Sorry！")*/
        });
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})
