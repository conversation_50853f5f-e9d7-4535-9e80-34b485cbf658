'use strict';
MetronicApp.controller('grade_controller', function($rootScope,$filter, $scope,$state,$stateParams,grade_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
    });
    var grade = {};
    $scope.grade = grade;
    //初始化controller
    initController($scope, grade, 'grade',  grade_service, filterFilter, $filter);
    grade.searchForm.orderGuize="id desc";
	grade.searchForm.searchItems.delstatus_eq="0";
    grade.search1();

    $scope.add=function(){
        $state.go('gradeedit',{id: ''});
    }
    $scope.edit=function(d){
        $state.go('gradeedit',{id: d.id});
    }
    $scope.detail=function(d){
        $state.go('gradedetail',{id: d.id});
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
});


MetronicApp.controller('gradeedit_controller', function($rootScope,$filter, $scope,$state,$stateParams,grade_service,filterFilter,$modal, $http, $timeout) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        Metronic.initAjax();
        ComponentsPickers.init();
       
    });
    var grade = {};
    $scope.grade = grade;
    var id= $stateParams.id;
    if(!isNull(id)){
       grade_service.loadById(id).then(function (res) {
            //初始化变量
           grade.item = res
        });
    }


    // 增加修改保存
    // 增加修改保存
    grade.save = function(action) {
        Metronic.blockUI({
            boxed: true
        });
        var submitform={};
        submitform.item = grade.item;
        var promise = grade_service.createOrUpdate(submitform);
        promise.then(function (data) {  // 调用承诺API获取数据 .resolve
            Metronic.unblockUI();
            toastr['success']($filter('T')("new_toastr_0061"), "Success");
        /*    toastr['success']("您的信息已保存成功！", "Success")*/
            grade.item={}
            if(action==1){
                $state.go('grade');
            }
        }, function (data) {  // 处理错误 .reject
            Metronic.unblockUI();
            toastr['error']($filter('T')("new_toastr_0044"), "Sorry！");
           /* toastr['error']("您的信息保存出错！", "Sorry！")*/
        });
    }
    // set sidebar closed and body solid layout mode
    $rootScope.settings.layout.pageBodySolid = true;
    $rootScope.settings.layout.pageSidebarClosed = false;
})
