
<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Begin: life time stats -->
        <div class="portlet">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-list"></i>{{'menu_0202'|T}}
                </div>
				 <div class="actions">
                    <div class="btn-group btn-group-sm btn-group-solid">
                        <!--<button type="button" class="btn blue" ng-click="add()" >{{'global_btn_0001'|T}}</button>-->
                        <!--<button type="button" class="btn red"  ng-click="followupTask.delete()">删除</button>-->
                    </div>

                </div>
            </div>
            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <!--搜索-->
                        <div class="row">

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label col-md-2 text-right" style=" margin-top: 5px;"> {{'table_filed_00325'|T}}<!--跟进日期--></label>
                                    <div class="col-md-10" ng-init="$root.settings.utils.initpicker()">
                                        <div class="col-md-5">
                                            <input type="text" class="form-control date-picker" data-date-format="yyyy-mm-dd"
                                                   placeholder="yyyy-mm-dd" ng-model="followupTask.searchForm.searchItems.followupTimeStart">
                                        </div>
                                        <div class="col-md-5">
                                            <input type="text" class="form-control date-picker" data-date-format="yyyy-mm-dd"
                                                   placeholder="yyyy-mm-dd" ng-model="followupTask.searchForm.searchItems.followupTimeEnd">
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-1 col-sm-12">
                                <button class="btn blue  filter-submit margin-bottom" ng-click="followupTask.search1()" style="padding: 5px 14px;"><i
                                        class="fa fa-search"></i> {{'global_btn_0005'|T}}
                                </button>
                            </div>
                            <div class="col-md-4 col-sm-12"></div>
                        </div>
                        <!--搜索-->
                    <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <!--<th width="1%">-->
                                        <!--<input type="checkbox" class="group-checkable">-->
                                    <!--</th>-->
                                    <th width="1%">
                                        {{'table_filed_00368'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00107'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00048'|T}}
                                    </th>
									 <th width="10%">
                                         {{'table_filed_00096'|T}}
									</th>

                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="d in followupTask.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                                    <!--<td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>-->
                                    <td>{{$index+1}}</td>
                                    <td>
                                        {{d.student_code}}
                                    </td>
                                    <td>
                                        {{d.student_chineseName}}<span ng-if="!isNull(d.student_chineseName)">-</span>{{d.student_englishName}}
                                    </td>
									 <td>
									 {{d.nextFollowupTime}}
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                    <!--结束table-->
                    <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    <div class="row">
                        <div class="col-md-8 col-sm-12">
                            <div class="dataTables_paginate paging_bootstrap_extended">
                                <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="followupTask.prePage()"
                                                                    class="btn btn-sm default prev"
                                                                    title="Prev"><i class="fa fa-angle-left"></i></a><input
                                        type="number"  ng-model="followupTask.searchForm.page.currentPage"
                                        class="pagination-panel-input form-control input-mini input-inline input-sm"
                                        maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="followupTask.nextPage()"
                                                                                                     class="btn btn-sm default next"
                                                                                                     title="Next"><i
                                        class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{followupTask.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                </div>
                            </div>
                            <div class="dataTables_length" ><label><span
                                    class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="followupTask.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="150">150</option>
                            </select> {{'table_filed_00390'|T}}</label></div>
                            <div class="dataTables_info"  role="status" aria-live="polite">
                                <span class="seperator">|</span>{{'table_filed_00389'|T}}{{followupTask.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12">

                        </div>
                    </div>
                    <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>
        </div>
        <!-- End: life time stats -->
    </div>
</div>
<!-- END PAGE CONTENT-->
