
<!-- BEGIN PAGE CONTENT-->
<div class="row">
<div class="col-md-12">
<!-- Begin: life time stats -->
<div class="portlet">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-list"></i>{{'menu_0203'|T}}
    </div>
    <div class="actions">
        <div class="btn-group btn-group-sm btn-group-solid">
            <!--<button type="button" class="btn blue" ng-click="add()" >{{'global_btn_0001'|T}}</button>-->
        </div>
    </div>
</div>
<div class="portlet-body">
<div class="table-container">
<div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
<!--搜索-->
<div class="row">
    <div class="col-md-3 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00046'|T}}</label>
            <div class="col-md-8">
                <input type="text" class="form-control col-md-8" ng-model="studentReturns.searchForm.searchItems.hetong">
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00048'|T}}</label>
            <div class="col-md-8">
                <input type="text" class="form-control col-md-8" ng-model="studentReturns.searchForm.searchItems.xingming">
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-12">
        <div class="form-group">
            <div class="col-md-4"></div>
            <div class="col-md-8">
                <button class="btn blue  filter-submit margin-bottom" ng-click="studentReturns.search1()" style="padding: 5px 14px;"><i
                        class="fa fa-search"></i> {{'global_btn_0005'|T}}
                </button>
            </div>
        </div>
    </div>
</div>
<!--搜索-->
<!--开始table-->
<div class="table-scrollable" style="overflow-y: auto">
    <table class="table table-striped table-bordered table-hover">
        <thead>
        <tr role="row" class="heading">
            <!--<th width="1%">-->
                <!--<input type="checkbox" class="group-checkable">-->
            <!--</th>-->
            <th width="1%">
                {{'table_filed_00368'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00046'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00049'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00048'|T}}
            </th>
            <th width="10%">
                 {{'static_words_0101'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00087'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00088'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00089'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00090'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00066'|T}}
            </th>
            <th width="10%">
                <div style="width:120px">{{'table_filed_00369'|T}}</div>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="d in studentReturns.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
            <!--<td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>-->
            <td>{{$index+1}}</td>
            <td >
                {{d.hetong}}
            </td>
            <td>
                {{d.xuehao}}
            </td>

            <td>
                {{d.xingming}}
            </td>

            <td>
                {{d.xiaoqu}}
            </td>

            <td>
                {{d.tuifeikeshi}}
            </td>
            <td>
                {{d.tuifeijine  | currency:" " }}
            </td>
            <td>
                {{d.tuifeishijian}}
            </td>
            <td>
                {{d.tuifeiyuanyin}}
            </td>
            <td>
                {{d.zhuangtai=='1'&&'table_filed_00463'||d.zhuangtai=='2'&&'global_btn_0015'||d.zhuangtai=='3'&&'table_filed_00464'||d.zhuangtai=='4'&&'table_filed_00465'|T}}
            </td>
            <td>
                <div class="btn-group btn-group-xs">
                    <button type="button" ng-if="d.zhuangtai=='1'" class="btn " style="color:red;" ng-click="tongguo(d)">{{'table_filed_00466'|T}}</button>
                    <button type="button" ng-if="d.zhuangtai=='4'" class="btn " style="color:red;" ng-click="tongguo1(d)">{{'table_filed_00467'|T}}</button>
                    <button type="button" ng-if="d.zhuangtai=='1'" class="btn " style="color:green;" ng-click="butongguo(d)">{{'table_filed_00468'|T}}</button>
                    <button type="button" ng-if="d.zhuangtai=='4'" class="btn " style="color:green;" ng-click="butongguo1(d)">{{'table_filed_00469'|T}}</button>
                    <button type="button" class="btn " style="color:green;" ng-click="chakan(d)">{{'global_btn_0002'|T}}</button>
                </div>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<!--结束table-->
<!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
<div class="row">
    <div class="col-md-8 col-sm-12">
        <div class="dataTables_paginate paging_bootstrap_extended">
            <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="studentReturns.prePage()"
                                                class="btn btn-sm default prev"
                                                title="Prev"><i class="fa fa-angle-left"></i></a><input
                    type="number"  ng-model="studentReturns.searchForm.page.currentPage"
                    class="pagination-panel-input form-control input-mini input-inline input-sm"
                    maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="studentReturns.nextPage()"
                                                                                 class="btn btn-sm default next"
                                                                                 title="Next"><i
                    class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{studentReturns.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
            </div>
        </div>
        <div class="dataTables_length" ><label><span
                class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="studentReturns.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="150">150</option>
        </select> {{'table_filed_00390'|T}}</label></div>
        <div class="dataTables_info"  role="status" aria-live="polite">
            <span class="seperator">|</span>{{'table_filed_00389'|T}}{{studentReturns.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
        </div>
    </div>
    <div class="col-md-4 col-sm-12">

    </div>
</div>
<!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
</div>
</div>
</div>
</div>
<!-- End: life time stats -->
</div>
</div>
<!-- END PAGE CONTENT-->



<script type="text/ng-template" id="tuifeixiangqing.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;" >{{'table_filed_00534'|T}}<!--选择教师--></p>
        <p class="p2"><button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
    </div>
    <div class="modal-body">
        <form class="form-horizontal" name="form">

            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrap" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <!--搜索-->
                        <div class="row">
                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">{{'table_filed_00048'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.xingming"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">{{'table_filed_00049'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.xuehao"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">{{'table_filed_00051'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.danyuanshu"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">{{'table_filed_00396'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.yifukuanjine"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">{{'table_filed_00253'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.xiaohaokeshi"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">{{'table_filed_00089'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.tuifeishijian"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">  {{'table_filed_00334'|T}}<!--退费类型--></label>
                                    <div class="col-md-8">
                                        {{returns.item.type=='1'&&'table_filed_00397'||returns.item.type=='2'&&'table_filed_00398'|T}}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6" >
                                <div class="form-group" ng-if="returns.item.type=='1'">
                                    <label class="control-label col-md-4">{{'table_filed_00399'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.chanpin"  name="postType" readonly/>
                                    </div>
                                </div>
                                <div class="form-group" ng-if="returns.item.type=='2'">
                                    <label class="control-label col-md-4">{{'table_filed_00400'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.danjia"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6" >
                                <div class="form-group">
                                    <label class="control-label col-md-4">{{'table_filed_00088'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" ng-model="returns.item.tuifeijine"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12" >
                                <div class="form-group">
                                    <label class="control-label col-md-2">{{'table_filed_00331'|T}}<!--退费原因--></label>
                                    <div class="col-md-10">
                                        <input type="text" class="form-control" ng-model="returns.item.tuifeiyuanyin"  name="postType" readonly/>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </form>
    </div>

    <div style="clear: both;"></div>
</script>









<!--退费弹窗、开始-->
<script type="text/ng-template" id="tuifei.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; padding-right: 30px;" >
        <p class="p1" style="font-size: 16px;" >{{'table_filed_00404'|T}}</p>

        <!--<p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>-->
        <p class="p2" >  <button class="btn btn-xs  red " ng-click="cancel()">{{'table_filed_00405'|T}}</button></p>
    </div>
    <div class="modal-body">
        <form class="form-horizontal" name="form">

            <div class="row">
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">SF{{'table_filed_00046'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control" disabled ng-model="contractview.item.contractNo"  name="contractNo"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00121'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control" disabled ng-model="contractview.item.startDate"  name="startDate"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00122'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control" disabled ng-model="contractview.item.endDate"  name="endDate"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00205'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control" disabled ng-model="contractview.item.productId"  name="productId"/>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00004'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control" disabled ng-model="contractview.item.gender"  name="gender"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00005'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control"disabled ng-model="contractview.item.age"  name="age"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">出生年月</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control"disabled ng-model="contractview.item.birthday"  name="birthday"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00007'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control"disabled ng-model="contractview.item.nationality"  name="nationality"/>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00035'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control" disabled ng-model="contractview.item.referral"  name="referral"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" >
                    <div class="form-group">
                        <label class="control-label col-md-3">{{'table_filed_00036'|T}}</label>
                        <div class="col-md-9">
                            <input type="text" class="form-control" disabled ng-model="contractview.item.contractOwnerId"  name="contractOwnerId"/>
                        </div>
                    </div>
                </div>




            </div>
        </form>
    </div>
    <div class="modal-footer lm_window_footer" style="padding-bottom: 10px;padding-top:5px;background-color: #f5f5f5;"  >
        <!-- <button class="btn btn-xs" ng-disabled="form.$invalid" ng-click="submit()">{{'global_btn_0012'|T}}</button>-->
        <button class="btn btn-xs" ng-click="cancel()">{{'global_btn_0013'|T}}</button>
        <!--<button class="btn btn-xs" ng-click="selectNode(this)">确认</button>-->
    </div>
    <div style="clear: both;"></div>
</script>
<!--退费弹窗、结束-->
