
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row" ng-form="bigform">
<div class="col-md-12">

<div class="portlet light bordered ">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-pencil"></i>{{teacher_course.item.suoshukehu||'table_filed_00391'|T}}
    </div>
    <div class="actions btn-set">
        <button type="button" name="back" class="btn default" ng-click="$root.settings.utils.back()"><i class="fa fa-angle-left"></i> {{'global_btn_0006'|T}}</button>
        <button ng-disabled="bigform.$invalid" class="btn green" ng-click="teacher_course.save(1)"popover-trigger="mouseenter"><i class="fa fa-check"></i> {{'global_btn_0007'|T}}</button>
        <!--<button ng-disabled="bigform.$invalid"  class="btn green" ng-click="teacher_course.save(2)"  popover-trigger="mouseenter"><i class="fa fa-check-circle"></i>保存并{{'global_btn_0001'|T}}</button>-->

    </div>
</div>
<div class="portlet-body">
    <div class="row">
        <div class="col-md-12">
                <div class="form">
                    <!-- BEGIN FORM-->
                    <form action="#" name="myform" class="form-horizontal">
                        <div class="form-body">
                           
                            <!--/row-->
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00300'|T}}<!--课程ID--></label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.course_id"  name="course_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00134'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.periodLowerLimit"  name="periodLowerLimit"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00135'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.periodUpperLimit"  name="periodUpperLimit"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00136'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.intervalType"  name="intervalType"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00137'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.grade_id"  name="grade_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00136'|T}}{{'table_filed_00332'|T}}<!--（1：一对一）--></label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.teachingType"  name="teachingType"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00139'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.startDate"  name="startDate"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00140'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.endDate"  name="endDate"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00141'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_course.item.unitPrice"  name="unitPrice"/>
									</div>
								</div>
							</div>
							

                        </div>

                     </form>
                    <!-- END FORM-->

                </div>
        </div>
    </div>
</div>
</div>
</form>
</div>
</div>
