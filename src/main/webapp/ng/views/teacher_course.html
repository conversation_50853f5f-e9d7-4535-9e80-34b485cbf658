
<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Begin: life time stats -->
        <div class="portlet">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-list"></i><!--教师可教授科目-->{{'table_filed_00313'|T}}
                </div>
				 <div class="actions">
                    <div class="btn-group btn-group-sm btn-group-solid">
                        <button type="button" class="btn blue" ng-click="add()" >{{'global_btn_0001'|T}}</button>
                        <button type="button" class="btn red"  ng-click="teacher_course.deletestatus()">{{'global_btn_0004'|T}}</button>
                    </div>

                </div>
            </div>
            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                    <!--开始table表头分{{'table_filed_00386'|T}}以及工具栏-->
                        <div class="row">
                            <div class="col-md-8 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_extended">
                                    <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="teacher_course.prePage()"
                                                                           class="btn btn-sm default prev"
                                                                           title="Prev"><i class="fa fa-angle-left"></i></a><input
                                            type="number"  ng-model="teacher_course.searchForm.page.currentPage"
                                            class="pagination-panel-input form-control input-mini input-inline input-sm"
                                            maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="teacher_course.nextPage()"
                                                                                                       class="btn btn-sm default next"
                                                                                                       title="Next"><i
                                            class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{teacher_course.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                    </div>
                                </div>
                                <div class="dataTables_length" ><label><span
                                        class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="teacher_course.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="150">150</option>
                                </select> {{'table_filed_00390'|T}}</label></div>
                                <div class="dataTables_info"  role="status" aria-live="polite">
                                    <span class="seperator">|</span>{{'table_filed_00389'|T}}{{teacher_course.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">

                            </div>
                        </div>
                    <!--结束table表头分{{'table_filed_00386'|T}}以及工具栏-->
                    <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <th width="1%">
                                        <input type="checkbox" class="group-checkable">
                                    </th>
                                    <th width="1%">
                                        {{'table_filed_00368'|T}}
                                    </th>
									 <th width="10%">
                                         {{'table_filed_00300'|T}}
										<!--课程ID-->
									</th>
									 <th width="10%">
										{{'table_filed_00134'|T}}
									</th>
									 <th width="10%">
										{{'table_filed_00135'|T}}
									</th>
									 <th width="10%">
										{{'table_filed_00136'|T}}（1：月，2：年）
									</th>
									 <th width="10%">
										{{'table_filed_00137'|T}}
									</th>
									 <th width="10%">
										{{'table_filed_00136'|T}}{{'table_filed_00332'|T}}<!--（1：一对一）-->
									</th>
									 <th width="10%">
										{{'table_filed_00139'|T}}
									</th>
									 <th width="10%">
										{{'table_filed_00140'|T}}
									</th>
									 <th width="10%">
										{{'table_filed_00141'|T}}
									</th>
                                   
                                    <th width="10%">
                                       <div style="width:120px">{{'table_filed_00369'|T}}</div>
                                    </th>
                                </tr>
                                <tr role="row" class="filter">
                                    <td></td>
                                    <td></td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.course_id_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.periodLowerLimit_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.periodUpperLimit_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.intervalType_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.grade_id_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.teachingType_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.startDate_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.endDate_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_course.searchForm.searchItems.unitPrice_lk">
                                    </td>
                                    <td>
                                        <button class="btn btn-xs  filter-submit margin-bottom" ng-click="teacher_course.search1()"><i
                                                class="fa fa-search"></i> {{'global_btn_0005'|T}}
                                        </button>
                                        <button class="btn btn-xs  filter-cancel" ng-click="teacher_course.reset()"><i class="fa fa-times"></i> {{'global_btn_0017'|T}}
                                        </button>
                                    </td>

                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="d in teacher_course.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                                    <td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
                                    <td>{{$index+1}}</td>
									 <td>
									 {{d.course_id}}
                                    </td>
									 <td>
									 {{d.periodLowerLimit}}
                                    </td>
									 <td>
									 {{d.periodUpperLimit}}
                                    </td>
									 <td>
									 {{d.intervalType}}
                                    </td>
									 <td>
									 {{d.grade_id}}
                                    </td>
									 <td>
									 {{d.teachingType}}
                                    </td>
									 <td>
									 {{d.startDate}}
                                    </td>
									 <td>
									 {{d.endDate}}
                                    </td>
									 <td>
									 {{d.unitPrice}}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-xs">
                                            <button type="button" class="btn " ng-click="edit(d)">{{'global_btn_0003'|T}}</button>
                                            <button type="button" class="btn " ng-click="teacher_course.deleteData(d)">{{'global_btn_0004'|T}}</button>
                                        </div>
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                    <!--结束table-->
                    <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    <div class="row">
                        <div class="col-md-8 col-sm-12">
                            <div class="dataTables_paginate paging_bootstrap_extended">
                                <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="teacher_course.prePage()"
                                                                    class="btn btn-sm default prev"
                                                                    title="Prev"><i class="fa fa-angle-left"></i></a><input
                                        type="number"  ng-model="teacher_course.searchForm.page.currentPage"
                                        class="pagination-panel-input form-control input-mini input-inline input-sm"
                                        maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="teacher_course.nextPage()"
                                                                                                     class="btn btn-sm default next"
                                                                                                     title="Next"><i
                                        class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{teacher_course.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                </div>
                            </div>
                            <div class="dataTables_length" ><label><span
                                    class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="teacher_course.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="150">150</option>
                            </select> {{'table_filed_00390'|T}}</label></div>
                            <div class="dataTables_info"  role="status" aria-live="polite">
                                <span cla{{'table_filed_00389'|T}}="seperator">|</span>{{'table_filed_00387'|T}}计{{teacher_course.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12">

                        </div>
                    </div>
                    <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>
        </div>
        <!-- End: life time stats -->
    </div>
</div>
<!-- END PAGE CONTENT-->
