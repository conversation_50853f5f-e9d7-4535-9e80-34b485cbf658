
<!-- BEGIN PAGE CONTENT-->
<div class="row">
<div class="col-md-12">
<!-- Begin: life time stats -->
<div class="portlet">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-list"></i>{{'menu_0905'|T}}
    </div>
    <div class="actions">
        <div class="btn-group btn-group-sm btn-group-solid">

        </div>
    </div>
</div>
<div class="portlet-body">
    <div class="table-container">
        <div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
            <!--搜索-->
            <div class="row">
                <div class="col-md-3 col-sm-12 ">
                    <div class="form-group">
                        <label class="control-label col-md-5" style=" margin-top: 5px;text-align: right;" >  {{'table_filed_00144'|T}}</label>
                        <div class="col-md-7">
                            <input type="text" class="form-control col-md-8" ng-model="xubao.searchForm.searchItems.xuesheng">
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-12 ">
                    <div class="form-group">
                        <label class="control-label col-md-5" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00121'|T}}</label>
                        <div class="col-md-7">
                            <input type="text" class="form-control col-md-8 date-picker" data-date-format="yyyy-mm-dd" ng-model="xubao.searchForm.searchItems.startTime">
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-12 ">
                    <div class="form-group">
                        <label class="control-label col-md-5" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00122'|T}}</label>
                        <div class="col-md-7">
                            <input type="text" class="form-control col-md-8 date-picker" data-date-format="yyyy-mm-dd" ng-model="xubao.searchForm.searchItems.endTime">
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <div class="col-md-5"></div>
                        <div class="col-md-7">
                            <button class="btn blue  filter-submit margin-bottom" ng-click="xubao.search1()" style="padding: 5px 14px;"><i
                                    class="fa fa-search"></i> {{'global_btn_0005'|T}}
                            </button>
                            <button class="btn blue  filter-submit margin-bottom" ng-click="xubao.export()" style="padding: 5px 14px;">
                                {{'global_btn_0011'|T}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!--搜索-->
            <!--开始table-->
            <div class="table-scrollable" style="overflow-y: auto">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                    <tr role="row" class="heading">
                        <th width="1%">
                            {{'table_filed_00368'|T}}
                        </th>
                        <th width="14%" >
                            {{'tab_0701'|T}}</span>
                        </th>
                        <th width="14%" >
                            {{'tab_0702'|T}}</span>
                        </th>
                        <!--<th width="14%"  >-->
                            <!--{{'table_filed_00109'|T}}</span>-->
                        <!--</th>-->
                        <th width="14%" >
                            {{'table_filed_00294'|T}}</span>
                        </th>
                        <th width="14%" ng-click="paixu('xuesheng',flag)">
                            {{'table_filed_00048'|T}}{{flag=='xuesheng1'&&'↑'||flag=='xuesheng2'&&'↓'||''}}</span>
                        </th>
                        <th width="14%">
                            {{'table_filed_00257'|T}}
                        </th>
                        <th width="14%">
                            {{'table_filed_00258'|T}}
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="d in xubao.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                        <td>{{$index+1}}</td>
                        <td>
                            {{d.chengshi}}
                        </td>
                        <td>
                            {{d.xiaoqu}}
                        </td>
                        <!--<td>-->
                            <!--{{d.bianma}}-->
                        <!--</td>-->
                        <td>
                            {{d.zhujiao}}
                        </td>
                        <td>
                            {{d.xuesheng}}
                        </td>
                        <td>
                            {{d.xiaoshishu}}
                        </td>
                        <td>
                            {{d.jiage}}
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--结束table-->
            <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
            <div class="row">
                <div class="col-md-8 col-sm-12">
                    <div class="dataTables_paginate paging_bootstrap_extended">
                        <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="xubao.prePage()"
                                                            class="btn btn-sm default prev"
                                                            title="Prev"><i class="fa fa-angle-left"></i></a><input
                                type="number"  ng-model="xubao.searchForm.page.currentPage"
                                class="pagination-panel-input form-control input-mini input-inline input-sm"
                                maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="xubao.nextPage()"
                                                                                             class="btn btn-sm default next"
                                                                                             title="Next"><i
                                class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{xubao.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                        </div>
                    </div>
                    <div class="dataTables_length" ><label><span
                            class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="xubao.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="150">150</option>
                    </select> {{'table_filed_00390'|T}}</label></div>
                    <div class="dataTables_info"  role="status" aria-live="polite">
                        <span class="seperator">|</span>{{'table_filed_00389'|T}}{{xubao.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">

                </div>
            </div>
            <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
        </div>
    </div>
</div>
</div>
<!-- End: life time stats -->
</div>
</div>
<!-- END PAGE CONTENT-->
