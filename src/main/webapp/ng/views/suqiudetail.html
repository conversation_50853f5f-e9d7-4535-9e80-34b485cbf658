
<!-- <PERSON>ND PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row" ng-form="bigform">
<div class="col-md-12">

<div class="portlet light bordered ">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-pencil"></i>{{'table_filed_00660'|T}}
    </div>

    <div class="actions btn-set">
        <button type="button" name="back" class="btn default" ng-click="$root.settings.utils.back()"><i class="fa fa-angle-left"></i> {{'global_btn_0006'|T}}</button>
        
    </div>
    <span style="float: right;font-size: 16px;color:#ffc620;padding-top:7px;margin-right: 15px;">{{'static_words_0111'|T}}</span>
</div>

<div class="portlet-body">
    <div style="color: #777777;font-size: 16px;padding:0 7% 1%;" >
        <span style="background-color:#f1ede9;padding:5px;">
           {{'table_filed_00660'|T}}
        </span>
    </div>
    <div class="row">
        <div class="col-md-10"style="background-color: #ffffff; border: 1px solid #cecece;margin:0% 8% 2%; padding:1% 0%; ">
            <div class="form">
                <!-- BEGIN FORM-->
                <form action="#" name="myform" class="form-horizontal">
                    <div class="form-body">

                        <!--/row-->

                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00144'|T}}<span ng-class="{required:form.student_id.$invalid}">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" class="form-control" ng-model="suqiu.item.student_chineseName"  name="student_id" disabled />
                                            
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00657'|T}}/{{'table_filed_00658'|T}}<span ng-class="{required:form.type.$invalid}">*</span></label>
                                        <div class="col-md-9">
                                            <select class="form-control" ng-model="suqiu.item.type" name="type" required disabled>
                                                <option value="1">{{'table_filed_00657'|T}}</option>
                                                <option value="2">{{'table_filed_00658'|T}}</option>
                                            </select>
                                            <div class="col-md-12">
                                                <span style="color: #ff0000;" class="ng-binding">常规：连续两个月和同一个老师固定时间上课；在下一个月排课中优先保留老师时段到20号</span>
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                                
                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00661'|T}}<span ng-class="{required:form.paiketype.$invalid}">*</span></label>
                                        <div class="col-md-9">
                                            <select class="form-control" ng-model="suqiu.item.paiketype" name="paiketype" required>
                                                <option value="1">{{'table_filed_00662'|T}}</option>
                                                <option value="2">{{'table_filed_00663'|T}}</option>
                                                <option value="3">{{'table_filed_00972'|T}}</option>
                                            </select>
                                            <div class="col-md-14">
                                                <span style="color: #ff0000;" class="ng-binding">日常排课：以当月为维度收集的学生诉求，不受限于月度各递交和确认节点进行的排课</span><br/>
                                                <span style="color: #ff0000;" class="ng-binding">月度排课：以下个月为维度收集的学生诉求，并按照每月15-31号的各节点进行的排课</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00118'|T}}</label>
                                        <div class="col-md-9">
                                            <input type="text" class="form-control" data-date-format="yyyy-mm-dd HH:MM" ng-model="suqiu.item.createTime" name="createTime" readonly/>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00496'|T}}</label>
                                        <div class="col-md-9">
                                            <input type="text" class="form-control form_datetime" data-date-format="yyyy-mm-dd HH:MM" ng-model="suqiu.item.lastModifiedTime" name="lastModifiedTime" readonly/>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00652'|T}}</label>
                                        <div class="col-md-9">
                                            <textarea rows="5" class="form-control" ng-model="suqiu.item.month_class_datetime"  name="month_class_datetime" disabled></textarea>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00653'|T}}</label>
                                        <div class="col-md-9">
                                            <textarea rows="5" class="form-control" ng-model="suqiu.item.freq_ratio"  name="freq_ratio" disabled></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00654'|T}}</label>
                                        <div class="col-md-9">
                                            <textarea rows="5" class="form-control" ng-model="suqiu.item.grade_id"  name="grade_id" disabled></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00655'|T}}</label>
                                        <div class="col-md-9">
                                            <textarea rows="5" class="form-control" ng-model="suqiu.item.coursetutor"  name="coursetutor" disabled></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6" >
                                    <div class="form-group">
                                        <label class="control-label col-md-3">{{'table_filed_00656'|T}}</label>
                                        <div class="col-md-9">
                                            <textarea rows="5" class="form-control" ng-model="suqiu.item.description"  name="description" disabled></textarea>
                                            <div class="col-md-12">
                                                <span style="color: #ff0000;" class="ng-binding">适用于家长的各类特殊需求、学生的自身特点、对授课内容要求等信息的补充说明</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                    </div>

                    <!--附件上传 start-->
					<div class=" col-md-12">
						<div class="btn-set btn-group-sm text-right">
							<!-- <button ng-disabled="suqiu.item.id==undefined||suqiu.item.id==''" type="button" class="btn blue" ng-click="suqiu.uploadFile('StudyPlan', suqiu.item.id, suqiu.uploadCallBack)"><i class="fa fa-upload"></i>学生信息{{'global_btn_0008'|T}}</button> -->
							<!-- <button ng-disabled="suqiu.item.id==undefined||suqiu.item.id==''" type="button" class="btn blue" ng-click="suqiu.uploadFile('ProgressReport', suqiu.item.id, suqiu.uploadCallBack)"><i class="fa fa-upload"></i>Progress Report{{'global_btn_0008'|T}}</button>
							<button ng-disabled="suqiu.item.id==undefined||suqiu.item.id==''" type="button" class="btn blue" ng-click="suqiu.uploadFile('StageTest', suqiu.item.id, suqiu.uploadCallBack)"><i class="fa fa-upload"></i>Stage Test{{'global_btn_0008'|T}}</button> -->
							<!--<button type="button" class="btn red" ng-click="suqiu.delete()">删除</button>-->
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<table class="table table-striped table-bordered table-hover" style="text-align: center;margin-bottom: 0px;">
									<thead>
									<tr role="row" class="heading">
										<th width="1%">
											<input type="checkbox" class="group-checkable">
										</th>
										<th width="1%">
											{{'table_filed_00368'|T}}
										</th>
										<th width="20%">
                                            {{'table_filed_00365'|T}}
										</th>
										<th width="5%">
                                            {{'table_filed_00421'|T}}
										</th>
										<th width="5%">
                                            {{'table_filed_00275'|T}}
										</th>
										<th width="5%">
											{{'table_filed_00369'|T}}
										</th>
									</tr>

									</thead>
									<tbody>
									<tr  ng-class-odd="'odd'" ng-class-even="'even'" ng-repeat="file in suqiu.files">
										<td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
										<td>{{$index+1}}</td>
										<td>{{file.originalName}}</td>
										<td>{{file.size/1024|number:2}}KB</td>
										<td>{{file.createTime}}</td>
										<td>
											<div class="btn-group btn-group-xs">
												<button type="button" class="btn " ng-click="suqiu.downloadFile(file)" >{{'global_btn_0009'|T}}</button>
												<!-- <button type="button" class="btn  red" ng-click="suqiu.deleteFile(file)" >{{'global_btn_0004'|T}}</button> -->
											</div>

										</td>
									</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
					<!--附件上传 end-->

                </form>
                <!-- END FORM-->

            </div>
        </div>
    </div>
</div>




<div class="portlet-body">
    <div style="color: #777777;font-size: 16px;padding:0 7% 1%;" >
        <span style="background-color:#f1ede9;padding:5px;">
           {{'table_filed_00659'|T}}
        </span>
    </div>
    <div class="row">
        <div class="col-md-10"style="background-color: #ffffff; border: 1px solid #cecece;margin:0% 8% 2%; padding:1% 0%; ">
            <div class="form">
                <!-- BEGIN FORM-->
                <form action="#" name="myform" class="form-horizontal">
                    <div class="form-body">     

                                <div class="col-md-12" >
                                    <!--{{'tab_0701'|T}}添加-->
                                    <div class="form-group">

                                        <!-- BEGIN FORM-->
                                        <form action="#" name="myform" class="form-horizontal">


                                            <div class="col-md-12">
                                                <textarea rows="5" class="form-control" ng-model="suqiucomm.item.name"  name="description"></textarea>
                                            </div>


                                            <div class="col-md-12" style="padding-top: 20px;">
                                                <button type="button" name="back" class="btn btn-xs" ng-click="suqiucomm.reset()" > {{'global_btn_0017'|T}}</button>
                                                <button type="button" name="back"  ng-disabled="myform.$invalid" class="btn btn-xs" ng-click="suqiucomm.save()"> {{'global_btn_0012'|T}}</button>
                                            </div>


                                        </form>
                                    </div>
                                </div>

                                <div class="col-md-12" >
                                    <div class="portlet-body" >

                                            <!--{{'tab_0701'|T}}列表-->
                                            <div class="col-md-12 col-sm-8">
                                                <div class="table-scrollable" style="overflow-y: auto">
                                                    <table class="table table-striped table-bordered table-hover">
                                                        <thead>
                                                        <tr role="row" class="heading">
                                                           <!-- <th width="1%">
                                                                <input type="checkbox" class="group-checkable">
                                                            </th>-->
                                                            <th width="1%">
                                                                {{'table_filed_00368'|T}}
                                                            </th>
                                                            <th width="10%">
                                                                {{'table_filed_00157'|T}}
                                                            </th>
                                                            <th width="10%">
                                                                {{'table_filed_00158'|T}}
                                                            </th>
                                                            <th width="70%">
                                                                {{'table_filed_00659'|T}}
                                                            </th>
                                                            <th width="9%">
                                                                <div style="width:120px">{{'table_filed_00369'|T}}</div>
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr ng-if="suqiucomm.dataList.length<=0">
                                                            <td colspan='4' style="text-align: center;">--{{'table_filed_00427'|T}}--</td>
                                                        </tr>
                                                        <tr ng-if="suqiucomm.dataList.length>0" ng-repeat="d in suqiucomm.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                                                            <!--<td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>-->
                                                            <td>{{$index+1}}</td>
                                                            <td>
                                                                {{d.creator_chineseName}}<span ng-if="d.creator_chineseName!=''">-</span>{{d.creator_englishName}}
                                                            </td>
                                                            <td>
                                                                {{d.createTime}}
                                                            </td>
                                                            <td>
                                                                {{d.name}}
                                                            </td>
                                                            <td>
                                                                <div class="btn-group btn-group-xs">
                                                                    <!-- <button type="button" class="btn " ng-click="suqiucomm.edit(d)">{{'global_btn_0003'|T}}</button> -->
                                                                    <button ng-if="d.show" type="button" class="btn" style="color:red;" ng-click="suqiucomm.deleteData(d,'605')">{{'global_btn_0004'|T}}</button>
                                                                </div>
                                                            </td>
                                                        </tr>

                                                        </tbody>
                                                    </table>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-12 col-sm-12">
                                                        <div class="dataTables_paginate " style="float: left;">
                                                            <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="suqiucomm.prePage()"
                                                                                                class="btn btn-sm default prev"
                                                                                                title="Prev"><i class="fa fa-angle-left"></i></a><input
                                                                    type="number"  ng-model="suqiucomm.searchForm.page.currentPage"
                                                                    class="pagination-panel-input form-control input-mini input-inline input-sm"
                                                                    maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="suqiucomm.nextPage()"
                                                                                                                                 class="btn btn-sm default next"
                                                                                                                                 title="Next"><i
                                                                    class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{suqiucomm.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                                            </div>
                                                        </div>
                                                        <div class=""  style="float: left;"><label><span
                                                                class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="suqiucomm.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                                            <option value="10">10</option>
                                                            <option value="20">20</option>
                                                            <option value="50">50</option>
                                                            <option value="100">100</option>
                                                            <option value="150">150</option>
                                                        </select> {{'table_filed_00390'|T}}</label></div>
                                                        <div class=""  style="float: left;margin-top: 5px;" role="status" aria-live="polite">
                                                            <span class="seperator">|</span>{{'table_filed_00389'|T}}{{suqiucomm.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                                        </div>
                                                    </div>
                                                </div>


                                            </div>



                                            <div class="todo-tasklist-devider">
                                            </div>
                                    </div>
                                </div>

                    </div>

                </form>
                <!-- END FORM-->

            </div>
        </div>
    </div>
</div>




</div>
</form>
</div>
</div>
