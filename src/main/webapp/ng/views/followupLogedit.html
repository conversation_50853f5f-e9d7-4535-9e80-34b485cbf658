
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row" ng-form="bigform">
<div class="col-md-12">

<div class="portlet light bordered ">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-pencil"></i>{{'menu_0202'|T}}
    </div>

    <div class="actions btn-set">
        <button type="button" name="back" class="btn default" ng-click="$root.settings.utils.back()"><i class="fa fa-angle-left"></i> {{'global_btn_0006'|T}}</button>
        <button ng-disabled="bigform.$invalid" class="btn green" ng-click="followupLog.save(1)"popover-trigger="mouseenter"><i class="fa fa-check"></i> {{'global_btn_0007'|T}}</button>
        <!--<button ng-disabled="bigform.$invalid"  class="btn green" ng-click="followupLog.save(2)"  popover-trigger="mouseenter"><i class="fa fa-check-circle"></i>保存并{{'global_btn_0001'|T}}</button>-->
    </div>
	<span style="float: right;font-size: 16px;color:#ffc620;padding-top:7px;margin-right: 15px;">{{'static_words_0111'|T}}</span>
</div>
<div class="portlet-body">
    <div class="row">
        <div class="col-md-12">
                <div class="form">
                    <!-- BEGIN FORM-->
                    <form name="form" class="form-horizontal">
                        <div class="form-body">
                           
                            <!--/row-->
							<div class="row">
								<div class="col-md-6" >
									<div class="form-group">
										<label class="control-label col-md-4">{{'table_filed_00144'|T}}<span ng-class="{required:form.student_id.$invalid}">*</span></label>
										<div class="col-md-8">
											<div class="input-group">
												<input type="text" class="form-control" ng-model="followupLog.item.student_name"  name="student_id" required/>
											<span class="input-group-btn">
												<button class="btn btn-sm" type="button" ng-click="followupLog.selectStudent()">{{'global_btn_0014'|T}}</button>
											</span>
											</div>
										</div>
									</div>
								</div>
								<div class="col-md-6" >
									<div class="form-group">
										<label class="control-label col-md-4">{{'table_filed_00097'|T}}<span ng-class="{required:form.followupWay.$invalid}">*</span></label>
										<div class="col-md-8">
											<select class="form-control" ng-model="followupLog.item.followupWay" name="followupWay" required>
												<option value="1">{{'table_filed_00419'|T}}</option>
												<option value="2">{{'table_filed_00420'|T}}</option>
												<option value="3">QQ</option>
												<option value="4">Wechat</option>
												<option value="5">面谈</option>
											</select>
										</div>
									</div>
								</div>

								<div class="col-md-6" >
									<div class="form-group">
										<label class="control-label col-md-4">{{'table_filed_00096'|T}}</label>
										<div class="col-md-8" ng-init="$root.settings.utils.initpicker()">
											<input type="text" class="form-control form_datetime" data-date-format="yyyy-mm-dd HH:MM" ng-model="followupLog.item.followupTime" name="followupTime"/>
										</div>
									</div>
								</div>
								<div class="col-md-6" >
									<div class="form-group">
										<label class="control-label col-md-4">{{'table_filed_00099'|T}}<span ng-class="{required:form.nextFollowupTime.$invalid}">*</span></label>
										<div class="col-md-8 " ng-init="$root.settings.utils.initpicker()">
											<input type="text" class="form-control date-picker" data-date-format="yyyy-mm-dd" ng-model="followupLog.item.nextFollowupTime"  name="nextFollowupTime" required/>
										</div>
									</div>
								</div>
								<div class="col-md-6" >
									<div class="form-group">
										<label class="control-label col-md-4">{{'table_filed_00118'|T}}</label>
										<div class="col-md-8">
											<input type="text" class="form-control" data-date-format="yyyy-mm-dd HH:MM" ng-model="followupLog.item.createTime" name="createTime" readonly/>
										</div>
									</div>
								</div>

								<div class="col-md-12" >
									<div class="form-group">
										<label class="control-label col-md-2">{{'table_filed_00098'|T}}</label>
										<div class="col-md-10">
											<textarea rows="5" class="form-control" ng-model="followupLog.item.followupContent"  name="followupContent"></textarea>
										</div>
									</div>
								</div>

							</div>
							<!--附件上传 start-->
							<div class="row">
								<div class="btn-set btn-group-sm text-right">
									<button ng-disabled="followupLog.item.id==undefined||followupLog.item.id==''" type="button" class="btn blue" ng-click="followupLog.uploadFile('StudyPlan', followupLog.item.id, followupLog.uploadCallBack)"><i class="fa fa-upload"></i>Study Plan{{'global_btn_0008'|T}}</button>
									<button ng-disabled="followupLog.item.id==undefined||followupLog.item.id==''" type="button" class="btn blue" ng-click="followupLog.uploadFile('ProgressReport', followupLog.item.id, followupLog.uploadCallBack)"><i class="fa fa-upload"></i>Progress Report{{'global_btn_0008'|T}}</button>
									<button ng-disabled="followupLog.item.id==undefined||followupLog.item.id==''" type="button" class="btn blue" ng-click="followupLog.uploadFile('StageTest', followupLog.item.id, followupLog.uploadCallBack)"><i class="fa fa-upload"></i>Stage Test{{'global_btn_0008'|T}}</button>
									<!--<button type="button" class="btn red" ng-click="followupLog.delete()">删除</button>-->
								</div>
								<div class="col-md-12">
									<div class="form-group">
										<table class="table table-striped table-bordered table-hover" style="text-align: center;margin-bottom: 0px;">
											<thead>
											<tr role="row" class="heading">
												<th width="1%">
													<input type="checkbox" class="group-checkable">
												</th>
												<th width="1%">
													{{'table_filed_00368'|T}}
												</th>
												<th width="20%">
                                                    {{'table_filed_00365'|T}}
												</th>
												<th width="5%">
                                                    {{'table_filed_00421'|T}}
												</th>
												<th width="5%">
                                                    {{'table_filed_00275'|T}}
												</th>
												<th width="5%">
													{{'table_filed_00369'|T}}
												</th>
											</tr>

											</thead>
											<tbody>
											<tr  ng-class-odd="'odd'" ng-class-even="'even'" ng-repeat="file in followupLog.files">
												<td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
												<td>{{$index+1}}</td>
												<td>{{file.originalName}}</td>
												<td>{{file.size/1024|number:2}}KB</td>
												<td>{{file.createTime}}</td>
												<td>
													<div class="btn-group btn-group-xs">
														<button type="button" class="btn " ng-click="followupLog.downloadFile(file)" >{{'global_btn_0009'|T}}</button>
														<button type="button" class="btn  red" ng-click="followupLog.deleteFile(file)" >{{'global_btn_0004'|T}}</button>
													</div>

												</td>
											</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
							<!--附件上传 end-->
                        </div>

                     </form>
                    <!-- END FORM-->

                </div>
        </div>
    </div>
</div>
</div>
</form>
</div>
</div>
