    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;" >{{'table_filed_00287'|T}}<!--选择教师--></p>
        <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
    </div>
    <div class="modal-body">
        <form class="form-horizontal" name="form">

            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrap" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <!--搜索-->
                        <div class="row">
                            <div class="col-md-5 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00002'|T}}：</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control col-md-8" ng-model="teacherview.searchForm.searchItems.chineseName_lk">
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-5 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00003'|T}}：</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control col-md-8" ng-model="teacherview.searchForm.searchItems.englishName_lk">
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-1 col-sm-12">
                                <button class="btn blue  filter-submit margin-bottom" ng-click="teacherview.search1()" style="padding: 5px 14px;"><i
                                        class="fa fa-search"></i> {{'global_btn_0005'|T}}
                                </button>
                            </div>
                            <div class="col-md-7 col-sm-12">

                            </div>
                        </div>
                        <!--搜索-->
                        <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <th width="1%">
                                        {{'table_filed_00368'|T}}
                                    </th>
                                    <th width="10%">
                                        ERP-{{'table_filed_00108'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00111'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00110'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00112'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00115'|T}}
                                    </th>
                                </tr>

                                </thead>
                                <tbody>
                                <tr ng-repeat="d in teacherview.dataList" ng-class-odd="'odd'" ng-class-even="'even'" ng-click="selectRow(d)">
                                    <td>{{$index+1}}</td>
                                    <td>
                                        {{d.code}}
                                    </td>
                                    <td>
                                        {{d.englishName}}
                                    </td>
                                    <td>
                                        {{d.chineseName}}
                                    </td>
                                    <td>
                                        {{d.abbr}}
                                    </td>
                                    <td>
                                        {{d.type=="1"&&"全职"||d.type=="2"&&"兼职"||d.type=="3"&&"半全职"||""}}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--结束table-->
                        <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                        <div class="row">
                            <div class="col-md-8 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_extended">
                                    <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="teacherview.prePage()"
                                                                        class="btn btn-sm default prev"
                                                                        title="Prev"><i class="fa fa-angle-left"></i></a><input
                                            type="number"  ng-model="teacherview.searchForm.page.currentPage"
                                            class="pagination-panel-input form-control input-mini input-inline input-sm"
                                            maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="teacherview.nextPage()"
                                                                                                         class="btn btn-sm default next"
                                                                                                         title="Next"><i
                                            class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{teacherview.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                    </div>
                                </div>
                                <div class="dataTables_length" ><label><span
                                        class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="teacherview.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="150">150</option>
                                </select> {{'table_filed_00390'|T}}</label></div>
                                <div class="dataTables_info"  role="status" aria-live="polite">
                                    <span class="seperator">|</span>{{'table_filed_00389'|T}}{{teacherview.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">

                            </div>
                        </div>
                        <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>

        </form>
    </div>

    <div style="clear: both;"></div>