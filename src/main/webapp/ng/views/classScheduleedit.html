
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row" ng-form="bigform">
<div class="col-md-12">

<div class="portlet light bordered ">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-pencil"></i>{{classSchedule.item.suoshukehu||'table_filed_00391'|T}}
    </div>
    <div class="actions btn-set">
        <button type="button" name="back" class="btn default" ng-click="$root.settings.utils.back()"><i class="fa fa-angle-left"></i> {{'global_btn_0006'|T}}</button>
        <button ng-disabled="bigform.$invalid" class="btn green" ng-click="classSchedule.save(1)"popover-trigger="mouseenter"><i class="fa fa-check"></i> {{'global_btn_0007'|T}}</button>
        <!--<button ng-disabled="bigform.$invalid"  class="btn green" ng-click="classSchedule.save(2)"  popover-trigger="mouseenter"><i class="fa fa-check-circle"></i>保存并{{'global_btn_0001'|T}}</button>-->

    </div>
</div>
<div class="portlet-body">
    <div class="row">
        <div class="col-md-12">
                <div class="form">
                    <!-- BEGIN FORM-->
                    <form action="#" name="myform" class="form-horizontal">
                        <div class="form-body">
                           
                            <!--/row-->
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">course_id</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.course_id"  name="course_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">scheduledDate</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.scheduledDate"  name="scheduledDate"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">startTime</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.startTime"  name="startTime"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">endTime</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.endTime"  name="endTime"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">teacher_id</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.teacher_id"  name="teacher_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">teachingWay</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.teachingWay"  name="teachingWay"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">classroom_id</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.classroom_id"  name="classroom_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">status</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.status"  name="status"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">description</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.description"  name="description"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">leaveState</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.leaveState"  name="leaveState"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">isAuto</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.isAuto"  name="isAuto"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">creator_id</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.creator_id"  name="creator_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">createTime</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.createTime"  name="createTime"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">lastModifier_id</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.lastModifier_id"  name="lastModifier_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">lastModifiedTime</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.lastModifiedTime"  name="lastModifiedTime"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">delStatus</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="classSchedule.item.delStatus"  name="delStatus"/>
									</div>
								</div>
							</div>
							

                        </div>

                     </form>
                    <!-- END FORM-->

                </div>
        </div>
    </div>
</div>
</div>
</form>
</div>
</div>
