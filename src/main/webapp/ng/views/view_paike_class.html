
<!-- BEGIN PAGE CONTENT-->
<div class="row">
<div class="col-md-12">
<!-- Begin: life time stats -->
<div class="portlet">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-list"></i>{{'static_words_0501'|T}}
    </div>
    <div class="actions">
        <div class="btn-group btn-group-sm btn-group-solid">
            <!--<button type="button" class="btn blue" ng-click="view_paike_class.autoArrangement()">{{'table_filed_00167'|T}}</button>-->
            <button type="button" class="btn blue"  ng-click="view_paike_class.batchConfirm()">{{'table_filed_00498'|T}}</button>
            <button type="button" class="btn red" ng-click="view_paike_class.batchSoftDelete()">{{'table_filed_00272'|T}}</button>
        </div>

    </div>
</div>
<div class="portlet-body">
<div class="table-container">
<div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">

<!--搜索-->
<form>
    <div class="row">
        <div class="col-md-4">
            <label class="control-label col-md-3 text-right" style=" margin-top: 5px;">{{'table_filed_00102'|T}}</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" class="form-control" ng-model="student1.item.name" disabled/>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-sm" ng-click="xuanzeStudent()">{{'global_btn_0014'|T}}</button>
                                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <label class="control-label col-md-3 text-right" style=" margin-top: 5px;">{{'table_filed_00244'|T}}</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" class="form-control" ng-model="teacher.item.name" disabled/>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-sm" ng-click="xuanzeTeacher()" >{{'global_btn_0014'|T}}</button>
                                    </span>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="form-group">
                <label class="control-label col-md-2 text-right" style=" margin-top: 5px;"> {{'table_filed_00081'|T}}</label>
                <div class="col-md-10" ng-init="$root.settings.utils.initpicker()">
                    <div class="col-md-5">
                        <input type="text" class="form-control date-picker" data-date-format="yyyy-mm-dd"
                               placeholder="yyyy-mm-dd" ng-model="view_paike_class.searchForm.searchItems.scheduledDate_ge">
                    </div>
                    <div class="col-md-5">
                        <input type="text" class="form-control date-picker" data-date-format="yyyy-mm-dd"
                               placeholder="yyyy-mm-dd" ng-model="view_paike_class.searchForm.searchItems.scheduledDate_le">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12"><div class="form-group"></div></div>
        <div class="col-md-4">
            <div class="form-group">
                <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;">{{'table_filed_00066'|T}}</label>
                <div class="col-md-9">
                    <select class="form-control" ng-model="view_paike_class.searchForm.searchItems.status_eq">
                        <option value="">--{{'table_filed_00264'|T}}--</option>
                        <option value="0">{{'table_filed_00453'|T}}</option>
                        <option value="1">{{'table_filed_00454'|T}}</option>
                        <option value="2">{{'table_filed_00499'|T}}</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;"> {{'table_filed_00151'|T}}</label>
                <div class="col-md-9">
                    <select class="form-control" ng-model="view_paike_class.searchForm.searchItems.leaveState_eq">
                        <option value="">--{{'table_filed_00264'|T}}--</option>
                        <option value="0">{{'table_filed_00500'|T}}</option>
                        <option value="1">{{'table_filed_00357'|T}}</option>
                        <option value="2">{{'table_filed_00314'|T}}<!--教师请假--></option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label class="control-label col-md-2 text-right" style=" margin-top: 5px;"> </label>
                <div class="col-md-10">
                    <div class="col-md-5">
                        <button class="btn btn-sm blue" ng-click="view_paike_class.search1()"><i class="fa fa-search"></i> {{'global_btn_0005'|T}}</button>
                    </div>
                    <div class="col-md-5">
                        <button class="btn btn-sm red" ng-click="view_paike_class.reset()">{{'global_btn_0017'|T}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<!--搜索-->


<!--开始table-->
<div class="table-scrollable" style="overflow-y: auto">
    <table class="table table-striped table-bordered table-hover">

        <thead>
        <tr role="row" class="heading">
            <th width="1%"> <input type="checkbox" ng-model="headcheck" class="group-checkableaaa" ng-click="view_paike_class.checkAll(headcheck)"/></span></th>
            <th width="1%">{{'table_filed_00368'|T}}</th>
            <!--<th width="5%">-->
                <!--{{'table_filed_00143'|T}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
            <!--</th>-->
            <th width="4%">
                {{'table_filed_00066'|T}}
            </th>
            <th width="8%">
                {{'table_filed_00144'|T}}
            </th>
            <th width="8%">
                {{'table_filed_00145'|T}}
            </th>
            <th width="8%">
                {{'table_filed_00294'|T}}
            </th>
            <th width="5%">
                {{'table_filed_00067'|T}}
            </th>
            <th width="7%">
                {{'table_filed_00081'|T}}
            </th>
            <th width="7%">
                {{'table_filed_00122'|T}}
            </th>
            <th width="7%">
                {{'tab_0704'|T}}
            </th>
            <th width="8%">
                {{'table_filed_00148'|T}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

            </th>
            <th width="5%">
                {{'table_filed_00059'|T}}
            </th>
            <th width="5%">
                {{'tab_0703'|T}}
            </th>
            <th width="5%">
                {{'table_filed_00151'|T}}
            </th>
            <th width="5%">
                {{'table_filed_00151a'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00369'|T}}
            </th>

        </tr>

        </thead>
        <tbody>
        <tr ng-repeat="d in view_paike_class.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
            <td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
            <td>{{$index+1}}</td>
            <!--<td>-->
                <!--{{d.courseType=='1'&&'table_filed_00299'||d.courseType=='2'&&'table_filed_00450'||d.courseType=='3'&&'table_filed_00451'||d.courseType=='4'&&'table_filed_00452'|T}}-->
            <!--</td>-->
            <td>
                {{d.status=='0'&&'table_filed_00453'||d.status=='1'&&'table_filed_00454'||d.status=='2'&&'table_filed_00499'|T}}
            </td>
            <td>
                {{d.stu_chineseName}}
            </td>
            <td>
                {{d.stu_englishName}}
            </td>
            <td>
                {{d.tutorenglishName}}
            </td>
            <td>
                {{d.course_name}}
            </td>
            <td>
                {{d.scheduledDate}}
            </td>
            <td>
                {{d.riqiEnd}}
            </td>
            <td>
                {{d.startTime}}--{{d.endTime}}
            </td>
            <td>
                {{d.us_englishName}}
            </td>
            <td>
                {{d.teachingWayName}}
            </td>
            <td>
                {{d.classroom_name}}
            </td>
            <td>
                {{d.leaveState=='0'&&'table_filed_00500'||d.leaveState=='1'&&'table_filed_00357'||d.leaveState=='2'&&'menu_0302'|T}}
            </td>
            <td>
                <span ng-if="d.isAuto=='1'">Auto</span>
                <span ng-if="d.isAuto!='1'">{{d.ctrator_userName}}</span>
            </td>
            <td>
                <!--<div class="btn-group btn-group-xs">-->
                <!--初始化-->
                                        <span ng-if="d.status=='0'&&d.attendanceStatus==''">
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.confirm(d)">  {{'table_filed_00501'|T}}</button>
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.softDeleteData(d)"><span style="color: #ff0000">{{'global_btn_0004'|T}}</span></button>
                                        </span>
                <!--已确认 删除-->
                                         <span ng-if="d.status=='1'&&d.s_approvalStatus!='2'&&d.t_approvalStatus!='2'&&d.attendanceStatus==''&&d.leaveState=='0'">
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.softDeleteData(d)"><span style="color: #ff0000">{{'global_btn_0004'|T}}</span></button>
                                        </span>
                <!--已删除，未签到，未请假-->
                                        <span ng-if="d.status=='2'&&d.attendanceStatus==''&&d.leaveState=='0'">
                                           <button type="button" class="btn btn-xs" ng-click="view_paike_class.revert(d)"> {{'table_filed_00502'|T}}</button>
                                        </span>
                <!--未请假-->
                                        <span ng-if="(d.leaveState=='0'||d.leaveState=='3')&&d.status=='1'&&d.attendanceStatus==''&&d.courseType=='1'">
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.studentLeave(d)">{{'table_filed_00357'|T}}</button>
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.teacherLeave(d)">{{'table_filed_00314'|T}}</button>
                                        </span>
                <!--学员请假-->
                                         <span ng-if="d.status=='1'&&d.leaveState=='1'&&d.s_approvalStatus=='1'&&d.attendanceStatus==''&&d.courseType=='1'">
                                             <button type="button" class="btn btn-xs" ng-click="view_paike_class.cancelStudentLeave(d,'401d')">{{'table_filed_00503'|T}}</button>
                                             <!--<button type="button" class="btn btn-xs" ng-click="view_paike_class.approval(d)">审核</button>-->
                                        </span>
                <!--教师请假-->
                                        <span ng-if="d.status=='1'&&d.leaveState=='2'&&d.t_approvalStatus=='1'&&d.attendanceStatus==''&&d.courseType=='1'">
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.cancelTeacherLeave(d)">{{'table_filed_00315'|T}}<!--{{'table_filed_00361'|T}}教师请假--></button>
                                            <!--<button type="button" class="btn btn-xs" ng-click="view_paike_class.approval(d)">审核</button>-->
                                        </span>
            <!--取消审核学员请假-->
                                        <span ng-if="d.islockDate=='1' && d.status=='2'&&d.leaveState=='1'&&d.s_approvalStatus=='2'&&d.attendanceStatus==''&&d.courseType=='1'">
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.cancelStudentLeave(d,'401i')">{{'table_filed_00503_1'|T}}</button>
                                            <!--<button type="button" class="btn btn-xs" ng-click="view_paike_class.approval(d)">审核</button>-->
                                        </span>
                <!--取消审核教师请假-->
                                        <span ng-if="d.islockDate=='1' && d.status=='2'&&d.leaveState=='2'&&d.t_approvalStatus=='2'&&d.attendanceStatus==''&&d.courseType=='1'">
                                            <button type="button" class="btn btn-xs" ng-click="view_paike_class.cancelTeacherLeave(d,'401h')">{{'table_filed_00315_1'|T}}<!--{{'table_filed_00361'|T}}教师请假--></button>
                                            <!--<button type="button" class="btn btn-xs" ng-click="view_paike_class.approval(d)">审核</button>-->
                                        </span>
                <!--</div>-->
            </td>
        </tr>
        </tbody>
    </table>
</div>
<!--结束table-->
<!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
<div class="row">
    <div class="col-md-8 col-sm-12">
        <div class="dataTables_paginate paging_bootstrap_extended">
            <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="view_paike_class.prePage()"
                                                                        class="btn btn-sm default prev"
                                                                        title="Prev"><i class="fa fa-angle-left"></i></a><input
                    type="number"  ng-model="view_paike_class.searchForm.page.currentPage"
                    class="pagination-panel-input form-control input-mini input-inline input-sm"
                    maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="view_paike_class.nextPage()"
                                                                                 class="btn btn-sm default next"
                                                                                 title="Next"><i
                    class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{view_paike_class.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
            </div>
        </div>
        <div class="dataTables_length" ><label><span
                class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="view_paike_class.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="150">150</option>
        </select> {{'table_filed_00390'|T}}</label></div>
        <div class="dataTables_info"  role="status" aria-live="polite">
            <span class="seperator">|</span>{{'table_filed_00389'|T}}{{view_paike_class.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
        </div>
    </div>
    <div class="col-md-4 col-sm-12">

    </div>
</div>
<!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
</div>
</div>
</div>
</div>
<!-- End: life time stats -->
</div>
</div>
<!-- END PAGE CONTENT-->

<!--选择学员、开始-->
<script type="text/ng-template" id="student_All.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;" >{{'table_filed_00288'|T}}<!--选择学员--></p>
        <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
        <!--<p class="p2" >  <button class="btn btn-xs  red " ng-click="cancel()">弃学</button></p>-->
    </div>
    <div class="modal-body">
        <form class="form-horizontal" name="form">

            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrap" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <!--搜索-->
                        <div class="row">
                            <div class="col-md-4 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00002'|T}}：</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control col-md-8" ng-model="student.searchForm.searchItems.chineseName_lk">
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-1 col-sm-12">
                                <button class="btn blue  filter-submit margin-bottom" ng-click="student.search1()" style="padding: 5px 14px;"><i
                                        class="fa fa-search"></i> {{'global_btn_0005'|T}}
                                </button>
                            </div>
                            <div class="col-md-7 col-sm-12">

                            </div>
                        </div>
                        <!--搜索-->
                        <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <th width="1%">
                                        <input type="checkbox" class="group-checkable">
                                    </th>
                                    <th width="1%">
                                        {{'table_filed_00368'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00002'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00042'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00003'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00004'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00006'|T}}
                                    </th>

                                    <th width="10%">
                                        {{'table_filed_00007'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'tab_0701'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'static_words_0101'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00034'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00036'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00039'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00298'|T}}
                                        <!--  OC课程状态-->
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00035'|T}}
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="d in student.dataList" ng-class-odd="'odd'" ng-class-even="'even'" ng-click="xuan(d)">
                                    <td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
                                    <td>{{$index+1}}</td>
                                    <td>
                                        <!--   <a href="#/studentxiangxi.html/{{d.id}}"> </a>-->
                                        {{d.chineseName}}
                                    </td>
                                    <td>
                                        {{d.status}}
                                    </td>
                                    <td>
                                        {{d.englishName}}
                                    </td>

                                    <td >
                                        {{d.gender=="1"&&"table_filed_00444"||d.gender=="2"&&"table_filed_00445"|T}}
                                    </td>
                                    <td>
                                        {{d.birthday}}
                                    </td>
                                    <td>
                                        {{d.nationality}}
                                    </td>
                                    <td>
                                        <span  ng-repeat="one in student.city" ng-if="d.city_id==one.id"> {{one.name}}</span>
                                        <span  ng-if="!d.city_id"> {{d.city_id}} </span>
                                    </td>

                                    <td >
                                        <span ng-repeat="one in student.zone" ng-if="d.zone_id==one.id">{{one.name}}</span>
                                        <span ng-if="!d.zone_id">{{d.zone_id}}</span>
                                    </td>

                                    <td>
                                        {{d.source}}
                                    </td>
                                    <td>
                                        {{d.contractOwnerId}}
                                    </td>
                                    <td>
                                        {{d.tutorId}}
                                    </td>

                                    <td >
                                        {{d.isOC=="0"&&"table_filed_00409"||d.isOC=="1"&&"table_filed_00408"|T}}
                                    </td>
                                    <td>
                                        {{d.referral}}
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <!--结束table-->
                        <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                        <div class="row">
                            <div class="col-md-8 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_extended">
                                    <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="student.prePage()"
                                                                                                class="btn btn-sm default prev"
                                                                                                title="Prev"><i class="fa fa-angle-left"></i></a><input
                                            type="number"  ng-model="student.searchForm.page.currentPage"
                                            class="pagination-panel-input form-control input-mini input-inline input-sm"
                                            maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="student.nextPage()"
                                                                                                         class="btn btn-sm default next"
                                                                                                         title="Next"><i
                                            class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{student.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                    </div>
                                </div>
                                <div class="dataTables_length" ><label><span
                                        class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="student.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="150">150</option>
                                </select> {{'table_filed_00390'|T}}</label></div>
                                <div class="dataTables_info"  role="status" aria-live="polite">
                                    <span class="seperator">|</span>{{'table_filed_00389'|T}}{{student.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">

                            </div>
                        </div>
                        <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>

        </form>
    </div>

    <div style="clear: both;"></div>
</script>
<!--选择学员、结束-->

<!--选择教师、开始-->
<script type="text/ng-template" id="teacher_All.html">
<div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
    <p class="p1" style="font-size: 16px;" > {{'table_filed_00287'|T}}<!--选择教师--></p>
    <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
    <!--<p class="p2" >  <button class="btn btn-xs  red " ng-click="cancel()">弃学</button></p>-->
</div>
<div class="modal-body">
<form class="form-horizontal" name="form">

<div class="portlet-body">
<div class="table-container">
<div id="datatable_ajax_wrapww" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
<!--搜索-->
<div class="row">
    <div class="col-md-3 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00002'|T}}：</label>
            <div class="col-md-8">
                <input type="text" class="form-control col-md-8" ng-model="teacherview.searchForm.searchItems.chineseName_lk">
            </div>
        </div>

    </div>
    <div class="col-md-3 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00003'|T}}：</label>
            <div class="col-md-8">
                <input type="text" class="form-control col-md-8" ng-model="teacherview.searchForm.searchItems.englishName_lk">
            </div>
        </div>

    </div>
    <div class="col-md-1 col-sm-12">
        <button class="btn blue  filter-submit margin-bottom" ng-click="teacherview.search1()" style="padding: 5px 14px;"><i
                class="fa fa-search"></i> {{'global_btn_0005'|T}}
        </button>
    </div>
    <div class="col-md-7 col-sm-12">

    </div>
</div>
<!--搜索-->
<!--开始table-->
<div class="table-scrollable" style="overflow-y: auto">
    <table class="table table-striped table-bordered table-hover">
        <thead>
        <tr role="row" class="heading">

            <th width="1%">
                {{'table_filed_00368'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00108'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00110'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00111'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00112'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00406'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00004'|T}}
            </th>
            <th width="10%">
                {{'table_filed_00007'|T}}
            </th>

            <th width="10%">
                {{'table_filed_00008'|T}}
            </th>


            <th width="10%">
                {{'table_filed_00115'|T}}
            </th>

            <th width="10%">
                {{'table_filed_00066'|T}}
            </th>



        </tr>
        <!-- <tr role="row" class="filter">
             <td></td>
             <td></td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.code_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.chineseName_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.englishName_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.abbr_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.birthday_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.gender_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.nationality_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.IDNo_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.possportNo_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.mobile_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.email_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.wechat_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.address_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.type_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.zone_id_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.status_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.department_id_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.department_name_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.position_id_lk">
             </td>
              <td>
                 <input type="text" class="form-control form-filter input-sm" ng-model="teacherview.searchForm.searchItems.position_name_lk">
             </td>
             <td>
                 <button class="btn btn-xs  filter-submit margin-bottom" ng-click="teacherview.searchN()"><i
                         class="fa fa-search"></i> {{'global_btn_0005'|T}}
                 </button>
                 <button class="btn btn-xs  filter-cancel" ng-click="teacherview.reset()"><i class="fa fa-times"></i> {{'global_btn_0017'|T}}
                 </button>
             </td>

         </tr>-->
        </thead>
        <tbody>
        <tr ng-repeat="d in teacherview.dataList" ng-class-odd="'odd'" ng-click="xuan(d)" ng-class-even="'even'">

            <td>{{$index+1}}</td>
            <td>
                {{d.code}}
            </td>
            <td>
                {{d.chineseName}}
            </td>
            <td>
                {{d.englishName}}
            </td>
            <td>
                {{d.abbr}}
            </td>
            <td>
                {{d.birthday}}
            </td>
            <td>
                {{d.gender=="1"&&"table_filed_00444"||d.gender=="2"&&"table_filed_00445"|T}}
            </td>
            <td>
                {{d.nationality}}
            </td>
            <td>
                {{d.mobile}}
            </td>

            <td>
                {{d.type=="1"&&"table_filed_00478"||d.type=="2"&&"table_filed_00479"||d.type=="3"&&"table_filed_00480"|T}}
            </td>

            <td>
                {{d.status=="1"&&"table_filed_00481"||d.status=="2"&&"table_filed_00482"||d.status=="3"&&"table_filed_00483"|T}}
            </td>


        </tr>

        </tbody>
    </table>
</div>
<!--结束table-->
<!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
<div class="row">
    <div class="col-md-8 col-sm-12">
        <div class="dataTables_paginate paging_bootstrap_extended">
            <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="teacherview.prePage()"
                                                                        class="btn btn-sm default prev"
                                                                        title="Prev"><i class="fa fa-angle-left"></i></a><input
                    type="number"  ng-model="teacherview.searchForm.page.currentPage"
                    class="pagination-panel-input form-control input-mini input-inline input-sm"
                    maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="teacherview.nextPage()"
                                                                                 class="btn btn-sm default next"
                                                                                 title="Next"><i
                    class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{teacherview.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
            </div>
        </div>
        <div class="dataTables_length" ><label><span
                class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="teacherview.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="150">150</option>
        </select> {{'table_filed_00390'|T}}</label></div>
        <div class="dataTables_info"  role="status" aria-live="polite">
            <span class="seperator">|</span>{{'table_filed_00389'|T}}{{teacherview.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
        </div>
    </div>
    <div class="col-md-4 col-sm-12">

    </div>
</div>
<!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->

</div>
</div>
</div>

</form>
</div>

<div style="clear: both;"></div>
</script>
<!--选择教师、结束-->

<!--删除{{'table_filed_00386'|T}}面 start-->
<script type="text/ng-template" id="deletePage.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;" >{{'global_btn_0004'|T}}</p>

        <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
    </div>

    <div class="modal-body">
        <form class="form-horizontal" name="form">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="control-label col-md-2">{{'table_filed_00166'|T}}<span ng-class="{required:form.description.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <textarea class="form-control " ng-model="classSchedule.item.description" name="description" required></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="col-md-6">
                            <label  class="control-label" style="height: 29px">
                                <input type="checkbox" class="group-checkable icheck" ng-true-value="1" ng-false-value="0"  ng-model="classSchedule.item.flag0"/>{{'table_filed_00517'|T}}
                            </label>
                        </div>
                        <div class="col-md-6">
                            <label  class="control-label" style="height: 29px">
                                <input type="checkbox" class="group-checkable icheck" ng-true-value="1" ng-false-value="0"  ng-model="classSchedule.item.flag1"/>{{'table_filed_00518'|T}}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="modal-footer lm_window_footer" style="padding-bottom: 15px;padding-top:5px;background-color: #f5f5f5;">
        <button class="btn green"  style="padding: 1px 7px;" ng-disabled="form.$invalid" ng-click="submit()">{{'table_filed_00501'|T}}</button>
        <button class="btn default" style="padding: 1px 7px; " ng-click="cancel()">{{'table_filed_00361'|T}}</button>
    </div>
    <div style="clear: both;"></div>
</script>
<!--删除{{'table_filed_00386'|T}}面 end-->

<!--请假{{'table_filed_00386'|T}}面 start-->
<script type="text/ng-template" id="leavePage.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;" ng-if="leave.actor=='student'">{{'table_filed_00357'|T}}</p>
        <p class="p1" style="font-size: 16px;" ng-if="leave.actor=='teacher'">{{'table_filed_00314'|T}}<!--教师请假--></p>
        <div class="col-md-8 " ng-if="leave.actor=='student'">
            <input type="text" ng-model="leave.item.summ" name="summ" readonly style="border:0;background-color:#f5f5f5;"/>
        </div>
        <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
    </div>

    <div class="modal-body">
        <form class="form-horizontal" name="form">
            <div class="row">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00093'|T}}</label>
                        <div class="col-md-8 "  ng-init="$root.settings.utils.initpicker()">
                            <input type="text" class="form-control form_datetime" data-date-format="yyyy-mm-dd HH:MM" ng-model="leave.item.handleTime" name="handleTime"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00153'|T}}</label>
                        <div class="col-md-8" style="height: 30px;">
                            <div class="col-md-4"><input type="radio" ng-model="leave.item.isInadvance" value="1" name="isInadvance" disabled/><label class="control-label">{{'table_filed_00408'|T}}</label></div>
                            <div class="col-md-4"><input type="radio"  ng-model="leave.item.isInadvance" value="0" name="isInadvance" disabled /><label class="control-label">{{'table_filed_00409'|T}}</label></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00064'|T}}</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.leaveDatetimeStart" name="startTime" readonly/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00065'|T}}</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control" ng-model="leave.item.leaveDatetimeEnd" name="endTime" readonly/>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" ng-if="leave.actor=='student' ">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00154'|T}}<span ng-class="{required:form.deductionClass.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.deductionClass" name="deductionClass" required/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"  ng-if="leave.actor=='student' && (leave.item.teacherType=='1' || leave.item.teacherType=='2')">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00155'|T}}<span ng-class="{required:form.givenClass.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.givenClass" name="givenClass" required/>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" ng-if="leave.actor=='teacher' && leave.item.isInadvance=='0'">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00159'|T}}<span ng-class="{required:form.givenClass.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.givenClass" name="givenClass" required/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" ng-if="leave.actor=='teacher' && leave.item.isInadvance=='0' && leave.item.teacherType!='1'">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00318'|T}}<!--扣教师（小时）--><span ng-class="{required:form.deductionClass.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.deductionClass" name="deductionClass" required/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" ng-if="leave.actor=='teacher' && leave.item.isInadvance=='0' && leave.item.teacherType=='1'">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00160'|T}}<span ng-class="{required:form.deductionWage.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.deductionWage" name="deductionWage" required/>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" ng-if="leave.actor=='student'">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00665'|T}}</label>
                        <div class="col-md-8">
                            <select class="form-control" ng-model="leave.item.reschedule" name="reschedule" required>
                                <option value="1">{{'table_filed_00666'|T}}</option>
                                <option value="2">{{'table_filed_00667'|T}}</option>
                                <option value="3">{{'table_filed_00668'|T}}</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" >
                    <div class="form-group">
                        <label class="control-label col-md-2">{{'table_filed_00156'|T}}<span ng-class="{required:form.reason.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <textarea class="form-control " ng-model="leave.item.reason" name="reason" required></textarea>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00157'|T}}</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.creatorName" name="creatorName" readonly/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00118'|T}}</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control " ng-model="leave.item.createTime" name="createTime" readonly/>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="modal-footer lm_window_footer" style="padding-bottom: 15px;padding-top:5px;background-color: #f5f5f5;">
        <button class="btn green"  style="padding: 1px 7px;" ng-disabled="form.$invalid" ng-click="confirm()">{{'global_btn_0007'|T}}</button>
        <button class="btn default" style="padding: 1px 7px; " ng-click="cancel()">{{'table_filed_00361'|T}}</button>
    </div>
    <div style="clear: both;"></div>
</script>
<!--请假{{'table_filed_00386'|T}}面 end-->
<!--自动排课 start-->
<script type="text/ng-template" id="autoArrangement.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;">{{'table_filed_00167'|T}}</p>
        <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
    </div>

    <div class="modal-body">
        <form class="form-horizontal" name="form">
            <div class="row" ng-init="$root.settings.utils.initpicker()">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00121'|T}}<span ng-class="{required:form.startDate.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <input type="text" class="form-control  date-picker" name="startDate" data-date-format="yyyy-mm-dd" ng-model="autoArrangement.item.startDate" name="startDate" required/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4">{{'table_filed_00122'|T}}<span ng-class="{required:form.endDate.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <input type="text" class="form-control date-picker" name="endDate" data-date-format="yyyy-mm-dd" ng-model="autoArrangement.item.endDate" name="endDate" required/>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label col-md-4"> {{'static_words_0101'|T}}<span ng-class="{required:form.zone_id.$invalid}">*</span></label>
                        <div class="col-md-8">
                            <select class="form-control" ng-model="autoArrangement.item.zone_id" name="zone_id" ng-options="zone.zone_id as zone.zone_name group by zone.city_name for zone in autoArrangement.zoneList" required></select>
                        </div>
                    </div>
                </div>

            </div>
        </form>
    </div>

    <div class="modal-footer lm_window_footer" style="padding-bottom: 15px;padding-top:5px;background-color: #f5f5f5;">
        <button class="btn green"  style="padding: 1px 7px;" ng-disabled="form.$invalid" ng-click="submit()">{{'table_filed_00501'|T}}</button>
        <button class="btn default" style="padding: 1px 7px; " ng-click="cancel()">{{'table_filed_00361'|T}}</button>
    </div>
    <div style="clear: both;"></div>
</script>
<!--自动排课 end-->