
<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Begin: life time stats -->
        <div class="portlet">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-list"></i>{{'menu_0907'|T}}
                </div>

            </div>
            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <!--
                                <tr role="row" class="heading">
                                    <td>
                                        <button type="button" class="btn red" ng-click="added()"> + </button>
                                        <button type="button" class="btn red" ng-click="reduce()"> - </button>
                                    </td>
                                </tr>
                                -->
                                <tr role="row" class="heading">
                                    <td>
                                        <textarea id="custom_sql" ng-model="zidingyi.searchForm.sql" rows="3" cols="200"></textarea>
                                    </td>
                                </tr>
                                <tr role="row" class="heading">
                                    <td>
                                        <button type="button" class="btn red" ng-click="zidingyi.search1()"> {{'global_btn_0005'|T}}</button>
                                        <button type="button" class="btn red" ng-click="zidingyi.export()">{{'global_btn_0011'|T}}</button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div>
                            {{zidingyi.message}}
                        </div>
                        <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <th ng-repeat="d1 in zidingyi.heardList" >
                                        {{d1}}
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="dd in zidingyi.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                                    <td ng-repeat="ddd in dd.infoList1 track by $index" >
                                        {{ddd}}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--结束table-->
                        <!--开始table表头分{{'table_filed_00386'|T}}以及工具栏-->
                        <div class="row">
                            <div class="col-md-8 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_extended">
                                    <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="zidingyi.prePage()"
                                                                        class="btn btn-sm default prev"
                                                                        title="Prev"><i class="fa fa-angle-left"></i></a><input
                                            type="number"  ng-model="zidingyi.searchForm.page.currentPage"
                                            class="pagination-panel-input form-control input-mini input-inline input-sm"
                                            maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="zidingyi.nextPage()"
                                                                                                         class="btn btn-sm default next"
                                                                                                         title="Next"><i
                                            class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{zidingyi.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                    </div>
                                </div>
                                <div class="dataTables_length" ><label><span
                                        class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="zidingyi.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="150">150</option>
                                </select> {{'table_filed_00390'|T}}</label></div>
                                <div class="dataTables_info"  role="status" aria-live="polite">
                                    <span class="seperator">|</span>{{'table_filed_00389'|T}}{{zidingyi.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">

                            </div>
                        </div>
                        <!--结束table表头分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>
        </div>
        <!-- End: life time stats -->
    </div>
</div>
<!-- END PAGE CONTENT-->
<script src="/tesystem/ng/custom_report/custom_report.js"></script>