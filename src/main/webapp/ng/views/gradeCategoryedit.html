
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row" ng-form="bigform">
<div class="col-md-12">

<div class="portlet light bordered ">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-pencil"></i>{{gradeCategory.item.suoshukehu||'table_filed_00391'|T}}
    </div>
    <div class="actions btn-set">
        <button type="button" name="back" class="btn default" ng-click="$root.settings.utils.back()"><i class="fa fa-angle-left"></i> {{'global_btn_0006'|T}}</button>
        <button ng-disabled="bigform.$invalid" class="btn green" ng-click="gradeCategory.save(1)"popover-trigger="mouseenter"><i class="fa fa-check"></i>{{'global_btn_0007'|T}}</button>
        <!--<button ng-disabled="bigform.$invalid"  class="btn green" ng-click="gradeCategory.save(2)"  popover-trigger="mouseenter"><i class="fa fa-check-circle"></i>保存并{{'global_btn_0001'|T}}</button>&ndash;&gt;-->

    </div>
</div>
<div class="portlet-body">
    <div class="row">
        <div class="col-md-12">
                <div class="form">
                    <!-- BEGIN FORM-->
                    <form action="#" name="myform" class="form-horizontal">
                        <div class="form-body">
                           
                            <!--/row-->
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00061'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="gradeCategory.item.name"  name="name"/>
									</div>
								</div>
							</div>
							

                        </div>

                     </form>
                    <!-- END FORM-->

                </div>
        </div>
    </div>
</div>
</div>
</form>
</div>
</div>
