
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row" ng-form="bigform">
<div class="col-md-12">

<div class="portlet light bordered ">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-pencil"></i>{{teacher_zone.item.suoshukehu||'table_filed_00391'|T}}
    </div>
    <div class="actions btn-set">
        <button type="button" name="back" class="btn default" ng-click="$root.settings.utils.back()"><i class="fa fa-angle-left"></i> {{'global_btn_0006'|T}}</button>
        <button ng-disabled="bigform.$invalid" class="btn green" ng-click="teacher_zone.save(1)"popover-trigger="mouseenter"><i class="fa fa-check"></i> {{'global_btn_0007'|T}}</button>
        <!--<button ng-disabled="bigform.$invalid"  class="btn green" ng-click="teacher_zone.save(2)"  popover-trigger="mouseenter"><i class="fa fa-check-circle"></i>保存并{{'global_btn_0001'|T}}</button>-->

    </div>
</div>
<div class="portlet-body">
    <div class="row">
        <div class="col-md-12">
                <div class="form">
                    <!-- BEGIN FORM-->
                    <form action="#" name="myform" class="form-horizontal">
                        <div class="form-body">
                           
                            <!--/row-->
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'tab_0701'|T}}ID</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_zone.item.city_id"  name="city_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3"> {{'table_filed_00195'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_zone.item.zone_id"  name="zone_id"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00121'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_zone.item.startDate"  name="startDate"/>
									</div>
								</div>
							</div>
							 <div class="col-md-6" >
								<div class="form-group">
									<label class="control-label col-md-3">{{'table_filed_00122'|T}}</label>
									<div class="col-md-9">
										<input type="text" class="form-control" ng-model="teacher_zone.item.endDate"  name="endDate"/>
									</div>
								</div>
							</div>
							

                        </div>

                     </form>
                    <!-- END FORM-->

                </div>
        </div>
    </div>
</div>
</div>
</form>
</div>
</div>
