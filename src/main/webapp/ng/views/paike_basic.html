<style>
    * {
        margin: 0;
        padding: 0;
    }

    #wrapper ul {
        list-style: none;
        margin-bottom: 0px;
    }

    .data_color {
        background: #080153;
        font-weight: bold;
        font-family: "黑体";
        color: #fff;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .thead {
        width: 100%;
        display: table;
        border-collapse: collapse;
    }

    .thead li {
        display: table-cell;
        width: 12.5%;
        border-left: 1px solid #fff;
        border-bottom: 1px solid #fff;
        margin-left: -1px;
        /*  background: #fff;*/
        text-align: center;
        /*  background: #c5d9f1;*/
        font-size: 14px;
        padding: 5px 0;

        background: rgb(0, 80, 153);
        font-weight: bold;
        font-family: "黑体";
        color: #fff;
    }

    .tbody {
        width: 100%;
        display: table;
        border-collapse: collapse;
        height: 32px;
    }

    .th {
        display: table-cell;
        width: 12.5%;
        border-left: 1px solid #ffffff;
        border-bottom: 1px solid #ffffff;
        margin-left: -1px;
        /*  background: #d6d8e8;*/
        text-align: center;

        background: rgb(0, 80, 153);
        font-weight: bold;
        font-family: "黑体";
        color: #fff;

        /*字体居中显示*/
        line-height: 64px;
        /*  margin-top:50%;
        margin-bottom:50%;*/
    }

    .th1 {
        display: table-cell;
        width: 12.5%;
        border-left: 1px solid #ffffff;
        border-bottom: 1px solid #ffffff;
        margin-left: -1px;
        /*  background: #d6d8e8;*/
        text-align: center;

        background: rgb(0, 80, 153);
        font-weight: bold;
        font-family: "黑体";
        color: #fff;

        /*字体居中显示*/
        line-height: 32px;
        /*  margin-top:50%;
          margin-bottom:50%;*/
    }

    .td {
        display: table-cell;
        width: 12.5%;
        border-left: 1px solid #fff;
        border-bottom: 1px solid #fff;
        margin-left: -1px;
        /*background: #cdcdcd;*/
        text-align: center;
        line-height: 32px;
        height: 64px;
        position: relative;
    }

    .td div.smallItem {
        height: 12.5%;
        text-align: center;
        width: 100%;
    }

    .td .layer {
        position: absolute;
        width: 100%;
        height: 100%;
        display: block;
        top: 0;
        left: 0;
        vertical-align: middle;
        word-break: break-all;
        word-wrap: break-word;

        font-family: "Arial";
    }

    .td1 {
        display: table-cell;
        width: 12.5%;
        border-left: 1px solid #fff;
        border-bottom: 1px solid #fff;
        margin-left: -1px;
        /*background: #cdcdcd;*/
        text-align: center;
        line-height: 32px;
        height: 32px;
        position: relative;
    }

    .td1 div.smallItem1 {
        height: 25%;
        text-align: center;
        width: 100%;
    }

    .td1 .layer1 {
        position: absolute;
        width: 100%;
        height: 100%;
        display: block;
        top: 0;
        left: 0;
        vertical-align: middle;
        word-break: break-all;
        word-wrap: break-word;

        font-family: "Arial";
    }

    .layerPadding {
        padding-top: 32px;
    }

    .checkeddiv {
        /*学生有时间*/
        background: rgb(210, 227, 235);
        text-align: center;
    }

    .uncheckeddiv {
        /*不能排课时间*/
        background: rgb(240, 241, 238);
        text-align: center;
    }

    .teacherdiv {
        /*已排的课*/
        background: rgb(254, 242, 184);
        text-align: center;
    }

    .teacherdivon {
        /*online已排的课*/
        background: rgb(252, 228, 214);
        text-align: center;
    }

    .teacherdivoff {
        /*offine已排的课*/
        background: rgb(254, 242, 184);
        text-align: center;
    }

    .teacherdivspe {
        /*specialist已排的课*/
        background: rgb(149 231 194);
        text-align: center;
    }
</style>

<!-- BEGIN PAGE HEADER-->
<!--<div class="portlet-title" style="margin-bottom:2%; background-color: #ffffff;">
    <div class="caption  col-md-5" style="padding-top: 4px;">
          <i class="fa fa-user col-md-1" style="margin-top: 7px;"></i>
        <div style="font-size: 18px;color: #6d4b52; padding-left: 0;" class="col-md-11">学员：{{student.item.chineseName}}</div>
    </div>

    <div class="actions btn-set" style="float: right;">
        <button type="button" name="back" class="btn blue" ng-click="$root.settings.utils.back()"><i class="fa fa-angle-left"></i> {{'global_btn_0006'|T}}</button>
    </div>
    <div style="clear: both;"></div>
</div>-->

<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Begin: life time stats -->
        <div class="portlet">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-list"></i>{{'static_words_0502'|T}}
                </div>
                <div class="actions">
                    <div class="btn-group btn-group-sm btn-group-solid">
                        <!--        <button type="button" class="btn blue" ng-click="add()" >{{'global_btn_0001'|T}}</button>
                <button type="button" class="btn red"  ng-click="student.delete()">删除</button>-->
                    </div>
                </div>
            </div>

            <div class="portlet-body">

                <div class="row">
                    <div class="col-md-12">
                        <div class="portlet">
                            <div class="portlet-body">
                                <div class="table-container">
                                    <div id="datatable_a"
                                        class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                                        <div class="row">
                                            <div class="col-md-12">

                                                <div class="col-md-4">
                                                    <!-- <div class="form-group"></div>-->
                                                    <label
                                                        class="control-label col-md-3 text_right">{{'table_filed_00144'|T}}</label>
                                                    <div class="col-md-3 " title="{{student1.item.chineseName}}">
                                                        <input type="text" class="form-control" disabled
                                                            ng-model="student1.item.chineseName" />
                                                    </div>
                                                    <div class="col-md-3 " title="{{student1.item.englishName}}">
                                                        <input type="text" class="form-control" disabled
                                                            ng-model="student1.item.englishName" />
                                                    </div>
                                                    <div class="col-md-2 ">
                                                        <button type="button" class="btn blue"
                                                            ng-disabled="(!(($root.zone_id!='all')&&($root.timezone=='Asia/Shanghai')))"
                                                            style="height:30px"
                                                            ng-click="xuanzeStudent()">{{'global_btn_0014'|T}}</button>
                                                    </div>
                                                </div>
                                                <!--offline已耗-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_006677'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.offlineYihao" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--online已耗-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_006688'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.onlineYihao" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--总课时-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00162'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control" disabled
                                                                ng-model="student1.studentKeshi.sumKeshi" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--offline已排-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00669'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.offlineYipai" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--online已排-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00670'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.onlineYipai" />
                                                        </div>
                                                    </div>
                                                </div>

                                                <!--offline已排-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00969'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.speYihao" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--online已排-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00970'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.speYipai" />
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00971'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.speTotal" />
                                                        </div>
                                                    </div>
                                                </div>

                                                <!--offline已排-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00974'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.obtYihao" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--online已排-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00975'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.obtYipai" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00976'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.obtTotal" />
                                                        </div>
                                                    </div>
                                                </div>

                                                <!--已消耗-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00165'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.yixiaohao" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--已排课-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00163'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.yipaike" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--未排课-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label
                                                            class="control-label col-md-3 text_right">{{'table_filed_00164'|T}}</label>
                                                        <div class="col-md-6 ">
                                                            <input type="text" class="form-control " disabled
                                                                ng-model="student1.studentKeshi.weipaike" />
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <div class="col-md-6 ">
                                                            <button type="button" class="btn blue" style="height:30px"
                                                                ng-disabled="(!(($root.zone_id!='all')&&($root.timezone=='Asia/Shanghai')))"
                                                                ng-click="checkstutas()">{{'btn_0501'|T}}</button>
                                                            <!-- <button type="button" class="btn blue" style="height:30px" ng-if="student1.status==false" ng-if="student1.status==true"  ng-click="checkstutas(2)"></button>-->
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" ng-if="student1.status==true">
                    <div class="col-md-12">
                        <div class="portlet">

                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="fa fa-list"></i>{{'table_filed_00067'|T}}
                                </div>
                                <div class="actions">
                                    <div class="btn-group btn-group-sm btn-group-solid">
                                        <!-- <button type="button" class="btn blue" ng-click="view_coures_teacher.createOrUpdate()"  >{{'global_btn_0001'|T}}</button>-->
                                    </div>
                                </div>
                            </div>


                            <div class="portlet-body">
                                <div class="table-container">
                                    <div id="datatable_ajax_wrappea"
                                        class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                                        <div class="row">
                                            <div class="col-md-12">

                                                <!--开始table-->
                                                <div class="table-scrollable" style="overflow-y: auto">
                                                    <table class="table table-striped table-bordered table-hover">
                                                        <thead>
                                                            <tr role="row" class="heading">

                                                                <th width="1%">
                                                                    {{'table_filed_00368'|T}}
                                                                </th>

                                                                <th width="10%">
                                                                    {{'table_filed_00067'|T}}
                                                                </th>
                                                                <th width="10%">
                                                                    {{'table_filed_00068'|T}}
                                                                </th>
                                                                <th width="10%">
                                                                    {{'table_filed_00061'|T}}
                                                                </th>
                                                                <!--
                                            <th width="10%">
                                                {{'table_filed_00002'|T}}
                                            </th>
                                             -->
                                                                <th width="10%">
                                                                    {{'table_filed_00003'|T}}
                                                                </th>

                                                                <th width="10%">
                                                                    {{'table_filed_00059'|T}}
                                                                </th>
                                                                <th width="10%">
                                                                    {{'table_filed_00064'|T}}
                                                                </th>
                                                                <th width="10%">
                                                                    {{'table_filed_00065'|T}}
                                                                </th>
                                                                <th width="10%">
                                                                    {{'table_filed_00051'|T}}
                                                                </th>
                                                                <th width="10%">
                                                                    {{'table_filed_00066'|T}}
                                                                </th>


                                                            </tr>

                                                        </thead>
                                                        <tbody>
                                                            <tr ng-repeat="d in view_coures_teacher.dataList"
                                                                ng-class-odd="'odd'" ng-class-even="'even'">

                                                                <td>{{$index+1}}</td>

                                                                <td>
                                                                    {{d.course_name}}
                                                                </td>
                                                                <td>
                                                                    {{d.gradeCategory_name}}
                                                                </td>
                                                                <td>
                                                                    {{d.grade_name}}
                                                                </td>
                                                                <!--  
                                            <td>
                                                {{d.teacher_chineseName}}
                                            </td>-->
                                                                <td>
                                                                    {{d.teacher_englishName}}
                                                                </td>

                                                                <td>
                                                                    <!--  
                                                {{d.teachingWay=='1'&& 'tab_0703' ||d.teachingWay=='2'&&'table_filed_00441'|T}}
                                            	-->
                                                                    {{d.teachingWayName}}
                                                                </td>
                                                                <td>
                                                                    {{d.startDate}}
                                                                </td>
                                                                <td>
                                                                    {{d.endDate}}
                                                                </td>
                                                                <td>
                                                                    {{d.units}}
                                                                </td>
                                                                <td>
                                                                    {{d.status=='1'&&'table_filed_00442'||d.status=='2'&&'table_filed_00443'|T}}
                                                                </td>

                                                            </tr>

                                                        </tbody>
                                                    </table>
                                                </div>
                                                <!--结束table-->
                                                <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                                                <div class="row">
                                                    <div class="col-md-8 col-sm-12">
                                                        <div class="dataTables_paginate paging_bootstrap_extended">
                                                            <div class="pagination-panel"> {{'table_filed_00385'|T}} <a
                                                                    ng-click="view_coures_teacher.prePage()"
                                                                    class="btn btn-sm default prev" title="Prev"><i
                                                                        class="fa fa-angle-left"></i></a><input
                                                                    type="number"
                                                                    ng-model="view_coures_teacher.searchForm.page.currentPage"
                                                                    class="pagination-panel-input form-control input-mini input-inline input-sm"
                                                                    maxlenght="5"
                                                                    style="text-align:center; margin: 0 5px;"><a
                                                                    ng-click="view_coures_teacher.nextPage()"
                                                                    class="btn btn-sm default next" title="Next"><i
                                                                        class="fa fa-angle-right"></i></a>
                                                                {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span
                                                                    class="pagination-panel-total">{{view_coures_teacher.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                                            </div>
                                                        </div>
                                                        <div class="dataTables_length"><label><span
                                                                    class="seperator">|</span>{{'table_filed_00388'|T}}
                                                                <select
                                                                    ng-model="view_coures_teacher.searchForm.page.itemsperpage"
                                                                    class="form-control input-xsmall input-sm input-inline">
                                                                    <option value="10">10</option>
                                                                    <option value="20">20</option>
                                                                    <option value="50">50</option>
                                                                    <option value="100">100</option>
                                                                    <option value="150">150</option>
                                                                </select> {{'table_filed_00390'|T}}</label></div>
                                                        <div class="dataTables_info" role="status" aria-live="polite">
                                                            <span
                                                                class="seperator">|</span>{{'table_filed_00389'|T}}{{view_coures_teacher.searchForm.page.totalItems}}
                                                            {{'table_filed_00390'|T}}
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 col-sm-12">

                                                    </div>
                                                </div>
                                                <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-container" ng-if="student1.item.id!=0">
                    <div class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <div class="row">
                            <div class="col-md-12">

                                <div>
                                    <div class="col-md-12"
                                        style="background-color: #ffffff;border: 1px solid #cecece; padding-bottom: 30px;">
                                        <div class="form">
                                            <div class="form-body">

                                                <!------------------------------------------------------------------------------------------------------------------------------------------------->
                                                <!--table-开始-->
                                                <div class="col-md-12">
                                                    <div class="form">
                                                        <!-- BEGIN FORM-->
                                                        <form name="myform1" class="form-horizontal">
                                                            <div class="form-body">

                                                                <div class="col-md-12">
                                                                    <div class="form-group">
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-12">
                                                                    <div class="form-group">
                                                                        <div class="col-md-1">
                                                                        </div>
                                                                        <div class="col-md-11">
                                                                            <div class="form-group ">
                                                                                <div class="col-md-1">
                                                                                    <button style="padding: 5px 14px;"
                                                                                        type="button" class="btn blue"
                                                                                        ng-click="student_freeTime.preWeek()">{{'btn_0201'|T}}</button>
                                                                                </div>
                                                                                <div class="col-md-1">
                                                                                    <button style="padding: 5px 14px;"
                                                                                        type="button" class="btn blue"
                                                                                        ng-click="student_freeTime.nextWeek()">{{'btn_0202'|T}}</button>
                                                                                </div>
                                                                                <div class="col-md-1"
                                                                                    style="display:none;">
                                                                                    <input type="checkbox"
                                                                                        style="margin-top: 11px;"
                                                                                        ng-true-value="2"
                                                                                        ng-false-value="1"
                                                                                        ng-model="student1.item.jiari"
                                                                                        class="group-checkable">{{'static_words_0505'|T}}
                                                                                </div>
                                                                                <label class="control-label col-md-1"
                                                                                    style="padding-left: 0px;">
                                                                                    {{'table_filed_00852'|T}}</label>
                                                                                <div class="col-md-2 date form_date"
                                                                                    ng-init="$root.settings.utils.initpicker()">
                                                                                    <input type="text"
                                                                                        style="width: 80%;"
                                                                                        class="form-control date-picker"
                                                                                        readonly
                                                                                        ng-style="{'background-color':'#fff','cursor':'pointer'}"
                                                                                        data-date-format="yyyy-mm-dd"
                                                                                        ng-model="student_freeTime.toDay" />
                                                                                </div>
                                                                                <div class="col-md-2">
                                                                                    <button style="padding: 5px 14px;"
                                                                                        type="button" class="btn green"
                                                                                        ng-click="student1.studentDetail(student1.item.id)">{{'btn_0502'|T}}</button>
                                                                                </div>


                                                                                <label class="control-label col-md-1"
                                                                                    style="padding-left: 0px;">循环排课截止到</label>
                                                                                <div class="col-md-2 date form_date"
                                                                                    ng-init="$root.settings.utils.initpicker()">
                                                                                    <input type="text"
                                                                                        style="width: 80%;"
                                                                                        class="form-control date-picker"
                                                                                        data-date-format="yyyy-mm-dd"
                                                                                        id="xunhuan_date" />
                                                                                </div>


                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </form>
                                                    </div>

                                                </div>
                                                <!--table-结束-->


                                                <!--日期表start-->

                                                <div id="wrapper" class="col-md-9">
                                                    <ul class="thead clearfix">
                                                        <li></li>
                                                        <!--背景   ：0 80 153
                                                        字体   ：白色  黑体  加粗-->
                                                        <li ng-repeat="title in student_freeTime.weekTitle">
                                                            <span>{{title.name}}</span>
                                                            <br>
                                                            <span>{{title.date}}</span>
                                                        </li>

                                                    </ul>
                                                    <ul class="tbody" ng-repeat="timeItem in student_freeTime.list">
                                                        <li ng-if="timeItem.flag=='2'" class="th">
                                                            {{timeItem.startTime}}-{{timeItem.endTime}}</li>
                                                        <li ng-if="timeItem.flag=='1'" class="th1">
                                                            {{timeItem.startTime}}-{{timeItem.endTime}}</li>
                                                        <li ng-if="timeItem.flag=='2'" class="td"
                                                            ng-repeat="t_freeTime in timeItem.s_freeTimeList"
                                                            ng-click="student_freeTime.toCheck(t_freeTime)"
                                                            ng-init="initclass(t_freeTime)">
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,1)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv1,'uncheckeddiv':t_freeTime.isuncheckeddiv1,'teacherdivoff':t_freeTime.isteacherdivoff1,'teacherdivon':t_freeTime.isteacherdivon1,'teacherdivspe':t_freeTime.isteacherdivspe1}">
                                                            </div>
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,2)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv2,'uncheckeddiv':t_freeTime.isuncheckeddiv2,'teacherdivoff':t_freeTime.isteacherdivoff2,'teacherdivon':t_freeTime.isteacherdivon2,'teacherdivspe':t_freeTime.isteacherdivspe2}">
                                                            </div>
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,3)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv3,'uncheckeddiv':t_freeTime.isuncheckeddiv3,'teacherdivoff':t_freeTime.isteacherdivoff3,'teacherdivon':t_freeTime.isteacherdivon3,'teacherdivspe':t_freeTime.isteacherdivspe3}">
                                                            </div>
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,4)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv4,'uncheckeddiv':t_freeTime.isuncheckeddiv4,'teacherdivoff':t_freeTime.isteacherdivoff4,'teacherdivon':t_freeTime.isteacherdivon4,'teacherdivspe':t_freeTime.isteacherdivspe4}">
                                                            </div>
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,5)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv5,'uncheckeddiv':t_freeTime.isuncheckeddiv5,'teacherdivoff':t_freeTime.isteacherdivoff5,'teacherdivon':t_freeTime.isteacherdivon5,'teacherdivspe':t_freeTime.isteacherdivspe5}">
                                                            </div>
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,6)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv6,'uncheckeddiv':t_freeTime.isuncheckeddiv6,'teacherdivoff':t_freeTime.isteacherdivoff6,'teacherdivon':t_freeTime.isteacherdivon6,'teacherdivspe':t_freeTime.isteacherdivspe6}">
                                                            </div>
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,7)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv7,'uncheckeddiv':t_freeTime.isuncheckeddiv7,'teacherdivoff':t_freeTime.isteacherdivoff7,'teacherdivon':t_freeTime.isteacherdivon7,'teacherdivspe':t_freeTime.isteacherdivspe7}">
                                                            </div>
                                                            <div class="smallItem"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,8)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv8,'uncheckeddiv':t_freeTime.isuncheckeddiv8,'teacherdivoff':t_freeTime.isteacherdivoff8,'teacherdivon':t_freeTime.isteacherdivon8,'teacherdivspe':t_freeTime.isteacherdivspe8}">
                                                            </div>
                                                            <div class="layer" ng-init="initTecharList(t_freeTime)"
                                                                title="{{t_freeTime.techar}}"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,0,false)"
                                                                ng-class="{'layerPadding':t_freeTime.hasPadding}">
                                                                {{t_freeTime.techar|limitTo:30}}
                                                            </div>
                                                        </li>
                                                        <li ng-if="timeItem.flag=='1'" class="td1"
                                                            ng-repeat="t_freeTime in timeItem.s_freeTimeList"
                                                            ng-click="student_freeTime.toCheck(t_freeTime)"
                                                            ng-init="initclass(t_freeTime)">
                                                            <div class="smallItem1"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,1)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv1,'uncheckeddiv':t_freeTime.isuncheckeddiv1,'teacherdivoff':t_freeTime.isteacherdivoff1,'teacherdivon':t_freeTime.isteacherdivon1,'teacherdivspe':t_freeTime.isteacherdivspe1}">
                                                            </div>
                                                            <div class="smallItem1"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,2)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv2,'uncheckeddiv':t_freeTime.isuncheckeddiv2,'teacherdivoff':t_freeTime.isteacherdivoff2,'teacherdivon':t_freeTime.isteacherdivon2,'teacherdivspe':t_freeTime.isteacherdivspe2}">
                                                            </div>
                                                            <div class="smallItem1"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,3)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv3,'uncheckeddiv':t_freeTime.isuncheckeddiv3,'teacherdivoff':t_freeTime.isteacherdivoff3,'teacherdivon':t_freeTime.isteacherdivon3,'teacherdivspe':t_freeTime.isteacherdivspe3}">
                                                            </div>
                                                            <div class="smallItem1"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,4)"
                                                                ng-class="{'checkeddiv':t_freeTime.ischeckeddiv4,'uncheckeddiv':t_freeTime.isuncheckeddiv4,'teacherdivoff':t_freeTime.isteacherdivoff4,'teacherdivon':t_freeTime.isteacherdivon4,'teacherdivspe':t_freeTime.isteacherdivspe4}">
                                                            </div>
                                                            <div class="layer1" ng-init="initTecharList(t_freeTime)"
                                                                title="{{t_freeTime.techar}}"
                                                                ng-dblclick="student_freeTime.todelete(t_freeTime,0,false)"
                                                                ng-class="{'layerPadding':t_freeTime.hasPadding}">
                                                                {{t_freeTime.techar|limitTo:30}}
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>

                                                <!--日期表end-->
                                                <div class="col-md-3">
                                                    <div class="table-scrollable"
                                                        style="overflow-y:scroll;height:800px;">
                                                        <table class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr role="row" class="heading">
                                                                    <th width="1%">
                                                                        <input type="checkbox"
                                                                            ng-model="view_student_paike.checkedAll"
                                                                            class="group-checkable">
                                                                    </th>
                                                                    <th width="10%">
                                                                        {{'table_filed_00112'|T}}
                                                                    </th>

                                                                    <th width="10%">
                                                                        {{'table_filed_00148'|T}}
                                                                    </th>
                                                                    <th width="10%">
                                                                        {{'table_filed_00058'|T}}
                                                                    </th>
                                                                    <th width="10%">
                                                                        {{'table_filed_00147'|T}}
                                                                    </th>

                                                                </tr>

                                                            </thead>
                                                            <tbody>
                                                                <tr ng-repeat="d in view_student_paike.dataListzhiding"
                                                                    ng-class-odd="'odd'" ng-class-even="'even'">
                                                                    <td><span> <input type="checkbox"
                                                                                ng-model="d.checked"
                                                                                class="group-checkable" /></span></td>
                                                                    <td>
                                                                        {{d.abbr}}
                                                                    </td>
                                                                    <td>
                                                                        {{d.englishName}}
                                                                    </td>
                                                                    <td>
                                                                        {{d.course_name}}
                                                                    </td>
                                                                    <td>
                                                                        {{d.chineseName}}
                                                                    </td>
                                                                </tr>
                                                                <!--  
                                                    <tr ng-repeat="d in view_student_paike.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                                                        <td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
                                                        <td>
                                                            {{d.abbr}}
                                                        </td>
                                                        <td>
                                                            {{d.englishName}}
                                                        </td>
                                                        <td>
                                                            {{d.course_name}}
                                                        </td>
                                                        <td>
                                                            {{d.chineseName}}
                                                        </td>
                                                    </tr>
                                                    -->

                                                            </tbody>
                                                        </table>
                                                    </div>

                                                    <!--     <div class="row">
                                                <div class="col-md-8 col-sm-12">
                                                    <div class="dataTables_paginate paging_bootstrap_extended">
                                                        <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="view_student_paike.prePage()"
                                                                                            class="btn btn-sm default prev"
                                                                                            title="Prev"><i class="fa fa-angle-left"></i></a><input
                                                                type="text"  ng-model="view_student_paike.searchForm.page.currentPage"
                                                                class="pagination-panel-input form-control input-mini input-inline input-sm"
                                                                maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="view_student_paike.nextPage()"
                                                                                                                             class="btn btn-sm default next"
                                                                                                                             title="Next"><i
                                                                class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{view_student_paike.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                                        </div>
                                                    </div>
                                                    <div class="dataTables_length" ><label><span
                                                            class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="view_student_paike.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                                        <option value="10">10</option>
                                                        <option value="20">20</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                        <option value="150">150</option>
                                                    </select> {{'table_filed_00390'|T}}</label></div>
                                                    <div class="dataTables_info"  role="status" aria-live="polite">
                                                        <span class="seperator">|</span>{{'table_filed_00389'|T}}{{view_student_paike.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                                    </div>
                                                </div>
                                                <div class="col-md-4 col-sm-12">

                                                </div>
                                            </div>-->
                                                </div>
                                                <!--------------------------------------------------------------------------------------------------------------------->






                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



            </div>
        </div>
        <!-- End: life time stats -->
    </div>
</div>
<!-- END PAGE CONTENT-->


<!--{{'tab_0203'|T}}、开始-->
<script type="text/ng-template" id="student_All.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;" >{{'table_filed_00288'|T}}</p>
        <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
        <!--<p class="p2" >  <button class="btn btn-xs  red " ng-click="cancel()">弃学</button></p>-->
    </div>
    <div class="modal-body">
        <form class="form-horizontal" name="form">

            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <!--搜索-->
                        <div class="row">
                            <div class="col-md-4 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00002'|T}}</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control col-md-8" ng-model="student.searchForm.searchItems.chineseName_lk">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00003'|T}}</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control col-md-8" ng-model="student.searchForm.searchItems.englishName_lk">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1 col-sm-12">
                                <button class="btn blue  filter-submit margin-bottom" ng-click="student.search1()" style="padding: 5px 14px;"><i
                                        class="fa fa-search"></i> {{'global_btn_0005'|T}}
                                </button>
                            </div>
                            <div class="col-md-3 col-sm-12">

                            </div>
                        </div>
                        <!--搜索-->
                        <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <th width="1%">
                                        <input type="checkbox" class="group-checkable">
                                    </th>
                                    <th width="1%">
                                        {{'table_filed_00368'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00002'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00042'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00003'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00004'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00006'|T}}
                                    </th>

                                    <th width="10%">
                                        {{'table_filed_00007'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'tab_0701'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'static_words_0101'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00034'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00036'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00298'|T}}
                                      <!--  OC课程状态-->
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00035'|T}}
                                    </th>
                                </tr>

                                </thead>
                                <tbody>
                                <tr ng-repeat="d in student.dataList" ng-class-odd="'odd'" ng-class-even="'even'" ng-click="xuan(d)">
                                    <td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
                                    <td>{{$index+1}}</td>
                                    <td>
                                     <!--   <a href="#/studentxiangxi.html/{{d.id}}">     </a>-->
                                            {{d.chineseName}}

                                    </td>
                                    <td>
                                        {{d.status=="1"&&"table_filed_00417"||d.status=="2"&&"table_filed_00401"||d.status=="3"&&"table_filed_00402"||d.status=="4"&&"table_filed_00403"||d.status=="5"&&"table_filed_00418"|T}}
                                    </td>
                                    <td>
                                        {{d.englishName}}
                                    </td>

                                    <td >
                                        {{d.gender=="1"&&"table_filed_00444"||d.gender=="2"&&"table_filed_00445"|T}}
                                    </td>
                                    <td>
                                        {{d.birthday}}
                                    </td>
                                    <td>
                                        {{d.nationality}}
                                    </td>
                                    <td>
                                       {{d.city_name}}
                                    </td>

                                    <td >
                                        {{d.zone_name}}
                                    </td>

                                    <td>
                                        {{d.source}}
                                    </td>
                                    <td>
                                        {{d.ownerName}}
                                    </td>
                                    <td >
                                        {{d.isOC=="0"&&"table_filed_00409"||d.isOC=="1"&&"table_filed_00408"|T}}
                                    </td>
                                    <td>
                                        {{d.referral}}
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <!--结束table-->
                        <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                        <div class="row">
                            <div class="col-md-8 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_extended">
                                    <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="student.prePage()"
                                                                        class="btn btn-sm default prev"
                                                                        title="Prev"><i class="fa fa-angle-left"></i></a><input
                                            type="number"  ng-model="student.searchForm.page.currentPage"
                                            class="pagination-panel-input form-control input-mini input-inline input-sm"
                                            maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="student.nextPage()"
                                                                                                         class="btn btn-sm default next"
                                                                                                         title="Next"><i
                                            class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{student.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                    </div>
                                </div>
                                <div class="dataTables_length" ><label><span
                                        class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="student.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="150">150</option>
                                </select> {{'table_filed_00390'|T}}</label></div>
                                <div class="dataTables_info"  role="status" aria-live="polite">
                                    <span class="seperator">|</span>{{'table_filed_00389'|T}}{{student.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">

                            </div>
                        </div>
                        <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>

        </form>
    </div>

    <div style="clear: both;"></div>
</script>
<!--{{'tab_0203'|T}}、结束-->

<!--学员排课开始-->
<script type="text/ng-template" id="paike_teacher.html">
    <div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
        <p class="p1" style="font-size: 16px;" >{{'table_filed_00446'|T}}</p>
        <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
        <img ng-if="isCycle==true;" id="recur_icon" src="images/actualiser.png" width="26px" />
        <!--<p class="p2" >  <button class="btn btn-xs  red " ng-click="cancel()">弃学</button></p>-->
    </div>
    <div class="modal-body">
        <form class="form-horizontal" name="form">

            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_aja" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <!--搜索-->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" >{{'table_filed_00064'|T}}<span  ng-class="{required:form.start1.$invalid}">*</span></label>
                                        <div class="col-md-6">
                                            <select class="form-control form-filter input-sm" ng-change="jiaoshi()" ng-model="classSchedules.item.start1" name="start1" required>
                                                
                                                <option value="08">08</option>  <option value="09">09</option>   <option value="10">10</option>
                                                <option value="11">11</option>  <option value="12">12</option>   <option value="13">13</option>
                                                <option value="14">14</option>  <option value="15">15</option>   <option value="16">16</option>
                                                <option value="17">17</option>  <option value="18">18</option>   <option value="19">19</option>
                                                <option value="20">20</option>  <option value="21">21</option>   <option value="22">22</option>
                                            </select>
                                       </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" ><span  ng-class="{required:form.start2.$invalid}">*</span></label>
                                    <div class="col-md-6">
                                        <select class="form-control form-filter input-sm" ng-change="jiaoshi()"  ng-model="classSchedules.item.start2" name="start2"  required>
                                            <option value="00">00</option>  <option value="15">15</option>   <option value="30">30</option>
                                            <option value="45">45</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>


                            <div class="col-md-12">
                                <div class="form-group">
                             <!--       <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > 中文名称：</label>-->
                                    <div class="col-md-2">

                                    </div>
                                    <div class="col-md-1">
                                        <span  ng-class="{required:form.time.$invalid}">*</span>
                                    </div>
                                    <div class="col-md-2">
                                        <input style="margin-left: -10px;" type="radio"  value="1" name="time" required  ng-model="classSchedules.item.time"  />1小时&nbsp;&nbsp;&nbsp;
                                   </div>
									
                                    <div class="col-md-2">
                                      <input style="margin-left: -10px;" type="radio"  value="2"   name="time"  required  ng-model="classSchedules.item.time"/>2小时&nbsp;&nbsp;&nbsp;
                                   </div>
									
                                    <div class="col-md-3">
                                     <input style="margin-left: -10px;" type="radio"  value="3"    name="time"   required  ng-model="classSchedules.item.time" />3小时&nbsp;&nbsp;&nbsp;
                                    </div>
                                    
                                </div>

                        <div class="col-md-12">&nbsp;</div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00067'|T}}<span  ng-class="{required:form.course_id.$invalid}">*</span></label>
                                    <div class="col-md-6">
                                        <select class="form-control form-filter input-sm" ng-options="d.course_id as d.course_name  for d in classSchedules.course_list" required  ng-model="classSchedules.item.course_id"  name="course_id">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00062'|T}}<span  ng-class="{required:form.teacher_id.$invalid}">*</span></label>
                                    <div class="col-md-6">
                                        <select class="form-control form-filter input-sm" ng-options="d.id as d.englishName  for d in classSchedules.teacher_list" required  ng-model="classSchedules.item.teacher_id"  name="teacher_id">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00059'|T}}<span  ng-class="{required:form.teachingWay_id.$invalid}">*</span></label>
                                    <div class="col-md-6">
                                        <select class="form-control form-filter input-sm" required  ng-init="classSchedules.item.teachingWay_id = '上门授课'"   ng-model="classSchedules.item.teachingWay_id" required name="teachingWay_id" ng-options="teachingWay.id as teachingWay.name for teachingWay in classSchedules.item.teachingWayList" name="teachingWay_id">
                                            
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6" ng-if="classSchedules.item.classroom==1">
                                <div class="form-group">
                                    <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'tab_0703'|T}}<span  ng-class="{required:form.classroom_id.$invalid}">*</span></label>
                                    <div class="col-md-6">
                                        <select class="form-control form-filter input-sm" ng-options="d.id as d.name  for d in classSchedules.classroom_list" required  ng-model="classSchedules.item.classroom_id"  name="classroom_id">
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>
                        </div>
                    </div>
                           <!--start 框-->
                            <!--<div ng-if="isCycle==false;" class="portlet light bordered ">-->
                                <!--<div class="row">-->
                                    <!--<div class="col-md-12">-->
                                        <!--<div class="form-group">-->
                                            <!--<div class="col-md-3">-->
                                               <!--&lt;!&ndash; ng-if="kehu.item.teshu==1"  <input style="margin-left: -10px;" type="checkbox"  ng-true-value="1" ng-false_value="0" name="shangshi"   ng-model="kehu.item.teshu"/>&ndash;&gt;特殊排课&nbsp;&nbsp;&nbsp;-->
                                            <!--</div>-->
                                        <!--</div>-->
                                    <!--</div>-->
                                <!--</div>-->
                                <!--<div class="row" >-->
                                        <!--<div class="col-md-12">-->
                                            <!--<div class="form-group">-->
                                                <!--<div class="col-md-1">-->
                                                <!--</div>-->
                                                <!--<div class="col-md-9">-->
                                                    <!--<input style="margin-left: -10px;" type="radio"  value="2" name="courseType"  ng-change="yichangpaike()" ng-model="classSchedules.item.courseType"/>{{'table_filed_00304'|T}}&nbsp;&nbsp;&nbsp;-->

                                                <!--</div>-->
                                            <!--</div>-->
                                        <!--</div>-->

                                      <!--&lt;!&ndash;  <div class="col-md-12">-->
                                            <!--<div class="form-group">-->
                                                <!--<label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > 实际上课日期：</label>-->
                                                <!--<div class="col-md-3">-->
                                                    <!--<input type="text" class="form-control col-md-8" ng-model="student.searchForm.searchItems.chineseName_lk">-->
                                                <!--</div>-->
                                            <!--</div>-->
                                        <!--</div>&ndash;&gt;-->

                                    <!--<div class="col-md-12" >-->
                                        <!--<div class="form-group"  ng-init="$root.settings.utils.initpicker()">-->
                                            <!--<label class="control-label col-md-3">{{'table_filed_00169'|T}}：</label>-->
                                            <!--<div class="col-md-9">-->
                                                <!--<div class="input-group date form_date" >-->
                                                    <!--<input type="text"   ng-model="classSchedules.item.realDate"  name="startDate"   placeholder=""-->
                                                           <!--class="form-control date-picker"    data-date-format="yyyy-mm-dd"/>-->
                                                <!--</div>-->
                                            <!--</div>-->
                                        <!--</div>-->
                                    <!--</div>-->

                                    <!--&lt;!&ndash;    <div class="col-md-12">-->
                                    <!--<div class="form-group">-->
                                        <!--<label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > 开始时间：</label>-->
                                        <!--<div class="col-md-3">-->
                                            <!--<select >-->
                                                <!--<option value="08">08</option>  <option value="09">09</option>   <option value="10">10</option>-->
                                                <!--<option value="11">11</option>  <option value="12">12</option>   <option value="13">13</option>-->
                                                <!--<option value="14">14</option>  <option value="15">15</option>   <option value="16">16</option>-->
                                                <!--<option value="17">17</option>  <option value="18">18</option>   <option value="19">19</option>-->
                                                <!--<option value="20">20</option>  <option value="21">21</option>   <option value="22">22</option>-->
                                            <!--</select>-->
                                        <!--</div>-->
                                        <!--<div class="col-md-3">-->
                                            <!--<select>-->
                                                <!--<option value="00">00</option>  <option value="15">15</option>   <option value="30">30</option>-->
                                                <!--<option value="45">45</option>-->
                                            <!--</select>-->
                                        <!--</div>&ndash;&gt;-->

                                        <!--<div class="col-md-12">-->
                                            <!--<div class="form-group">-->
                                                <!--<label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00064'|T}}:</label>-->
                                                <!--<div class="col-md-3">-->
                                                    <!--<select class="form-control form-filter input-sm"  ng-model="classSchedules.item.start3" name="start3" ng-required="classSchedules.item.courseType=='2'">-->
                                                        <!--<option value="08">08</option>  <option value="09">09</option>   <option value="10">10</option>-->
                                                        <!--<option value="11">11</option>  <option value="12">12</option>   <option value="13">13</option>-->
                                                        <!--<option value="14">14</option>  <option value="15">15</option>   <option value="16">16</option>-->
                                                        <!--<option value="17">17</option>  <option value="18">18</option>   <option value="19">19</option>-->
                                                        <!--<option value="20">20</option>  <option value="21">21</option>   <option value="22">22</option>-->
                                                    <!--</select>-->
                                                <!--</div>-->
                                                <!--<label class="control-label col-md-1" style=" margin-top: 5px;text-align: right;" ></label>-->
                                                <!--<div class="col-md-3">-->
                                                    <!--<select class="form-control form-filter input-sm"  ng-model="classSchedules.item.start4" name="start4" ng-required="classSchedules.item.courseType=='2'" >-->
                                                        <!--<option value="00">00</option>  <option value="15">15</option>   <option value="30">30</option>-->
                                                        <!--<option value="45">45</option>-->
                                                    <!--</select>-->
                                                <!--</div>-->
                                            <!--</div>-->
                                        <!--</div>-->


                                     <!--&lt;!&ndash;   <div class="col-md-3">-->
                                            <!--<input type="text" class="form-control col-md-8" ng-model="student.searchForm.searchItems.chineseName_lk">-->
                                        <!--</div>-->
                                        <!--<div class="col-md-3">-->
                                            <!--<input type="text" class="form-control col-md-8" ng-model="student.searchForm.searchItems.chineseName_lk">-->
                                        <!--</div>&ndash;&gt;-->

                                    <!--<div class="col-md-12">-->
                                        <!--<div class="form-group">-->
                                            <!--&lt;!&ndash;       <label class="control-label col-md-3" style=" margin-top: 5px;text-align: right;" > 中文名称：</label>&ndash;&gt;-->
                                            <!--<div class="col-md-3">-->

                                            <!--</div>-->
                                            <!--<div class="col-md-2">-->
                                                <!--<input style="margin-left: -10px;" type="radio"  value="1" name="shangshi"   ng-model="classSchedules.item.realTime" checked />1小时&nbsp;&nbsp;&nbsp;-->
                                            <!--</div>-->
                                            <!--<div class="col-md-2">-->
                                                <!--<input style="margin-left: -10px;" type="radio"  value="2" name="shangshi"   ng-model="classSchedules.item.realTime" />2小时&nbsp;&nbsp;&nbsp;-->
                                            <!--</div>-->
                                            <!--<div class="col-md-2">-->
                                                <!--<input style="margin-left: -10px;" type="radio"  value="3" name="shangshi"   ng-model="classSchedules.item.realTime"/>3小时&nbsp;&nbsp;&nbsp;-->
                                            <!--</div>-->
                                            <!--<div class="col-md-3">-->

                                            <!--</div>-->
                                        <!--</div>-->
                                    <!--</div>-->
                                        <!--<div class="col-md-12">-->
                                            <!--<div class="form-group">-->
                                                <!--<div class="col-md-1">-->
                                                 <!--</div>-->
                                                <!--<div class="col-md-9">-->
                                                    <!--<input style="margin-left: -10px;" type="radio"  value="3" name="courseType"   ng-model="classSchedules.item.courseType" />{{'table_filed_00305'|T}}&nbsp;&nbsp;&nbsp;-->
                                                <!--</div>-->
                                            <!--</div>-->
                                        <!--</div>-->
                                        <!--<div class="col-md-12">-->
                                            <!--<div class="form-group">-->
                                                <!--<div class="col-md-1">-->
                                                <!--</div>-->
                                                <!--<div class="col-md-9">-->
                                                    <!--<input style="margin-left: -10px;" type="radio"  value="4" name="courseType"   ng-model="classSchedules.item.courseType" />{{'table_filed_00306'|T}}&nbsp;&nbsp;&nbsp;-->
                                                <!--</div>-->
                                            <!--</div>-->
                                        <!--</div>-->
                                <!--</div>-->
                            <!--</div>-->
                        <!--end 框-->
                    </div>
                </div>
        </form>
    </div>

    <div style="clear: both;">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <div class="col-md-3">
                    </div>
                    <div class="col-md-2">
                            <button style="padding: 5px 14px;" type="button" class="btn blue" ng-disabled="form.$invalid"  ng-click="submit()">{{'global_btn_0007'|T}}</button>
                    </div>
                    <div class="col-md-2">
                            <button  style="padding: 5px 14px;" type="button" class="btn blue"  ng-click="cancel()"> {{'global_btn_0013'|T}} </button>
                    </div>
                    <div class="col-md-2">
                      <!--     <button  style="padding: 5px 14px;" type="button" class="btn blue" > 删除 </button>-->
                    </div>
                    <div class="col-md-3">
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<!--{{'tab_0203'|T}}、结束-->


<!--学员排课list-->
<script type="text/ng-template" id="paike_teachers.html">
<div class="modal-header lm_window_head" style= "background-color: #f5f5f5; padding-bottom: 30px;padding-top: 10px; " >
    <p class="p1" style="font-size: 16px;" >{{'table_filed_00446'|T}}</p>
    <p class="p2" >  <button class="btn btn-xs " ng-click="cancel()">{{'global_btn_0013'|T}}</button></p>
    <!--<p class="p2" >  <button class="btn btn-xs  red " ng-click="cancel()">弃学</button></p>-->
</div>
<div class="modal-body">
    <form class="form-horizontal" name="form">
        <div class="portlet-body">
            <div class="table-container">
                <div id="datatable_ajax" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                <div class="table-scrollable" style="overflow-y: auto">
                <table class="table table-striped table-bordered table-hover">
                <thead>
                <tr role="row" class="heading">
                    <th width="1%">
                        <input type="checkbox" class="group-checkable">
                    </th>
                    <th width="10%">
                        {{'table_filed_00143'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00067'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00081'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00064'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00065'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00147'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00148'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00112'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00059'|T}}
                    </th>
                    <th width="10%">
                        {{'tab_0703'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00066'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00166'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00044'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00151'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00167'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00118'|T}}
                    </th>

                    <th width="10%">
                        {{'table_filed_00169'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00170'|T}}
                    </th>
                    <th width="10%">
                        {{'table_filed_00171'|T}}
                    </th>
                </tr>

                </thead>
                <tbody>
                <tr ng-repeat="d in classSche.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                    <td><span ng-if="d.status=='0'"> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
                    <td>
                        {{d.courseType=='1'&&'table_filed_00299'||d.courseType=='2'&&'table_filed_00450'||d.courseType=='3'&&'table_filed_00451'||d.courseType=='4'&&'table_filed_00452'|T}}
                    </td>
                    <td>
                        {{d.course_name}}
                    </td>
                    <td>
                        {{d.scheduledDate}}
                    </td>
                    <td>
                        {{d.startTime}}
                    </td>
                    <td>
                        {{d.endTime}}
                    </td>
                    <td>
                        {{d.us_chineseName}}
                    </td>
                    <td>
                        {{d.us_englishName}}
                    </td>
                    <td>
                        {{d.abbr}}
                    </td>
                    <td ng-if="d.teachingWay.length==1">
                        {{d.teachingWay=='1'&&'table_filed_00447'||d.teachingWay=='2'&&'table_filed_00448'||d.teachingWay=='3'&&'table_filed_00449'||d.teachingWay=='4'&&'table_filed_00535'|T}}
						                
 					</td>
					<td ng-if="d.teachingWay.length>1">
						{{d.teachingWayName}}   
						              
 					</td>
                    <td>
                        {{d.classroom_name}}
                    </td>
                    <td>
                      <!--  {{d.status=='0'&&'初始化'||d.status=='1'&&''||d.status=='2'&&'已删除'}}-->
                        {{d.status=='0'&&'table_filed_00453'||d.status=='1'&&'table_filed_00454'||d.status=='2'&&'global_btn_0004'|T}}
                    </td>
                    <td>
                        {{d.description}}
                    </td>
                    <td>
                        {{d.notes}}
                    </td>
                    <td>
                        {{d.leaveState=='0'&&'table_filed_00500'||d.leaveState=='1'&&'table_filed_00357'||d.leaveState=='2'&&'menu_0302'|T}}
                    </td>
                    <td>
                        {{d.isAuto=='0'&&'table_filed_00409'||d.isAuto=='1'&&'table_filed_00408'|T}}
                    </td>
                    <td>
                        {{d.createTime}}
                    </td>
                    <td>
                        {{d.realDate}}
                    </td>
                    <td>
                        {{d.realStartTime}}
                    </td>
                    <td>
                        {{d.realEndTime}}
                    </td>
                </tr>
                </tbody>
                </table>
                </div>
                    </div>

                </div>
            </div>

    </form>
</div>

<div style="clear: both;">
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <div class="col-md-3">
                </div>
                <div class="col-md-2">
                    <button  style="padding: 5px 14px;" type="button" class="btn blue"  ng-click="cancel()"> {{'global_btn_0013'|T}} </button>
                </div>
                <div class="col-md-2" >
                    <button  style="padding: 5px 14px;" type="button" class="btn blue"   ng-click="add()"> {{'global_btn_0001'|T}} </button>
                </div>
                <div class="col-md-2">
                    <button  style="padding: 5px 14px;" type="button" class="btn blue"  ng-click="shanchu()"> {{'global_btn_0004'|T}}</button>
                </div>
                <div class="col-md-3">
                </div>
            </div>
        </div>
    </div>
</div>
</script>
<!--学员排课list结束-->