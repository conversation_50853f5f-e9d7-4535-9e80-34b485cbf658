
<!-- BEGIN PAGE CONTENT-->
<div class="row">
<div class="col-md-12">
<!-- Begin: life time stats -->
<div class="portlet">
<div class="portlet-title">
    <div class="caption">
        <i class="fa fa-list"></i>{{'table_filed_00378'|T}}
    </div>
    <div class="actions">
        <div class="btn-group btn-group-sm btn-group-solid">

        </div>

    </div>
</div>
<div class="portlet-body">
<div class="table-container">
<div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
<!--搜索-->
<div class="row">
    <div class="col-md-12 col-sm-12 "> <div class="form-group"><div class="col-md-12"></div> </div></div>
    <div class="col-md-4 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-5" style=" margin-top: 5px;text-align: right;" > {{'tab_0701'|T}}：</label>
            <div class="col-md-7">
                <select class="form-control" ng-change="changezone()" ng-options="d.id as d.name for d in cityList" ng-model="keshishengyu.searchForm.searchItems.chengshi">
                    <option value="">--{{'table_filed_00264'|T}}--</option>
                </select>
            </div>
        </div>
    </div>
    <div class="col-md-4 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-5" style=" margin-top: 5px;text-align: right;" > {{'static_words_0101'|T}}：</label>
            <div class="col-md-7">
                <select class="form-control" ng-options="d.id as d.name for d in zoneList"  ng-model="keshishengyu.searchForm.searchItems.xiaoqu">
                    <option value="">--{{'table_filed_00264'|T}}--</option>
                </select>
                <!--   <input type="text" class="form-control col-md-8" ng-model="kebiao.searchForm.searchItems.jiaoxuedian">-->
            </div>
        </div>
    </div>
    <div class="col-md-4 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-5" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00232'|T}}：</label>
            <div class="col-md-7">
                <input type="text" class="form-control col-md-8" ng-model="keshishengyu.searchForm.searchItems.xueshengC">
            </div>
        </div>
    </div>
    <div class="col-md-12 col-sm-12 "> <div class="form-group"><div class="col-md-12"></div> </div></div>
    <div class="col-md-4 col-sm-12 ">
        <div class="form-group">
            <label class="control-label col-md-5" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00234'|T}}：</label>
            <div class="col-md-7">
                <input type="text" class="form-control col-md-8" ng-model="keshishengyu.searchForm.searchItems.xueshengE">
            </div>
        </div>
    </div>
    <div class="col-md-1 col-sm-12"></div>
    <div class="col-md-1 col-sm-12">
        <button class="btn blue  filter-submit margin-bottom" ng-click="keshishengyu.search1()" style="padding: 5px 14px;"><i
                class="fa fa-search"></i> {{'global_btn_0005'|T}}
        </button>
    </div>
    <div class="col-md-1 col-sm-12"></div>
    <div class="col-md-1 col-sm-12 ">
        <button class="btn blue  filter-submit margin-bottom" ng-click="keshishengyu.export()" style="padding: 5px 14px;">
            {{'global_btn_0011'|T}}
        </button>
    </div>
    <div class="col-md-4 col-sm-12"></div>
</div>
<!--搜索-->
<!--开始table-->
<div class="table-scrollable" style="overflow-y: auto">
    <table class="table table-striped table-bordered table-hover">
        <thead>
        <tr role="row" class="heading">
            <th width="1%">
                {{'table_filed_00368'|T}}
            </th>
            <th width="9%"  ng-click="paixu('chengshi',flag)">
                {{'tab_0701'|T}}{{flag=='chengshi1'&&'↑'||flag=='chengshi2'&&'↓'||''}}</span>
            </th>
            <th width="9%" ng-click="paixu('jiaoxuedian',flag)">
                {{'static_words_0101'|T}}{{flag=='jiaoxuedian1'&&'↑'||flag=='jiaoxuedian2'&&'↓'||''}}</span>
            </th>
            <th width="9%"  ng-click="paixu('xuehao',flag)">
                {{'table_filed_00107'|T}}{{flag=='xuehao1'&&'↑'||flag=='xuehao2'&&'↓'||''}}</span>
            </th>
            <th width="9%" ng-click="paixu('xueshengC',flag)">
                {{'table_filed_00144'|T}}{{flag=='xueshengC1'&&'↑'||flag=='xueshengC2'&&'↓'||''}}</span>
            </th>
            <th width="9%" ng-click="paixu('xueshengE',flag)">
                {{'table_filed_00145'|T}}{{flag=='xueshengE1'&&'↑'||flag=='xueshengE2'&&'↓'||''}}</span>
            </th>
            <th width="9%">
                {{'table_filed_00379'|T}}
            </th>
            <th width="9%">
                {{'table_filed_00380'|T}}
            </th>
            <th width="9%">
                {{'table_filed_00381'|T}}
            </th>
            <th width="9%">
                {{'table_filed_00382'|T}}
            </th>
        </tr>

        </thead>
        <tbody>
        <tr ng-repeat="d in keshishengyu.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
            <td>{{$index+1}}</td>
            <td>
                {{d.chengshi}}
            </td>
            <td>
                {{d.jiaoxuedian}}
            </td>
            <td>
                {{d.xuehao}}
            </td>
            <td>
                {{d.xueshengC}}
            </td>
            <td >
                {{d.xueshengE}}
            </td>
            <td>
                {{d.weipai}}
            </td>
            <td>
                {{d.yihao}}
            </td>
            <td>
                {{d.yipai}}
            </td>
            <td>
                {{d.yigou}}
            </td>
        </tr>
        </tbody>
    </table>
</div>
<!--结束table-->
<!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
<div class="row">
    <div class="col-md-8 col-sm-12">
        <div class="dataTables_paginate paging_bootstrap_extended">
            <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="kebiao.prePage()"
                                                class="btn btn-sm default prev"
                                                title="Prev"><i class="fa fa-angle-left"></i></a><input
                    type="number"  ng-model="keshishengyu.searchForm.page.currentPage"
                    class="pagination-panel-input form-control input-mini input-inline input-sm"
                    maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="keshishengyu.nextPage()"
                                                                                 class="btn btn-sm default next"
                                                                                 title="Next"><i
                    class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{keshishengyu.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
            </div>
        </div>
        <div class="dataTables_length" ><label><span
                class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="keshishengyu.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="150">150</option>
        </select> {{'table_filed_00390'|T}}</label></div>
        <div class="dataTables_info"  role="status" aria-live="polite">
            <span class="seperator">|</span>{{'table_filed_00389'|T}}{{keshishengyu.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
        </div>
    </div>
    <div class="col-md-4 col-sm-12">

    </div>
</div>
<!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
</div>
</div>
</div>
</div>
<!-- End: life time stats -->
</div>
</div>
<!-- END PAGE CONTENT-->
