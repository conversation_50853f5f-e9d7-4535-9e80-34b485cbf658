
<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Begin: life time stats -->
        <div class="portlet">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-list"></i>{{'tab_0204'|T}}
                </div>
				 <div class="actions">
                    <div class="btn-group btn-group-sm btn-group-solid">
                        <button type="button" class="btn blue" ng-click="add()" >{{'global_btn_0001'|T}}</button>
                        <button type="button" class="btn red"  ng-click="teacher_freeTime.deletestatus()">{{'global_btn_0004'|T}}</button>
                    </div>

                </div>
            </div>
            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                    <!--开始table表头分{{'table_filed_00386'|T}}以及工具栏-->
                        <div class="row">
                            <div class="col-md-8 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_extended">
                                    <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="teacher_freeTime.prePage()"
                                                                           class="btn btn-sm default prev"
                                                                           title="Prev"><i class="fa fa-angle-left"></i></a><input
                                            type="number"  ng-model="teacher_freeTime.searchForm.page.currentPage"
                                            class="pagination-panel-input form-control input-mini input-inline input-sm"
                                            maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="teacher_freeTime.nextPage()"
                                                                                                       class="btn btn-sm default next"
                                                                                                       title="Next"><i
                                            class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{teacher_freeTime.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                    </div>
                                </div>
                                <div class="dataTables_length" ><label><span
                                        class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="teacher_freeTime.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="150">150</option>
                                </select> {{'table_filed_00390'|T}}</label></div>
                                <div class="dataTables_info"  role="status" aria-live="polite">
                                    <span class="seperator">|</span>{{'table_filed_00389'|T}}{{teacher_freeTime.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">

                            </div>
                        </div>
                    <!--结束table表头分{{'table_filed_00386'|T}}以及工具栏-->
                    <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <th width="1%">
                                        <input type="checkbox" class="group-checkable">
                                    </th>
                                    <th width="1%">
                                        {{'table_filed_00368'|T}}
                                    </th>
									 <th width="10%">
                                         {{'table_filed_00081'|T}}
									</th>
									 <th width="10%">
                                         {{'table_filed_00064'|T}}
									</th>
									 <th width="10%">
                                         {{'table_filed_00065'|T}}
									</th>
									 <th width="10%">
										{{'table_filed_00059'|T}}
									</th>
                                   
                                    <th width="10%">
                                       <div style="width:120px">{{'table_filed_00369'|T}}</div>
                                    </th>
                                </tr>
                                <tr role="row" class="filter">
                                    <td></td>
                                    <td></td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_freeTime.searchForm.searchItems.freeDate_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_freeTime.searchForm.searchItems.startTime_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_freeTime.searchForm.searchItems.endTime_lk">
                                    </td>
									 <td>
                                        <input type="text" class="form-control form-filter input-sm" ng-model="teacher_freeTime.searchForm.searchItems.teachingWay_lk">
                                    </td>
                                    <td>
                                        <button class="btn btn-xs  filter-submit margin-bottom" ng-click="teacher_freeTime.search1()"><i
                                                class="fa fa-search"></i> {{'global_btn_0005'|T}}
                                        </button>
                                        <button class="btn btn-xs  filter-cancel" ng-click="teacher_freeTime.reset()"><i class="fa fa-times"></i> {{'global_btn_0017'|T}}
                                        </button>
                                    </td>

                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="d in teacher_freeTime.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                                    <td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>
                                    <td>{{$index+1}}</td>
									 <td>
									 {{d.freeDate}}
                                    </td>
									 <td>
									 {{d.startTime}}
                                    </td>
									 <td>
									 {{d.endTime}}
                                    </td>
									 <td>
									 {{d.teachingWay}}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-xs">
                                            <button type="button" class="btn " ng-click="edit(d)">{{'global_btn_0003'|T}}</button>
                                            <button type="button" class="btn " ng-click="teacher_freeTime.deleteData(d)">{{'global_btn_0004'|T}}</button>
                                        </div>
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                    <!--结束table-->
                    <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    <div class="row">
                        <div class="col-md-8 col-sm-12">
                            <div class="dataTables_paginate paging_bootstrap_extended">
                                <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="teacher_freeTime.prePage()"
                                                                    class="btn btn-sm default prev"
                                                                    title="Prev"><i class="fa fa-angle-left"></i></a><input
                                        type="number"  ng-model="teacher_freeTime.searchForm.page.currentPage"
                                        class="pagination-panel-input form-control input-mini input-inline input-sm"
                                        maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="teacher_freeTime.nextPage()"
                                                                                                     class="btn btn-sm default next"
                                                                                                     title="Next"><i
                                        class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{teacher_freeTime.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                </div>
                            </div>
                            <div class="dataTables_length" ><label><span
                                    class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="teacher_freeTime.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="150">150</option>
                            </select> {{'table_filed_00390'|T}}</label></div>
                            <div class="dataTables_info"  role="status" aria-live="polite">
                                <span cla{{'table_filed_00389'|T}}="seperator">|</span>{{'table_filed_00387'|T}}计{{teacher_freeTime.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12">

                        </div>
                    </div>
                    <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>
        </div>
        <!-- End: life time stats -->
    </div>
</div>
<!-- END PAGE CONTENT-->
