

<style type="text/css">

    td div{
        padding: 2px;
    }
    .fontDiv{
        font-size: 14px;
        font-weight: 600;
    }
    .fixedTop{
        position: fixed;
        top: 0px;
        width: 110%;
        background-color: #f1f3f9;
        height: 41px;
    }
    .fixedBottom{
        position: fixed;
        bottom: 0px;
        width: 110%;
        background-color: #f1f3f9;
        height: 60px;
    }
    .form-control{
        font-size: 13px;
    }
    a:hover,a:focus{
        text-decoration: none;
        outline: none;
    }
    #accordion:before{
        content: "";
        width: 2px;
        background: #02c4ff;
        position: absolute;
        top: 35px;
        left: 27px;
        bottom: 20px;
    }
    #accordion .panel{
        border: none;
        border-radius: 0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        margin: 0 10px 12px 40px;
        position: relative;
    }
    #accordion .panel:before{
        content: "";
        width: 2px;
        height: 100%;
        /*background: linear-gradient(to bottom, #688e26 0%,#ff816a 100%);*/
        position: absolute;
        top: 0;
        left: -2px;
    }
    #accordion .panel-heading{
        padding: 0;
        background: #fff;
        position: relative;
    }
    #accordion .panel-heading:before{
        content: "";
        width: 15px;
        height: 15px;
        border-radius: 50px;
        background: #fff;
        border: 1px solid #550527;
        position: absolute;
        top: 50%;
        left: -35px;
        transform: translateY(-50%);
    }
    #accordion .panel-title a{
        display: block;
        padding: 15px 55px 15px 10px;
        font-size: 14px;
        font-weight: 600;
        color: #333333;
        border: none;
        margin: 0;
        position: relative;
    }
    #accordion .panel-title a:before,
    #accordion .panel-title a.collapsed:before{
        content: "\f068";
        font-family: fontawesome;
        width: 25px;
        height: 25px;
        line-height: 25px;
        border-radius: 50%;
        font-size: 15px;
        font-weight: normal;
        color: #688e26;
        text-align: center;
        border: 1px solid #688e26;
        position: absolute;
        top: 50%;
        right: 25px;
        transform: translateY(-50%);
        transition: all 0.5s ease 0s;
    }
    #accordion .panel-title a.collapsed:before{ content: "\f067"; }
    #accordion .panel-body{
        padding: 0 30px 15px;
        border: none;
        font-size: 14px;
        color: #305275;
        line-height: 28px;
    }
    .shengyu{
        height: 600px;
    }
    .fontGouOrange{
        padding-left: 20px;
        font-size: 12px;
        color: #ffc000;
    }

</style>
<div  class="visible-xs">
    <!--搜索-->
    <div ng-hide="pageDiv != 'sousuo'" class="row fixedTop" style="height: 100%; ">
        <!-- 头 -->
        <div class="row " >
            <div class="col-xs-6 " ng-click=" redirectClick('leirong')" style="margin: 10px;"><i class="fa fa-chevron-left font-dark"></i></div>
        </div>
        <!-- 搜索条件 -->
        <div class="row" style="margin-top: 25px">
            <!--城市-->
            <div class="row form-group">
                <label class="control-label col-xs-3" style=" margin-top: 5px;text-align: right;" > {{'tab_0701'|T}}</label>
                <div class="col-xs-7" ng-init="$scroot.settings.utils.initpicker()">
                    <select class="form-control" ng-model="view_attendanceBook.searchForm.searchItems.approvalStatus_eq">
                        <option value="">--{{'table_filed_00264'|T}}--</option>
                        <option value="0">{{'table_filed_00472'|T}}</option>
                        <option value="1">{{'table_filed_00493'|T}}</option>
                        <option value="2">{{'global_btn_0015'|T}}</option>
                        <option value="3">{{'table_filed_00464'|T}}</option>
                    </select>
                </div>
            </div>
            <!--校区-->
            <div class="row form-group">
                <label class="control-label col-xs-3" style=" margin-top: 5px;text-align: right;" > {{'tab_0702'|T}}</label>
                <div class="col-xs-7" ng-init="$root.settings.utils.initpicker()">
                    <select class="form-control" ng-model="view_attendanceBook.searchForm.searchItems.approvalStatus_eq">
                        <option value="">--{{'table_filed_00264'|T}}--</option>
                        <option value="0">{{'table_filed_00472'|T}}</option>
                        <option value="1">{{'table_filed_00493'|T}}</option>
                        <option value="2">{{'global_btn_0015'|T}}</option>
                        <option value="3">{{'table_filed_00464'|T}}</option>
                    </select>
                </div>
            </div>
            <!--开始时间-->
            <div class="row form-group">
                <label class="control-label col-xs-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00064'|T}}</label>
                <div class="col-xs-7">
                    <input type="text" class="form-control col-md-8 date-picker" data-date-format="yyyy-mm-dd" ng-model="view_attendanceBook.searchForm.searchItems.scheduledDate_start">
                </div>
            </div>
            <!--结束时间-->
            <div class="row form-group">
                <label class="control-label col-xs-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00065'|T}}</label>
                <div class="col-xs-7">
                    <input type="text" class="form-control col-md-8 date-picker" data-date-format="yyyy-mm-dd" ng-model="view_attendanceBook.searchForm.searchItems.scheduledDate_start">
                </div>
            </div>
            <!--学生-->
            <div class="row form-group">
                <label class="control-label col-xs-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00048'|T}}</label>
                <div class="col-xs-7" ng-init="$root.settings.utils.initpicker()">
                    <select class="form-control" ng-model="view_attendanceBook.searchForm.searchItems.approvalStatus_eq">
                        <option value="">--{{'table_filed_00264'|T}}--</option>
                        <option value="0">{{'table_filed_00472'|T}}</option>
                        <option value="1">{{'table_filed_00493'|T}}</option>
                        <option value="2">{{'global_btn_0015'|T}}</option>
                        <option value="3">{{'table_filed_00464'|T}}</option>
                    </select>
                </div>
            </div>
            <!--老师-->
            <div class="row form-group">
                <label class="control-label col-xs-3" style=" margin-top: 5px;text-align: right;" > {{'table_filed_00062'|T}}</label>
                <div class="col-xs-7" ng-init="$root.settings.utils.initpicker()">
                    <select class="form-control" ng-model="view_attendanceBook.searchForm.searchItems.approvalStatus_eq">
                        <option value="">--{{'table_filed_00264'|T}}--</option>
                        <option value="0">{{'table_filed_00472'|T}}</option>
                        <option value="1">{{'table_filed_00493'|T}}</option>
                        <option value="2">{{'global_btn_0015'|T}}</option>
                        <option value="3">{{'table_filed_00464'|T}}</option>
                    </select>
                </div>
            </div>
            <!--提交-->
            <div class="row form-group">
                <div class=" col-xs-offset-5">
                    <button class="btn blue  filter-submit margin-bottom" ng-click="view_attendanceBook.search1()" style="padding: 5px 14px;">
                        <i class="fa fa-search"></i> {{'global_btn_0005'|T}}
                    </button>
                </div>
            </div>
        </div>
        <div class="clear"></div>
        <!--尾-->
        <div class="row fixedBottom">
            <div class="col-xs-3"></div>
            <div class="col-xs-4" style="padding: 10px">
                <div><img  src="../ng/images/phone/qiandaoON.png"/></div>
                <div style="padding-top: 3px;color: #157efb;">签到</div>
            </div>
            <div class="col-xs-4" style="padding: 10px">
                <a href="#/zongkebiao.html">
                    <div><img width="24px" src="../ng/images/phone/kechengbiaoOFF.png"/></div>
                    <div style="padding-top: 3px;color: #919191;">课表</div>
                </a>
            </div>
            <div class="col-xs-1"></div>
        </div>
        <!-- End: life time stats -->
    </div>

    <!--内容-->
    <div ng-hide="pageDiv != 'leirong'" class="row">

        <!-- 头 -->
        <div class="row fixedTop" >
            <div  class="col-xs-6 " style="margin: 10px;"><a href="#" ng-click=" redirectClick('sousuo')"><img src="../ng/images/phone/loudou.png"/></a></div>
            <div class="col-xs-5 " style="margin-top: 10px;">
                <div class="form-group">
                    <label class="control-label col-xs-6" style="text-align: right; font-size: 18px;padding-right: 0px;" >All</label>
                    <div class="col-xs-6" ng-click="kaiguanClick(kaiguan)">
                        <img ng-if="kaiguan" src="../ng/images/phone/KGOFF.png"/>
                        <img ng-if="!kaiguan" src="../ng/images/phone/KGON.png"/>
                    </div>
                </div>
            </div>
        </div>
        <!-- 表格 -->
        <div class="clear"></div>
        <div class="row" style="margin-top: 11px; margin-bottom:200%;">
            <div class="table-scrollable" style="overflow-y: auto">
                <table class="table table-striped table-hover">
                    <tbody>
                    <!--<tr ng-repeat="d in view_attendanceBook.dataList" ng-class-odd="'odd'" ng-class-even="'even'">-->
                    <!--<td>-->
                    <!--<div>08:00</div>1-->
                    <!--<div>10:00</div>-->
                    <!--</td>-->
                    <!--<td>Alice Zheng</td>-->
                    <!--<td>Rm 1</td>-->
                    <!--<td>//</td>-->
                    <!--</tr>-->
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:01</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                            <img style="margin-top:  10px; width: 30px;" src="../ng/images/phone/GOUgreen2.png"/>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                            <img style="margin-top:  10px;width: 30px;" src="../ng/images/phone/GOUOrange2.png"/>
                            <div class="fontGouOrange">教师迟到</div>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                            <img ng-click=" redirectClick('yichang')" style="margin-top:  10px; width: 30px;" src="../ng/images/phone/GOU2.png"/>
                            <img style="margin-top:  8px; " src="../ng/images/phone/CHA.png"/>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 1%"></td><!--占位-->
                        <td style="width: 10%">
                            <div>08:00</div>
                            <div>10:00</div>
                        </td>
                        <td style="width: 2px;padding-left: 0px;padding-right:  0px;">
                            <img src="../ng/images/phone/line.png"/></td>
                        <td style="width: 35%">
                            <div class="fontDiv">Alice Zheng</div>
                            <div>Gavin Herbertson</div>
                        </td>
                        <td style="width: 23%">
                            <div class="fontDiv">Rm 1</div>
                            <div>English</div>
                        </td>
                        <td>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="clear"></div>
        <!--尾-->
        <div class="row fixedBottom">
            <div class="col-xs-3"></div>
            <div class="col-xs-4" style="padding: 10px">
                <div><img  src="../ng/images/phone/qiandaoON.png"/></div>
                <div style="padding-top: 3px;color: #157efb;">签到</div>
            </div>
            <div class="col-xs-4" style="padding: 10px">
                <a href="#/zongkebiao.html">
                    <div><img width="24px" src="../ng/images/phone/kechengbiaoOFF.png"/></div>
                    <div style="padding-top: 3px;color: #919191;">课表</div>
                </a>
            </div>
            <div class="col-xs-1"></div>
        </div>
        <!-- End: life time stats -->
    </div>

    <!--异常签到-->
    <div ng-hide="pageDiv != 'yichang'" class="row" style="height: 100%; ">
        <!-- 头 -->
        <div class="row fixedTop " >
            <div class="col-xs-5 " style="margin: 10px;"><i ng-click=" redirectClick('leirong')" class="fa fa-chevron-left font-dark"></i></div>
            <div style="padding: 10px; font-size: 15px;font-weight: 600">异常签到</div>
        </div>
        <div class="clear"></div>
        <!--异常签到-->
        <div class="container" style="margin-top: 30px;">
            <div class="row">
                <div class="col-md-offset-3 col-md-6">
                    <div class="panel-group" style="" id="accordion" role="tablist" aria-multiselectable="true">
                        <!--教师迟到-->
                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab" id="headingOne">
                                <h4 class="panel-title">
                                    <a role="button" ng-class="{true: 'collapsed', false: ''}[collapseOne]" data-toggle="collapse" data-parent="#accordion" href="collapseOne" ng-click="collapseFun('one',collapseOne)" aria-expanded="true" aria-controls="collapseOne">
                                        {{'table_filed_00179'|T}}
                                    </a>
                                </h4>
                            </div>
                            <div  ng-if="!collapseOne" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne">
                                <div class="panel-body">
                                    <form class="form-horizontal" name="form">
                                        <div class="row">
                                            <!--二分一单元 一单元 ng-if="attendanceBook.baseData.teacherType!='1'&&attendanceBook.item.attendanceStatus=='21'"-->
                                            <div class=" form-group">
                                                <div class="col-xs-12">
                                                    <div class="mt-radio-inline">
                                                        <label  class="mt-radio  control-label col-xs-6" style="padding-left: 0px;">
                                                            <div class="col-xs-3">
                                                                <input type="radio" ng-model="attendanceBook.item.units" value="half" name="units" />
                                                            </div>
                                                            <div class="col-xs-6" style="padding-top: 6px;">1/2&nbsp;Unit</div>
                                                        </label>
                                                        <label class="mt-radio control-label  col-xs-6" style="padding-left: 0px;">
                                                            <div class="col-xs-3">
                                                                <input type="radio" ng-model="attendanceBook.item.units" value="one" name="units" />
                                                            </div>
                                                            <div class="col-xs-6" style="padding-top: 6px;">1&nbsp;Unit</div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--学员耗课-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00185'|T}}<span ng-class="{required:form.consumedClass.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.consumedClass" name="consumedClass" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--学员获赠-->
                                            <div class="col-xs-6" ng-if="attendanceBook.item.attendanceStatus!='11' &&attendanceBook.item.attendanceStatus!='12'">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00186'|T}}<span ng-class="{required:form.givenClass.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.givenClass" name="givenClass" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--教师工资-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00187'|T}}<span ng-class="{required:form.wage.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wage" name="wage" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--倒扣工资(全职)-->
                                            <div class="col-xs-6" ng-if="attendanceBook.item.attendanceStatus!='11' &&attendanceBook.item.attendanceStatus!='12' &&attendanceBook.baseData.teacherType=='1'">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00188'|T}} <span ng-class="{required:form.wageDeduction.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group input-group-sm ">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wageDeduction2" name="wageDeduction2" required/>
                                                            <span class="input-group-addon">
                                                            <select type="text" ng-model="attendanceBook.item.currency_id" required name="currency_id" ng-options="currency.id as currency.name for currency in attendanceBook.item.currencyList" >
                                                            </select>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--倒扣工资-->
                                            <div class="col-xs-6" ng-if="attendanceBook.item.attendanceStatus!='11' &&attendanceBook.item.attendanceStatus!='12'&& attendanceBook.baseData.teacherType!='1'">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00188'|T}}<span ng-class="{required:form.wageDeduction.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wageDeduction" name="wageDeduction" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--修改原因-->
                                            <div class="col-xs-12">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2">{{'table_filed_00178'|T}}</label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.reason" name="reason"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00189'|T}}<input type="checkbox" ng-model="attendanceBook.item.hasIncidental" ng-true-value="true" ng-false-value="false" name="hasIncidental"/></label>
                                                    <div class="col-md-8" ng-if="attendanceBook.item.hasIncidental">
                                                        <input  type="text" class="form-control" ng-model="attendanceBook.item.incidental" name="incidental"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费摘要-->
                                            <div class="col-xs-12" ng-if="attendanceBook.item.hasIncidental">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2"> {{'table_filed_00346'|T}}<!--杂费摘要--></label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.description" name="description"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                    <div class="row">
                                        <div class=" col-xs-offset-9">
                                            <button class="btn green"  style="padding: 1px 7px;" ng-disabled="form.$invalid" ng-click="submit()">{{'global_btn_0012'|T}}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--教师缺勤-->
                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab" id="headingTwo">
                                <h4 class="panel-title">
                                    <a role="button" ng-class="{true: 'collapsed', false: ''}[collapseTwo]" data-toggle="collapse" data-parent="#accordion" href="collapseTwo" ng-click="collapseFun('two',collapseTwo)" aria-expanded="false" aria-controls="collapseTwo">
                                        {{'table_filed_00180'|T}}
                                    </a>
                                </h4>
                            </div>
                            <div  ng-if="!collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">
                                <div class="panel-body">
                                    <form class="form-horizontal" name="form">
                                        <div class="row">
                                            <!--学员耗课-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00185'|T}}<span ng-class="{required:form.consumedClass.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.consumedClass" name="consumedClass" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--学员获赠-->
                                            <div class="col-xs-6" ng-if="attendanceBook.item.attendanceStatus!='11' &&attendanceBook.item.attendanceStatus!='12'">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00186'|T}}<span ng-class="{required:form.givenClass.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.givenClass" name="givenClass" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--教师工资-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00187'|T}}<span ng-class="{required:form.wage.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wage" name="wage" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--倒扣工资(全职)-->
                                            <div class="col-xs-6" ng-if="attendanceBook.item.attendanceStatus!='11' &&attendanceBook.item.attendanceStatus!='12' &&attendanceBook.baseData.teacherType=='1'">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00188'|T}} <span ng-class="{required:form.wageDeduction.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group input-group-sm ">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wageDeduction2" name="wageDeduction2" required/>
                                                            <span class="input-group-addon">
                                                            <select type="text" ng-model="attendanceBook.item.currency_id" required name="currency_id" ng-options="currency.id as currency.name for currency in attendanceBook.item.currencyList" >
                                                            </select>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--倒扣工资-->
                                            <div class="col-xs-6" ng-if="attendanceBook.item.attendanceStatus!='11' &&attendanceBook.item.attendanceStatus!='12'&& attendanceBook.baseData.teacherType!='1'">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00188'|T}}<span ng-class="{required:form.wageDeduction.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wageDeduction" name="wageDeduction" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--修改原因-->
                                            <div class="col-xs-12">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2">{{'table_filed_00178'|T}}</label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.reason" name="reason"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00189'|T}}<input type="checkbox" ng-model="attendanceBook.item.hasIncidental" ng-true-value="true" ng-false-value="false" name="hasIncidental"/></label>
                                                    <div class="col-md-8" ng-if="attendanceBook.item.hasIncidental">
                                                        <input  type="text" class="form-control" ng-model="attendanceBook.item.incidental" name="incidental"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费摘要-->
                                            <div class="col-xs-12" ng-if="attendanceBook.item.hasIncidental">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2"> {{'table_filed_00346'|T}}<!--杂费摘要--></label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.description" name="description"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                    <div class="row">
                                        <div class=" col-xs-offset-9">
                                            <button class="btn green"  style="padding: 1px 7px;" ng-disabled="form.$invalid" ng-click="submit()">{{'global_btn_0012'|T}}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--学员迟到-->
                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab" id="headingTree">
                                <h4 class="panel-title">
                                    <a role="button" ng-class="{true: 'collapsed', false: ''}[collapseTree]" data-toggle="collapse" data-parent="#accordion" href="collapseTree" ng-click="collapseFun('tree',collapseTree)" aria-expanded="false" aria-controls="collapseTree">
                                        {{'table_filed_00181'|T}}
                                    </a>
                                </h4>
                            </div>
                            <div  ng-if="!collapseTree" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTree">
                                <div class="panel-body">
                                    <form class="form-horizontal" name="form">
                                        <div class="row">
                                            <!--学员耗课-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00185'|T}}<span ng-class="{required:form.consumedClass.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.consumedClass" name="consumedClass" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--教师工资-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00187'|T}}<span ng-class="{required:form.wage.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wage" name="wage" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                           <!--修改原因-->
                                            <div class="col-xs-12">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2">{{'table_filed_00178'|T}}</label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.reason" name="reason"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00189'|T}}<input type="checkbox" ng-model="attendanceBook.item.hasIncidental" ng-true-value="true" ng-false-value="false" name="hasIncidental"/></label>
                                                    <div class="col-md-8" ng-if="attendanceBook.item.hasIncidental">
                                                        <input  type="text" class="form-control" ng-model="attendanceBook.item.incidental" name="incidental"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费摘要-->
                                            <div class="col-xs-12" ng-if="attendanceBook.item.hasIncidental">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2"> {{'table_filed_00346'|T}}<!--杂费摘要--></label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.description" name="description"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                    <div class="row">
                                        <div class=" col-xs-offset-9">
                                            <button class="btn green"  style="padding: 1px 7px;" ng-disabled="form.$invalid" ng-click="submit()">{{'global_btn_0012'|T}}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--学员缺勤-->
                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab" id="headingFour">
                                <h4 class="panel-title">
                                    <a role="button" ng-class="{true: 'collapsed', false: ''}[collapseFour]" data-toggle="collapse" data-parent="#accordion" href="collapseFour" ng-click="collapseFun('four',collapseFour)" aria-expanded="false" aria-controls="collapseFour">
                                        {{'table_filed_00182'|T}}
                                    </a>
                                </h4>
                            </div>
                            <div  ng-if="!collapseFour" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingFour">
                                <div class="panel-body">
                                    <form class="form-horizontal" name="form">
                                        <div class="row">
                                            <!--学员耗课-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00185'|T}}<span ng-class="{required:form.consumedClass.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.consumedClass" name="consumedClass" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                           <!--教师工资-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00187'|T}}<span ng-class="{required:form.wage.$invalid}">*</span></label>
                                                    <div class="col-md-8">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control " ng-model="attendanceBook.item.wage" name="wage" required/>
                                                            <span class="input-group-addon">{{'table_filed_00351'|T}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--修改原因-->
                                            <div class="col-xs-12">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2">{{'table_filed_00178'|T}}</label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.reason" name="reason"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费-->
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <label class="control-label col-md-4">{{'table_filed_00189'|T}}<input type="checkbox" ng-model="attendanceBook.item.hasIncidental" ng-true-value="true" ng-false-value="false" name="hasIncidental"/></label>
                                                    <div class="col-md-8" ng-if="attendanceBook.item.hasIncidental">
                                                        <input  type="text" class="form-control" ng-model="attendanceBook.item.incidental" name="incidental"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--杂费摘要-->
                                            <div class="col-xs-12" ng-if="attendanceBook.item.hasIncidental">
                                                <div class="form-group">
                                                    <label class="control-label col-md-2"> {{'table_filed_00346'|T}}<!--杂费摘要--></label>
                                                    <div class="col-md-8">
                                                        <textarea class="form-control " ng-model="attendanceBook.item.description" name="description"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                    <div class="row">
                                        <div class=" col-xs-offset-9">
                                            <button class="btn green"  style="padding: 1px 7px;" ng-disabled="form.$invalid" ng-click="submit()">{{'global_btn_0012'|T}}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </form>
        </div>
        <div class="shengyu"></div>
        <!--尾-->
        <div class="row fixedBottom">
            <div class="col-xs-3"></div>
            <div class="col-xs-4" style="padding: 10px">
                <div><img  src="../ng/images/phone/qiandaoON.png"/></div>
                <div style="padding-top: 3px;color: #157efb;">签到</div>
            </div>
            <div class="col-xs-4" style="padding: 10px">
                <a href="#/zongkebiao.html">
                    <div><img width="24px" src="../ng/images/phone/kechengbiaoOFF.png"/></div>
                    <div style="padding-top: 3px;color: #919191;">课表</div>
                </a>
            </div>
            <div class="col-xs-1"></div>
        </div>
    </div>
</div>