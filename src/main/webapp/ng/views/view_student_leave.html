
<!-- BEGIN PAGE CONTENT-->
<div class="row">
    <div class="col-md-12">
        <!-- Begin: life time stats -->
        <div class="portlet">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-list"></i>{{'table_filed_00504'|T}}
                </div>
				 <div class="actions">
                    <div class="btn-group btn-group-sm btn-group-solid">
                        <!--<button type="button" class="btn blue" ng-click="add()" >审核通过</button>-->
                        <!--<button type="button" class="btn blue" ng-click="add()" >驳回</button>-->
                    </div>
                </div>
            </div>
            <div class="portlet-body">
                <div class="table-container">
                    <div id="datatable_ajax_wrapper" class="dataTables_wrapper dataTables_extended_wrapper no-footer">
                        <!--搜索-->
                        <div class="row">
                            <div class="col-md-3 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" >{{'table_filed_00177'|T}}</label>
                                    <div class="col-md-8">
                                        <select class="form-control" ng-model="view_student_leave.searchForm.searchItems.approvalStatus_eq">
                                            <option value="">--{{'table_filed_00264'|T}}--</option>
                                            <option value="0">{{'table_filed_00472'|T}}</option>
                                            <option value="1">{{'table_filed_00493'|T}}</option>
                                            <option value="2">{{'global_btn_0015'|T}}</option>
                                            <option value="3">{{'table_filed_00464'|T}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" >{{'table_filed_00327'|T}}</label>
                                    <div class="col-md-8" ng-init="$root.settings.utils.initpicker()">
                                        <input type="text" class="form-control date-picker" data-date-format="yyyy-mm-dd"
                                               placeholder="yyyy-mm-dd" ng-model="view_student_leave.searchForm.searchItems.leaveTimeStart">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12 ">
                                <div class="form-group">
                                    <div class="col-md-8" ng-init="$root.settings.utils.initpicker()">
                                        <input type="text" class="form-control date-picker" data-date-format="yyyy-mm-dd"
                                               placeholder="yyyy-mm-dd" ng-model="view_student_leave.searchForm.searchItems.leaveTimeEnd">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12"><div class="form-group"></div></div>
                            <div class="col-md-3 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" >{{'table_filed_00002'|T}}</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control col-md-8" ng-model="view_student_leave.searchForm.searchItems.chineseName_lk">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12 ">
                                <div class="form-group">
                                    <label class="control-label col-md-4" style=" margin-top: 5px;text-align: right;" >{{'table_filed_00003'|T}}</label>
                                    <div class="col-md-8" >
                                        <input type="text" class="form-control col-md-8" ng-model="view_student_leave.searchForm.searchItems.englishName_lk">
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <div class="col-md-8">
                                        <button class="btn blue  filter-submit margin-bottom" ng-click="view_student_leave.searchN()" style="padding: 5px 14px;"><i
                                                class="fa fa-search"></i> {{'global_btn_0005'|T}}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                        <!--搜索-->
                    <!--开始table-->
                        <div class="table-scrollable" style="overflow-y: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr role="row" class="heading">
                                    <!--<th width="1%">-->
                                        <!--<input type="checkbox" class="group-checkable">-->
                                    <!--</th>-->
                                    <th width="1%">
                                        {{'table_filed_00368'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00107'|T}}
                                    </th>
                                    <th width="10%">
                                        {{'table_filed_00002'|T}}
                                    </th>
                                    <th width="5%">
                                        {{'table_filed_00003'|T}}
                                    </th>
                                    <th width="10%">
                                         {{'static_words_0101'|T}}
                                    </th>
									 <th width="10%">
										<!--办理时间 -->
                                         {{'table_filed_00093'|T}}
									</th>
                                    <!--<th width="10%">-->
                                        <!--请假开始时间-->
                                    <!--</th>-->
                                    <!--<th width="10%">-->
                                        <!--请假结束时间-->
                                    <!--</th>-->
                                    <th width="10%">
                                        <!--请假时间-->
                                        {{'table_filed_00327'|T}}
                                    </th>
									 <th width="5%">
                                         {{'table_filed_00337'|T}}
						        <!--是否提前12小时-->
									</th>
                                    <th width="5%">
                                        {{'重新排课'|T}}
                               <!--是否提前12小时-->
                                   </th>
                                   <th width="5%">
                                            {{'table_filed_00154'|T}}
                                <!--是否提前12小时-->
                                    </th>

									 <th width="5%">
                                         {{'table_filed_00177'|T}}
									</th>


                                    <th width="5%">
										<!--审批状态-->
                                         {{'table_filed_00650'|T}}
									</th>

                                    <th width="5%">
										<!--审批状态-->
                                         {{'table_filed_00251'|T}}
									</th>

                                    <th width="5%">
                                         {{'table_filed_00039'|T}}
                                    </th>
                                    <th width="10%">
                                         {{'table_filed_00090'|T}}
                                    </th>
                                    <th width="5%">
                                       <div style="width:120px">{{'table_filed_00369'|T}}</div>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="d in view_student_leave.dataList" ng-class-odd="'odd'" ng-class-even="'even'">
                                    <!--<td><span> <input type="checkbox" ng-model="d.checked" class="group-checkable"/></span></td>-->
                                    <td>{{$index+1}}</td>
                                    <td>
                                        {{d.code}}
                                    </td>
                                    <td>
                                        {{d.chineseName}}
                                    </td>
                                    <td>
                                        {{d.englishName}}
                                    </td>
                                    <td>
                                        {{d.zone_name}}
                                    </td>
									 <td>
									 {{d.handleTime}}
                                    </td>
                                    <!--<td>-->
                                        <!--{{d.startTime}}-->
                                    <!--</td>-->
                                    <!--<td>-->
                                        <!--{{d.endTime}}-->
                                    <!--</td>-->
                                    <td>
                                        {{d.leaveDate}}&nbsp;{{d.startTime}}-{{d.endTime}}
                                    </td>
                                    <td>
                                        {{d.isInadvance=='1'&&'table_filed_00408'||d.isInadvance=='0'&&'table_filed_00409'|T}}
                                    </td>
                                    <td>
                                        {{d.reschedule=='1'&&'table_filed_00666'||d.reschedule=='2'&&'table_filed_00667'||d.reschedule=='3'&&'table_filed_00668'|T}}
                                    </td>
                                    <td>
                                        {{d.deductionClass}}
                                    </td>
                                    <td>
                                        {{d.approvalStatus=='0'&&'table_filed_00472'||d.approvalStatus=='1'&&'table_filed_00493'||d.approvalStatus=='2'&&'global_btn_0015'||d.approvalStatus=='3'&&'table_filed_00464'|T}}
                                    </td>

                                    <td>
                                        {{d.teacherEnglishName}}
                                    </td>
                                    <td>
                                        {{d.consultant}}
                                    </td>

                                    <td>
                                     {{d.tutor_englishName}}
                                    </td>
                                    <td>
                                     {{d.reason}}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-xs">
                                            <button type="button" class="btn " ng-click="detail(d)">{{'global_btn_0002'|T}}</button>
                                            <button ng-if="d.approvalStatus=='1'" type="button" class="btn " ng-click="view_student_leave.approvalData(d,'2')">{{'global_btn_0015'|T}} </button>
                                            <button ng-if="d.approvalStatus=='1'" type="button" class="btn " ng-click="view_student_leave.approvalData(d,'3')">{{'table_filed_00464'|T}} </button>
                                        </div>
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                    <!--结束table-->
                    <!--开始table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    <div class="row">
                        <div class="col-md-8 col-sm-12">
                            <div class="dataTables_paginate paging_bootstrap_extended">
                                <div class="pagination-panel"> {{'table_filed_00385'|T}} <a ng-click="view_student_leave.prePage()"
                                                                    class="btn btn-sm default prev"
                                                                    title="Prev"><i class="fa fa-angle-left"></i></a><input
                                        type="number"  ng-model="view_student_leave.searchForm.page.currentPage"
                                        class="pagination-panel-input form-control input-mini input-inline input-sm"
                                        maxlenght="5" style="text-align:center; margin: 0 5px;"><a   ng-click="view_student_leave.nextPage()"
                                                                                                     class="btn btn-sm default next"
                                                                                                     title="Next"><i
                                        class="fa fa-angle-right"></i></a> {{'table_filed_00386'|T}},&nbsp;&nbsp;&nbsp;{{'table_filed_00387'|T}}<span class="pagination-panel-total">{{view_student_leave.searchForm.page.totalPages}}</span>{{'table_filed_00386'|T}}
                                </div>
                            </div>
                            <div class="dataTables_length" ><label><span
                                    class="seperator">|</span>{{'table_filed_00388'|T}} <select ng-model="view_student_leave.searchForm.page.itemsperpage" class="form-control input-xsmall input-sm input-inline">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="150">150</option>
                            </select> {{'table_filed_00390'|T}}</label></div>
                            <div class="dataTables_info"  role="status" aria-live="polite">
                                <span class="seperator">|</span>{{'table_filed_00389'|T}}{{view_student_leave.searchForm.page.totalItems}} {{'table_filed_00390'|T}}
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-12">

                        </div>
                    </div>
                    <!--结束table尾部分{{'table_filed_00386'|T}}以及工具栏-->
                    </div>
                </div>
            </div>
        </div>
        <!-- End: life time stats -->
    </div>
</div>
<!-- END PAGE CONTENT-->
