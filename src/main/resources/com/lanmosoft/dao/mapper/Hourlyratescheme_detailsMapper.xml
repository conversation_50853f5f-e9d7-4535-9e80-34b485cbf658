<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC 
	"-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
	
<mapper namespace="com.lanmosoft.dao.mapper.Hourlyratescheme_detailsMapper">
  
  	<insert id="insert" parameterType="com.lanmosoft.dao.model.Hourlyratescheme_details">
	    insert into hourlyratescheme_details (id, scheme_id, intervalType, 
      periodLowerLimit, periodUpperLimit, unitPrice, 
      currency_id, startDate, endDate
      )
    values (#{id,jdbcType=VARCHAR}, #{scheme_id,jdbcType=VARCHAR}, #{intervalType,jdbcType=VARCHAR}, 
      #{periodLowerLimit,jdbcType=INTEGER}, #{periodUpperLimit,jdbcType=INTEGER}, 
      <if test="unitPrice != null" >
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice == null" >
         0,
      </if>
      #{currency_id,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}
      )
 	</insert>
 	
 	<update id="updateunitprice" parameterType="com.lanmosoft.dao.model.Hourlyratescheme_details">
    	UPDATE hourlyratescheme_details
	    <set>
		
		<if test="unitPrice != null" >
        unitPrice = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice == null" >
        unitPrice = 0,
      </if>
		
	    </set>
	    WHERE id = #{id}
 	</update>
 	
 	<update id="update" parameterType="com.lanmosoft.dao.model.Hourlyratescheme_details">
    	UPDATE hourlyratescheme_details 
	    <set>
		<if test="scheme_id != null" >
        scheme_id = #{scheme_id,jdbcType=VARCHAR},
      </if>
      <if test="intervalType != null" >
        intervalType = #{intervalType,jdbcType=CHAR},
      </if>
      <if test="periodLowerLimit != null" >
        periodLowerLimit = #{periodLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="periodUpperLimit != null" >
        periodUpperLimit = #{periodUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null" >
        unitPrice = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="currency_id != null" >
        currency_id = #{currency_id,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        startDate = #{startDate,jdbcType=TIMESTAMP},
      </if>

        endDate = #{endDate,jdbcType=TIMESTAMP},

	    </set>
	    WHERE id = #{id}
 	</update>
 	
 	<update id="updateForce" parameterType="com.lanmosoft.dao.model.Hourlyratescheme_details">
    	UPDATE hourlyratescheme_details 
	    <set>
       scheme_id = #{scheme_id,jdbcType=VARCHAR},
      intervalType = #{intervalType,jdbcType=CHAR},
      periodLowerLimit = #{periodLowerLimit,jdbcType=INTEGER},
      periodUpperLimit = #{periodUpperLimit,jdbcType=INTEGER},
      unitPrice = #{unitPrice,jdbcType=DECIMAL},
      currency_id = #{currency_id,jdbcType=VARCHAR},
      startDate = #{startDate,jdbcType=TIMESTAMP},
      endDate = #{endDate,jdbcType=TIMESTAMP},
	    </set>
	    WHERE id = #{id}
 	</update>
 	 
    <delete id="delete" parameterType="java.lang.String">
	  	delete from hourlyratescheme_details where id = #{id}
	</delete>
	
	<delete id="deleteByCondition" parameterType="com.lanmosoft.model.WhereCondition">
	  	delete from hourlyratescheme_details <include refid="Where_Clause" />
	</delete>
	
	
	<update id="deleteBystatus" parameterType="com.lanmosoft.model.WhereCondition">
			UPDATE hourlyratescheme_details 
		<set>
			delstatus=1
	    </set>
	   <include refid="Where_Clause"/>
	</update>

	 <update id="updateByCondition" parameterType="map">
    	UPDATE hourlyratescheme_details 
	   <set>
		<if test="domain.id != null" >
        id = #{domain.id,jdbcType=VARCHAR},
      </if>
      <if test="domain.scheme_id != null" >
        scheme_id = #{domain.scheme_id,jdbcType=VARCHAR},
      </if>
      <if test="domain.intervalType != null" >
        intervalType = #{domain.intervalType,jdbcType=CHAR},
      </if>
      <if test="domain.periodLowerLimit != null" >
        periodLowerLimit = #{domain.periodLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="domain.periodUpperLimit != null" >
        periodUpperLimit = #{domain.periodUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="domain.unitPrice != null" >
        unitPrice = #{domain.unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="domain.currency_id != null" >
        currency_id = #{domain.currency_id,jdbcType=VARCHAR},
      </if>
      <if test="domain.startDate != null" >
        startDate = #{domain.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="domain.endDate != null" >
        endDate = #{domain.endDate,jdbcType=TIMESTAMP},
      </if>
	    </set>
	   <where>${wc.condition}</where>
 	</update>
	 
	<select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.Hourlyratescheme_details">
		select <include refid="Base_Column_List"/> from hourlyratescheme_details where id = #{id}
	</select>
	
    <select id="query" resultType="com.lanmosoft.dao.model.Hourlyratescheme_details" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List_view" /> from view_hourlyratescheme_details <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>
  
   	<select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from hourlyratescheme_details <include refid="Where_Clause"/>
  	</select>
  	
  	<select id="queryMainzone" resultType="com.lanmosoft.dao.model.Hourlyratescheme_details" parameterType="com.lanmosoft.dao.model.Hourlyratescheme_details" >
	    select <include refid="Base_Column_List" />   from hourlyratescheme_details where
  	   
  	     	teacher_id=#{teacher_id}  and mainzone='1' 
  	     	
  	     	 <if test="id != null" > and id!=#{id}</if>
  	</select>
  	
  	<select id="querycheck" resultType="com.lanmosoft.dao.model.Hourlyratescheme_details" parameterType="com.lanmosoft.dao.model.Hourlyratescheme_details" >
  	   select <include refid="Base_Column_List" /> from hourlyratescheme_details where
  	   
  	     	teacher_id=#{teacher_id} 
  	     	
  	     	 <if test="id != null" > and id!=#{id}</if>   and  
  	     	 
  	     (
  	     (<![CDATA[startDate<=#{startDate} and  #{startDate}<=endDate]]>) or 
  	     
  	     (<![CDATA[startDate<=#{endDate} and #{endDate}<=endDate]]>) or
  	     
  	      (<![CDATA[ #{startDate}<=startDate and #{endDate}>=endDate ]]>)
  	    )
     </select>
     
     <select id="querycheckhd" resultType="com.lanmosoft.dao.model.Hourlyratescheme_details" parameterType="com.lanmosoft.dao.model.Hourlyratescheme_details" >
  	   select <include refid="Base_Column_List_view" /> from view_hourlyratescheme_details where
  	     	1=1    
  	     	 <if test="id != null" >  and id!=#{id}</if>
  	     	 <if test="startDate != null" > and startDate=#{startDate}</if>   
           <if test="scheme_id != null" >  and scheme_id=#{scheme_id}</if>   
            <if test="intervalType != null" > and  intervalType=#{intervalType}</if>  
            and
  	     	 
  	     (
  	     (<![CDATA[periodLowerLimit<=#{periodLowerLimit} and  #{periodLowerLimit}<=periodUpperLimit]]>) or 
  	     
  	     (<![CDATA[periodLowerLimit<=#{periodUpperLimit} and #{periodUpperLimit}<=periodUpperLimit]]>) or
  	     
  	      (<![CDATA[ #{periodLowerLimit}<=periodLowerLimit and #{periodUpperLimit}>=periodUpperLimit ]]>)
  	    )
     </select>
  	
  	<select id="queryTeacher_id" resultType="String" parameterType="com.lanmosoft.model.WhereCondition" >
    	select distinct teacher_id from hourlyratescheme_details <include refid="Where_Clause" />
  	</select>
  
	<sql id="Where_Clause">
	    <if test="condition != null"><where>${condition}</where></if> 
	</sql>
  
	<sql id="Base_Column_List" >
	id, scheme_id, intervalType, periodLowerLimit, periodUpperLimit, unitPrice, currency_id, 
    startDate, endDate
	</sql>
	
	<sql id="Base_Column_List_view" >
	id, scheme_id, intervalType, periodLowerLimit, periodUpperLimit, unitPrice, currency_id, 
    startDate, endDate,
    currencyName
	</sql>
</mapper>