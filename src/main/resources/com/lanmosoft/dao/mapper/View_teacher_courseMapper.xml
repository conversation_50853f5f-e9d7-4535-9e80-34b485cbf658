<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC 
	"-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
	
<mapper namespace="com.lanmosoft.dao.mapper.View_teacher_courseMapper">
  
  	<insert id="insert" parameterType="com.lanmosoft.dao.model.View_teacher_course">
	    INSERT INTO view_teacher_course (id
  ,teacher_id
  ,chineseName
  ,englishName
  ,course_id
  ,course_name
  ,periodLowerLimit
  ,periodUpperLimit
  ,intervalType
  ,gradeCategory_id
  ,gradeCategory_name
  ,grade_id
  ,level
  ,grade_name
  ,teachingType
  ,startDate
  ,endDate
  ,unitPrice
  )
	    VALUES (#{id,jdbcType=VARCHAR},
  #{teacher_id,jdbcType=VARCHAR},
  #{chineseName,jdbcType=VARCHAR},
  #{englishName,jdbcType=VARCHAR},
  #{course_id,jdbcType=VARCHAR},
  #{course_name,jdbcType=VARCHAR},
  #{periodLowerLimit,jdbcType=INTEGER},
  #{periodUpperLimit,jdbcType=INTEGER},
  #{intervalType,jdbcType=VARCHAR},
  #{gradeCategory_id,jdbcType=VARCHAR},
  #{gradeCategory_name,jdbcType=VARCHAR},
  #{grade_id,jdbcType=VARCHAR},
  #{level,jdbcType=VARCHAR},
  #{grade_name,jdbcType=VARCHAR},
  #{teachingType,jdbcType=VARCHAR},
  #{startDate,jdbcType=TIMESTAMP},
  #{endDate,jdbcType=TIMESTAMP},
  #{unitPrice,jdbcType=DECIMAL}
  )
 	</insert>
 	
 	<update id="update" parameterType="com.lanmosoft.dao.model.View_teacher_course">
    	UPDATE view_teacher_course 
	    <set>
		<if test="id != null" >
        id = #{id,jdbcType=VARCHAR},
      </if>
		<if test="teacher_id != null" >
        teacher_id = #{teacher_id,jdbcType=VARCHAR},
      </if>
		<if test="chineseName != null" >
        chineseName = #{chineseName,jdbcType=VARCHAR},
      </if>
		<if test="englishName != null" >
        englishName = #{englishName,jdbcType=VARCHAR},
      </if>
		<if test="course_id != null" >
        course_id = #{course_id,jdbcType=VARCHAR},
      </if>
		<if test="course_name != null" >
        course_name = #{course_name,jdbcType=VARCHAR},
      </if>
		<if test="periodLowerLimit != null" >
        periodLowerLimit = #{periodLowerLimit,jdbcType=INTEGER},
      </if>
		<if test="periodUpperLimit != null" >
        periodUpperLimit = #{periodUpperLimit,jdbcType=INTEGER},
      </if>
		<if test="intervalType != null" >
        intervalType = #{intervalType,jdbcType=VARCHAR},
      </if>
		<if test="gradeCategory_id != null" >
        gradeCategory_id = #{gradeCategory_id,jdbcType=VARCHAR},
      </if>
		<if test="gradeCategory_name != null" >
        gradeCategory_name = #{gradeCategory_name,jdbcType=VARCHAR},
      </if>
		<if test="grade_id != null" >
        grade_id = #{grade_id,jdbcType=VARCHAR},
      </if>
		<if test="level != null" >
        level = #{level,jdbcType=VARCHAR},
      </if>
		<if test="grade_name != null" >
        grade_name = #{grade_name,jdbcType=VARCHAR},
      </if>
		<if test="teachingType != null" >
        teachingType = #{teachingType,jdbcType=VARCHAR},
      </if>
		<if test="startDate != null" >
        startDate = #{startDate,jdbcType=TIMESTAMP},
      </if>
		<if test="endDate != null" >
        endDate = #{endDate,jdbcType=TIMESTAMP},
      </if>
		<if test="unitPrice != null" >
        unitPrice = #{unitPrice,jdbcType=DECIMAL},
      </if>
	    </set>
	    WHERE id = #{id}
 	</update>
 	
 	<update id="updateForce" parameterType="com.lanmosoft.dao.model.View_teacher_course">
    	UPDATE view_teacher_course 
	    <set>
        id = #{id,jdbcType=VARCHAR},
        teacher_id = #{teacher_id,jdbcType=VARCHAR},
        chineseName = #{chineseName,jdbcType=VARCHAR},
        englishName = #{englishName,jdbcType=VARCHAR},
        course_id = #{course_id,jdbcType=VARCHAR},
        course_name = #{course_name,jdbcType=VARCHAR},
        periodLowerLimit = #{periodLowerLimit,jdbcType=INTEGER},
        periodUpperLimit = #{periodUpperLimit,jdbcType=INTEGER},
        intervalType = #{intervalType,jdbcType=VARCHAR},
        gradeCategory_id = #{gradeCategory_id,jdbcType=VARCHAR},
        gradeCategory_name = #{gradeCategory_name,jdbcType=VARCHAR},
        grade_id = #{grade_id,jdbcType=VARCHAR},
        level = #{level,jdbcType=VARCHAR},
        grade_name = #{grade_name,jdbcType=VARCHAR},
        teachingType = #{teachingType,jdbcType=VARCHAR},
        startDate = #{startDate,jdbcType=TIMESTAMP},
        endDate = #{endDate,jdbcType=TIMESTAMP},
        unitPrice = #{unitPrice,jdbcType=DECIMAL},
	    </set>
	    WHERE id = #{id}
 	</update>
 	 
    <delete id="delete" parameterType="java.lang.String">
	  	delete from view_teacher_course where id = #{id}
	</delete>
	
	<delete id="deleteByCondition" parameterType="com.lanmosoft.model.WhereCondition">
	  	delete from view_teacher_course <include refid="Where_Clause" />
	</delete>
	
	
	<update id="deleteBystatus" parameterType="com.lanmosoft.model.WhereCondition">
			UPDATE view_teacher_course 
		<set>
			delstatus=1
	    </set>
	   <include refid="Where_Clause"/>
	</update>

	 <update id="updateByCondition" parameterType="map">
    	UPDATE view_teacher_course 
	   <set>
		<if test="domain.id != null" >
        id = #{domain.id,jdbcType=VARCHAR},
      </if>
		<if test="domain.teacher_id != null" >
        teacher_id = #{domain.teacher_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.chineseName != null" >
        chineseName = #{domain.chineseName,jdbcType=VARCHAR},
      </if>
		<if test="domain.englishName != null" >
        englishName = #{domain.englishName,jdbcType=VARCHAR},
      </if>
		<if test="domain.course_id != null" >
        course_id = #{domain.course_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.course_name != null" >
        course_name = #{domain.course_name,jdbcType=VARCHAR},
      </if>
		<if test="domain.periodLowerLimit != null" >
        periodLowerLimit = #{domain.periodLowerLimit,jdbcType=INTEGER},
      </if>
		<if test="domain.periodUpperLimit != null" >
        periodUpperLimit = #{domain.periodUpperLimit,jdbcType=INTEGER},
      </if>
		<if test="domain.intervalType != null" >
        intervalType = #{domain.intervalType,jdbcType=VARCHAR},
      </if>
		<if test="domain.gradeCategory_id != null" >
        gradeCategory_id = #{domain.gradeCategory_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.gradeCategory_name != null" >
        gradeCategory_name = #{domain.gradeCategory_name,jdbcType=VARCHAR},
      </if>
		<if test="domain.grade_id != null" >
        grade_id = #{domain.grade_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.level != null" >
        level = #{domain.level,jdbcType=VARCHAR},
      </if>
		<if test="domain.grade_name != null" >
        grade_name = #{domain.grade_name,jdbcType=VARCHAR},
      </if>
		<if test="domain.teachingType != null" >
        teachingType = #{domain.teachingType,jdbcType=VARCHAR},
      </if>
		<if test="domain.startDate != null" >
        startDate = #{domain.startDate,jdbcType=TIMESTAMP},
      </if>
		<if test="domain.endDate != null" >
        endDate = #{domain.endDate,jdbcType=TIMESTAMP},
      </if>
		<if test="domain.unitPrice != null" >
        unitPrice = #{domain.unitPrice,jdbcType=DECIMAL},
      </if>
	    </set>
	   <where>${wc.condition}</where>
 	</update>
	 
	<select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.View_teacher_course">
		select <include refid="Base_Column_List"/> from view_teacher_course where id = #{id}
	</select>
	
    <select id="query" resultType="com.lanmosoft.dao.model.View_teacher_course" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List3" /> from view_teacher_course <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>
  
   	<select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from view_teacher_course <include refid="Where_Clause"/>
  	</select>
  	
  	<select id="queryDistinct" resultType="com.lanmosoft.dao.model.View_teacher_course" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List3" /> from view_teacher_course_grade <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>

    <select id="queryDistinctTeacherID" resultType="String" parameterType="com.lanmosoft.model.WhereCondition" >
        select distinct(teacher_id) from view_teacher_course_grade <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
    </select>
  
   	<select id="countDistinct" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from view_teacher_course_grade <include refid="Where_Clause"/>
  	</select>
  
	<sql id="Where_Clause">
	    <if test="condition != null"><where>${condition}</where></if> 
	</sql>
  
	<sql id="Base_Column_List" >
	  id
,teacher_id
,chineseName
,englishName
,course_id
,course_name
,periodLowerLimit
,periodUpperLimit
,intervalType
,gradeCategory_id
,gradeCategory_name
,grade_id
,level
,grade_name
,teachingType
,startDate
,endDate
,unitPrice
,zone_id
	</sql>
<sql id="Base_Column_List2" >
id
,teacher_id
,course_id
,periodLowerLimit
,periodUpperLimit
,intervalType
,gradeCategory_id
,grade_id
,grade_name
,teachingType
,startDate
,endDate
,unitPrice
,chineseName
,englishName
,course_name
,gradeCategory_name
,grade_name
</sql>

<sql id="Base_Column_List3" >
	  id
,teacher_id

,status
,course_id
,course_name


,gradeCategory_id
,gradeCategory_name
,grade_id

,grade_name


,endDate

,BUType
,schemeNo
,scheme_id
,level
,englishName
,chineseName
	</sql>
	
</mapper>