<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC 
	"-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
	
<mapper namespace="com.lanmosoft.dao.mapper.Teacher_contractMapper">
  
  	<insert id="insert" parameterType="com.lanmosoft.dao.model.Teacher_contract">
	    INSERT INTO teacher_contract (id
  ,teacher_id
  ,startDate
  ,endDate
  ,postType
  ,creator_id
  ,createTime
  ,lastModifier_id
  ,lastModifiedTime
  ,delStatus
  )
	    VALUES (#{id,jdbcType=VARCHAR},
  #{teacher_id,jdbcType=VARCHAR},
  #{startDate,jdbcType=TIMESTAMP},
  #{endDate,jdbcType=TIMESTAMP},
  #{postType,jdbcType=VARCHAR},
  #{creator_id,jdbcType=VARCHAR},
  #{createTime,jdbcType=TIMESTAMP},
  #{lastModifier_id,jdbcType=VARCHAR},
  #{lastModifiedTime,jdbcType=TIMESTAMP},
  #{delStatus,jdbcType=VARCHAR}
  )
 	</insert>
 	
 	<update id="update" parameterType="com.lanmosoft.dao.model.Teacher_contract">
    	UPDATE teacher_contract 
	    <set>
		<if test="id != null" >
        id = #{id,jdbcType=VARCHAR},
      </if>
		<if test="teacher_id != null" >
        teacher_id = #{teacher_id,jdbcType=VARCHAR},
      </if>
		<if test="startDate != null" >
        startDate = #{startDate,jdbcType=TIMESTAMP},
      </if>
		<if test="endDate != null" >
        endDate = #{endDate,jdbcType=TIMESTAMP},
      </if>
		<if test="postType != null" >
        postType = #{postType,jdbcType=VARCHAR},
      </if>
		<if test="creator_id != null" >
        creator_id = #{creator_id,jdbcType=VARCHAR},
      </if>
		<if test="createTime != null" >
        createTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
		<if test="lastModifier_id != null" >
        lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
      </if>
		<if test="lastModifiedTime != null" >
        lastModifiedTime = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
		<if test="delStatus != null" >
        delStatus = #{delStatus,jdbcType=VARCHAR},
      </if>
	    </set>
	    WHERE id = #{id}
 	</update>
 	
 	<update id="updateForce" parameterType="com.lanmosoft.dao.model.Teacher_contract">
    	UPDATE teacher_contract 
	    <set>
        id = #{id,jdbcType=VARCHAR},
        teacher_id = #{teacher_id,jdbcType=VARCHAR},
        startDate = #{startDate,jdbcType=TIMESTAMP},
        endDate = #{endDate,jdbcType=TIMESTAMP},
        postType = #{postType,jdbcType=VARCHAR},
        creator_id = #{creator_id,jdbcType=VARCHAR},
        createTime = #{createTime,jdbcType=TIMESTAMP},
        lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
        lastModifiedTime = #{lastModifiedTime,jdbcType=TIMESTAMP},
        delStatus = #{delStatus,jdbcType=VARCHAR},
	    </set>
	    WHERE id = #{id}
 	</update>
 	 
    <delete id="delete" parameterType="java.lang.String">
	  	delete from teacher_contract where id = #{id}
	</delete>
	
	<delete id="deleteByCondition" parameterType="com.lanmosoft.model.WhereCondition">
	  	delete from teacher_contract <include refid="Where_Clause" />
	</delete>
	
	
	<update id="deleteBystatus" parameterType="com.lanmosoft.model.WhereCondition">
			UPDATE teacher_contract 
		<set>
			delstatus=1
	    </set>
	   <include refid="Where_Clause"/>
	</update>

	 <update id="updateByCondition" parameterType="map">
    	UPDATE teacher_contract 
	   <set>
		<if test="domain.id != null" >
        id = #{domain.id,jdbcType=VARCHAR},
      </if>
		<if test="domain.teacher_id != null" >
        teacher_id = #{domain.teacher_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.startDate != null" >
        startDate = #{domain.startDate,jdbcType=TIMESTAMP},
      </if>
		<if test="domain.endDate != null" >
        endDate = #{domain.endDate,jdbcType=TIMESTAMP},
      </if>
		<if test="domain.postType != null" >
        postType = #{domain.postType,jdbcType=VARCHAR},
      </if>
		<if test="domain.creator_id != null" >
        creator_id = #{domain.creator_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.createTime != null" >
        createTime = #{domain.createTime,jdbcType=TIMESTAMP},
      </if>
		<if test="domain.lastModifier_id != null" >
        lastModifier_id = #{domain.lastModifier_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.lastModifiedTime != null" >
        lastModifiedTime = #{domain.lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
		<if test="domain.delStatus != null" >
        delStatus = #{domain.delStatus,jdbcType=VARCHAR},
      </if>
	    </set>
	   <where>${wc.condition}</where>
 	</update>
	 
	<select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.Teacher_contract">
		select <include refid="Base_Column_List"/> from teacher_contract where id = #{id}
	</select>
	
    <select id="query" resultType="com.lanmosoft.dao.model.Teacher_contract" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List" /> from teacher_contract <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>
  
   	<select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from teacher_contract <include refid="Where_Clause"/>
  	</select>
  
	<sql id="Where_Clause">
	    <if test="condition != null"><where>${condition}</where></if> 
	</sql>
  
	<sql id="Base_Column_List" >
	  id
,teacher_id
,startDate
,endDate
,postType
,creator_id
,createTime
,lastModifier_id
,lastModifiedTime
,delStatus
	</sql>
</mapper>