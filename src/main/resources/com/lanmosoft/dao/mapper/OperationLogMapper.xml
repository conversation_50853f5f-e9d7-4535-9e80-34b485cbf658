<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC 
	"-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
	
<mapper namespace="com.lanmosoft.dao.mapper.OperationLogMapper">
  
  	<insert id="insert" parameterType="com.lanmosoft.dao.model.OperationLog">
	    INSERT INTO operationLog (id
  ,user_id
  ,content
  ,createTime
  )
	    VALUES (#{id,jdbcType=VARCHAR},
  #{user_id,jdbcType=VARCHAR},
  #{content,jdbcType=VARCHAR},
  #{createTime,jdbcType=TIMESTAMP}
  )
 	</insert>

	<select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.OperationLog">
		select <include refid="Base_Column_List"/> from operationLog where id = #{id}
	</select>
	
    <select id="query" resultType="com.lanmosoft.dao.model.OperationLog" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List" /> from operationLog <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>
  
   	<select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from operationLog <include refid="Where_Clause"/>
  	</select>
  
	<sql id="Where_Clause">
	    <if test="condition != null"><where>${condition}</where></if> 
	</sql>
  
	<sql id="Base_Column_List" >
	  id
  ,user_id
  ,content
  ,createTime
	</sql>
</mapper>