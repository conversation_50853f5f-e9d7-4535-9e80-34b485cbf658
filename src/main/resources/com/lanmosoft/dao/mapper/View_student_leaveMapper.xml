<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC 
	"-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
	
<mapper namespace="com.lanmosoft.dao.mapper.View_student_leaveMapper">
  
	<select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.View_student_leave">
		select <include refid="Base_Column_List"/> from view_student_leave where id = #{id}
	</select>
	
    <select id="query" resultType="com.lanmosoft.dao.model.View_student_leave" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List" /> from view_student_leave <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>
  
   	<select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from view_student_leave <include refid="Where_Clause"/>
  	</select>
  
	<sql id="Where_Clause">
	    <if test="condition != null"><where>${condition}</where></if> 
	</sql>
  
	<sql id="Base_Column_List" >
		id
		,classSchedule_id
		,student_id
		,handleTime
		,isInadvance
		,reschedule
		,leaveDate
		,startTime
		,endTime
		,reason
		,deductionClass
		,givenClass
		,givenWage
		,contract_id
		,approvalStatus
		,lockStatus
		,creator_id
		,createTime
		,lastModifier_id
		,lastModifiedTime
		,code
		,chineseName
		,englishName
		,zone_name
		,zone_id
		,tutorId
		,tutor_englishName
		,TeacherId
		,TeacherEnglishName
		,Consultant
	</sql>
</mapper>