<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC 
	"-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
	
<mapper namespace="com.lanmosoft.dao.mapper.Student_freeTimeMapper">
  
  	<insert id="insert" parameterType="com.lanmosoft.dao.model.Student_freeTime">
	    INSERT INTO student_freeTime (
  student_id
  ,freeDate
  ,startTime
  ,endTime
  ,teachingWay
  ,isFree
  ,freeTimeCode
  ,BUType
  )
	    VALUES (
  #{student_id,jdbcType=VARCHAR},
  #{freeDate,jdbcType=TIMESTAMP},
  #{startTime,jdbcType=VARCHAR},
  #{endTime,jdbcType=VARCHAR},
  #{teachingWay,jdbcType=VARCHAR},
  #{isFree,jdbcType=VARCHAR},
  #{freeTimeCode,jdbcType=VARCHAR},
  #{BUType,jdbcType=VARCHAR}
  )
 	</insert>
 	
 	<insert id="batchInsert" parameterType="java.util.List">
	    INSERT INTO student_freeTime (
	    student_id
		,freeDate
		,startTime
		,endTime
		,teachingWay
		,isFree
		,freeTimeCode
		,BUType
  		) VALUES 
	    <foreach collection="list" item="item" index="index" separator="," >  
        (
		#{item.student_id,jdbcType=VARCHAR},
		#{item.freeDate,jdbcType=TIMESTAMP},
		#{item.startTime,jdbcType=VARCHAR},
		#{item.endTime,jdbcType=VARCHAR},
		#{item.teachingWay,jdbcType=VARCHAR},
		#{item.isFree,jdbcType=VARCHAR},
		#{item.freeTimeCode,jdbcType=VARCHAR},
		#{item.BUType,jdbcType=VARCHAR},
		)
    	</foreach>  
 	</insert>
 	
 	<update id="update" parameterType="com.lanmosoft.dao.model.Student_freeTime">
    	UPDATE student_freeTime 
	    <set>
		<if test="student_id != null" >
        student_id = #{student_id,jdbcType=VARCHAR},
      </if>
		<if test="freeDate != null" >
        freeDate = #{freeDate,jdbcType=TIMESTAMP},
      </if>
		<if test="startTime != null" >
        startTime = #{startTime,jdbcType=VARCHAR},
      </if>
		<if test="endTime != null" >
        endTime = #{endTime,jdbcType=VARCHAR},
      </if>
		<if test="teachingWay != null" >
        teachingWay = #{teachingWay,jdbcType=VARCHAR},
      </if>
		<if test="isFree != null" >
        isFree = #{isFree,jdbcType=VARCHAR},
      </if>
		<if test="freeTimeCode != null" >
        freeTimeCode = #{freeTimeCode,jdbcType=VARCHAR},
      </if>
        <if test="BUType != null" >
        BUType = #{BUType,jdbcType=VARCHAR},
      </if>
	    </set>
	    WHERE id = #{id}
 	</update>
 	
 	<update id="updateForce" parameterType="com.lanmosoft.dao.model.Student_freeTime">
    	UPDATE student_freeTime 
	    <set>
        student_id = #{student_id,jdbcType=VARCHAR},
        freeDate = #{freeDate,jdbcType=TIMESTAMP},
        startTime = #{startTime,jdbcType=VARCHAR},
        endTime = #{endTime,jdbcType=VARCHAR},
        teachingWay = #{teachingWay,jdbcType=VARCHAR},
        isFree = #{isFree,jdbcType=VARCHAR},
        freeTimeCode = #{freeTimeCode,jdbcType=VARCHAR},
        BUType = #{BUType,jdbcType=VARCHAR},
	    </set>
	    WHERE id = #{id}
 	</update>
 	 
    <delete id="delete" parameterType="java.lang.String">
	  	delete from student_freeTime where id = #{id}
	</delete>
	
	<delete id="deleteByCondition" parameterType="com.lanmosoft.model.WhereCondition">
	  	delete from student_freeTime <include refid="Where_Clause" />
	</delete>
	
	
	<update id="deleteBystatus" parameterType="com.lanmosoft.model.WhereCondition">
			UPDATE student_freeTime 
		<set>
			delstatus=1
	    </set>
	   <include refid="Where_Clause"/>
	</update>

	 <update id="updateByCondition" parameterType="map">
    	UPDATE student_freeTime 
	   <set>
		<if test="domain.student_id != null" >
        student_id = #{domain.student_id,jdbcType=VARCHAR},
      </if>
		<if test="domain.freeDate != null" >
        freeDate = #{domain.freeDate,jdbcType=TIMESTAMP},
      </if>
		<if test="domain.startTime != null" >
        startTime = #{domain.startTime,jdbcType=VARCHAR},
      </if>
		<if test="domain.endTime != null" >
        endTime = #{domain.endTime,jdbcType=VARCHAR},
      </if>
		<if test="domain.teachingWay != null" >
        teachingWay = #{domain.teachingWay,jdbcType=VARCHAR},
      </if>
		<if test="domain.isFree != null" >
        isFree = #{domain.isFree,jdbcType=VARCHAR},
      </if>
		<if test="domain.freeTimeCode != null" >
        freeTimeCode = #{domain.freeTimeCode,jdbcType=VARCHAR},
      </if>
        <if test="domain.BUType != null" >
        BUType = #{domain.BUType,jdbcType=VARCHAR},
      </if>
	    </set>
	   <where>${wc.condition}</where>
 	</update>
	 
	<select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.Student_freeTime">
		select <include refid="Base_Column_List"/> from student_freeTime where id = #{id}
	</select>
	
    <select id="query" resultType="com.lanmosoft.dao.model.Student_freeTime" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List" /> from student_freeTime <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>

	<select id="queryLong" resultType="com.lanmosoft.dao.model.Student_freeTime" parameterType="com.lanmosoft.model.WhereCondition" >
		select <include refid="Base_Column_List" /> from student_freeTimeLong <include refid="Where_Clause" />
		<if test="orderBy != null" >${orderBy}</if>
		<if test="length > 0" >LIMIT ${offset}, ${length}</if>
	</select>
  
   	<select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from student_freeTime <include refid="Where_Clause"/>
  	</select>
  	
  	<select id="queryFreeHour" resultType="double" parameterType="com.lanmosoft.model.WhereCondition" >
	    select COALESCE(SUM(CHAR_LENGTH(freeTimeCode)/4), 0) from student_freeTime <include refid="Where_Clause"/>
  	</select>
  	<select id="matchingHour" resultType="double" parameterType="com.lanmosoft.model.WhereCondition" >
	    select COALESCE(SUM(CHAR_LENGTH(sf.freeTimeCode)/4), 0) from student_freeTime sf,teacher_freeTime tf where
	    sf.freeDate = tf.freeDate and
		sf.student_id = #{student_id} and
		tf.teacher_id = #{teacher_id} and
		<![CDATA[
		(sf.freeDate >= #{startDate} and sf.freeDate<= #{endDate}) and
		(
		  (replace(sf.endTime, ':','')+0)=(replace(tf.endTime, ':','')+0) or
		  (
		     (replace(tf.endTime, ':','')+0)-(replace(sf.endTime, ':','')+0)<100 and 
		     (replace(tf.endTime, ':','')+0)-(replace(sf.endTime, ':','')+0)>0
		  )
		)
	    ]]>
  	</select>
  
	<sql id="Where_Clause">
	    <if test="condition != null"><where>${condition}</where></if> 
	</sql>
  
	<sql id="Base_Column_List" >
	  id
,student_id
,freeDate
,startTime
,endTime
,teachingWay
,isFree
,freeTimeCode
,BUType
,freeDate as freeDateStr
	</sql>
</mapper>