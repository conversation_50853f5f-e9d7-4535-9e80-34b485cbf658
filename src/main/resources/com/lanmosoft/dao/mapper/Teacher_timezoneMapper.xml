<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lanmosoft.dao.mapper.Teacher_timezoneMapper" >
  
    <insert id="insert" parameterType="com.lanmosoft.dao.model.Teacher_timezone" >
    INSERT INTO teacher_timezone (id
,teacher_id
,timezone_id
,startDate
,endDate
,notes
,creator_id
,createTime
,lastModifier_id
,lastModifiedTime
,delStatus
)
        VALUES(#{id,jdbcType=VARCHAR}, 
 #{teacher_id,jdbcType=VARCHAR}, 
 #{timezone_id,jdbcType=VARCHAR}, 
 #{startDate,jdbcType=TIMESTAMP}, 
 #{endDate,jdbcType=TIMESTAMP}, 
 #{notes,jdbcType=VARCHAR}, 
 #{creator_id,jdbcType=VARCHAR}, 
 #{createTime,jdbcType=TIMESTAMP}, 
 #{lastModifier_id,jdbcType=VARCHAR}, 
 #{lastModifiedtime,jdbcType=TIMESTAMP}, 
 #{delStatus,jdbcType=VARCHAR}
)
  </insert>
  
  <update id="update" parameterType="com.lanmosoft.dao.model.Teacher_timezone" >
      UPDATE teacher_timezone
      <set >
      <if test="teacher_id != null" >
        teacher_id = #{teacher_id,jdbcType=VARCHAR},
     </if>
      <if test="timezone_id != null" >
        timezone_id = #{timezone_id,jdbcType=VARCHAR},
     </if>
     <if test="startDate != null" >
        startDate = #{startDate,jdbcType=TIMESTAMP},
     </if>
     <if test="endDate != null" >
        endDate = #{endDate,jdbcType=TIMESTAMP},
     </if>
     <if test="notes != null" >
        notes = #{notes,jdbcType=VARCHAR},
     </if>
      <if test="creator_id != null" >
        creator_id = #{creator_id,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        createTime = #{createTime,jdbcType=TIMESTAMP},
     </if>
      <if test="lastModifier_id != null" >
        lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
     </if>
      <if test="lastModifiedtime != null" >
        lastModifiedTime = #{lastModifiedtime,jdbcType=TIMESTAMP},
     </if>
      <if test="delStatus != null" >
        delStatus = #{delStatus,jdbcType=CHAR},
     </if>
    </set>
    WHERE id = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="updateForce" parameterType="com.lanmosoft.dao.model.Teacher_timezone" >
    UPDATE teacher_timezone
    <set>
      teacher_id = #{teacher_id,jdbcType=VARCHAR},
      timezone_id = #{timezone_id,jdbcType=VARCHAR},
      startDate = #{startDate,jdbcType=TIMESTAMP},
      endDate = #{endDate,jdbcType=TIMESTAMP},
      notes = #{notes,jdbcType=VARCHAR},
      creator_id = #{creator_id,jdbcType=VARCHAR},
      createTime = #{createTime,jdbcType=TIMESTAMP},
      lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
      lastModifiedTime = #{lastModifiedtime,jdbcType=TIMESTAMP},
      delStatus = #{delStatus,jdbcType=VARCHAR}
     </set>
    WHERE id = #{id,jdbcType=VARCHAR}
  </update>
  
  <delete id="delete" parameterType="java.lang.String" >
    delete from teacher_timezone where id = #{id,jdbcType=VARCHAR}
  </delete>
  
  <delete id="deleteByCondition" parameterType="com.lanmosoft.model.WhereCondition">
	  	delete from teacher_timezone <include refid="Where_Clause" />
  </delete>
  
  <update id="deleteBystatus" parameterType="com.lanmosoft.model.WhereCondition">
			UPDATE teacher_timezone 
		<set>
			delStatus=1
	    </set>
	   <include refid="Where_Clause"/>
 </update>
 
 <update id="updateByCondition" parameterType="map">
    	UPDATE teacher_timezone 
	   <set>
		<if test="domain.teacher_id != null" >
        teacher_id = #{domain.teacher_id,jdbcType=VARCHAR},
     </if>
      <if test="domain.timezone_id != null" >
        timezone_id = #{domain.timezone_id,jdbcType=VARCHAR},
     </if>
     <if test="domain.startDate != null" >
        startDate = #{domain.startDate,jdbcType=TIMESTAMP},
     </if>
     <if test="domain.endDate != null" >
        endDate = #{domain.endDate,jdbcType=TIMESTAMP},
     </if>
     <if test="domain.notes != null" >
        notes = #{domain.notes,jdbcType=VARCHAR},
     </if>
      <if test="domain.creator_id != null" >
        creator_id = #{domain.creator_id,jdbcType=VARCHAR},
      </if>
      <if test="domain.createTime != null" >
        createTime = #{domain.createTime,jdbcType=TIMESTAMP},
     </if>
      <if test="domain.lastModifier_id != null" >
        lastModifier_id = #{domain.lastModifier_id,jdbcType=VARCHAR},
     </if>
      <if test="domain.lastModifiedtime != null" >
        lastModifiedTime = #{domain.lastModifiedtime,jdbcType=TIMESTAMP},
     </if>
      <if test="domain.delStatus != null" >
        delStatus = #{domain.delStatus,jdbcType=VARCHAR},
     </if>
	    </set>
	   <where>${wc.condition}</where>
 	</update>
 	
 	<select id="querycheck" resultType="com.lanmosoft.dao.model.Teacher_timezone" parameterType="com.lanmosoft.dao.model.Teacher_timezone" >
  	   select <include refid="Base_Column_List_table" /> from teacher_timezone where
  	   
  	     	teacher_id=#{teacher_id} 
  	     	
  	     	 <if test="id != null" > and id!=#{id}</if>   and  
  	     	 
  	     (
  	     (<![CDATA[startDate<=#{startDate} and  #{startDate}<=endDate]]>) or 
  	     
  	     (<![CDATA[startDate<=#{endDate} and #{endDate}<=endDate]]>) or
  	     
  	      (<![CDATA[ #{startDate}<=startDate and #{endDate}>=endDate ]]>)
  	    )
     </select>
     
     <select id="getTeaTimezone" resultType="com.lanmosoft.dao.model.Teacher_timezone" parameterType="com.lanmosoft.dao.model.Teacher_timezone" >
  	   select <include refid="Base_Column_List_view" /> from view_teacher_timezone where
  	   
  	     	teacher_id=#{teacher_id} 
  	     	
  	     	 <if test="id != null" > and id!=#{id}</if>   and  
  	     	 
  	     (
  	     (<![CDATA[startDate<=#{startDate} and  #{startDate}<=endDate]]>) or 
  	     
  	     (<![CDATA[startDate<=#{endDate} and #{endDate}<=endDate]]>) or
  	     
  	      (<![CDATA[ #{startDate}<=startDate and #{endDate}>=endDate ]]>)
  	    )
  	     order by startDate ASC
     </select>
	 
	<select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.Teacher_timezone">
		select <include refid="Base_Column_List"/> from teacher_timezone where id = #{id}
	</select>
	
	<select id="query" resultType="com.lanmosoft.dao.model.Teacher_timezone" parameterType="com.lanmosoft.model.WhereCondition" >
    	select <include refid="Base_Column_List" /> from view_teacher_timezone <include refid="Where_Clause" />
        <if test="orderBy != null" >${orderBy}</if>
        <if test="length > 0" >LIMIT ${offset}, ${length}</if>
  	</select>
  
   	<select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition" >
	    select count(*) from teacher_timezone <include refid="Where_Clause"/>
  	</select>
  	
  	
  
	<sql id="Where_Clause">
	    <if test="condition != null"><where>${condition}</where></if> 
	</sql>
  
  
  <sql id="Base_Column_List" >
    id
,teacher_id
,timezone_id
,startDate
,endDate
,notes
,creator_id
,createTime
,lastModifier_id
,lastModifiedtime
,delStatus
,timezoneName
,timezone
,startDate as startDateStr
,endDate as endDateStr
  </sql>
  
  <sql id="Base_Column_List_table" >
    id
,teacher_id
,timezone_id
,startDate
,endDate
,notes
,creator_id
,createTime
,lastModifier_id
,lastModifiedtime
,delStatus
  </sql>
  
    <sql id="Base_Column_List_view" >
    id
,teacher_id
,timezone_id
,startDate
,endDate
,notes
,creator_id
,createtime
,lastmodifier_id
,lastmodifiedtime
,delStatus
,timezoneName
,timezone
,startDate as startDateStr
,endDate as endDateStr
  </sql>
  
</mapper>