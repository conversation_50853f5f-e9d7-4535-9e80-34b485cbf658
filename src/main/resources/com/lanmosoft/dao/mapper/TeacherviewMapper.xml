<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.lanmosoft.dao.mapper.TeacherviewMapper">

    <insert id="insert" parameterType="com.lanmosoft.dao.model.Teacherview">
        INSERT INTO teacherview (id
        ,isTeaching
        ,category
        ,code
        ,teCode
        ,chineseName
        ,englishName
        ,abbr
        ,birthday
        ,gender
        ,nationality
        ,IDNo
        ,possportNo
        ,mobile
        ,email
        ,wechat
        ,address
        ,type
        ,zone_id
        ,status
        ,department_id
        ,department_name
        ,position_id
        ,position_name
        ,creator_id
        ,createTime
        ,lastModifier_id
        ,lastModifiedTime
        ,delStatus
        ,sms
        ,city_id
        ,BUType
        ,payment_method
        ,university
        ,partner_id
        ,settlement_id
        ,classroom
        ,zoom_id
        ,tencentid
        )
        VALUES (#{id,jdbcType=VARCHAR},
        #{isTeaching,jdbcType=VARCHAR},
        #{category,jdbcType=VARCHAR},
        #{code,jdbcType=VARCHAR},
        #{teCode,jdbcType=VARCHAR},
        #{chineseName,jdbcType=VARCHAR},
        #{englishName,jdbcType=VARCHAR},
        #{abbr,jdbcType=VARCHAR},
        #{birthday,jdbcType=TIMESTAMP},
        #{gender,jdbcType=VARCHAR},
        #{nationality,jdbcType=VARCHAR},
        #{IDNo,jdbcType=VARCHAR},
        #{possportNo,jdbcType=VARCHAR},
        #{mobile,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR},
        #{wechat,jdbcType=VARCHAR},
        #{address,jdbcType=VARCHAR},
        #{type,jdbcType=VARCHAR},
        #{zone_id,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{department_id,jdbcType=VARCHAR},
        #{department_name,jdbcType=VARCHAR},
        #{position_id,jdbcType=VARCHAR},
        #{position_name,jdbcType=VARCHAR},
        #{creator_id,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{lastModifier_id,jdbcType=VARCHAR},
        #{lastModifiedTime,jdbcType=TIMESTAMP},
        #{delStatus,jdbcType=VARCHAR},
        #{sms,jdbcType=VARCHAR},
        #{city_id,jdbcType=VARCHAR},
        #{BUType,jdbcType=VARCHAR},
        #{payment_method,jdbcType=VARCHAR},
        #{university,jdbcType=VARCHAR},
        #{partner_id,jdbcType=VARCHAR},
        #{settlement_id,jdbcType=VARCHAR},
        #{classroom,jdbcType=VARCHAR},
        #{zoom_id,jdbcType=VARCHAR},
        #{tencentid,jdbcType=VARCHAR}
        )
    </insert>

    <update id="update" parameterType="com.lanmosoft.dao.model.Teacherview">
        UPDATE teacherview
        <set>
            <if test="id != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="isTeaching != null">
                isTeaching = #{isTeaching,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="teCode != null">
                teCode = #{teCode,jdbcType=VARCHAR},
            </if>
            <if test="chineseName != null">
                chineseName = #{chineseName,jdbcType=VARCHAR},
            </if>
            <if test="englishName != null">
                englishName = #{englishName,jdbcType=VARCHAR},
            </if>
            <if test="abbr != null">
                abbr = #{abbr,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="nationality != null">
                nationality = #{nationality,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="possportNo != null">
                possportNo = #{possportNo,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="wechat != null">
                wechat = #{wechat,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="zone_id != null">
                zone_id = #{zone_id,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="department_id != null">
                department_id = #{department_id,jdbcType=VARCHAR},
            </if>
            <if test="department_name != null">
                department_name = #{department_name,jdbcType=VARCHAR},
            </if>
            <if test="position_id != null">
                position_id = #{position_id,jdbcType=VARCHAR},
            </if>
            <if test="position_name != null">
                position_name = #{position_name,jdbcType=VARCHAR},
            </if>
            <if test="creator_id != null">
                creator_id = #{creator_id,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifier_id != null">
                lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                lastModifiedTime = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delStatus != null">
                delStatus = #{delStatus,jdbcType=VARCHAR},
            </if>
            <if test="sms != null">
                sms = #{sms,jdbcType=VARCHAR},
            </if>

            city_id = #{city_id,jdbcType=VARCHAR},
            <if test="BUType != null">
                BUType = #{BUType,jdbcType=VARCHAR},
            </if>
            <if test="payment_method != null">
                payment_method = #{payment_method,jdbcType=VARCHAR},
            </if>
            <if test="university != null">
                university = #{university,jdbcType=VARCHAR},
            </if>
            <if test="partner_id != null">
                partner_id = #{partner_id,jdbcType=VARCHAR},
            </if>
            <if test="settlement_id != null">
                settlement_id = #{settlement_id,jdbcType=VARCHAR},
            </if>
            <if test="classroom != null">
                classroom = #{classroom,jdbcType=VARCHAR},
            </if>
            <if test="zoom_id != null">
                zoom_id = #{zoom_id,jdbcType=VARCHAR},
            </if>
            <if test="tencentid != null">
                tencentid = #{tencentid,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateForce" parameterType="com.lanmosoft.dao.model.Teacherview">
        UPDATE teacherview
        <set>
            id = #{id,jdbcType=VARCHAR},
            isTeaching = #{isTeaching,jdbcType=VARCHAR},
            category = #{category,jdbcType=VARCHAR},
            code = #{code,jdbcType=VARCHAR},
            teCode = #{teCode,jdbcType=VARCHAR},
            chineseName = #{chineseName,jdbcType=VARCHAR},
            englishName = #{englishName,jdbcType=VARCHAR},
            abbr = #{abbr,jdbcType=VARCHAR},
            birthday = #{birthday,jdbcType=TIMESTAMP},
            gender = #{gender,jdbcType=VARCHAR},
            nationality = #{nationality,jdbcType=VARCHAR},
            IDNo = #{IDNo,jdbcType=VARCHAR},
            possportNo = #{possportNo,jdbcType=VARCHAR},
            mobile = #{mobile,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            wechat = #{wechat,jdbcType=VARCHAR},
            address = #{address,jdbcType=VARCHAR},
            type = #{type,jdbcType=VARCHAR},
            zone_id = #{zone_id,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            department_id = #{department_id,jdbcType=VARCHAR},
            department_name = #{department_name,jdbcType=VARCHAR},
            position_id = #{position_id,jdbcType=VARCHAR},
            position_name = #{position_name,jdbcType=VARCHAR},
            creator_id = #{creator_id,jdbcType=VARCHAR},
            createTime = #{createTime,jdbcType=TIMESTAMP},
            lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
            lastModifiedTime = #{lastModifiedTime,jdbcType=TIMESTAMP},
            delStatus = #{delStatus,jdbcType=VARCHAR},
            sms = #{sms,jdbcType=VARCHAR},
            city_id = #{city_id,jdbcType=VARCHAR},
            BUType = #{BUType,jdbcType=VARCHAR},
            payment_method = #{payment_method,jdbcType=VARCHAR},
            university = #{university,jdbcType=VARCHAR},
            partner_id = #{partner_id,jdbcType=VARCHAR},
            settlement_id = #{settlement_id,jdbcType=VARCHAR},
            classroom = #{classroom,jdbcType=VARCHAR},
            zoom_id = #{zoom_id,jdbcType=VARCHAR},
            tencentid = #{tencentid,jdbcType=VARCHAR}
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.String">
        delete from teacherview where id = #{id}
    </delete>

    <delete id="deleteByCondition" parameterType="com.lanmosoft.model.WhereCondition">
        delete from teacherview
        <include refid="Where_Clause"/>
    </delete>


    <update id="deleteBystatus" parameterType="com.lanmosoft.model.WhereCondition">
        UPDATE teacherview
        <set>
            delstatus=1
        </set>
        <include refid="Where_Clause"/>
    </update>

    <update id="updateByCondition" parameterType="map">
        UPDATE teacherview
        <set>
            <if test="domain.id != null">
                id = #{domain.id,jdbcType=VARCHAR},
            </if>
            <if test="domain.isTeaching != null">
                isTeaching = #{domain.isTeaching,jdbcType=VARCHAR},
            </if>
            <if test="domain.category != null">
                category = #{domain.category,jdbcType=VARCHAR},
            </if>
            <if test="domain.code != null">
                code = #{domain.code,jdbcType=VARCHAR},
            </if>
            <if test="domain.teCode != null">
                teCode = #{domain.teCode,jdbcType=VARCHAR},
            </if>
            <if test="domain.chineseName != null">
                chineseName = #{domain.chineseName,jdbcType=VARCHAR},
            </if>
            <if test="domain.englishName != null">
                englishName = #{domain.englishName,jdbcType=VARCHAR},
            </if>
            <if test="domain.abbr != null">
                abbr = #{domain.abbr,jdbcType=VARCHAR},
            </if>
            <if test="domain.birthday != null">
                birthday = #{domain.birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="domain.gender != null">
                gender = #{domain.gender,jdbcType=VARCHAR},
            </if>
            <if test="domain.nationality != null">
                nationality = #{domain.nationality,jdbcType=VARCHAR},
            </if>
            <if test="domain.IDNo != null">
                IDNo = #{domain.IDNo,jdbcType=VARCHAR},
            </if>
            <if test="domain.possportNo != null">
                possportNo = #{domain.possportNo,jdbcType=VARCHAR},
            </if>
            <if test="domain.mobile != null">
                mobile = #{domain.mobile,jdbcType=VARCHAR},
            </if>
            <if test="domain.email != null">
                email = #{domain.email,jdbcType=VARCHAR},
            </if>
            <if test="domain.wechat != null">
                wechat = #{domain.wechat,jdbcType=VARCHAR},
            </if>
            <if test="domain.address != null">
                address = #{domain.address,jdbcType=VARCHAR},
            </if>
            <if test="domain.type != null">
                type = #{domain.type,jdbcType=VARCHAR},
            </if>
            <if test="domain.zone_id != null">
                zone_id = #{domain.zone_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.status != null">
                status = #{domain.status,jdbcType=VARCHAR},
            </if>
            <if test="domain.department_id != null">
                department_id = #{domain.department_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.department_name != null">
                department_name = #{domain.department_name,jdbcType=VARCHAR},
            </if>
            <if test="domain.position_id != null">
                position_id = #{domain.position_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.position_name != null">
                position_name = #{domain.position_name,jdbcType=VARCHAR},
            </if>
            <if test="domain.creator_id != null">
                creator_id = #{domain.creator_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.createTime != null">
                createTime = #{domain.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="domain.lastModifier_id != null">
                lastModifier_id = #{domain.lastModifier_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.lastModifiedTime != null">
                lastModifiedTime = #{domain.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="domain.delStatus != null">
                delStatus = #{domain.delStatus,jdbcType=VARCHAR},
            </if>
            <if test="domain.sms != null">
                sms = #{domain.sms,jdbcType=VARCHAR},
            </if>
            <if test="domain.city_id != null">
                city_id = #{domain.city_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.university != null">
                university = #{domain.university,jdbcType=VARCHAR},
            </if>
            <if test="domain.partner_id != null">
                partner_id = #{domain.partner_id=VARCHAR},
            </if>
            <if test="domain.settlement_id != null">
                settlement_id = #{domain.settlement_id=VARCHAR},
            </if>
            <if test="domain.classroom != null">
                classroom = #{domain.classroom=VARCHAR},
            </if>
            <if test="domain.zoom_id != null">
                zoom_id = #{domain.zoom_id=VARCHAR},
            </if>
            <if test="domain.tencentid != null">
                tencentid = #{domain.zoom_id=VARCHAR},
            </if>
        </set>
        <where>${wc.condition}</where>
    </update>

    <!--把所有老师更新为inactive状态-->
    <update id="updateInactive">
        UPDATE `user`
        SET `classscheduleStatus` = '1'
    </update>

    <update id="updateActive">
        UPDATE `user`
        SET `classscheduleStatus` = '0'
        WHERE
        id in (SELECT teacher_id from view_classschedule_teacher_30 )
    </update>

    <select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.Teacherview">
        select
        <include refid="Base_Column_List"/>
        from teacherview_zone where id = #{id}
    </select>

    <select id="query" resultType="com.lanmosoft.dao.model.Teacherview"
            parameterType="com.lanmosoft.model.WhereCondition">
        select
        <include refid="Base_Column_List"/>
        from teacherview_zone
        <include refid="Where_Clause"/>
        <if test="orderBy != null">${orderBy}</if>
        <if test="length > 0">LIMIT ${offset}, ${length}</if>
    </select>

    <select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition">
        select count(*) from teacherview
        <include refid="Where_Clause"/>
    </select>

    <sql id="Where_Clause">
        <if test="condition != null">
            <where>${condition}</where>
        </if>
    </sql>

    <sql id="Base_Column_List">
        id
        ,isTeaching
        ,category
        ,code
        ,teCode
        ,chineseName
        ,englishName
        ,abbr
        ,birthday
        ,gender
        ,nationality
        ,IDNo
        ,possportNo
        ,mobile
        ,email
        ,wechat
        ,address
        ,type
        ,zone_id
        ,status
        ,department_id
        ,department_name
        ,position_id
        ,position_name
        ,creator_id
        ,createTime
        ,lastModifier_id
        ,lastModifiedTime
        ,delStatus
        ,sms
        ,city_id
        ,BUType
        ,zoneName
        ,classscheduleStatus
        ,payment_method
        ,university
        ,partner_id
        ,cityName
        ,partnerName
        ,settlement_id
        ,classroom
        ,zoom_id
        ,tencentid
    </sql>
</mapper>