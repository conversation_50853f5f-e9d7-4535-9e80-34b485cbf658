<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.lanmosoft.dao.mapper.CourseMapper">

    <insert id="insert" parameterType="com.lanmosoft.dao.model.Course">
        INSERT INTO course (id
        ,name
        ,creator_id
        ,createTime
        ,lastModifier_id
        ,lastModifiedTime
        ,delStatus
        ,onlinete_id
        )
        VALUES (#{id,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR},
        #{creator_id,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{lastModifier_id,jdbcType=VARCHAR},
        #{lastModifiedTime,jdbcType=TIMESTAMP},
        #{delStatus,jdbcType=VARCHAR},
        #{onlinete_id,jdbcType=VARCHAR}
        )
    </insert>

    <update id="update" parameterType="com.lanmosoft.dao.model.Course">
        UPDATE course
        <set>
            <if test="id != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="creator_id != null">
                creator_id = #{creator_id,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifier_id != null">
                lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                lastModifiedTime = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delStatus != null">
                delStatus = #{delStatus,jdbcType=VARCHAR},
            </if>
            onlinete_id = #{onlinete_id,jdbcType=VARCHAR},
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateForce" parameterType="com.lanmosoft.dao.model.Course">
        UPDATE course
        <set>
            id = #{id,jdbcType=VARCHAR},
            name = #{name,jdbcType=VARCHAR},
            creator_id = #{creator_id,jdbcType=VARCHAR},
            createTime = #{createTime,jdbcType=TIMESTAMP},
            lastModifier_id = #{lastModifier_id,jdbcType=VARCHAR},
            lastModifiedTime = #{lastModifiedTime,jdbcType=TIMESTAMP},
            delStatus = #{delStatus,jdbcType=VARCHAR},
            onlinete_id = #{onlinete_id,jdbcType=VARCHAR},
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.String">
        delete from course where id = #{id}
    </delete>

    <delete id="deleteByCondition" parameterType="com.lanmosoft.model.WhereCondition">
        delete from course
        <include refid="Where_Clause"/>
    </delete>


    <update id="deleteBystatus" parameterType="com.lanmosoft.model.WhereCondition">
        UPDATE course
        <set>
            delstatus=1
        </set>
        <include refid="Where_Clause"/>
    </update>

    <update id="updateByCondition" parameterType="map">
        UPDATE course
        <set>
            <if test="domain.id != null">
                id = #{domain.id,jdbcType=VARCHAR},
            </if>
            <if test="domain.name != null">
                name = #{domain.name,jdbcType=VARCHAR},
            </if>
            <if test="domain.creator_id != null">
                creator_id = #{domain.creator_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.createTime != null">
                createTime = #{domain.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="domain.lastModifier_id != null">
                lastModifier_id = #{domain.lastModifier_id,jdbcType=VARCHAR},
            </if>
            <if test="domain.lastModifiedTime != null">
                lastModifiedTime = #{domain.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="domain.delStatus != null">
                delStatus = #{domain.delStatus,jdbcType=VARCHAR},
            </if>
            <if test="domain.delStatus != null">
                onlinete_id = #{domain.onlinete_id,jdbcType=VARCHAR},
            </if>
        </set>
        <where>${wc.condition}</where>
    </update>

    <select id="loadById" parameterType="String" resultType="com.lanmosoft.dao.model.Course">
        select
        <include refid="Base_Column_List"/>
        from course where id = #{id}
    </select>

    <select id="query" resultType="com.lanmosoft.dao.model.Course" parameterType="com.lanmosoft.model.WhereCondition">
        select
        <include refid="Base_Column_List"/>
        from course
        <include refid="Where_Clause"/>
        <if test="orderBy != null">${orderBy}</if>
        <if test="length > 0">LIMIT ${offset}, ${length}</if>
    </select>

    <select id="count" resultType="int" parameterType="com.lanmosoft.model.WhereCondition">
        select count(*) from course
        <include refid="Where_Clause"/>
    </select>

    <sql id="Where_Clause">
        <if test="condition != null">
            <where>${condition}</where>
        </if>
    </sql>

    <sql id="Base_Column_List">
        id
        ,name
        ,creator_id
        ,createTime
        ,lastModifier_id
        ,lastModifiedTime
        ,delStatus
        ,onlinete_id
    </sql>
</mapper>