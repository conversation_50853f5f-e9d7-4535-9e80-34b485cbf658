<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:jaxws="http://cxf.apache.org/jaxws"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
       	http://www.springframework.org/schema/beans/spring-beans-3.1.xsd




        http://cxf.apache.org/jaxws
        http://cxf.apache.org/schemas/jaxws.xsd"

	   default-lazy-init="true">
	<import resource="classpath:META-INF/cxf/cxf.xml"/>
	<!-- <import resource="classpath:META-INF/cxf/cxf-extension-soap.xml" />
        警告提示已经废弃了cxf-extension-soap.xml文件-->
	<import resource="classpath:META-INF/cxf/cxf-servlet.xml"/>

	<bean id="studentServiceImpl" class="com.lanmosoft.web.StudentServiceImpl"></bean>
	<bean id="customerFeature" class="com.lanmosoft.web.filter.CustomerFeature"></bean>

	<!--	<bean class="com.mangofactory.swagger.configuration.SpringSwaggerConfig" />-->
	<!--	&lt;!&ndash; 将自定义的swagger配置类加载到spring容器 &ndash;&gt;-->
	<!--	<bean class="config.SwaggerConfig" />-->

	<!--	<mvc:resources mapping="/swagger/**" location="/WEB-INF/swagger/"/>-->

	<!--	&lt;!&ndash; 定义无需Controller的url<->view直接映射 &ndash;&gt;-->
	<!--	<mvc:view-controller path="/" view-name="../WEB-INF/swagger/index"/>-->

	<jaxws:endpoint id="studentManager" address="/studentManager" implementor="#studentServiceImpl">
		<jaxws:properties>
			<entry key="schema-validation-enabled" value="true"/>
			<entry key="autoRewriteSoapAddress" value="true"></entry>
		</jaxws:properties>
		<jaxws:dataBinding>
			<bean class="org.apache.cxf.xmlbeans.XmlBeansDataBinding"/>
		</jaxws:dataBinding>
		<jaxws:features>
			<ref bean="customerFeature"></ref>
		</jaxws:features>
		<!-- <jaxws:inInterceptors>
            <ref bean="authInterceptor"/>
        </jaxws:inInterceptors> -->
	</jaxws:endpoint>
</beans>
