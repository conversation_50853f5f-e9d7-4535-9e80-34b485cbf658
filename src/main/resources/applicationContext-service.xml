<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" 
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:context="http://www.springframework.org/schema/context"
        xmlns:task="http://www.springframework.org/schema/task"

       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
            http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.1.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd"

       default-lazy-init="true">
	<!-- @Scheduled注解 -->
    
    <!-- Activates scanning of @Autowired -->
    <context:annotation-config/>

	<!-- Activates scanning of @Service -->
    <context:component-scan base-package="com.lanmosoft.service"/>
	<task:scheduled-tasks>
		<!--<task:scheduled ref="erpTaskJob" method="job1" cron="0 0 1 1 * ?"/> 每月1号1点执行 -->
		<task:scheduled ref="erpTaskJob" method="job2" cron="0 30 0 * * ?"/><!-- 每天0：30执行 -->
		<task:scheduled ref="erpTaskJob" method="job3" cron="0 30 7 * * ?"/><!-- 每天7：30执行 -->
		<task:scheduled ref="erpTaskJob" method="job4" cron="0 05 3 * * ?"/><!-- 每天3：05执行 -->
		<task:scheduled ref="erpTaskJob" method="job5" cron="0 0 0/1 * * ?"/><!-- 每一小时执行一次 -->
		<task:scheduled ref="erpTaskJob" method="job333" cron="0 30 0/1 * * ?"/><!-- 每一小时执行一次 -->
		<task:scheduled ref="erpTaskJob" method="job6" cron="0 15 2 * * ?"/><!-- 每天2：15执行 -->
		<task:scheduled ref="erpTaskJob" method="job7" cron="0 15 0 * * ?"/><!-- 每天0：15执行 -->
		<task:scheduled ref="erpTaskJob" method="job5obt" cron="0 0/15 * * * ?"/><!-- 每隔15分钟执行 -->
	</task:scheduled-tasks>  
      
  
</beans>
