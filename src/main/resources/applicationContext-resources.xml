<?xml version="1.0" encoding="UTF-8"?><!-- bak the file by lanmosoft.com -->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">

    <context:property-placeholder location="classpath:lanmo.properties,classpath:mail.properties,classpath:sms.properties,classpath:paikeguwen.properties,classpath:paike.properties"/>

    <bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName" value="com.mysql.jdbc.Driver"/>
        <property name="url" value="jdbc:mysql://${database_url}/${database_name}?createDatabaseIfNotExist=true&amp;useUnicode=true&amp;characterEncoding=utf-8"/>
        <property name="username" value="${database_username}"/>
        <property name="password" value="${database_password}"/>

        <!-- 基础连接池配置 - 优化后 -->
		<property name="initialSize" value="10" />          <!-- 初始连接数：从5增加到10 -->
		<property name="maxActive" value="50" />            <!-- 最大活跃连接：从15增加到50 -->
		<property name="maxIdle" value="20" />              <!-- 最大空闲连接：从30减少到20 -->
	    <property name="minIdle" value="10" />              <!-- 最小空闲连接：从5增加到10 -->
	    <property name="maxWait" value="10000" />           <!-- 最大等待时间：从6秒增加到10秒 -->

	    <!-- 连接验证配置 - 增强 -->
	    <property name="testOnBorrow" value="true"/>
	    <property name="testOnReturn" value="true"/>        <!-- 新增：归还时验证 -->
	    <property name="testWhileIdle" value="true"/>
	    <property name="validationQuery" value="SELECT 1"/>
	    <property name="validationQueryTimeout" value="3"/> <!-- 新增：验证查询超时 -->

	    <!-- 空闲连接回收配置 - 新增 -->
	    <property name="timeBetweenEvictionRunsMillis" value="30000"/>  <!-- 30秒检查一次 -->
	    <property name="minEvictableIdleTimeMillis" value="300000"/>   <!-- 5分钟空闲后回收 -->
	    <property name="numTestsPerEvictionRun" value="5"/>            <!-- 每次检查5个连接 -->

	    <!-- 连接泄漏检测 - 新增 -->
	    <property name="removeAbandoned" value="true"/>                <!-- 启用连接泄漏检测 -->
	    <property name="removeAbandonedTimeout" value="300"/>          <!-- 5分钟未归还视为泄漏 -->
	    <property name="logAbandoned" value="true"/>                   <!-- 记录泄漏日志 -->

	    <!-- 其他优化配置 -->
	    <property name="poolPreparedStatements" value="true"/>
	    <property name="maxOpenPreparedStatements" value="100"/>       <!-- 新增：预编译语句池大小 -->
        <property name="defaultAutoCommit" value="false"/>             <!-- 改为false，由Spring管理事务 -->
        <property name="defaultTransactionIsolation" value="2"/>       <!-- READ_COMMITTED -->

        <!-- JMX监控 - 新增 -->
        <property name="jmxName" value="com.lanmosoft:type=DataSource,name=dataSource"/>
	</bean>

    <!-- Transaction manager for a single JDBC DataSource -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="transactionTemplate"
		class="org.springframework.transaction.support.TransactionTemplate">
		<property name="transactionManager">
			<ref local="transactionManager" />
		</property>
	</bean>

    <!-- Activates scanning of @Autowired -->
    <context:annotation-config/>

    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory1" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource" />
        <property name="typeAliasesPackage" value="com.lanmosoft.dao.model" />
    </bean>
        <!-- scan for mappers and let them be autowired -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.lanmosoft.dao.mapper" />
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory1" />
    </bean>


</beans>
