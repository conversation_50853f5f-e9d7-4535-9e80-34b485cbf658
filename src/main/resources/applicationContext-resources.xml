<?xml version="1.0" encoding="UTF-8"?><!-- bak the file by lanmosoft.com -->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">

    <context:property-placeholder location="classpath:lanmo.properties,classpath:mail.properties,classpath:sms.properties,classpath:paikeguwen.properties,classpath:paike.properties"/>

    <bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName" value="com.mysql.jdbc.Driver"/>
        <property name="url" value="jdbc:mysql://${database_url}/${database_name}?createDatabaseIfNotExist=true&amp;useUnicode=true&amp;characterEncoding=utf-8"/>
        <property name="username" value="${database_username}"/>
        <property name="password" value="${database_password}"/>
		<property name="initialSize" value="5" />
		<property name="maxActive" value="15" />
		<property name="maxIdle" value="30" /> 
	    <property name="minIdle" value="5" /> 
	    <property name="maxWait" value="6000" /> 
	    <property name="testOnBorrow" value="true"/> 
	    <property name="testWhileIdle" value="true"/> 
	    <property name="validationQuery" value="SELECT  1"/> 
	     <property name="poolPreparedStatements" value="true"/>
        <property name="defaultAutoCommit" value="true"/>
	</bean>
    
    <!-- Transaction manager for a single JDBC DataSource -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>
    
    <bean id="transactionTemplate"
		class="org.springframework.transaction.support.TransactionTemplate">
		<property name="transactionManager">
			<ref local="transactionManager" />
		</property>
	</bean>

    <!-- Activates scanning of @Autowired -->
    <context:annotation-config/>

    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory1" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource" />
        <property name="typeAliasesPackage" value="com.lanmosoft.dao.model" />
    </bean>
        <!-- scan for mappers and let them be autowired -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.lanmosoft.dao.mapper" />
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory1" />
    </bean>


</beans>
