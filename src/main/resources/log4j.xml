<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">

<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

    <appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern"
                value="%p - %C{1}.%M(%L) | %m%n"/>
        </layout>
    </appender>

    <logger name="net.sf.ehcache">
        <level value="ERROR"/>
    </logger>

    <logger name="org.apache">
        <level value="WARN"/>
    </logger>
    
    <logger name="org.hibernate">
        <level value="WARN"/>
    </logger>

    <!--logger name="org.hibernate.SQL">
        <level value="DEBUG"/>
    </logger-->

    <!-- Suppress warnings from Commons Validator -->
    <logger name="org.apache.commons.validator.ValidatorResources">
        <level value="ERROR"/>
    </logger>

    <logger name="org.springframework">
        <level value="WARN"/>
    </logger>

    <logger name="com.lanmosoft">
        <level value="DEBUG"/>
    </logger>


    <root>
        <level value="WARN"/>
        <appender-ref ref="CONSOLE"/>
    </root>

</log4j:configuration>
