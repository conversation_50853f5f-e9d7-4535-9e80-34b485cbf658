package com.lanmosoft.service.biz;

import com.lanmosoft.dao.mapper.QuerySchemeMapper;
import com.lanmosoft.dao.model.QueryScheme;
import com.lanmosoft.model.WhereCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QuerySchemeService {
    @Autowired
    QuerySchemeMapper querySchemeMapper;

    /**
     * 保存查询方案
     * @param queryScheme
     */
    public void insert(QueryScheme queryScheme) {
        querySchemeMapper.insert(queryScheme);
    }

    /**
     * 条件查询
     * @param wc
     * @return
     */
    public List<QueryScheme> query(WhereCondition wc) {
        return querySchemeMapper.query(wc);
    }

    /**
     * 查询条数
     * @param wc
     * @return
     */
    public int count(WhereCondition wc) {
        return  querySchemeMapper.count(wc);
    }

    /**
     * 删除
     * @param id
     */
    public void delete(String id) {
        querySchemeMapper.delete(id);
    }
}
