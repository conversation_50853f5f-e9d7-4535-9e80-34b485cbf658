package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.ErpLogMapper;
import com.lanmosoft.dao.model.ErpLog;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class ErpLogService {
	@Autowired
	ErpLogMapper erpLogMapper;
    public void insert(ErpLog t){
    	erpLogMapper.insert(t);
    }
    
    public void batchInsert(List<ErpLog> erpLogs){
    	erpLogMapper.batchInsert(erpLogs);
    }
    
    public void update(ErpLog t){
    	erpLogMapper.update(t); 
    }
   
    public void updateForce(ErpLog t){
    	erpLogMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	erpLogMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	erpLogMapper.deleteByCondition(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,ErpLog t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	erpLogMapper.updateByCondition(map); 
    }
    public List<ErpLog> query(WhereCondition wc){
    	return erpLogMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return erpLogMapper.count(wc);
    }
   
    public ErpLog loadById(String id){
    	return erpLogMapper.loadById(id);
    }
}
