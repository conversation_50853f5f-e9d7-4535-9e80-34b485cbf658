package com.lanmosoft.service.biz;
import com.lanmosoft.dao.mapper.FollowupLogMapper;
import com.lanmosoft.dao.model.FollowupLog;
import com.lanmosoft.model.WhereCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class FollowupLogService {
	@Autowired
	FollowupLogMapper followupLogMapper;
    public void insert(FollowupLog t){
    	followupLogMapper.insert(t);
    }
    
    public void update(FollowupLog t){
    	followupLogMapper.update(t); 
    }
   
    public void updateForce(FollowupLog t){
    	followupLogMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	followupLogMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	followupLogMapper.deleteByCondition(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,FollowupLog t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	followupLogMapper.updateByCondition(map); 
    }
    public List<FollowupLog> query(WhereCondition wc){
    	return followupLogMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return followupLogMapper.count(wc);
    }
   
    public FollowupLog loadById(String id){
    	return followupLogMapper.loadById(id);
    }

    public List<FollowupLog> queryAll(WhereCondition wc) {
        return followupLogMapper.queryAll(wc);
    }
    public int countAll(WhereCondition wc){
        return followupLogMapper.countAll(wc);
    }
}
