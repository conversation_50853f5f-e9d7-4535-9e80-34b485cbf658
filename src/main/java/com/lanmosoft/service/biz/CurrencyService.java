package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.CurrencyMapper;
import com.lanmosoft.dao.model.Currency;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class CurrencyService {
	@Autowired
	CurrencyMapper currencyMapper;
    public void insert(Currency t){
    	currencyMapper.insert(t);
    }
    
    public void update(Currency t){
    	currencyMapper.update(t); 
    }
   
    public void updateForce(Currency t){
    	currencyMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	currencyMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	currencyMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	currencyMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Currency t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	currencyMapper.updateByCondition(map); 
    }
    public List<Currency> query(WhereCondition wc){
    	return currencyMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return currencyMapper.count(wc);
    }
   
    public Currency loadById(String id){
    	return currencyMapper.loadById(id);
    }
}
