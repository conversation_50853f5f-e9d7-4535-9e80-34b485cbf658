package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.TeachingwayMapper;
import com.lanmosoft.dao.model.Teachingway;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class TeachingwayService {
	@Autowired
	TeachingwayMapper teachingwayMapper;
    public void insert(Teachingway t){
    	teachingwayMapper.insert(t);
    }
    
    public void update(Teachingway t){
    	teachingwayMapper.update(t); 
    }
   
    public void updateForce(Teachingway t){
    	teachingwayMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	teachingwayMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	teachingwayMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	teachingwayMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Teachingway t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	teachingwayMapper.updateByCondition(map); 
    }
    public List<Teachingway> query(WhereCondition wc){
    	return teachingwayMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return teachingwayMapper.count(wc);
    }
   
    public Teachingway loadById(String id){
    	return teachingwayMapper.loadById(id);
    }
}
