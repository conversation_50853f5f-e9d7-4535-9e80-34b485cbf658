package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.GradeMapper;
import com.lanmosoft.dao.model.Grade;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class GradeService {
	@Autowired
	GradeMapper gradeMapper;
    public void insert(Grade t){
    	gradeMapper.insert(t);
    }
    
    public void update(Grade t){
    	gradeMapper.update(t); 
    }
   
    public void updateForce(Grade t){
    	gradeMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	gradeMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	gradeMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	gradeMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Grade t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	gradeMapper.updateByCondition(map); 
    }
    public List<Grade> query(WhereCondition wc){
    	return gradeMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return gradeMapper.count(wc);
    }
   
    public Grade loadById(String id){
    	return gradeMapper.loadById(id);
    }
}
