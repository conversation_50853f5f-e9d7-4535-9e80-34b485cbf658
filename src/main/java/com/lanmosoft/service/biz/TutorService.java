package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.TutorMapper;
import com.lanmosoft.dao.model.Tutor;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class TutorService {
	@Autowired
	TutorMapper tutorMapper;
    public void insert(Tutor t){
    	tutorMapper.insert(t);
    }
    
    public void update(Tutor t){
    	tutorMapper.update(t); 
    }
   
    public void updateForce(Tutor t){
    	tutorMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	tutorMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	tutorMapper.deleteByCondition(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Tutor t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	tutorMapper.updateByCondition(map); 
    }
    public List<Tutor> query(WhereCondition wc){
    	return tutorMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return tutorMapper.count(wc);
    }
   
    public Tutor loadById(String id){
    	return tutorMapper.loadById(id);
    }
}
