package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.View_student_leaveMapper;
import com.lanmosoft.dao.model.View_student_leave;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class View_student_leaveService {
	@Autowired
	View_student_leaveMapper view_student_leaveMapper;
    public void insert(View_student_leave t){
    	view_student_leaveMapper.insert(t);
    }
    
    public void update(View_student_leave t){
    	view_student_leaveMapper.update(t); 
    }
   
    public void updateForce(View_student_leave t){
    	view_student_leaveMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	view_student_leaveMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	view_student_leaveMapper.deleteByCondition(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,View_student_leave t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	view_student_leaveMapper.updateByCondition(map); 
    }
    public List<View_student_leave> query(WhereCondition wc){
    	return view_student_leaveMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return view_student_leaveMapper.count(wc);
    }
   
    public View_student_leave loadById(String id){
    	return view_student_leaveMapper.loadById(id);
    }
}
