package com.lanmosoft.service.biz;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.FilerecordMapper;
import com.lanmosoft.dao.model.Filerecord;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class FilerecordService {
	@Autowired
	FilerecordMapper filerecordMapper;
    public void insert(Filerecord t){
    	filerecordMapper.insert(t);
    }
    
    public void update(Filerecord t){
    	filerecordMapper.update(t); 
    }
   
    public void updateForce(Filerecord t){
    	filerecordMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	filerecordMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	List<Filerecord> filerecords = filerecordMapper.query(wc);
		/*删除文件*/
		for(Filerecord fr : filerecords){
			String logicPath = fr.getLogicPath();
			File file = new File(logicPath);
			if(file.exists()){
				file.delete();
			}
		}
    	filerecordMapper.deleteByCondition(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Filerecord t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	filerecordMapper.updateByCondition(map); 
    }
    public List<Filerecord> query(WhereCondition wc){
    	return filerecordMapper.query(wc); 
    }
 
    public List<Filerecord> query_followup(WhereCondition wc){
    	return filerecordMapper.query_followup(wc); 
    }
    public List<Filerecord> query_classschedule(WhereCondition wc){
    	return filerecordMapper.query_classschedule(wc); 
    }
    
    public int count_followup(WhereCondition wc){
    	return filerecordMapper.count_followup(wc);
    }
    
    public int count_classschedule(WhereCondition wc){
    	return filerecordMapper.count_classschedule(wc);
    }
    
    public int count(WhereCondition wc){
    	return filerecordMapper.count(wc);
    }
   
    public Filerecord loadById(String id){
    	return filerecordMapper.loadById(id);
    }
    /**
     * 删除文件记录 同时删除文件
     * @param ids 引用者id
     */
	public void deleteByReference_ids(List<String> ids) {
		WhereCondition wc = new WhereCondition();
		wc.andIn("reference_id", ids);
		List<Filerecord> filerecords = filerecordMapper.query(wc);
		/*删除文件*/
		for(Filerecord fr : filerecords){
			String logicPath = fr.getLogicPath();
			File file = new File(logicPath);
			if(file.exists()){
				file.delete();
			}
		}
		/*删除文件记录*/
		filerecordMapper.deleteByCondition(wc);
		
	}
}
