package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.TeacherviewMapper;
import com.lanmosoft.dao.model.Teacherview;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class TeacherviewService {
	@Autowired
	TeacherviewMapper teacherviewMapper;
    public void insert(Teacherview t){
    	teacherviewMapper.insert(t);
    }
    
    public void update(Teacherview t){
    	teacherviewMapper.update(t); 
    }
   
    public void updateForce(Teacherview t){
    	teacherviewMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	teacherviewMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	teacherviewMapper.deleteByCondition(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Teacherview t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	teacherviewMapper.updateByCondition(map); 
    }
    public List<Teacherview> query(WhereCondition wc){
    	return teacherviewMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return teacherviewMapper.count(wc);
    }
   
    public Teacherview loadById(String id){
    	return teacherviewMapper.loadById(id);
    }

    public void updateInactive() {
        teacherviewMapper.updateInactive();
    }

    public void updateActive() {
        teacherviewMapper.updateActive();
    }
}
