package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.View_coures_teacherMapper;
import com.lanmosoft.dao.model.View_coures_teacher;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class View_coures_teacherService {
	@Autowired
	View_coures_teacherMapper view_coures_teacherMapper;
    public void insert(View_coures_teacher t){
    	view_coures_teacherMapper.insert(t);
    }
    
    public void update(View_coures_teacher t){
    	view_coures_teacherMapper.update(t); 
    }
   
    public void updateForce(View_coures_teacher t){
    	view_coures_teacherMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	view_coures_teacherMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	view_coures_teacherMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	view_coures_teacherMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,View_coures_teacher t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	view_coures_teacherMapper.updateByCondition(map); 
    }
    public List<View_coures_teacher> query(WhereCondition wc){
    	return view_coures_teacherMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return view_coures_teacherMapper.count(wc);
    }
   
    public View_coures_teacher loadById(String id){
    	return view_coures_teacherMapper.loadById(id);
    }
}
