package com.lanmosoft.service.biz;
import com.lanmosoft.dao.mapper.ContractMapper;
import com.lanmosoft.dao.model.Contract;
import com.lanmosoft.model.WhereCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class ContractService {
	@Autowired
	ContractMapper contractMapper;
    public void insert(Contract t){
    	contractMapper.insert(t);
    }
    
    public void update(Contract t){
    	contractMapper.update(t); 
    }
   
    public void updateForce(Contract t){
    	contractMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	contractMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	contractMapper.deleteByCondition(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Contract t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	contractMapper.updateByCondition(map); 
    }
    public List<Contract> query(WhereCondition wc){
    	return contractMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return contractMapper.count(wc);
    }
   
    public Contract loadById(String id){
    	return contractMapper.loadById(id);
    }
    
    /**
     * 获取某学生可用合同
     * @param student_id 学生ID
     * @param isInculuedGiven 是否包含赠送的合同
     * @return
     */
    public List<Contract> getUseableContract(String student_id, boolean isInculuedGiven){
    	return contractMapper.getUseableContract(student_id, isInculuedGiven);
    }
    
    
    /**
     * 获取某学生合同可用单元数
     * @param student_id
     * @return
     */
    public Double countUnitOfContract(String student_id){
    	return contractMapper.countUnitOfContract(student_id);
    }
   
    
    
    /**
     * 获取消耗课时对应的合同
     * @param student_id
     * @param consumedClass
     * @return
     */
    public List<Contract> getContractByClass1(String student_id, Double consumedClass){
    	List<Contract> list = getUseableContract(student_id,true);
    	List<Contract> relist = new ArrayList<Contract>();
    	if(CollectionUtils.isEmpty(list)){
    		Contract item1 = new Contract();
    		item1.setCurrentConsumedClass(consumedClass);
    		item1.setConsumedClass(consumedClass);
			relist.add(item1);
    	}else{
    		Contract item1 = list.get(0);
    		if((item1.getAmount()*2-(item1.getConsumedClass()==null?0D:item1.getConsumedClass()))>=consumedClass){
				item1.setCurrentConsumedClass(consumedClass);
				item1.setConsumedClass((item1.getConsumedClass()==null?0D:item1.getConsumedClass())+consumedClass);
				relist.add(item1);
    		}else{
    			item1.setCurrentConsumedClass(item1.getAmount()*2-(item1.getConsumedClass()==null?0D:item1.getConsumedClass()));
    			item1.setConsumedClass(item1.getAmount()*2);
    			relist.add(item1);
				if(list.size()==1){
					Contract item2 = new Contract();
					item2.setCurrentConsumedClass(consumedClass - item1.getCurrentConsumedClass());
					item2.setConsumedClass(consumedClass - item1.getCurrentConsumedClass());
					relist.add(item2);
	    		}else{
	    			Contract item2 = list.get(1);
	    			item2.setCurrentConsumedClass(consumedClass - item1.getCurrentConsumedClass());
	    			item2.setConsumedClass(consumedClass - item1.getCurrentConsumedClass());
	    			relist.add(item2);
	    		}
    		}
    	}
		return relist;
    }
    
    /**
     * 获取未消耗完课时的合同
     * @param student_id
     * @param
     * @return
     */
    public List<Contract> getContractByClass(String student_id){
		return getUseableContract(student_id,true);
    }

	public void insertHis(Contract t){
    	contractMapper.insertHis(t);
    }

	public List<Contract> getContractByClass(String student_id, List<String> contractType2List) {
		return getUseableContract(student_id,true,contractType2List);
	}

	public List<Contract> getContractByClass(String student_id, List<String> contractType2List, String course_id) {
		return getUseableContract(student_id,true,contractType2List, course_id);
	}

	/**
     * 获取某学生可用合同
     * @param student_id 学生ID
     * @param isInculuedGiven 是否包含赠送的合同
     * @return
     */
    public List<Contract> getUseableContract(String student_id, boolean isInculuedGiven, List<String> contractType2List){
    	return contractMapper.getUseableContractType(student_id, isInculuedGiven,contractType2List);
    }

	public List<Contract> getUseableContract(String student_id, boolean isInculuedGiven, List<String> contractType2List, String course_id){
		return contractMapper.getUseableContractTypeCourse(student_id, isInculuedGiven,contractType2List, course_id);
	}

	/**
	 * 根据SF合同ID获取TE对应的合同信息
	 * @param contractId
	 * @return
	 */
    public Contract getContractByContractId(String contractId) {

    	WhereCondition wc = new WhereCondition();

    	wc.andEquals("contractId",contractId);
		List<Contract> query = query(wc);
		if(CollectionUtils.isEmpty(query)){
			return null;
		}
		return query.get(0);
	}
}
