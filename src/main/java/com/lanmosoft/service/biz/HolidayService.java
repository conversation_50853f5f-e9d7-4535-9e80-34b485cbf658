package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.HolidayMapper;
import com.lanmosoft.dao.model.Holiday;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class HolidayService {
	@Autowired
	HolidayMapper holidayMapper;
    public void insert(Holiday t){
    	holidayMapper.insert(t);
    }
    
    public void update(Holiday t){
    	holidayMapper.update(t); 
    }
   
    public void updateForce(Holiday t){
    	holidayMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	holidayMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	holidayMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	holidayMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Holiday t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	holidayMapper.updateByCondition(map); 
    }
    public List<Holiday> query(WhereCondition wc){
    	return holidayMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return holidayMapper.count(wc);
    }
   
    public Holiday loadById(String id){
    	return holidayMapper.loadById(id);
    }
}
