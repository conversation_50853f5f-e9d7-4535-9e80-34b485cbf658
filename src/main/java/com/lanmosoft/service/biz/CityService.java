package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.CityMapper;
import com.lanmosoft.dao.model.City;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class CityService {
	@Autowired
	CityMapper cityMapper;
    public void insert(City t){
    	cityMapper.insert(t);
    }
    
    public void update(City t){
    	cityMapper.update(t); 
    }
   
    public void updateForce(City t){
    	cityMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	cityMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	cityMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	cityMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,City t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	cityMapper.updateByCondition(map); 
    }
    public List<City> query(WhereCondition wc){
    	return cityMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return cityMapper.count(wc);
    }
   
    public City loadById(String id){
    	return cityMapper.loadById(id);
    }
}
