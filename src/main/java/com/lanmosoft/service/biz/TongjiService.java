package com.lanmosoft.service.biz;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.TongjiMapper;
import com.lanmosoft.dao.model.Zidingyi;


@Service
public class TongjiService {
	
	@Autowired
	TongjiMapper tongjiMapper;
	
	public int count(Zidingyi z){
    	return tongjiMapper.count(z);
    }
	
	public List<Map> query(Zidingyi z){
    	return tongjiMapper.query(z);
    }

	public List<LinkedHashMap> query2(Zidingyi z){
		return tongjiMapper.query2(z);
	}
	
	public void delete(Zidingyi z){
    	tongjiMapper.delete(z);
    }

}
