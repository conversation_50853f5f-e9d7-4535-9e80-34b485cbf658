package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.Teacher_freeTimeMapper;
import com.lanmosoft.dao.model.Teacher_freeTime;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class Teacher_freeTimeService {
	@Autowired
	Teacher_freeTimeMapper teacher_freeTimeMapper;
    public void insert(Teacher_freeTime t){
    	teacher_freeTimeMapper.insert(t);
    }
    
    public void update(Teacher_freeTime t){
    	teacher_freeTimeMapper.update(t); 
    }
   
    public void updateForce(Teacher_freeTime t){
    	teacher_freeTimeMapper.updateForce(t); 
    }
   
    public void delete(Integer id){
    	teacher_freeTimeMapper.delete(String.valueOf(id));
    }
   
    public void deleteByCondition(WhereCondition wc){
    	teacher_freeTimeMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	teacher_freeTimeMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Teacher_freeTime t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	teacher_freeTimeMapper.updateByCondition(map); 
    }
    public List<Teacher_freeTime> query(WhereCondition wc){
    	return teacher_freeTimeMapper.query(wc); 
    }

	public List<Teacher_freeTime> queryLong(WhereCondition wc){
		return teacher_freeTimeMapper.queryLong(wc);
	}
    
    public int count(WhereCondition wc){
    	return teacher_freeTimeMapper.count(wc);
    }
   
    public Teacher_freeTime loadById(String id){
    	return teacher_freeTimeMapper.loadById(id);
    }

	public void batchInsert(List<Teacher_freeTime> t_FreeTimeNew) {
		
		teacher_freeTimeMapper.batchInsert(t_FreeTimeNew);
	}
}
