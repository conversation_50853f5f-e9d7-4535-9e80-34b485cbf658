package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lanmosoft.util.LanDateUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.ClassScheduleMapper;
import com.lanmosoft.dao.model.ClassSchedule;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class ClassScheduleService {
	@Autowired
	ClassScheduleMapper classScheduleMapper;
    public void insert(ClassSchedule t){
    	classScheduleMapper.insert(t);
    }
    
    public void update(ClassSchedule t){
    	classScheduleMapper.update(t); 
    }
   
    public void updateForce(ClassSchedule t){
    	classScheduleMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	classScheduleMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	classScheduleMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	classScheduleMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,ClassSchedule t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	classScheduleMapper.updateByCondition(map); 
    }
    public List<ClassSchedule> query(WhereCondition wc){
    	return classScheduleMapper.query(wc); 
    }
    public List<ClassSchedule> querycheck(ClassSchedule wc){
    	return classScheduleMapper.querycheck(wc); 
    }
    public List<ClassSchedule> querycheckNextDay(ClassSchedule wc){
    	return classScheduleMapper.querycheckNextDay(wc); 
    }
    public List<ClassSchedule> querycheckRoom(ClassSchedule wc){
    	return classScheduleMapper.querycheckRoom(wc); 
    }
    
    
    public int count(WhereCondition wc){
    	return classScheduleMapper.count(wc);
    }
   
    public ClassSchedule loadById(String id){
    	return classScheduleMapper.loadById(id);
    }
    
    /**
     * 学生和老师在指定的时间段是否被占用
     * @param scheduleDate
     * @param startTime
     * @param endTime
     * @param teacher_id
     * @param student_id
     * @return
     */
    public boolean isOccupied(String scheduleDate, String startTime, String endTime, String teacher_id, String student_id){
    	Map<String, String> map = new HashMap<String, String>();
    	map.put("scheduleDate", scheduleDate);
    	map.put("startTime", startTime);
    	map.put("endTime", endTime);
    	map.put("teacher_id", teacher_id);
    	map.put("student_id", student_id);
    	int count = classScheduleMapper.countClass(map);
    	if(count>0) return true;
    	else return false;
		
    	
    }
    /**
     * 教室在指定的时间段是否被占用
     * @param scheduleDate
     * @param startTime
     * @param endTime
     * @param teacher_id
     * @param student_id
     * @return
     */
    public boolean classroomIsOccupied(String scheduleDate, String startTime, String endTime, String classroom_id){
    	Map<String, String> map = new HashMap<String, String>();
    	map.put("scheduleDate", scheduleDate);
    	map.put("startTime", startTime);
    	map.put("endTime", endTime);
    	map.put("classroom_id", classroom_id);
    	int count = classScheduleMapper.countClass(map);
    	if(count>0) return true;
    	else return false;
		
    	
    }
    public double sumClass(WhereCondition wc){
    	return classScheduleMapper.sumClass(wc);
    }


	/**
	 * 判断时刻,获取where条件
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public String getQueryWhereClassroomBytime(String startTime ,String endTime,String day){
		String sql ="";
		long longStart = 0;
		long longEnd =0;
		int flag = LanDateUtils.compart_time(startTime, endTime);
		String endFormat = "yyyy-MM-dd HH:mm";
		String startFormat = "yyyy-MM-dd HH:mm";

		if(StringUtils.equals(endTime,"24:00") || StringUtils.equals(endTime,"00:00")){
			endTime = "23:59:59";
			endFormat = "yyyy-MM-dd HH:mm:ss";
		}
		//先判断时间是否跨天
		if(flag != -1){ //跨天
			String endDate = LanDateUtils.getNext_Day(day,1);
			longStart = LanDateUtils.stringToLong(day+" "+startTime,startFormat);
			longEnd = LanDateUtils.stringToLong(endDate+" "+endTime,endFormat);

		}else{//不跨天
			longStart = LanDateUtils.stringToLong(day+" "+startTime,startFormat);
			longEnd = LanDateUtils.stringToLong(day+" "+endTime,endFormat);
		}
		sql += " AND (( ";
		sql += " 			'"+longStart+"' <= longStartTime AND longEndTime <= '"+longEnd+"' ";
		sql += " 		)OR (	 ";
		sql += " 			longStartTime < '"+longStart+"' AND '"+longEnd+"' < longEndTime ";
		sql += " 		)OR (	 ";
		sql += " 			longStartTime < '"+longStart+"' AND '"+longStart+"' < longEndTime ";
		sql += " 		)OR (	 ";
		sql += " 		longStartTime < '"+longEnd+"' AND '"+longEnd+"' < longEndTime	 ";
		sql += " )) ";



		return sql;
	}
}
