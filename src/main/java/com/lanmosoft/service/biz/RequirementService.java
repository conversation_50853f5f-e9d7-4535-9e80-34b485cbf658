package com.lanmosoft.service.biz;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.mapper.RequirementMapper;
import com.lanmosoft.dao.model.Requirement;
import com.lanmosoft.model.WhereCondition;
/**
 * 服务层
 * <AUTHOR>
 *
 */
@Service
public class RequirementService {
	@Autowired
	RequirementMapper requirementMapper;
    public void insert(Requirement t){
    	requirementMapper.insert(t);
    }
    
    public void update(Requirement t){
    	requirementMapper.update(t); 
    }
   
    public void updateForce(Requirement t){
    	requirementMapper.updateForce(t); 
    }
   
    public void delete(String id){
    	requirementMapper.delete(id);
    }
   
    public void deleteByCondition(WhereCondition wc){
    	requirementMapper.deleteByCondition(wc); 
    }
	public void deleteBystatus(WhereCondition wc){
    	requirementMapper.deleteBystatus(wc); 
    }
	
	public void updateByCondition(WhereCondition wc,Requirement t){
    	Map map  = new HashMap();
    	map.put("domain", t);
    	map.put("wc", wc);
    	requirementMapper.updateByCondition(map); 
    }
    public List<Requirement> query(WhereCondition wc){
    	return requirementMapper.query(wc); 
    }
    
    public int count(WhereCondition wc){
    	return requirementMapper.count(wc);
    }
    
    public List<Requirement> querycheck(Requirement r){
    	return requirementMapper.querycheck(r);
    }
   
    public Requirement loadById(String id){
    	return requirementMapper.loadById(id);
    }
}
