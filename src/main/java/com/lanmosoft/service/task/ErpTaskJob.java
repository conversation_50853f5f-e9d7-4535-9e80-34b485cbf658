package com.lanmosoft.service.task;

import com.lanmosoft.dao.mapper.TongjiMapper;
import com.lanmosoft.dao.mapper.XulieMapper;
import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.model.ContractInfo;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.LanmoMailSender;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.lanmobase.SequenceManager;
import com.lanmosoft.service.model.MailSenderInfo;
import com.lanmosoft.service.model.SmsSenderInfo;
import com.lanmosoft.service.web.erp.ErpServiceImpl;
import com.lanmosoft.thread.AsynTaskService;
import com.lanmosoft.util.*;
import com.lanmosoft.webapp.webmodel.LoginModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.util.HSSFColor;
import org.hibernate.search.annotations.Analyzer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.sql.Time;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ErpTaskJob {
	
	@Autowired
	ErpServiceImpl erpServiceImpl;
	@Autowired
	Teacher_courseService teacher_courseService;
	@Autowired
	StudentService studentService;
	@Autowired
	TongjiService tongjiService;
	@Autowired
	ContractService contractService;
	@Autowired
	ClassroomService classroomService;
	@Autowired
	ZoneService zoneService;
	@Autowired
	SmsSenderInfo smsSenderInfo;
	@Autowired
	Consumption_student_contractService consumption_student_contractService;
	@Autowired
	Consumption_studentService consumption_studentService;
	@Autowired
	EmailqueueService emailqueueService;
	@Autowired
	Student_timezoneService student_timezoneService;
	@Autowired
	TimezoneService timezoneService;
	@Autowired
	Teacher_timezoneService teacher_timezoneService;
	@Autowired
	LanmoMailSender lanmoMailSender;
	@Autowired
	Student_zoneService student_zoneService;
	@Autowired
	View_consumption_student_contractService view_consumption_student_contractService;
	@Autowired
	CourseService courseService;
	@Autowired
	SequenceManager sequenceManager;
	@Autowired
	HttpServletRequest request2;
	@Autowired
	OperationLogService operationLogService;
	@Autowired
	AsynTaskService asynTaskService;
	@Autowired
	TeacherviewService teacherviewService;

	protected OperationLog operationLog=new OperationLog();
	/**
	 * 每月1号1:00执行，发送上个月合同数据到ERP
	 */
	public void job1(){
		Date nowDate = LanDateUtils.getNowDate();
		String firstDay = LanDateUtils.getFirstDayOfMonth(nowDate, -1);
		String lastDay = LanDateUtils.getLastDayOfMonth(nowDate, -1);
		System.out.println("firstDay:"+firstDay);
		System.out.println("lastDay"+lastDay);
		//查询数据
		List<ContractInfo> contractInfos = erpServiceImpl.generateContractInfo(firstDay, lastDay);
		if(CollectionUtils.isNotEmpty(contractInfos)){
			//发送数据
			List<ErpLog> erpLogs = erpServiceImpl.sendContractInfo(contractInfos);
			//记录日志
			erpServiceImpl.erpLogger(erpLogs);
		}
	}



	/**
	 * 定时任务，每天17:30执行，发送上课通知短信
	 */
	public void job33(String tz, String sdt, String edt){
		//get next day in target timezone


		//获取当前时间的下一天
		String next_day = LanDateUtils.getNext_Day(LanDateUtils.format(new Date(), "yyyy-MM-dd"), 1);

		//学生 huangbo
		// 书写sql
		String sql=" SELECT a.BUType , a.studentTimeZone,a.scheduledDate, a.startTime kaishi,a.endTime jiesu,a.classroom_id,b.id xueyuanId,b.chineseName xueyuan,b.mobile shouji,b.secondarMobile beiyongshouji,d.name kecheng,e.name teachingwayName,f.city_id,f.`name` xiaoqu";
		//sql+=" FROM view_classschedule_timezone a join student b on a.student_id = b.id join student_zone c on a.zone_id = c.zone_id  and c.student_id = b.id and c.sms = '1' join course d on a.course_id = d.id join teachingway e on a.teachingWay = e.id join zone f on a.zone_id = f.id ";
		sql+=" FROM view_classschedule_timezone a join student b on a.student_id = b.id join student_zone c on a.zone_id = c.zone_id  and c.student_id = b.id join course d on a.course_id = d.id join teachingway e on a.teachingWay = e.id join zone f on a.zone_id = f.id ";
		sql+=" where concat(a.scheduledDate, ' ', a.startTime) >= '" + sdt + "' and concat(a.scheduledDate, ' ', a.startTime) <= '" + edt + "' AND a.`status` = '1' and  ";//and a.BUType ='1'
		sql+="  a.studentTimeZone='" + tz + "'";
		sql+=" ORDER BY a.endTime ASC ";

		//自定义查询数据
		Zidingyi z=new Zidingyi();
		z.setSql(sql);
		// to replace tongjiservice bean
		ClassPathXmlApplicationContext classPathXmlApplicationContext = new ClassPathXmlApplicationContext(
				"classpath*:applicationContext-resources.xml");
		TongjiMapper ts = (TongjiMapper) classPathXmlApplicationContext.getBean("tongjiMapper");
		//XulieMapper ss = (XulieMapper) classPathXmlApplicationContext.getBean("xulieMapper");

		//List<Map> list=ts.query(z);
		List<Map> list=tongjiService.query(z);
		System.out.println("sql1-->"+sql);
		//用来存取数据
		Map<String,String> ms=new HashMap<String,String>();
		//遍历查询到的学生数据
		if(list != null && list.size() != 0){
			for (Map<String ,String> m : list) {
				String shouji = m.get("shouji") != null ? m.get("shouji").toString() : "";
				String beiyongshouji = m.get("beiyongshouji") != null ? m.get("beiyongshouji").toString() : "";

				//手机为空error
				if(StringUtils.isBlank(shouji)){
					shouji = "error";
				}
				if(StringUtils.isBlank(beiyongshouji)){
					beiyongshouji = "error";
				}
				if(StringUtils.isNotBlank(shouji) || StringUtils.isNotBlank(beiyongshouji)){
					String kaishi = m.get("kaishi") != null ? m.get("kaishi").toString() : "";
					String jiesu = m.get("jiesu") != null ? m.get("jiesu").toString() : "";
					String classroom_id = m.get("classroom_id") != null ? m.get("classroom_id").toString() : "";
					String xueyuanId = m.get("xueyuanId") != null ? m.get("xueyuanId").toString() : "";
					String xueyuan = m.get("xueyuan") != null ? m.get("xueyuan").toString() : "";
					String kecheng = m.get("kecheng") != null ? m.get("kecheng").toString() : "";
					String teachingwayName = m.get("teachingwayName") != null ? m.get("teachingwayName").toString() : "";
					String city_id = m.get("city_id") != null ? m.get("city_id").toString() : "";
					String xiaoqu = m.get("xiaoqu") != null ? m.get("xiaoqu").toString() : "";
					String scheduledDate = m.get("scheduledDate") != null ? new SimpleDateFormat("yyyy-MM-dd").format(m.get("scheduledDate")) : "";  ;
					List llstart = TimezoneUtil.timezoneChange(scheduledDate, kaishi ,tz,"S");
					List llend = TimezoneUtil.timezoneChange(scheduledDate, jiesu ,tz,"S");
					//拼写短些内容
					String content1="【必益教育】"+xueyuan+",你好，您明天在必益学院"+xiaoqu+"的课程安排如下：";
					String content2="";
					//判断是否是上海地区
					if("Id_city00000001".equals(city_id)){

						content2 = " "+(String)llstart.get(2) + " "+(String)llstart.get(1)+"-"+(String)llend.get(1) +" "+tz+" "+kecheng;

						//判断上课方式
						if("远程授课".equals(teachingwayName)){
							content2 +="-OL ";
						}else if("上门授课".equals(teachingwayName)){
							content2 +="-HM ";
						}else{
							//查询教室
							if(!"".equals(classroom_id)){
								Classroom cr=classroomService.loadById(classroom_id);
								content2+="-"+cr.getName();
							}
						}
					}else{
						content2 = " "+(String)llstart.get(2) + " "+(String)llstart.get(1)+"-"+(String)llend.get(1) +" "+tz+" "+kecheng;
						//查询教室
//						if(!"".equals(classroom_id)){
//							Classroom cr=classroomService.loadById(classroom_id);
//							content2+="-"+cr.getName();
//						}

					}

					String content3=" 请提前做好准备";

					//合并
					String key = xueyuanId+"&!"+shouji+"&!"+xiaoqu+"&!"+beiyongshouji;

					String content = ms.get(key);
					if(content != null && content.length() != 0){
						String[] split = content.split("&!");
						String content2arr = split[1];
						content2 = content2arr + content2;
						ms.put(key,content1+"&!"+content2+"&!"+content3);
					}else{
						ms.put(key,content1+"&!"+content2+"&!"+content3);
					}

				}
			}
		}

		//发送学生短信
		if(ms != null && ms.size() != 0){
			for(Map.Entry<String,String> entry : ms.entrySet()){
				String[] keyarr  = entry.getKey().split("&!");
				String shouji = keyarr[1];
				String beiyongshouji = keyarr[3];

				String valuearr = entry.getValue().replaceAll("&!","");
				//try {
					if(!StringUtils.equalsIgnoreCase("error",shouji)){
						//sendSms(shouji,valuearr,smsSenderInfo.getSmsSvcUrl(),smsSenderInfo.getPassword(),smsSenderInfo.getCust_code());
						String context="Chris:学生短信发送：手机号："+shouji+",学生:"+keyarr[0]+"内容:"+valuearr;
						System.out.println(context);

						//获取发送邮件服务器账户信息
						MailSenderInfo mailSenderInfo = new MailSenderInfo();
						mailSenderInfo.setHost("smtp.mxhichina.com");
						mailSenderInfo.setPort(25);
						mailSenderInfo.setProtocol("SMTP");
						mailSenderInfo.setFrom("<EMAIL>");
						mailSenderInfo.setUserName("<EMAIL>");
						mailSenderInfo.setPassword("BEHKabc123");
						//LanmoMailSender lanmoMailSender = new LanmoMailSender();
						boolean isSuccess =lanmoMailSender.sendMassageAppoint("<EMAIL>", null, null, context, "SUBJECT",mailSenderInfo);
						/*operationLog.setId(sequenceManager.generateId("operationLog"));
						operationLog.setCreateTime(new Date());
						operationLog.setContent(context);
						operationLog.setContent(context);
						operationLogService.insert(operationLog);*/

					}
					/*if(!StringUtils.equalsIgnoreCase("error",beiyongshouji)){
						//sendSms(beiyongshouji,valuearr,smsSenderInfo.getSmsSvcUrl(),smsSenderInfo.getPassword(),smsSenderInfo.getCust_code());
						String context="Chris:学生短信发送：手机号："+beiyongshouji+",学生:"+keyarr[0]+"内容:"+valuearr;
						System.out.println(context);

						operationLog.setId(sequenceManager.generateId("operationLog"));
						operationLog.setCreateTime(new Date());
						operationLog.setContent(context);
						operationLogService.insert(operationLog);

					}*/

				//} catch (IOException e) {
				//	e.printStackTrace();
				//}
			}
		}

		//老师
		// huangbo
		// 书写sql
//		String sqlls=" SELECT s.englishName xueyuanEName, a.BUType,a.startTime kaishi,a.endTime jiesu,a.classroom_id,b.id laoshiId,b.englishName laoshi,b.mobile shouji,d. NAME kecheng,e. NAME teachingwayName,f.city_id,f.`name` xiaoqu ";
//		sqlls+=" FROM classschedule a JOIN user b ON a.teacher_id = b.id JOIN teacher_zone c ON a.zone_id = c.zone_id AND c.teacher_id = b.id AND c.sms = '1' JOIN course d ON a.course_id = d.id JOIN teachingway e ON a.teachingWay = e.id JOIN zone f ON a.zone_id = f.id JOIN student s on s.id = a.student_id ";
//		sqlls+=" WHERE a.scheduledDate = '"+next_day+"' AND a.`status` = '1' and a.BUType ='1' ";
//		sqlls+=" ORDER BY a.endTime ASC ";
//
//		//自定义查询数据
//		Zidingyi zls=new Zidingyi();
//		zls.setSql(sqlls);
//		List<Map> listls=tongjiService.query(zls);
//		System.out.println("sqlls-->"+sqlls);
//		//用来存取数据
//		Map<String,String> mt=new HashMap<String,String>();
//		//遍历查询到的学生数据
//		if(listls != null && listls.size() != 0){
//			for (Map<String ,String> m : listls) {
//				String shouji = m.get("shouji") != null ? m.get("shouji").toString() : "";
//				if(!"".equals(shouji)){
//					String kaishi = m.get("kaishi") != null ? m.get("kaishi").toString() : "";
//					String jiesu = m.get("jiesu") != null ? m.get("jiesu").toString() : "";
//					String classroom_id = m.get("classroom_id") != null ? m.get("classroom_id").toString() : "";
//					String laoshiId = m.get("laoshiId") != null ? m.get("laoshiId").toString() : "";
//					String laoshi = m.get("laoshi") != null ? m.get("laoshi").toString() : "";
//					String kecheng = m.get("kecheng") != null ? m.get("kecheng").toString() : "";
//					String teachingwayName = m.get("teachingwayName") != null ? m.get("teachingwayName").toString() : "";
//					String city_id = m.get("city_id") != null ? m.get("city_id").toString() : "";
//					String xiaoqu = m.get("xiaoqu") != null ? m.get("xiaoqu").toString() : "";
//					String xueyuanEName = m.get("xueyuanEName") != null ? m.get("xueyuanEName").toString() : "";
//
//					//拼写短些内容
//					String content1="【必益教育·】Dear "+laoshi+",You have following class at "+xiaoqu+" tomorrow:";
//					String content2="";
//					//判断是否是上海地区
//					if("Id_city00000001".equals(city_id)){
//
//						content2 = " \r\n "+kaishi+"-"+jiesu +" "+ xueyuanEName +" "+ kecheng;
//
//						//判断上课方式
//						if("远程授课".equals(teachingwayName)){
//							content2 +="-OL ";
//						}else if("上门授课".equals(teachingwayName)){
//							content2 +="-HM ";
//						}else{
//							//查询教室
//							if(!"".equals(classroom_id)){
//								Classroom cr=classroomService.loadById(classroom_id);
//								content2+="-"+cr.getName();
//							}
//						}
//					}else{
//						content2 = kaishi+"-"+jiesu +" "+ xueyuanEName +" "+ kecheng;
//						//查询教室
////						if(!"".equals(classroom_id)){
////							Classroom cr=classroomService.loadById(classroom_id);
////							content2+="-"+cr.getName();
////						}
//
//					}
//
//					String content3=" Please be sure to attend.Thank you.";
//
//					//合并
//					String key = laoshiId+"&!"+shouji+"&!"+xiaoqu;
//
//					String content = mt.get(key);
//					if(content != null && content.length() != 0){
//						String[] split = content.split("&!");
//						String content2arr = split[1];
//						content2 = content2arr + content2;
//						mt.put(key,content1+"&!"+content2+"&!"+content3);
//					}else{
//						mt.put(key,content1+"&!"+content2+"&!"+content3);
//					}
//
//				}
//			}
//		}
		//发送老师短信
//		if(mt != null && mt.size() != 0){
//			for(Map.Entry<String,String> entry : mt.entrySet()){
//				String[] keyarr  = entry.getKey().split("&!");
//				String valuearr = entry.getValue().replaceAll("&!","");
//				try {
//					sendSms(keyarr[1],valuearr,smsSenderInfo.getSmsSvcUrl(),smsSenderInfo.getPassword(),smsSenderInfo.getCust_code());
//
//					String context="老师短信发送：手机号："+keyarr[1]+",老师:"+keyarr[0]+"内容:"+valuearr;
//					System.out.println(context);
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			}
//		}
	}




	
	/**
	 * 每天0:30执行，检查更新老师可教授课程及学生解冻数据
	 */
	public void job2(){
		String startDate=LanDateUtils.format(new Date(),"yyyy-MM-dd");
		//String startDate="2016-02-01";
		WhereCondition wc = new WhereCondition();
		wc.andEquals("isActivated", "0");
		wc.andEquals("startDate", startDate);
		List<Teacher_course> list=teacher_courseService.query(wc);
		for(Teacher_course tc:list){
			wc = new WhereCondition();
			wc.andEquals("isActivated", "1");
			wc.andLessThan("startDate", startDate);
			wc.andEquals("course_id", tc.getCourse_id());
			wc.andEquals("teacher_id", tc.getTeacher_id());
			List<Teacher_course> li=teacher_courseService.query(wc);
			for(Teacher_course tc1:li){
				tc1.setIsActivated("0");
				tc1.setEndDate(LanDateUtils.stringToDate(startDate));
				teacher_courseService.update(tc1);
			}
			tc.setIsActivated("1");
			teacher_courseService.update(tc);
		}
		
		wc = new WhereCondition();
		wc.andEquals("status", "5");
		wc.andLessEquals("thawedDate", startDate);
		List<Student> liS=studentService.query(wc);
		for(Student s:liS){
			s.setStatus("");
			s.setFreezeDate(null);
			s.setThawedDate(null);
			studentService.updateForce(s);
		}
	}

	/**
	 * 定时任务学员上课状态的更新 每天3：05执行执行
	 */
	public void job4(){
		WhereCondition wc = new WhereCondition();
		wc.andNotEquals("classStatus","5");
		//查询出所有学生的上课状态,除了冻结的
		List<Student_zone> listSz = student_zoneService.query(wc);
		for (Student_zone sz:listSz) {
			//查询执行中的合同
			wc = new WhereCondition();
			wc.andEquals("status", "2");
			wc.andEquals("studentId", sz.getStudent_id());
			//判断合同类型
			addContractType2(sz.getBUType(),wc);
			List<Contract> listExecute=contractService.query(wc);
			if(listExecute.isEmpty()){
				//查询待确认合同
				wc.remove("status");
				wc.andEquals("status","1");
				List<Contract> listConfirmed = contractService.query(wc);
				if(listConfirmed.isEmpty()){
					boolean isNull = true;
					wc = new WhereCondition();
					wc.andEquals("student_id", sz.getStudent_id());
					List<Consumption_student> listCs = consumption_studentService.query(wc);
					if (!listCs.isEmpty()) {
						our:for (Consumption_student cs : listCs) {
							wc = new WhereCondition();
							wc.andEquals("consumption_student_id", cs.getId());
							wc.andIsNull("contract_id");
							List<View_consumption_student_contract> listVCsc = view_consumption_student_contractService.query(wc);
							if (!listVCsc.isEmpty()) {
								for (View_consumption_student_contract vcsc:listVCsc) {
									if(sz.getBUType().equals(vcsc.getBUType())){
										isNull = false;
										break our;
									}
								}
							}
						}
						if (isNull) {
							sz.setClassStatus("4");
						} else {
							sz.setClassStatus("1");
						}
					}
				}else{
					sz.setClassStatus("2");//设置待确认
				}
			}else{
				sz.setClassStatus("3");//设置执行中
			}

			student_zoneService.update(sz);
		}

	}

	/**
	 * 定时任务，每天17:30执行，发送上课通知短信
	 */
	public void job3(){
		//获取当前时间的下一天
		String next_day = LanDateUtils.getNext_Day(LanDateUtils.format(new Date(), "yyyy-MM-dd"), 1);
		//学生 huangbo
		// 书写sql
		String sql=" SELECT a.BUType , a.startTime kaishi,a.endTime jiesu,a.classroom_id,b.id xueyuanId,b.chineseName xueyuan,b.mobile shouji,b.secondarMobile beiyongshouji,d.name kecheng,e.name teachingwayName,f.city_id,f.`name` xiaoqu";
		sql+=" FROM classschedule a join student b on a.student_id = b.id join student_zone c on a.zone_id = c.zone_id  and c.student_id = b.id and c.sms = '1' join course d on a.course_id = d.id join teachingway e on a.teachingWay = e.id join zone f on a.zone_id = f.id ";
		sql+=" WHERE a.scheduledDate = '"+next_day+"' AND a.`status` = '1'  ";//and a.BUType ='1'
		sql+=" ORDER BY a.endTime ASC ";

		//自定义查询数据
		Zidingyi z=new Zidingyi();
		z.setSql(sql);
		List<Map> list=tongjiService.query(z);
		System.out.println("sql1-->"+sql);
		//用来存取数据
		Map<String,String> ms=new HashMap<String,String>();
		//遍历查询到的学生数据
		if(list != null && list.size() != 0){
			for (Map<String ,String> m : list) {
				String shouji = m.get("shouji") != null ? m.get("shouji").toString() : "";
				String beiyongshouji = m.get("beiyongshouji") != null ? m.get("beiyongshouji").toString() : "";

				//手机为空error
				if(StringUtils.isBlank(shouji)){
					shouji = "error";
				}
				if(StringUtils.isBlank(beiyongshouji)){
					beiyongshouji = "error";
				}
				if(StringUtils.isNotBlank(shouji) || StringUtils.isNotBlank(beiyongshouji)){
					String kaishi = m.get("kaishi") != null ? m.get("kaishi").toString() : "";
					String jiesu = m.get("jiesu") != null ? m.get("jiesu").toString() : "";
					String classroom_id = m.get("classroom_id") != null ? m.get("classroom_id").toString() : "";
					String xueyuanId = m.get("xueyuanId") != null ? m.get("xueyuanId").toString() : "";
					String xueyuan = m.get("xueyuan") != null ? m.get("xueyuan").toString() : "";
					String kecheng = m.get("kecheng") != null ? m.get("kecheng").toString() : "";
					String teachingwayName = m.get("teachingwayName") != null ? m.get("teachingwayName").toString() : "";
					String city_id = m.get("city_id") != null ? m.get("city_id").toString() : "";
					String xiaoqu = m.get("xiaoqu") != null ? m.get("xiaoqu").toString() : "";
					String buType = m.get("BUType") != null ? m.get("BUType").toString() : "";

					String content1 = "";
					//拼写短些内容
					if ("4".equals(buType)) {
						content1 = "【OBT】" + xueyuan + ",你好，您明天的课程安排如下：";
					} else {
						content1="【必益教育】"+xueyuan+",你好，您明天在必益学院"+xiaoqu+"的课程安排如下：";
					}
					String content2="";
					//判断是否是上海地区
					if("Id_city00000001".equals(city_id)){

						content2 = " "+kaishi+"-"+jiesu +" "+kecheng;

						//判断上课方式
						if("远程授课".equals(teachingwayName)){
							content2 +="-OL ";
						}else if("上门授课".equals(teachingwayName)){
							content2 +="-HM ";
						}else{
							//查询教室
							if(!"".equals(classroom_id)){
								Classroom cr=classroomService.loadById(classroom_id);
								content2+="-"+cr.getName();
							}
						}
					}else{
						content2 = kaishi+"-"+jiesu +" "+kecheng;
						//查询教室
//						if(!"".equals(classroom_id)){
//							Classroom cr=classroomService.loadById(classroom_id);
//							content2+="-"+cr.getName();
//						}

					}

					String content3=" 请提前做好准备";

					//合并
					String key = xueyuanId+"&!"+shouji+"&!"+xiaoqu+"&!"+beiyongshouji;

					String content = ms.get(key);
					if(content != null && content.length() != 0){
						String[] split = content.split("&!");
						String content2arr = split[1];
						content2 = content2arr + content2;
						ms.put(key,content1+"&!"+content2+"&!"+content3);
					}else{
						ms.put(key,content1+"&!"+content2+"&!"+content3);
					}

				}
			}
		}
		//发送学生短信
		if(ms != null && ms.size() != 0){
			for(Map.Entry<String,String> entry : ms.entrySet()){
				String[] keyarr  = entry.getKey().split("&!");
				String shouji = keyarr[1];
				String beiyongshouji = keyarr[3];

				String valuearr = entry.getValue().replaceAll("&!","");
				try {
					if(!StringUtils.equalsIgnoreCase("error",shouji)){
						sendSms(shouji,valuearr,smsSenderInfo.getSmsSvcUrl(),smsSenderInfo.getPassword(),smsSenderInfo.getCust_code());
						String context="学生短信发送：手机号："+shouji+",学生:"+keyarr[0]+"内容:"+valuearr;
						System.out.println(context);
						//因为学员Id_student00000708收不到短信,单独给他加一个日志
						if(keyarr[0].equals("Id_student00000708")){
							operationLog.setId(sequenceManager.generateId("operationLog"));
							operationLog.setCreateTime(new Date());
							operationLog.setContent(context);
							operationLogService.insert(operationLog);
						}
					}
					if(!StringUtils.equalsIgnoreCase("error",beiyongshouji)){
						sendSms(beiyongshouji,valuearr,smsSenderInfo.getSmsSvcUrl(),smsSenderInfo.getPassword(),smsSenderInfo.getCust_code());
						String context="学生短信发送：手机号："+beiyongshouji+",学生:"+keyarr[0]+"内容:"+valuearr;
						System.out.println(context);
						if(keyarr[0].equals("Id_student00000708")){
							operationLog.setId(sequenceManager.generateId("operationLog"));
							operationLog.setCreateTime(new Date());
							operationLog.setContent(context);
							operationLogService.insert(operationLog);
						}
					}

				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

		//老师
		// huangbo
		// 书写sql
		String sqlls=" SELECT s.englishName xueyuanEName, a.BUType,a.startTime kaishi,a.endTime jiesu,a.classroom_id,b.id laoshiId,b.englishName laoshi,b.mobile shouji,d. NAME kecheng,e. NAME teachingwayName,f.city_id,f.`name` xiaoqu ";
		sqlls+=" FROM classschedule a JOIN user b ON a.teacher_id = b.id JOIN teacher_zone c ON a.zone_id = c.zone_id AND c.teacher_id = b.id AND c.sms = '1' JOIN course d ON a.course_id = d.id JOIN teachingway e ON a.teachingWay = e.id JOIN zone f ON a.zone_id = f.id JOIN student s on s.id = a.student_id ";
		sqlls+=" WHERE a.scheduledDate = '"+next_day+"' AND a.`status` = '1' and a.BUType ='1' ";
		sqlls+=" ORDER BY a.endTime ASC ";

		//自定义查询数据
		Zidingyi zls=new Zidingyi();
		zls.setSql(sqlls);
		List<Map> listls=tongjiService.query(zls);
		System.out.println("sqlls-->"+sqlls);
		//用来存取数据
		Map<String,String> mt=new HashMap<String,String>();
		//遍历查询到的学生数据
		if(listls != null && listls.size() != 0){
			for (Map<String ,String> m : listls) {
				String shouji = m.get("shouji") != null ? m.get("shouji").toString() : "";
				if(!"".equals(shouji)){
					String kaishi = m.get("kaishi") != null ? m.get("kaishi").toString() : "";
					String jiesu = m.get("jiesu") != null ? m.get("jiesu").toString() : "";
					String classroom_id = m.get("classroom_id") != null ? m.get("classroom_id").toString() : "";
					String laoshiId = m.get("laoshiId") != null ? m.get("laoshiId").toString() : "";
					String laoshi = m.get("laoshi") != null ? m.get("laoshi").toString() : "";
					String kecheng = m.get("kecheng") != null ? m.get("kecheng").toString() : "";
					String teachingwayName = m.get("teachingwayName") != null ? m.get("teachingwayName").toString() : "";
					String city_id = m.get("city_id") != null ? m.get("city_id").toString() : "";
					String xiaoqu = m.get("xiaoqu") != null ? m.get("xiaoqu").toString() : "";
					String xueyuanEName = m.get("xueyuanEName") != null ? m.get("xueyuanEName").toString() : "";

					//拼写短些内容
					String content1="【必益教育】Dear "+laoshi+",You have following class at "+xiaoqu+" tomorrow:";
					String content2="";
					//判断是否是上海地区
					if("Id_city00000001".equals(city_id)){

						content2 = " \r\n "+kaishi+"-"+jiesu +" "+ xueyuanEName +" "+ kecheng;

						//判断上课方式
						if("远程授课".equals(teachingwayName)){
							content2 +="-OL ";
						}else if("上门授课".equals(teachingwayName)){
							content2 +="-HM ";
						}else{
							//查询教室
							if(!"".equals(classroom_id)){
								Classroom cr=classroomService.loadById(classroom_id);
								content2+="-"+cr.getName();
							}
						}
					}else{
						content2 = kaishi+"-"+jiesu +" "+ xueyuanEName +" "+ kecheng;
						//查询教室
//						if(!"".equals(classroom_id)){
//							Classroom cr=classroomService.loadById(classroom_id);
//							content2+="-"+cr.getName();
//						}

					}

					String content3=" Please be sure to attend.Thank you.";

					//合并
					String key = laoshiId+"&!"+shouji+"&!"+xiaoqu;

					String content = mt.get(key);
					if(content != null && content.length() != 0){
						String[] split = content.split("&!");
						String content2arr = split[1];
						content2 = content2arr + content2;
						mt.put(key,content1+"&!"+content2+"&!"+content3);
					}else{
						mt.put(key,content1+"&!"+content2+"&!"+content3);
					}

				}
			}
		}
		//发送老师短信
		if(mt != null && mt.size() != 0){
			for(Map.Entry<String,String> entry : mt.entrySet()){
				String[] keyarr  = entry.getKey().split("&!");
				String valuearr = entry.getValue().replaceAll("&!","");
				try {
					sendSms(keyarr[1],valuearr,smsSenderInfo.getSmsSvcUrl(),smsSenderInfo.getPassword(),smsSenderInfo.getCust_code());

					String context="老师短信发送：手机号："+keyarr[1]+",老师:"+keyarr[0]+"内容:"+valuearr;
					System.out.println(context);
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}
	/**
	 * 定时任务，发送老师和学生上课邮件提醒(停用)
	 */
	public void job512() {
		//获取当前时间，更具时间获取24小时后的课程数据
		Date date = LanDateUtils.getNowDate();
		Date nextDayDate = LanDateUtils.getNext_Day(date, 1);
		String nextDayStr = LanDateUtils.format(nextDayDate, TimezoneUtil.strTimeFormat_V1);
		String nextTimeStr = LanDateUtils.format(nextDayDate, "HH:mm");
		String[] nextTimeArr = nextTimeStr.split(":");
		String nextEndTimeStr = "";
		int hh = Integer.parseInt(nextTimeArr[0]) + 1;
		nextTimeStr = nextTimeArr[0] + ":00";
		if (hh < 10) {
			nextEndTimeStr = "0" + hh + ":" + "00";
		} else {
			nextEndTimeStr = hh + ":" + "00";
		}

		//sql
		String sql = "select a.id as id,a.schedule_id as schedule_id,a.issendmail as issendmail,b.student_id as student_id,b.teacher_id as teacher_id,b.scheduledDate as scheduledDate,b.endDate as endDate,b.startTime as  startTime,b.endTime as endTime, c.chineseName as studentchinesename,";
		sql += "c.englishName as studentenglishname,b.student_id as student_id,b.teacher_id as teacher_id,d.chineseName as tutorchinesename,d.englishName as tutorenglishname,g.name as subject,i.name as classroom,e.sms as sms,e.email as email,e.emailTemplate_id as emailTemplate_id,f.sms as tsms,f.email as temail,f.emailTemplate_id as temailTemplate_id, ";
		sql += " g.name as subject,c.email as sendEmail , c.secondarEmail as ssendSecondarEmail , d.email as tsendEmail ";
		sql += " FROM ";
		sql += " emailqueue a  INNER JOIN classschedule b on a.schedule_id = b.id AND a.issendmail = '0' And b.`status`='1' ";
		sql += " AND b.scheduledDate ='" + nextDayStr + "'";
		//sql += " AND b.scheduledDate ='2017-12-13'";
		sql+=" AND b.startTime >='"+nextTimeStr+"' and b.startTime<'"+nextEndTimeStr+"'" ;
		//sql += " AND b.startTime >='" + "08:00" + "' and b.startTime<'" + "18:00" + "'";
		sql += "  LEFT JOIN student c  on b.student_id = c.id LEFT JOIN USER d on b.teacher_id = d.id ";
		sql += "  LEFT JOIN  course g on b.course_id = g.id LEFT JOIN classroom i on b.classroom_id = i.id ";
		sql += "  LEFT JOIN  student_zone e on b.zone_id=e.zone_id and b.student_id=e.student_id ";
		sql += "  LEFT JOIN  teacher_zone f on b.zone_id=f.zone_id and b.teacher_id=f.teacher_id ";

		//获取数据
		Zidingyi z = new Zidingyi();
		z.setSql(sql);
		List<Map> list = tongjiService.query(z);
		System.out.println("sql1-->" + sql);
		//获取上海时间
		WhereCondition wc = new WhereCondition();
		wc.andEquals("timezone", "Asia/Shanghai");
		List<Timezone> timezoneSHList = timezoneService.query(wc);
		Timezone timezoneSH = timezoneSHList.get(0);

		//遍历数据
		if (list != null && list.size() != 0) {
			for (Map<String, Object> m : list) {
				if (m.get("schedule_id") != null && !"".equals(m.get("schedule_id").toString().trim())) {
					//遍历出所有数据
					String teacher_id = m.get("teacher_id") != null ? m.get("teacher_id").toString() : "";
					String student_id = m.get("student_id") != null ? m.get("student_id").toString() : "";
					String sms = m.get("sms") != null ? m.get("sms").toString() : "";
					String email = m.get("email") != null ? m.get("email").toString() : "";
					String tsms = m.get("tsms") != null ? m.get("tsms").toString() : "";
					String temail = m.get("temail") != null ? m.get("temail").toString() : "";
					String startTimeCS = m.get("startTime") != null ? m.get("startTime").toString() : "";
					String endTimeCS = m.get("endTime") != null ? m.get("endTime").toString() : "";
					String startDateCS = m.get("scheduledDate") != null ? m.get("scheduledDate").toString() : "";
					String endDateCS = m.get("endDate") != null ? m.get("endDate").toString() : "";
					String emailTemplate_id = m.get("emailTemplate_id") != null ? m.get("emailTemplate_id").toString() : "";
					String temailTemplate_id = m.get("temailTemplate_id") != null ? m.get("temailTemplate_id").toString() : "";
					String sename = m.get("studentenglishname") != null ? m.get("studentenglishname").toString() : "";
					String scname = m.get("studentchinesename") != null ? m.get("studentchinesename").toString() : "";
					String tename = m.get("tutorenglishname") != null ? m.get("tutorenglishname").toString() : "";
					String tcname = m.get("tutorchinesename") != null ? m.get("tutorchinesename").toString() : "";
					String room = m.get("classroom") != null ? m.get("classroom").toString() : "";
					String subject = m.get("subject") != null ? m.get("subject").toString() : "";
					String url = m.get("url") != null ? m.get("url").toString() : "";
					String sEmail = m.get("sendEmail") != null ? m.get("sendEmail").toString() : "";
					String ssendSecondarEmail = m.get("ssendSecondarEmail") != null ? m.get("ssendSecondarEmail").toString() : "";
					String tEmail = m.get("tsendEmail") != null ? m.get("tsendEmail").toString() : "";
					String eqId = m.get("id") != null ? m.get("id").toString() : "";
					String schedule_id=m.get("schedule_id")!=null?m.get("schedule_id").toString().trim():"";

					String sqlonlone = " SELECT url FROM teacher_onlineaddress ";
					sqlonlone += " WHERE teacher_id = '"+teacher_id+"'  AND startDate <= '"+nextDayStr+"' AND endDate >= '"+nextDayStr+"' ";
					Zidingyi zonlineaddress = new Zidingyi();
					zonlineaddress.setSql(sqlonlone);
					List<Map> listonlineaddress = tongjiService.query(zonlineaddress);
					System.out.println("sql1-->" + zonlineaddress);
					if(listonlineaddress != null && listonlineaddress.size() != 0){
						url = listonlineaddress.get(0).get("url").toString();
					}

					//判断学生的邮箱是否勾选 0未勾选，1勾选
					if ("1".equals(email)) {
						Student_timezone p = new Student_timezone();
						p.setStudent_id(student_id);
						p.setStartDate(nextDayDate);
						p.setEndDate(nextDayDate);
						//获取改学员的上课时间所在的数据，如果没有默认上海时区
						List<Student_timezone> ttzList = student_timezoneService.getStuTimezone(p);
						String startTime = "";
						String endTime = "";
						String startDateStr = "";
						String endDatetr = "";
						String timezone = "";
						String tzName = "";
						if (ttzList == null || ttzList.size() < 1) {
							//直接发送，时区上海
							startTime = startTimeCS;
							endTime = endTimeCS;
							startDateStr = startDateCS;
							endDatetr = endDateCS;
							tzName = timezoneSH.getDisplayName();
						} else {
							Student_timezone stz = ttzList.get(0);
							timezone = stz.getTimezone();
							tzName = stz.getTimezoneName();
							List retDateList = TimezoneUtil.timezoneChange(startDateCS, startTimeCS, timezone, "S");
							startDateStr = (String) retDateList.get(2);
							startTime = (String) retDateList.get(1);
							List retDateEndList = TimezoneUtil.timezoneChange(endDateCS, endTimeCS, timezone, "S");
							endDatetr = (String) retDateEndList.get(2);
							endTime = (String) retDateEndList.get(1);
						}

						//获取邮箱模板
						//String sqlA="select a.content as content,a.subject as subject from emailtemplate a where a.id='"+emailTemplate_id+"'";
						String sqlA = " SELECT ";
						sqlA += " a.content AS content, ";  //模板内容
						sqlA += " a. SUBJECT AS SUBJECT, "; //模板标题
						sqlA += " ea. HOST AS emailHost, "; //邮箱服务器地址
						sqlA += " ea. PORT AS emailPort, "; //邮箱服务器端口
						sqlA += " ea.protocol AS emailProtocol, "; //邮箱服务器类型
						sqlA += " ea.accountname AS emailFrom, "; //邮箱账户名称
						sqlA += " ea.email AS emailUserName, "; //邮箱地址
						sqlA += " ea. PASSWORD AS emailPassword ";//密码
						sqlA += " FROM ";
						sqlA += " emailtemplate a ";
						sqlA += " JOIN emailaccount ea ON a.email_id = ea.id ";
						sqlA += " WHERE " ;
						sqlA += "a.id='"+emailTemplate_id+"'";
						z.setSql(sqlA);
						List<Map> listMap=tongjiService.query(z);
						System.out.println("sql1-->"+sqlA);
						if(listMap != null && listMap.size()!= 0){
							for(Map mstu:listMap){
								String content=mstu.get("content")!=null?mstu.get("content").toString().trim():"";
								String SUBJECT=mstu.get("SUBJECT")!=null?mstu.get("SUBJECT").toString().trim():"";
								String emailHost=mstu.get("emailHost")!=null?mstu.get("emailHost").toString().trim():"";
								Integer emailPort=mstu.get("emailPort")!=null?Integer.parseInt(mstu.get("emailPort").toString().trim()):null;
								String emailProtocol=mstu.get("emailProtocol")!=null?mstu.get("emailProtocol").toString().trim():"";
								String emailFrom=mstu.get("emailFrom")!=null?mstu.get("emailFrom").toString().trim():"";
								String emailUserName=mstu.get("emailUserName")!=null?mstu.get("emailUserName").toString().trim():"";
								String emailPassword=mstu.get("emailPassword")!=null?mstu.get("emailPassword").toString().trim():"";



								if(content.contains("{student Englishname}")){
									content=content.replace("{student Englishname}",sename);
								}
								if(content.contains("{student Chinesename}")){
									content=content.replace("{student Chinesename}",scname);
								}
								if(content.contains("{Student Timezone}")){
									content=content.replace("{Student Timezone}",tzName);
								}

								if(content.contains("{StartTime}")){
									content=content.replace("{StartTime}",startTime);
								}
								if(content.contains("{EndTime}")){
									content=content.replace("{EndTime}",endTime);
								}
								if(content.contains("{Scheduleddate}")){
									content=content.replace("{Scheduleddate}",startDateStr);
								}
								if(content.contains("{Subject}")){
									content=content.replace("{Subject}",subject);
								}
								if(content.contains("{Tutor Englishname}")){
									content=content.replace("{Tutor Englishname}",tename);
								}
								if(content.contains("{Tutor Chinesename}")){
									content=content.replace("{Tutor Chinesename}",tcname);
								}
								if(content.contains("{Classroom}")){
									content=content.replace("{Classroom}",room);
								}
								if(content.contains("{Additional Info}")){
									content=content.replace("{Additional Info}",url);
								}
								if(content.contains("{Tutor Timezone}")){
									content=content.replace("{Tutor Timezone}",tzName);
								}
								String subjectEmail=m.get("subject").toString().trim();
								if(subjectEmail.contains("{Subject}")){
									subjectEmail=subjectEmail.replace("{Subject}",subject);
								}
								//发送邮件

								//获取发送邮件服务器账户信息
								MailSenderInfo mailSenderInfo = new MailSenderInfo();
								mailSenderInfo.setHost(emailHost);
								mailSenderInfo.setPort(emailPort);
								mailSenderInfo.setProtocol(emailProtocol);
								mailSenderInfo.setFrom(emailFrom);
								mailSenderInfo.setUserName(emailUserName);
								mailSenderInfo.setPassword(emailPassword);

								//发送邮箱
								//boolean isSuccess =lanmoMailSender.sendMassageDefault(tEmail, null, null, content, subject);
								//sEmail ="<EMAIL>";
								boolean isSuccess =lanmoMailSender.sendMassageAppoint(sEmail, null, null, content, SUBJECT,mailSenderInfo);
								//ssendSecondarEmail ="<EMAIL>";
								boolean isSuccessSecondar = false;
								if(StringUtils.isNotBlank(ssendSecondarEmail)){
									 isSuccessSecondar =lanmoMailSender.sendMassageAppoint(ssendSecondarEmail, null, null, content, SUBJECT,mailSenderInfo);//备用邮箱发送
									System.out.println("Failure Sending SecondarEmail To "+ssendSecondarEmail);
								}
								if(!isSuccess && !isSuccessSecondar){
									System.out.println("Failure Sending Email To "+sEmail);
									String context="学生邮箱发送失败"+schedule_id+"邮箱:"+ssendSecondarEmail+"备用邮箱:"+sEmail;
									operationLog.setId(sequenceManager.generateId("operationLog"));
									operationLog.setCreateTime(new Date());
									operationLog.setContent(context);
									operationLogService.insert(operationLog);

								}else{
									System.out.println("send email to "+sEmail+" is successful");
									//发送成功修改状态issendmail

								}

							}
						}
					}
					//判断老师的邮箱是否勾选 0未勾选，1勾选
					if("1".equals(temail)){
						Teacher_timezone p=new Teacher_timezone();
						p.setTeacher_id(teacher_id);
						p.setStartDate(nextDayDate);
						p.setEndDate(nextDayDate);
						//查询老师上课时间所在的时区，如果没有默认上海时区
						List<Teacher_timezone> ttzList=teacher_timezoneService.getTeaTimezone(p);
						String startTime="";
						String endTime="";
						String startDateStr="";
						String endDatetr="";
						String timezone="";
						String tzName="";
						if(ttzList==null||ttzList.size()<1){
							//直接发送，时区上海
							startTime=startTimeCS;
							endTime=endTimeCS;
							startDateStr=startDateCS;
							endDatetr=endDateCS;
							tzName=timezoneSH.getDisplayName();
						} else{
							Teacher_timezone stz=ttzList.get(0);
							timezone=stz.getTimezone();
							tzName=stz.getTimezoneName();
							List retDateList=TimezoneUtil.timezoneChange(startDateCS,startTimeCS,timezone,"S");
							startDateStr=(String)retDateList.get(2);
							startTime=(String)retDateList.get(1);
							List retDateEndList=TimezoneUtil.timezoneChange(endDateCS,endTimeCS,timezone,"S");
							endDatetr=(String)retDateEndList.get(2);
							endTime=(String)retDateEndList.get(1);
						}
						//获取老师模板
						//String sqlA="select a.content as content,a.subject as subject from emailtemplate a where a.id='"+temailTemplate_id+"'";
						String sqlA = " SELECT ";
								sqlA += " a.content AS content, ";  //模板内容
								sqlA += " a. SUBJECT AS SUBJECT, "; //模板标题
								sqlA += " ea. HOST AS emailHost, "; //邮箱服务器地址
								sqlA += " ea. PORT AS emailPort, "; //邮箱服务器端口
								sqlA += " ea.protocol AS emailProtocol, "; //邮箱服务器类型
								sqlA += " ea.accountname AS emailFrom, "; //邮箱账户名称
								sqlA += " ea.email AS emailUserName, "; //邮箱地址
								sqlA += " ea. PASSWORD AS emailPassword ";//密码
								sqlA += " FROM ";
								sqlA += " emailtemplate a ";
								sqlA += " JOIN emailaccount ea ON a.email_id = ea.id ";
								sqlA += " WHERE " ;
								sqlA += "a.id='"+temailTemplate_id+"'";
						z.setSql(sqlA);
						List<Map> listMap=tongjiService.query(z);
						System.out.println("sql1-->"+sqlA);
						for(Map mstu:listMap){

							String content=mstu.get("content")!=null?mstu.get("content").toString().trim():"";
							String SUBJECT=mstu.get("SUBJECT")!=null?mstu.get("SUBJECT").toString().trim():"";
							String emailHost=mstu.get("emailHost")!=null?mstu.get("emailHost").toString().trim():"";
							Integer emailPort=mstu.get("emailPort")!=null?Integer.parseInt(mstu.get("emailPort").toString().trim()):null;
							String emailProtocol=mstu.get("emailProtocol")!=null?mstu.get("emailProtocol").toString().trim():"";
							String emailFrom=mstu.get("emailFrom")!=null?mstu.get("emailFrom").toString().trim():"";
							String emailUserName=mstu.get("emailUserName")!=null?mstu.get("emailUserName").toString().trim():"";
							String emailPassword=mstu.get("emailPassword")!=null?mstu.get("emailPassword").toString().trim():"";

							if(content.contains("{student Englishname}")){
								content=content.replace("{student Englishname}",sename);
							}
							if(content.contains("{Tutor Timezone}")){
								content=content.replace("{Tutor Timezone}",tzName);
							}
							if(content.contains("{StartTime}")){
								content=content.replace("{StartTime}",startTime);
							}
							if(content.contains("{EndTime}")){
								content=content.replace("{EndTime}",endTime);
							}
							if(content.contains("{Scheduleddate}")){
								content=content.replace("{Scheduleddate}",startDateStr);
							}
							if(content.contains("{Subject}")){
								content=content.replace("{Subject}",subject);
							}
							if(content.contains("{Tutor Englishname}")){
								content=content.replace("{Tutor Englishname}",tename);
							}
							if(content.contains("{Additional Info}")){
								content=content.replace("{Additional Info}",url);
							}
							if(content.contains("{student Chinesename}")){
								content=content.replace("{student Chinesename}",scname);
							}

			 				if(content.contains("{Tutor Chinesename}")){
								content=content.replace("{Tutor Chinesename}",tcname);
							}
							if(content.contains("{Classroom}")){
								content=content.replace("{Classroom}",room);
							}
							if(content.contains("{Student Timezone}")){
								content=content.replace("{Student Timezone}",tzName);
							}

							String subjectEmail=m.get("subject").toString().trim();
							if(subjectEmail.contains("{Subject}")){
								subjectEmail=subjectEmail.replace("{Subject}",subject);
							}
							//获取发送邮件服务器账户信息
							MailSenderInfo mailSenderInfo = new MailSenderInfo();
							mailSenderInfo.setHost(emailHost);
							mailSenderInfo.setPort(emailPort);
							mailSenderInfo.setProtocol(emailProtocol);
							mailSenderInfo.setFrom(emailFrom);
							mailSenderInfo.setUserName(emailUserName);
							mailSenderInfo.setPassword(emailPassword);

							//发送邮箱
							//boolean isSuccess =lanmoMailSender.sendMassageDefault(tEmail, null, null, content, subject);
							//tEmail = "<EMAIL>";
							boolean isSuccess =lanmoMailSender.sendMassageAppoint(tEmail, null, null, content, SUBJECT,mailSenderInfo);
							if(!isSuccess){
								System.out.println("Failure Sending Email To "+tEmail);
								String context="老师箱发送失败"+schedule_id+"邮箱:"+tEmail;
								operationLog.setId(sequenceManager.generateId("operationLog"));
								operationLog.setCreateTime(new Date());
								operationLog.setContent(context);
								operationLogService.insert(operationLog);
							}else{
								System.out.println("send email to "+tEmail+" is successful");
							}
						}
					}

					//发送成功修改发送状态issendmail
					Emailqueue eq=new Emailqueue();
					eq.setId(eqId);
					eq.setIssendmail(Enums.IsSendEmail.send);
					emailqueueService.update(eq);


				}
			}
		}
	}

	/**
	 * 定时任务，发送老师和学生上课邮件提醒(升级版 老师邮件把一天的课发到一封邮件中)
	 */
	public void job5() {
		//获取当前时间，更具时间获取24小时后的课程数据
		Date date = LanDateUtils.getNowDate();
		Date nextDayDate = LanDateUtils.getNext_Day(date, 1);
		String nextDayStr = LanDateUtils.format(nextDayDate, TimezoneUtil.strTimeFormat_V1);
		String nextTimeStr = LanDateUtils.format(nextDayDate, "HH:mm");
		String[] nextTimeArr = nextTimeStr.split(":");
		String nextEndTimeStr = "";
		int hh = Integer.parseInt(nextTimeArr[0]) + 1;
		nextTimeStr = nextTimeArr[0] + ":00";
		if (hh < 10) {
			nextEndTimeStr = "0" + hh + ":" + "00";
		} else {
			nextEndTimeStr = hh + ":" + "00";
		}

		//sql
		String sql = "select b.id as classscheduleId, b.zone_id, a.id as id,a.schedule_id as schedule_id,a.issendmail as issendmail,a.issendmaiTeacher as issendmaiTeacher,b.student_id as student_id,b.teacher_id as teacher_id,b.scheduledDate as scheduledDate,b.endDate as endDate,b.startTime as  startTime,b.endTime as endTime, c.chineseName as studentchinesename,";
		sql += "c.englishName as studentenglishname,b.student_id as student_id,b.teacher_id as teacher_id,d.chineseName as tutorchinesename,d.englishName as tutorenglishname,g.name as subject,i.name as classroom,e.sms as sms,e.email as email,e.emailTemplate_id as emailTemplate_id,f.sms as tsms,f.email as temail,f.emailTemplate_id as temailTemplate_id, ";
		sql += " g.name as subject,c.email as sendEmail , c.secondarEmail as ssendSecondarEmail , d.email as tsendEmail ";
		sql += " FROM ";
		sql += " emailqueue a  INNER JOIN classschedule b on a.schedule_id = b.id AND a.issendmail = '0' And b.`status`='1' ";
		sql += " AND b.scheduledDate ='" + nextDayStr + "'";
		sql += " AND b.startTime >='"+nextTimeStr+"' and b.startTime<'"+nextEndTimeStr+"'" ;
		//sql += " AND b.startTime >='" + "10:00" + "' and b.startTime<'" + "14:00" + "'";
		sql += "  LEFT JOIN student c  on b.student_id = c.id LEFT JOIN USER d on b.teacher_id = d.id ";
		sql += "  LEFT JOIN  course g on b.course_id = g.id LEFT JOIN classroom i on b.classroom_id = i.id ";
		sql += "  LEFT JOIN  student_zone e on b.zone_id=e.zone_id and b.student_id=e.student_id ";
		sql += "  LEFT JOIN  teacher_zone f on b.zone_id=f.zone_id and b.teacher_id=f.teacher_id ";
		sql += "  where b.BUType <> '4' ";

		//获取数据
		Zidingyi z = new Zidingyi();
		z.setSql(sql);
		List<Map> list = tongjiService.query(z);
		System.out.println("sql1-->" + sql);
		//获取上海时间
		WhereCondition wc = new WhereCondition();
		wc.andEquals("timezone", "Asia/Shanghai");
		List<Timezone> timezoneSHList = timezoneService.query(wc);
		Timezone timezoneSH = timezoneSHList.get(0);
		String tids = "";//判断老师邮箱是否重复
		//遍历数据
		if (list != null && list.size() != 0) {
			for (Map<String, Object> m : list) {
				if (m.get("schedule_id") != null && !"".equals(m.get("schedule_id").toString().trim())) {
					//遍历出所有数据
					String teacher_id = m.get("teacher_id") != null ? m.get("teacher_id").toString() : "";
					String student_id = m.get("student_id") != null ? m.get("student_id").toString() : "";
					String sms = m.get("sms") != null ? m.get("sms").toString() : "";
					String email = m.get("email") != null ? m.get("email").toString() : "";
					String tsms = m.get("tsms") != null ? m.get("tsms").toString() : "";
					String temail = m.get("temail") != null ? m.get("temail").toString() : "";
					String startTimeCS = m.get("startTime") != null ? m.get("startTime").toString() : "";
					String endTimeCS = m.get("endTime") != null ? m.get("endTime").toString() : "";
					String startDateCS = m.get("scheduledDate") != null ? m.get("scheduledDate").toString() : "";
					String endDateCS = m.get("endDate") != null ? m.get("endDate").toString() : "";
					String emailTemplate_id = m.get("emailTemplate_id") != null ? m.get("emailTemplate_id").toString() : "";
					String temailTemplate_id = m.get("temailTemplate_id") != null ? m.get("temailTemplate_id").toString() : "";
					String sename = m.get("studentenglishname") != null ? m.get("studentenglishname").toString() : "";
					String scname = m.get("studentchinesename") != null ? m.get("studentchinesename").toString() : "";
					String tename = m.get("tutorenglishname") != null ? m.get("tutorenglishname").toString() : "";
					String tcname = m.get("tutorchinesename") != null ? m.get("tutorchinesename").toString() : "";
					String room = m.get("classroom") != null ? m.get("classroom").toString() : "";
					String subject = m.get("subject") != null ? m.get("subject").toString() : "";
					String url = m.get("url") != null ? m.get("url").toString() : "";
					String sEmail = m.get("sendEmail") != null ? m.get("sendEmail").toString() : "";
					String ssendSecondarEmail = m.get("ssendSecondarEmail") != null ? m.get("ssendSecondarEmail").toString() : "";
					String tEmail = m.get("tsendEmail") != null ? m.get("tsendEmail").toString() : "";
					String eqId = m.get("id") != null ? m.get("id").toString() : "";
					String issendmaiTeacher = m.get("issendmaiTeacher") != null ? m.get("issendmaiTeacher").toString() : "";
					String zone_id = m.get("zone_id") != null ? m.get("zone_id").toString() : "";
					String classscheduleId = m.get("classscheduleId") != null ? m.get("classscheduleId").toString() : "-1";

					String sqlonlone = " SELECT url FROM teacher_onlineaddress ";
					sqlonlone += " WHERE teacher_id = '"+teacher_id+"'  AND startDate <= '"+nextDayStr+"' AND endDate >= '"+nextDayStr+"' ";
					Zidingyi zonlineaddress = new Zidingyi();
					zonlineaddress.setSql(sqlonlone);
					List<Map> listonlineaddress = tongjiService.query(zonlineaddress);
					System.out.println("sql1-->" + zonlineaddress);
					if(listonlineaddress != null && listonlineaddress.size() != 0){
						url = listonlineaddress.get(0).get("url").toString();
					}

					//判断学生的邮箱是否勾选 0未勾选，1勾选
					if ("1".equals(email)) {
						Student_timezone p = new Student_timezone();
						p.setStudent_id(student_id);
						p.setStartDate(nextDayDate);
						p.setEndDate(nextDayDate);
						//获取改学员的上课时间所在的数据，如果没有默认上海时区
						List<Student_timezone> ttzList = student_timezoneService.getStuTimezone(p);
						String startTime = "";
						String endTime = "";
						String startDateStr = "";
						String endDatetr = "";
						String timezone = "";
						String tzName = "";
						if (ttzList == null || ttzList.size() < 1) {
							//直接发送，时区上海
							startTime = startTimeCS;
							endTime = endTimeCS;
							startDateStr = startDateCS;
							endDatetr = endDateCS;
							tzName = timezoneSH.getDisplayName();
						} else {
							Student_timezone stz = ttzList.get(0);
							timezone = stz.getTimezone();
							tzName = stz.getTimezoneName();
							List retDateList = TimezoneUtil.timezoneChange(startDateCS, startTimeCS, timezone, "S");
							startDateStr = (String) retDateList.get(2);
							startTime = (String) retDateList.get(1);
							List retDateEndList = TimezoneUtil.timezoneChange(endDateCS, endTimeCS, timezone, "S");
							endDatetr = (String) retDateEndList.get(2);
							endTime = (String) retDateEndList.get(1);
						}

						//获取邮箱模板
						//String sqlA="select a.content as content,a.subject as subject from emailtemplate a where a.id='"+emailTemplate_id+"'";
						String sqlA = " SELECT ";
						sqlA += " a.content AS content, ";  //模板内容
						sqlA += " a. SUBJECT AS SUBJECT, "; //模板标题
						sqlA += " ea. HOST AS emailHost, "; //邮箱服务器地址
						sqlA += " ea. PORT AS emailPort, "; //邮箱服务器端口
						sqlA += " ea.protocol AS emailProtocol, "; //邮箱服务器类型
						sqlA += " ea.accountname AS emailFrom, "; //邮箱账户名称
						sqlA += " ea.email AS emailUserName, "; //邮箱地址
						sqlA += " ea. PASSWORD AS emailPassword ";//密码
						sqlA += " FROM ";
						sqlA += " emailtemplate a ";
						sqlA += " JOIN emailaccount ea ON a.email_id = ea.id ";
						sqlA += " WHERE " ;
						sqlA += "a.id='"+emailTemplate_id+"'";
						z.setSql(sqlA);
						List<Map> listMap=tongjiService.query(z);
						System.out.println("sql1-->"+sqlA);
						if(listMap != null && listMap.size()!= 0){
							for(Map mstu:listMap){
								String content=mstu.get("content")!=null?mstu.get("content").toString().trim():"";
								String SUBJECT=mstu.get("SUBJECT")!=null?mstu.get("SUBJECT").toString().trim():"";
								String emailHost=mstu.get("emailHost")!=null?mstu.get("emailHost").toString().trim():"";
								Integer emailPort=mstu.get("emailPort")!=null?Integer.parseInt(mstu.get("emailPort").toString().trim()):null;
								String emailProtocol=mstu.get("emailProtocol")!=null?mstu.get("emailProtocol").toString().trim():"";
								String emailFrom=mstu.get("emailFrom")!=null?mstu.get("emailFrom").toString().trim():"";
								String emailUserName=mstu.get("emailUserName")!=null?mstu.get("emailUserName").toString().trim():"";
								String emailPassword=mstu.get("emailPassword")!=null?mstu.get("emailPassword").toString().trim():"";


								if(content.contains("{student Englishname}")){
									content=content.replace("{student Englishname}",sename);
								}
								if(content.contains("{student Chinesename}")){
									content=content.replace("{student Chinesename}",scname);
								}
								if(content.contains("{Student Timezone}")){
									content=content.replace("{Student Timezone}",tzName);
								}

								if(content.contains("{StartTime}")){
									content=content.replace("{StartTime}",startTime);
								}
								if(content.contains("{EndTime}")){
									content=content.replace("{EndTime}",endTime);
								}
								if(content.contains("{Scheduleddate}")){
									content=content.replace("{Scheduleddate}",startDateStr);
								}
								if(content.contains("{Subject}")){
									content=content.replace("{Subject}",subject);
								}
								if(content.contains("{Tutor Englishname}")){
									content=content.replace("{Tutor Englishname}",tename);
								}
								if(content.contains("{Tutor Chinesename}")){
									content=content.replace("{Tutor Chinesename}",tcname);
								}
								if(content.contains("{Classroom}")){
									content=content.replace("{Classroom}",room);
								}
								if(content.contains("{Additional Info}")){
									content=content.replace("{Additional Info}",url);
								}
								if(content.contains("{Tutor Timezone}")){
									content=content.replace("{Tutor Timezone}",tzName);
								}
								String subjectEmail=m.get("subject").toString().trim();
								if(subjectEmail.contains("{Subject}")){
									subjectEmail=subjectEmail.replace("{Subject}",subject);
								}
								//发送邮件

								//获取发送邮件服务器账户信息
								MailSenderInfo mailSenderInfo = new MailSenderInfo();
								mailSenderInfo.setHost(emailHost);
								mailSenderInfo.setPort(emailPort);
								mailSenderInfo.setProtocol(emailProtocol);
								mailSenderInfo.setFrom(emailFrom);
								mailSenderInfo.setUserName(emailUserName);
								mailSenderInfo.setPassword(emailPassword);

								//发送邮箱
								//sEmail = "<EMAIL>";
								boolean isSuccess = false;
								if (emailPort.toString() == "25") {
									isSuccess =lanmoMailSender.sendMassageAppoint(sEmail, null, null, content, SUBJECT,mailSenderInfo);
								} else {
									isSuccess =lanmoMailSender.sendMassageAppoint_ssl(sEmail, null, null, content, SUBJECT,mailSenderInfo);
								}

								boolean isSuccessSecondar = false;
								if(StringUtils.isNotBlank(ssendSecondarEmail)){
									//ssendSecondarEmail = "<EMAIL>";
									if (emailPort.toString() == "25") {
										isSuccessSecondar =lanmoMailSender.sendMassageAppoint(ssendSecondarEmail, null, null, content, SUBJECT,mailSenderInfo);//备用邮箱发送
									} else {
										isSuccessSecondar =lanmoMailSender.sendMassageAppoint_ssl(ssendSecondarEmail, null, null, content, SUBJECT,mailSenderInfo);//备用邮箱发送
									}


								}
								if(!isSuccess && !isSuccessSecondar){
									System.out.println("Failure Sending Email To "+sEmail);
									//发送成功修改发送状态issendmail
									Emailqueue eq=new Emailqueue();
									eq.setId(eqId);
									eq.setIssendmail(Enums.IsSendEmail.SendError);
									emailqueueService.update(eq);
									String context="学生邮箱发送失败"+classscheduleId+"邮箱:"+sEmail+"备用邮箱:"+ssendSecondarEmail;
									operationLog.setId(sequenceManager.generateId("operationLog"));
									operationLog.setCreateTime(new Date());
									operationLog.setContent(context);
									operationLogService.insert(operationLog);
								}else{
									System.out.println("send email to "+sEmail+" is successful");
									//发送成功修改状态issendmail
									//发送成功修改发送状态issendmail
									Emailqueue eq=new Emailqueue();
									eq.setId(eqId);
									eq.setIssendmail(Enums.IsSendEmail.send);
									emailqueueService.update(eq);

								}


							}
						}
					}

					//判断老师的邮箱是否勾选 0未勾选，1勾选
					if("1".equals(temail) && !StringUtils.equals("1",issendmaiTeacher) && !tids.contains(classscheduleId)){
						Teacher_timezone p=new Teacher_timezone();
						p.setTeacher_id(teacher_id);
						p.setStartDate(nextDayDate);
						p.setEndDate(nextDayDate);
						//查询老师上课时间所在的时区，如果没有默认上海时区
						List<Teacher_timezone> ttzList=teacher_timezoneService.getTeaTimezone(p);
						String startTime="";
						String endTime="";
						String startDateStr="";
						String endDatetr="";
						String timezone="Asia/Shanghai";
						String tzName="";
						Boolean timezoneFlag = false;
						if(ttzList==null||ttzList.size()<1){
							//直接发送，时区上海
							startTime=startTimeCS;
							endTime=endTimeCS;
							startDateStr=startDateCS;
							endDatetr=endDateCS;
							tzName=timezoneSH.getDisplayName();
						} else{
							timezoneFlag = true;
							Teacher_timezone stz=ttzList.get(0);
							timezone=stz.getTimezone();
							tzName=stz.getTimezoneName();
							List retDateList=TimezoneUtil.timezoneChange(startDateCS,startTimeCS,timezone,"S");
							startDateStr=(String)retDateList.get(2);
							startTime=(String)retDateList.get(1);
							List retDateEndList=TimezoneUtil.timezoneChange(endDateCS,endTimeCS,timezone,"S");
							endDatetr=(String)retDateEndList.get(2);
							endTime=(String)retDateEndList.get(1);
						}

						//获取老师当天所在时区的所有课
						List<ClassSchedule> tClassScheduless = findOneClassScheduleByTimezone(timezone,teacher_id,zone_id,startDateStr,timezoneFlag);
						//获取老师的模版
						Map temailTemp = findEmailTempById(temailTemplate_id);

						if(temailTemp != null && CollectionUtils.isNotEmpty(tClassScheduless)){
							String content=temailTemp.get("content")!=null?temailTemp.get("content").toString().trim():"";
							String SUBJECT=temailTemp.get("SUBJECT")!=null?temailTemp.get("SUBJECT").toString().trim():"";
							String emailHost=temailTemp.get("emailHost")!=null?temailTemp.get("emailHost").toString().trim():"";
							Integer emailPort=temailTemp.get("emailPort")!=null?Integer.parseInt(temailTemp.get("emailPort").toString().trim()):null;
							String emailProtocol=temailTemp.get("emailProtocol")!=null?temailTemp.get("emailProtocol").toString().trim():"";
							String emailFrom=temailTemp.get("emailFrom")!=null?temailTemp.get("emailFrom").toString().trim():"";
							String emailUserName=temailTemp.get("emailUserName")!=null?temailTemp.get("emailUserName").toString().trim():"";
							String emailPassword=temailTemp.get("emailPassword")!=null?temailTemp.get("emailPassword").toString().trim():"";

							//判断是否有循环语句
							if(content.contains("&$")){
								String content1 = content.substring(0,content.indexOf("&$"));
								String content2 = content.substring(content.indexOf("&$")+2,content.indexOf("&$",content.indexOf("&$")+1));
								String content3 = content.substring(content.indexOf("&$",content.indexOf("&$")+1)+2);

								content = content1;
								if(CollectionUtils.isNotEmpty(tClassScheduless)){
									for (ClassSchedule classSchedule:tClassScheduless) {
										String	contentWhile = replaceEmail(content2,classSchedule,timezone,tename,tcname);
										tids +=  classSchedule.getId()+",";
										content += contentWhile +"\r\n\r\n";
									}
								}
								content += content3;
							}


							if(content.contains("{student Englishname}")){   //学生英文名
								content=content.replace("{student Englishname}",sename);
								content=content.replaceAll("\\{student Englishname\\}",sename);
							}
							if(content.contains("{Tutor Timezone}")){   //老师上课时区
								content=content.replace("{Tutor Timezone}",tzName);
							}
							if(content.contains("{StartTime}")){ //上课开始时间
								content=content.replace("{StartTime}",startTime);
							}
							if(content.contains("{EndTime}")){ //上课结束时间
								content=content.replace("{EndTime}",endTime);
							}
							if(content.contains("{Scheduleddate}")){ //上课日期
								content=content.replace("{Scheduleddate}",startDateStr);
							}
							if(content.contains("{Subject}")){  //课程名字
								content=content.replace("{Subject}",subject);
							}
							if(content.contains("{Tutor Englishname}")){ //老师英文
								content=content.replace("{Tutor Englishname}",tename);
							}
							if(content.contains("{Additional Info}")){ //url
								content=content.replace("{Additional Info}",url);
							}
							if(content.contains("{student Chinesename}")){ //学生中文名
								content=content.replace("{student Chinesename}",scname);
							}

							if(content.contains("{Tutor Chinesename}")){ //老师中文名
								content=content.replace("{Tutor Chinesename}",tcname);
							}
							if(content.contains("{Classroom}")){ //教室
								content=content.replace("{Classroom}",room);
							}
							if(content.contains("{Student Timezone}")){//学生时区
								content=content.replace("{Student Timezone}",tzName);
							}

							//获取发送邮件服务器账户信息
							MailSenderInfo mailSenderInfo = new MailSenderInfo();
							mailSenderInfo.setHost(emailHost);
							mailSenderInfo.setPort(emailPort);
							mailSenderInfo.setProtocol(emailProtocol);
							mailSenderInfo.setFrom(emailFrom);
							mailSenderInfo.setUserName(emailUserName);
							mailSenderInfo.setPassword(emailPassword);

							//发送邮箱
							//boolean isSuccess =lanmoMailSender.sendMassageDefault(tEmail, null, null, content, subject);
							//tEmail = "<EMAIL>";

							boolean isSuccess = false;
							if (emailPort.toString() == "25") {
								isSuccess =lanmoMailSender.sendMassageAppoint(tEmail, null, null, content, SUBJECT,mailSenderInfo);
							} else {
								isSuccess =lanmoMailSender.sendMassageAppoint_ssl(tEmail, null, null, content, SUBJECT,mailSenderInfo);
							}
							if(!isSuccess){
								System.out.println("Failure Sending Email To "+tEmail);
								//发送成功更新老师邮箱状态
								if(CollectionUtils.isNotEmpty(tClassScheduless)){
									for (ClassSchedule c: tClassScheduless) {
										Emailqueue eq=new Emailqueue();
										eq.setId(c.getEmailqueueId());
										eq.setIssendmaiTeacher(Enums.IsSendEmail.SendError);
										System.out.println("更新老师失败Emailqueue id: "+c.getEmailqueueId()+"邮箱:"+tEmail);
										emailqueueService.update(eq);
										String context="老师邮箱发送失败"+c.getId();
										operationLog.setId(sequenceManager.generateId("operationLog"));
										operationLog.setCreateTime(new Date());
										operationLog.setContent(context);
										operationLogService.insert(operationLog);
									}
								}
							}else{
								System.out.println("send email to "+tEmail+" is successful");
								//发送成功更新老师邮箱状态
								if(CollectionUtils.isNotEmpty(tClassScheduless)){
									for (ClassSchedule c: tClassScheduless) {
										Emailqueue eq=new Emailqueue();
										eq.setId(c.getEmailqueueId());
										eq.setIssendmaiTeacher(Enums.IsSendEmail.send);
										System.out.println("更新老师Emailqueue id: "+c.getEmailqueueId());
										emailqueueService.update(eq);
									}
								}
							}

						}

					}




				}
			}
		}
	}


	/**
	 * 定时任务，发送老师和学生上课邮件提醒(升级版 老师邮件把一天的课发到一封邮件中)
	 */
	public void job5obt() {
		//获取当前时间，更具时间获取30分钟后的课程数据
		Date date = LanDateUtils.getNowDate();

		// 创建SimpleDateFormat对象，指定时间格式为“hh:mm”
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

		// 获取当前时间
		Date now = new Date();

		// 格式化当前时间为“hh:mm”格式
		String formattedNow = sdf.format(now) + ":00";
		//System.out.println("当前时间为：" + formattedNow);

		// 获取当前时间之后30分钟的时间
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(now);
		calendar.add(Calendar.MINUTE, 30);
		Date after30Minutes = calendar.getTime();

		// 格式化时间为“hh:mm”格式
		String formattedTime = sdf.format(after30Minutes) + ":00";
		//System.out.println("当前时间之后30分钟的时间为：" + formattedTime);


		Date nextDayDate = now;
		String nextDayStr = LanDateUtils.format(now, TimezoneUtil.strTimeFormat_V1);
		String nextTimeStr = formattedNow;
//		String[] nextTimeArr = nextTimeStr.split(":");
		String nextEndTimeStr = formattedTime;
//		int hh = Integer.parseInt(nextTimeArr[0]) + 1;
//		nextTimeStr = nextTimeArr[0] + ":00";
//		if (hh < 10) {
//			nextEndTimeStr = "0" + hh + ":" + "00";
//		} else {
//			nextEndTimeStr = hh + ":" + "00";
//		}

		//sql
		String sql = "select b.id as classscheduleId, b.zone_id, a.id as id,a.schedule_id as schedule_id,a.issendmail as issendmail,a.issendmaiTeacher as issendmaiTeacher,b.student_id as student_id,b.teacher_id as teacher_id,b.scheduledDate as scheduledDate,b.endDate as endDate,b.startTime as  startTime,b.endTime as endTime, c.chineseName as studentchinesename,";
		sql += "c.englishName as studentenglishname,b.student_id as student_id,b.teacher_id as teacher_id,d.chineseName as tutorchinesename,d.englishName as tutorenglishname,g.name as subject,i.name as classroom,e.sms as sms,e.email as email,e.emailTemplate_id as emailTemplate_id,f.sms as tsms,f.email as temail,f.emailTemplate_id as temailTemplate_id, ";
		sql += " g.name as subject,c.email as sendEmail , c.secondarEmail as ssendSecondarEmail , d.email as tsendEmail ";
		sql += " FROM ";
		sql += " emailqueue a  INNER JOIN classschedule b on a.schedule_id = b.id AND a.issendmail = '0' And b.`status`='1' ";
		sql += " AND b.scheduledDate ='" + nextDayStr + "'";
		sql += " AND b.startTime >='"+nextTimeStr+"' and b.startTime<'"+nextEndTimeStr+"'" ;
		sql += "  LEFT JOIN student c  on b.student_id = c.id LEFT JOIN USER d on b.teacher_id = d.id ";
		sql += "  LEFT JOIN  course g on b.course_id = g.id LEFT JOIN classroom i on b.classroom_id = i.id ";
		sql += "  LEFT JOIN  student_zone e on b.zone_id=e.zone_id and b.student_id=e.student_id ";
		sql += "  LEFT JOIN  teacher_zone f on b.zone_id=f.zone_id and b.teacher_id=f.teacher_id ";
		sql += "  where b.BUType = '4' "; // obt classes
		//System.out.println(">>> " + sql);

		//获取数据
		Zidingyi z = new Zidingyi();
		z.setSql(sql);
		List<Map> list = tongjiService.query(z);
		System.out.println("sql1-->" + sql);
		//获取上海时间
		WhereCondition wc = new WhereCondition();
		wc.andEquals("timezone", "Asia/Shanghai");
		List<Timezone> timezoneSHList = timezoneService.query(wc);
		Timezone timezoneSH = timezoneSHList.get(0);
		String tids = "";//判断老师邮箱是否重复
		//遍历数据
		if (list != null && list.size() != 0) {
			for (Map<String, Object> m : list) {
				if (m.get("schedule_id") != null && !"".equals(m.get("schedule_id").toString().trim())) {
					//遍历出所有数据
					String teacher_id = m.get("teacher_id") != null ? m.get("teacher_id").toString() : "";
					String student_id = m.get("student_id") != null ? m.get("student_id").toString() : "";
					String sms = m.get("sms") != null ? m.get("sms").toString() : "";
					String email = m.get("email") != null ? m.get("email").toString() : "";
					String tsms = m.get("tsms") != null ? m.get("tsms").toString() : "";
					String temail = m.get("temail") != null ? m.get("temail").toString() : "";
					String startTimeCS = m.get("startTime") != null ? m.get("startTime").toString() : "";
					String endTimeCS = m.get("endTime") != null ? m.get("endTime").toString() : "";
					String startDateCS = m.get("scheduledDate") != null ? m.get("scheduledDate").toString() : "";
					String endDateCS = m.get("endDate") != null ? m.get("endDate").toString() : "";
					String emailTemplate_id = m.get("emailTemplate_id") != null ? m.get("emailTemplate_id").toString() : "";
					String temailTemplate_id = m.get("temailTemplate_id") != null ? m.get("temailTemplate_id").toString() : "";
					String sename = m.get("studentenglishname") != null ? m.get("studentenglishname").toString() : "";
					String scname = m.get("studentchinesename") != null ? m.get("studentchinesename").toString() : "";
					String tename = m.get("tutorenglishname") != null ? m.get("tutorenglishname").toString() : "";
					String tcname = m.get("tutorchinesename") != null ? m.get("tutorchinesename").toString() : "";
					String room = m.get("classroom") != null ? m.get("classroom").toString() : "";
					String subject = m.get("subject") != null ? m.get("subject").toString() : "";
					String url = m.get("url") != null ? m.get("url").toString() : "";
					String sEmail = m.get("sendEmail") != null ? m.get("sendEmail").toString() : "";
					String ssendSecondarEmail = m.get("ssendSecondarEmail") != null ? m.get("ssendSecondarEmail").toString() : "";
					String tEmail = m.get("tsendEmail") != null ? m.get("tsendEmail").toString() : "";
					String eqId = m.get("id") != null ? m.get("id").toString() : "";
					String issendmaiTeacher = m.get("issendmaiTeacher") != null ? m.get("issendmaiTeacher").toString() : "";
					String zone_id = m.get("zone_id") != null ? m.get("zone_id").toString() : "";
					String classscheduleId = m.get("classscheduleId") != null ? m.get("classscheduleId").toString() : "-1";

					String sqlonlone = " SELECT url FROM teacher_onlineaddress ";
					sqlonlone += " WHERE teacher_id = '"+teacher_id+"'  AND startDate <= '"+nextDayStr+"' AND endDate >= '"+nextDayStr+"' ";
					Zidingyi zonlineaddress = new Zidingyi();
					zonlineaddress.setSql(sqlonlone);
					List<Map> listonlineaddress = tongjiService.query(zonlineaddress);
					System.out.println("sql1-->" + zonlineaddress);
					if(listonlineaddress != null && listonlineaddress.size() != 0){
						url = listonlineaddress.get(0).get("url").toString();
					}

					//判断学生的邮箱是否勾选 0未勾选，1勾选
					if ("1".equals(email)) {
						Student_timezone p = new Student_timezone();
						p.setStudent_id(student_id);
						p.setStartDate(nextDayDate);
						p.setEndDate(nextDayDate);
						//获取改学员的上课时间所在的数据，如果没有默认上海时区
						List<Student_timezone> ttzList = student_timezoneService.getStuTimezone(p);
						String startTime = "";
						String endTime = "";
						String startDateStr = "";
						String endDatetr = "";
						String timezone = "";
						String tzName = "";
						if (ttzList == null || ttzList.size() < 1) {
							//直接发送，时区上海
							startTime = startTimeCS;
							endTime = endTimeCS;
							startDateStr = startDateCS;
							endDatetr = endDateCS;
							tzName = timezoneSH.getDisplayName();
						} else {
							Student_timezone stz = ttzList.get(0);
							timezone = stz.getTimezone();
							tzName = stz.getTimezoneName();
							List retDateList = TimezoneUtil.timezoneChange(startDateCS, startTimeCS, timezone, "S");
							startDateStr = (String) retDateList.get(2);
							startTime = (String) retDateList.get(1);
							List retDateEndList = TimezoneUtil.timezoneChange(endDateCS, endTimeCS, timezone, "S");
							endDatetr = (String) retDateEndList.get(2);
							endTime = (String) retDateEndList.get(1);
						}

						//获取邮箱模板
						//String sqlA="select a.content as content,a.subject as subject from emailtemplate a where a.id='"+emailTemplate_id+"'";
						String sqlA = " SELECT ";
						sqlA += " a.content AS content, ";  //模板内容
						sqlA += " a. SUBJECT AS SUBJECT, "; //模板标题
						sqlA += " ea. HOST AS emailHost, "; //邮箱服务器地址
						sqlA += " ea. PORT AS emailPort, "; //邮箱服务器端口
						sqlA += " ea.protocol AS emailProtocol, "; //邮箱服务器类型
						sqlA += " ea.accountname AS emailFrom, "; //邮箱账户名称
						sqlA += " ea.email AS emailUserName, "; //邮箱地址
						sqlA += " ea. PASSWORD AS emailPassword ";//密码
						sqlA += " FROM ";
						sqlA += " emailtemplate a ";
						sqlA += " JOIN emailaccount ea ON a.email_id = ea.id ";
						sqlA += " WHERE " ;
						sqlA += "a.id='"+emailTemplate_id+"'";
						z.setSql(sqlA);
						List<Map> listMap=tongjiService.query(z);
						System.out.println("sql1-->"+sqlA);
						if(listMap != null && listMap.size()!= 0){
							for(Map mstu:listMap){
								String content=mstu.get("content")!=null?mstu.get("content").toString().trim():"";
								String SUBJECT=mstu.get("SUBJECT")!=null?mstu.get("SUBJECT").toString().trim():"";
								String emailHost=mstu.get("emailHost")!=null?mstu.get("emailHost").toString().trim():"";
								Integer emailPort=mstu.get("emailPort")!=null?Integer.parseInt(mstu.get("emailPort").toString().trim()):null;
								String emailProtocol=mstu.get("emailProtocol")!=null?mstu.get("emailProtocol").toString().trim():"";
								String emailFrom=mstu.get("emailFrom")!=null?mstu.get("emailFrom").toString().trim():"";
								String emailUserName=mstu.get("emailUserName")!=null?mstu.get("emailUserName").toString().trim():"";
								String emailPassword=mstu.get("emailPassword")!=null?mstu.get("emailPassword").toString().trim():"";


								if(content.contains("{student Englishname}")){
									content=content.replace("{student Englishname}",sename);
								}
								if(content.contains("{student Chinesename}")){
									content=content.replace("{student Chinesename}",scname);
								}
								if(content.contains("{Student Timezone}")){
									content=content.replace("{Student Timezone}",tzName);
								}

								if(content.contains("{StartTime}")){
									content=content.replace("{StartTime}",startTime);
								}
								if(content.contains("{EndTime}")){
									content=content.replace("{EndTime}",endTime);
								}
								if(content.contains("{Scheduleddate}")){
									content=content.replace("{Scheduleddate}",startDateStr);
								}
								if(content.contains("{Subject}")){
									content=content.replace("{Subject}",subject);
								}
								if(content.contains("{Tutor Englishname}")){
									content=content.replace("{Tutor Englishname}",tename);
								}
								if(content.contains("{Tutor Chinesename}")){
									content=content.replace("{Tutor Chinesename}",tcname);
								}
								if(content.contains("{Classroom}")){
									content=content.replace("{Classroom}",room);
								}
								if(content.contains("{Additional Info}")){
									content=content.replace("{Additional Info}",url);
								}
								if(content.contains("{Tutor Timezone}")){
									content=content.replace("{Tutor Timezone}",tzName);
								}
								String subjectEmail=m.get("subject").toString().trim();
								if(subjectEmail.contains("{Subject}")){
									subjectEmail=subjectEmail.replace("{Subject}",subject);
								}
								//发送邮件

								//获取发送邮件服务器账户信息
								MailSenderInfo mailSenderInfo = new MailSenderInfo();
								mailSenderInfo.setHost(emailHost);
								mailSenderInfo.setPort(emailPort);
								mailSenderInfo.setProtocol(emailProtocol);
								mailSenderInfo.setFrom(emailFrom);
								mailSenderInfo.setUserName(emailUserName);
								mailSenderInfo.setPassword(emailPassword);

								//发送邮箱
								//sEmail = "<EMAIL>";
								boolean isSuccess = false;
								if (emailPort.toString() == "25") {
									isSuccess =lanmoMailSender.sendMassageAppoint(sEmail, null, null, content, SUBJECT,mailSenderInfo);
								} else {
									isSuccess =lanmoMailSender.sendMassageAppoint_ssl(sEmail, null, null, content, SUBJECT,mailSenderInfo);
								}

								boolean isSuccessSecondar = false;
								if(StringUtils.isNotBlank(ssendSecondarEmail)){
									//ssendSecondarEmail = "<EMAIL>";
									if (emailPort.toString() == "25") {
										isSuccessSecondar =lanmoMailSender.sendMassageAppoint(ssendSecondarEmail, null, null, content, SUBJECT,mailSenderInfo);//备用邮箱发送
									} else {
										isSuccessSecondar =lanmoMailSender.sendMassageAppoint_ssl(ssendSecondarEmail, null, null, content, SUBJECT,mailSenderInfo);//备用邮箱发送
									}


								}
								if(!isSuccess && !isSuccessSecondar){
									System.out.println("Failure Sending Email To "+sEmail);
									//发送成功修改发送状态issendmail
									Emailqueue eq=new Emailqueue();
									eq.setId(eqId);
									eq.setIssendmail(Enums.IsSendEmail.SendError);
									emailqueueService.update(eq);
									String context="学生邮箱发送失败"+classscheduleId+"邮箱:"+sEmail+"备用邮箱:"+ssendSecondarEmail;
									operationLog.setId(sequenceManager.generateId("operationLog"));
									operationLog.setCreateTime(new Date());
									operationLog.setContent(context);
									operationLogService.insert(operationLog);
								}else{
									System.out.println("send email to "+sEmail+" is successful");
									//发送成功修改状态issendmail
									//发送成功修改发送状态issendmail
									Emailqueue eq=new Emailqueue();
									eq.setId(eqId);
									eq.setIssendmail(Enums.IsSendEmail.send);
									emailqueueService.update(eq);

								}


							}
						}
					}

					//判断老师的邮箱是否勾选 0未勾选，1勾选
					if("1".equals(temail) && !StringUtils.equals("1",issendmaiTeacher) && !tids.contains(classscheduleId)){
						Teacher_timezone p=new Teacher_timezone();
						p.setTeacher_id(teacher_id);
						p.setStartDate(nextDayDate);
						p.setEndDate(nextDayDate);
						//查询老师上课时间所在的时区，如果没有默认上海时区
						List<Teacher_timezone> ttzList=teacher_timezoneService.getTeaTimezone(p);
						String startTime="";
						String endTime="";
						String startDateStr="";
						String endDatetr="";
						String timezone="Asia/Shanghai";
						String tzName="";
						Boolean timezoneFlag = false;
						if(ttzList==null||ttzList.size()<1){
							//直接发送，时区上海
							startTime=startTimeCS;
							endTime=endTimeCS;
							startDateStr=startDateCS;
							endDatetr=endDateCS;
							tzName=timezoneSH.getDisplayName();
						} else{
							timezoneFlag = true;
							Teacher_timezone stz=ttzList.get(0);
							timezone=stz.getTimezone();
							tzName=stz.getTimezoneName();
							List retDateList=TimezoneUtil.timezoneChange(startDateCS,startTimeCS,timezone,"S");
							startDateStr=(String)retDateList.get(2);
							startTime=(String)retDateList.get(1);
							List retDateEndList=TimezoneUtil.timezoneChange(endDateCS,endTimeCS,timezone,"S");
							endDatetr=(String)retDateEndList.get(2);
							endTime=(String)retDateEndList.get(1);
						}

						//获取老师当天所在时区的所有课
						List<ClassSchedule> tClassScheduless = findOneClassScheduleByTimezone(timezone,teacher_id,zone_id,startDateStr,timezoneFlag);
						//获取老师的模版
						Map temailTemp = findEmailTempById(temailTemplate_id);

						if(temailTemp != null && CollectionUtils.isNotEmpty(tClassScheduless)){
							String content=temailTemp.get("content")!=null?temailTemp.get("content").toString().trim():"";
							String SUBJECT=temailTemp.get("SUBJECT")!=null?temailTemp.get("SUBJECT").toString().trim():"";
							String emailHost=temailTemp.get("emailHost")!=null?temailTemp.get("emailHost").toString().trim():"";
							Integer emailPort=temailTemp.get("emailPort")!=null?Integer.parseInt(temailTemp.get("emailPort").toString().trim()):null;
							String emailProtocol=temailTemp.get("emailProtocol")!=null?temailTemp.get("emailProtocol").toString().trim():"";
							String emailFrom=temailTemp.get("emailFrom")!=null?temailTemp.get("emailFrom").toString().trim():"";
							String emailUserName=temailTemp.get("emailUserName")!=null?temailTemp.get("emailUserName").toString().trim():"";
							String emailPassword=temailTemp.get("emailPassword")!=null?temailTemp.get("emailPassword").toString().trim():"";

							//判断是否有循环语句
							if(content.contains("&$")){
								String content1 = content.substring(0,content.indexOf("&$"));
								String content2 = content.substring(content.indexOf("&$")+2,content.indexOf("&$",content.indexOf("&$")+1));
								String content3 = content.substring(content.indexOf("&$",content.indexOf("&$")+1)+2);

								content = content1;
								if(CollectionUtils.isNotEmpty(tClassScheduless)){
									for (ClassSchedule classSchedule:tClassScheduless) {
										String	contentWhile = replaceEmail(content2,classSchedule,timezone,tename,tcname);
										tids +=  classSchedule.getId()+",";
										content += contentWhile +"\r\n\r\n";
									}
								}
								content += content3;
							}


							if(content.contains("{student Englishname}")){   //学生英文名
								content=content.replace("{student Englishname}",sename);
								content=content.replaceAll("\\{student Englishname\\}",sename);
							}
							if(content.contains("{Tutor Timezone}")){   //老师上课时区
								content=content.replace("{Tutor Timezone}",tzName);
							}
							if(content.contains("{StartTime}")){ //上课开始时间
								content=content.replace("{StartTime}",startTime);
							}
							if(content.contains("{EndTime}")){ //上课结束时间
								content=content.replace("{EndTime}",endTime);
							}
							if(content.contains("{Scheduleddate}")){ //上课日期
								content=content.replace("{Scheduleddate}",startDateStr);
							}
							if(content.contains("{Subject}")){  //课程名字
								content=content.replace("{Subject}",subject);
							}
							if(content.contains("{Tutor Englishname}")){ //老师英文
								content=content.replace("{Tutor Englishname}",tename);
							}
							if(content.contains("{Additional Info}")){ //url
								content=content.replace("{Additional Info}",url);
							}
							if(content.contains("{student Chinesename}")){ //学生中文名
								content=content.replace("{student Chinesename}",scname);
							}

							if(content.contains("{Tutor Chinesename}")){ //老师中文名
								content=content.replace("{Tutor Chinesename}",tcname);
							}
							if(content.contains("{Classroom}")){ //教室
								content=content.replace("{Classroom}",room);
							}
							if(content.contains("{Student Timezone}")){//学生时区
								content=content.replace("{Student Timezone}",tzName);
							}

							//获取发送邮件服务器账户信息
							MailSenderInfo mailSenderInfo = new MailSenderInfo();
							mailSenderInfo.setHost(emailHost);
							mailSenderInfo.setPort(emailPort);
							mailSenderInfo.setProtocol(emailProtocol);
							mailSenderInfo.setFrom(emailFrom);
							mailSenderInfo.setUserName(emailUserName);
							mailSenderInfo.setPassword(emailPassword);

							//发送邮箱
							//boolean isSuccess =lanmoMailSender.sendMassageDefault(tEmail, null, null, content, subject);
							//tEmail = "<EMAIL>";

							boolean isSuccess = false;
							if (emailPort.toString() == "25") {
								isSuccess =lanmoMailSender.sendMassageAppoint(tEmail, null, null, content, SUBJECT,mailSenderInfo);
							} else {
								isSuccess =lanmoMailSender.sendMassageAppoint_ssl(tEmail, null, null, content, SUBJECT,mailSenderInfo);
							}
							if(!isSuccess){
								System.out.println("Failure Sending Email To "+tEmail);
								//发送成功更新老师邮箱状态
								if(CollectionUtils.isNotEmpty(tClassScheduless)){
									for (ClassSchedule c: tClassScheduless) {
										Emailqueue eq=new Emailqueue();
										eq.setId(c.getEmailqueueId());
										eq.setIssendmaiTeacher(Enums.IsSendEmail.SendError);
										System.out.println("更新老师失败Emailqueue id: "+c.getEmailqueueId()+"邮箱:"+tEmail);
										emailqueueService.update(eq);
										String context="老师邮箱发送失败"+c.getId();
										operationLog.setId(sequenceManager.generateId("operationLog"));
										operationLog.setCreateTime(new Date());
										operationLog.setContent(context);
										operationLogService.insert(operationLog);
									}
								}
							}else{
								System.out.println("send email to "+tEmail+" is successful");
								//发送成功更新老师邮箱状态
								if(CollectionUtils.isNotEmpty(tClassScheduless)){
									for (ClassSchedule c: tClassScheduless) {
										Emailqueue eq=new Emailqueue();
										eq.setId(c.getEmailqueueId());
										eq.setIssendmaiTeacher(Enums.IsSendEmail.send);
										System.out.println("更新老师Emailqueue id: "+c.getEmailqueueId());
										emailqueueService.update(eq);
									}
								}
							}

						}

					}




				}
			}
		}
	}
	/**
	 * 定时任务,更新学员上课状态
	 */
	public  void job6(){
		//多线程,分步更新
		asynTaskService.updateStudentActive();
		asynTaskService.updateStudentInactive();
		asynTaskService.updateStudentHalfClosed();
		asynTaskService.updateStudentClosed();


		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String context="定时任务,更新学员上课状态"+df.format(new Date());
		operationLog.setId(sequenceManager.generateId("operationLog"));
		operationLog.setCreateTime(new Date());
		operationLog.setContent(context);
		operationLogService.insert(operationLog);
	}

	/**
	 * 定时任务,跟新老师上课状态Active
	 */
	public void job7(){
		//更新老师的active状态,因为active的老师远远小于总的老师,所以先全部转为inactive状态,在更新active状态
		//更新inactive状态
		teacherviewService.updateInactive();

		//更新activezhuaz状态
		teacherviewService.updateActive();

		//日志
		String context="定时任务,更新老师active状态";
		operationLog.setId(sequenceManager.generateId("operationLog"));
		operationLog.setCreateTime(new Date());
		operationLog.setContent(context);
		operationLogService.insert(operationLog);
	}

	/**
	 * 定时任务,每月15日给相应顾问发送学生剩余课时数邮件
	 */
	public void job8(){

		//sql get distinct consultant list
		String sql="SELECT DISTINCT(ownerName) FROM student where ownerName is not null and ownerName <> ''";

		Zidingyi z_consultant = new Zidingyi();
		z_consultant.setSql(sql);
		List<Map> list_consultant = tongjiService.query(z_consultant);
		if (list_consultant != null && list_consultant.size() != 0) {

			//List<Map> list_result = new ArrayList<Map>();
			for (Map<String, String> m_consultant : list_consultant) {

				//sql
				 sql = "select X.name as City, X.SA, X.Englishname as Student, X.ownername as Consultant, X.TotalHours, X.totalhours-x.used1-ifnull(Y.used2, 0) as Balance, X.totalhours-x.used1-ifnull(Y.used2, 0)-ifnull(Z.scheduled, 0) as ToBeScheduled, concat(truncate ((x.used1+ifnull(Y.used2, 0))/X.TotalHours*100,2),'%')as ConsumptionRate from ((select city.name, vsl.id, user.englishname as SA, vsl.Englishname, vsl.ownername, sum(ifnull(amount,0))*2 as TotalHours, sum(ifnull(consumedclass,0)) as Used1, sum(ifnull(amount,0))*2-sum(ifnull(consumedclass,0)) as Balance from view_student_list vsl left join contract on studentid = vsl.id left join user on vsl.tutorid = user.id left join city on vsl.city_id = city.id group by vsl.id order by SA desc) X left join (select student_id, sum(consumption_student_contract.consumedclass) as Used2 from consumption_student_contract left join consumption_student on consumption_student_id = consumption_student.id where consumption_student_contract.contract_id is null group by student_id) Y on X.id = Y.student_id) left join (select student_id, sum(time) as Scheduled from classschedule where id not in (select classschedule_id from consumption_student) and status <> 2 group by student_id) Z on X.id = Z.student_id where X.ownername = '" + m_consultant.get("ownerName") + "'";

				//获取数据
				Zidingyi z = new Zidingyi();
				z.setSql(sql);
				List<Map> list = tongjiService.query(z);
				if (list != null && list.size() != 0) {
					//list_result.addAll(list);
					// send email to consultant

				}

			}




		}




		//日志
		String context="定时任务,每月15日给相应顾问发送学生剩余课时数邮件";
		operationLog.setId(sequenceManager.generateId("operationLog"));
		operationLog.setCreateTime(new Date());
		operationLog.setContent(context);
		operationLogService.insert(operationLog);
	}

	/**
	 * 替换邮箱循环模版
	 * @param content
	 * @param classSchedule
	 * @return
	 */
	private String replaceEmail(String content, ClassSchedule classSchedule,String timezone,String tename,String tcname) {

		if(content.contains("{student Englishname}")){   //学生英文名
			Student student = studentService.loadById(classSchedule.getStudent_id());
			content=content.replace("{student Englishname}",student.getEnglishName());
		}
		if(content.contains("{Tutor Timezone}")){   //老师上课时区
			content=content.replace("{Tutor Timezone}",timezone);
		}
		if(content.contains("{StartTime}")){ //上课开始时间
			content=content.replace("{StartTime}",classSchedule.getStartTime());
		}
		if(content.contains("{EndTime}")){ //上课结束时间
			content=content.replace("{EndTime}",classSchedule.getEndTime());
		}
		if(content.contains("{Scheduleddate}")){ //上课日期
			content=content.replace("{Scheduleddate}",classSchedule.getScheduledDateStr());
		}
		if(content.contains("{Subject}")){  //课程名字
			Course course = courseService.loadById(classSchedule.getCourse_id());
			content=content.replace("{Subject}",course.getName());
		}
		if(content.contains("{Tutor Englishname}")){ //老师英文
			content=content.replace("{Tutor Englishname}",tename);
		}
		if(content.contains("{Additional Info}")){ //url
			//获取当前课的时间,转为上海时间
			String  day = TimezoneUtil.timezoneChange(classSchedule.getScheduledDate(), classSchedule.getStartTime(), timezone, "G").get(2).toString();
			String sqlonlone = " SELECT url FROM teacher_onlineaddress ";
			sqlonlone += " WHERE teacher_id = '"+classSchedule.getTeacher_id()+"'  AND startDate <= '"+day+"' AND endDate >= '"+day+"' ";
			Zidingyi zonlineaddress = new Zidingyi();
			zonlineaddress.setSql(sqlonlone);
			List<Map> listonlineaddress = tongjiService.query(zonlineaddress);
			System.out.println("sql1-->" + zonlineaddress);
			String url= "";
			if(listonlineaddress != null && listonlineaddress.size() != 0){
				url = listonlineaddress.get(0).get("url").toString();
			}
			content=content.replace("{Additional Info}",url);
		}
		if(content.contains("{student Chinesename}")){ //学生中文名
			Student student = studentService.loadById(classSchedule.getStudent_id());
			content=content.replace("{student Chinesename}",student.getChineseName());
		}

		if(content.contains("{Tutor Chinesename}")){ //老师中文名
			content=content.replace("{Tutor Chinesename}",tcname);
		}
		if(content.contains("{Classroom}")){ //教室
			String classroom_id = classSchedule.getClassroom_id();
			String classroomName = "";
			if(StringUtils.isNotBlank(classroom_id)){
				Classroom classroom = classroomService.loadById(classroom_id);
				classroomName = classroom.getName();
			}
			content=content.replace("{Classroom}",classroomName);
		}
//		if(content.contains("{Student Timezone}")){//学生时区
//			content=content.replace("{Student Timezone}",tzName);
//		}
		return content;
	}

	/**
	 * 获取模版
	 * @param temailTemplate_id
	 * @return
	 */
	private Map findEmailTempById(String temailTemplate_id) {
		Zidingyi z = new Zidingyi();

		String sqlA = " SELECT ";
		sqlA += " a.content AS content, ";  //模板内容
		sqlA += " a. SUBJECT AS SUBJECT, "; //模板标题
		sqlA += " ea. HOST AS emailHost, "; //邮箱服务器地址
		sqlA += " ea. PORT AS emailPort, "; //邮箱服务器端口
		sqlA += " ea.protocol AS emailProtocol, "; //邮箱服务器类型
		sqlA += " ea.accountname AS emailFrom, "; //邮箱账户名称
		sqlA += " ea.email AS emailUserName, "; //邮箱地址
		sqlA += " ea. PASSWORD AS emailPassword ";//密码
		sqlA += " FROM ";
		sqlA += " emailtemplate a ";
		sqlA += " JOIN emailaccount ea ON a.email_id = ea.id ";
		sqlA += " WHERE " ;
		sqlA += "a.id='"+temailTemplate_id+"'";
		z.setSql(sqlA);
		List<Map> list = tongjiService.query(z);
		return CollectionUtils.isEmpty(list)||list.size()!=1 ? null : list.get(0);
	}

	/**
	 *
	 * //获取老师当天所在时区的所有课
	 * @param timezone
	 * @param teacher_id
	 * @param zone_id
	 * @param day
	 * @param timezoneFlag
	 * @return
	 */
	private List<ClassSchedule> findOneClassScheduleByTimezone(String timezone, String teacher_id, String zone_id, String day, Boolean timezoneFlag) {

		List<ClassSchedule> list = new ArrayList<ClassSchedule>();

		String select =" select c.*,eq.id as emailqueueId  from classschedule c LEFT JOIN emailqueue eq on c.id = eq.schedule_id  ";
		String where = " where c.`status` ='1' and c.zone_id = '"+zone_id+"' and teacher_id='"+teacher_id+"' AND IFNULL(eq.issendmaiTeacher,'0') = '0' ";

		//不是上海时区,所以需要转换
		if(timezoneFlag){
			where += startAndEndDateTimezone(day, day, timezone, "scheduledDate");
		}else{
			where += " and scheduledDate ='"+day+"' ";
		}
		Zidingyi zi = new Zidingyi();
		zi.setSql(select+where+" ORDER BY scheduledDate, startTime ");
		List<Map> listMap = tongjiService.query(zi);

		if(CollectionUtils.isNotEmpty(listMap)){
			for (Map m :listMap) {
				ClassSchedule c = new ClassSchedule();
				BeanTranUtil.transMap2Bean1(m,c);

				//把上海转为指定的时区
				if(timezoneFlag){
					//更改上课时间
					List startlist = TimezoneUtil.timezoneChange(c.getScheduledDate(), c.getStartTime(), timezone, "S");
					Date riqiDate = TimezoneUtil.changRiqi(c.getScheduledDate(),c.getStartTime(),c.getEndTime());
					List endList = TimezoneUtil.timezoneChange(riqiDate, c.getEndTime(), timezone, "S");

					c.setScheduledDate(LanDateUtils.stringToDate(startlist.get(2).toString()));
					c.setScheduledDateStr(startlist.get(2).toString());
					c.setStartTime(startlist.get(1).toString());
					c.setEndTime(endList.get(1).toString());
					c.setEndDate(LanDateUtils.stringToDate(endList.get(2).toString()));
					c.setEndDateStr(endList.get(2).toString());
				}

				list.add(c);
			}
		}
		return list;
	}

	/**
	 * 发送信息
	 * @param mobiles
	 * @param content
	 * @param smsSvcUrl
	 * @param password
	 * @param cust_code
	 * @throws IOException
	 */
	public void sendSms(String mobiles, String content, String smsSvcUrl,
			String password, String cust_code) throws IOException {
		String urlencContent = URLEncoder.encode(content,"utf-8");
		//String sign = MD5.getMD5((urlencContent + password).getBytes());
        String sign=MD5.sign(urlencContent, password, "utf-8");
		String postData = "content=" + urlencContent + "&destMobiles="
				+ mobiles + "&sign=" + sign + "&cust_code=" + cust_code
				+ "&sp_code=&task_id=0";
		System.err.println(postData);
		URL myurl = new URL(smsSvcUrl);
		URLConnection urlc = myurl.openConnection();
		urlc.setReadTimeout(1000 * 30);
		urlc.setDoOutput(true);
		urlc.setDoInput(true);
		urlc.setAllowUserInteraction(false);

		DataOutputStream server = new DataOutputStream(urlc.getOutputStream());
		System.out.println("发送数据=" + postData);

		server.write(postData.getBytes("utf-8"));
		server.close();

		BufferedReader in = new BufferedReader(new InputStreamReader(urlc.getInputStream(), "utf-8"));
		String resXml = "", s = "";
		while ((s = in.readLine()) != null)
			resXml = resXml + s + "\r\n";
		in.close();
		System.out.println("接收数据=" + URLDecoder.decode(resXml,"utf-8"));
	}

	/**
	 * main
	 * @param args
	 */
	public static void main(String[] args) {
//		String value = "12:00-13:00/Kimberly Liu/History-!@OLb!@OL!@OLe!@\r\n14:00-15:00/Kimberly Liu/History-!@OLb!@OL!@OLe!@";
//		String[] OLb = value.split("!@OLb!@");
//
//		Map<Integer,Integer> map = new HashMap<Integer, Integer>();
//		for (int i =1 ;i < OLb.length;i++){
//			int crb = MathUtils.getFromIndex(value,"!@OLb!@",i);
//			int cre = MathUtils.getFromIndex(value,"!@OLe!@",i)-7;
//			map.put(crb,cre);
//		}
//		//int crb1 = value.indexOf("!@OLb!@",crb+1);
//		//int cre1 = value.indexOf("!@OLe!@",cre+8)-7;
//		value = value.replace("!@OLb!@", "");
//		value = value.replace("!@OLe!@", "");

		ErpTaskJob e = new ErpTaskJob();
		e.job333();

	}

	public void job333() {
		ClassPathXmlApplicationContext classPathXmlApplicationContext = new ClassPathXmlApplicationContext(
				"classpath*:applicationContext-resources.xml");
		TongjiMapper ts = (TongjiMapper) classPathXmlApplicationContext.getBean("tongjiMapper");
		//System.out.println("ok");
		Zidingyi z = new Zidingyi();
		z.setSql("select DISTINCT(timezone.timezone) from student_timezone inner join timezone on timezone.id = student_timezone.timezone_id where CURDATE() >= student_timezone.startDate and CURDATE() <= student_timezone.endDate");
		List<Map> l = tongjiService.query(z);
		//List<Map> l = ts.query(z);
		if(CollectionUtils.isNotEmpty(l)){
			//format = every xx:30
			String format = LanDateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
			DateFormat df = new SimpleDateFormat("hh:mm");
			for (Map m : l) {
				String tz = m.get("timezone").toString();
				//ll = having converted to target timezone
				List ll = TimezoneUtil.timezoneChange(format.split(" ")[0],format.split(" ")[1],tz,"S");
				//System.out.println(tz + ": " + (String)ll.get(2) + " " + (String)ll.get(1));
				if (((String) ll.get(1)).compareTo("14:30") >= 0 && ((String) ll.get(1)).compareTo("14:35") < 0 && !(tz.equals("Asia/Shanghai"))) {
					//target timezone date + 1
					String next_day = LanDateUtils.getNext_Day((String)ll.get(2), 1);
					String next_2days = LanDateUtils.getNext_Day((String)ll.get(2), 2);
					//next day converted to CN date time
					String startdatetime = TimezoneUtil.timezoneChangeStringmiao(next_day + " 00:00:00", tz, "G");
					String enddatetime = TimezoneUtil.timezoneChangeStringmiao(next_2days + " 00:00:00", tz, "G");
					//System.out.println("bingo " + startdatetime + "  " + enddatetime + "  " + tz + ": " + (String)ll.get(2) + " " + (String)ll.get(1));
					job33(tz, startdatetime, enddatetime);
				}

			}

		}


	}

	/**
	 * 判断合同类型,添加对应的条件
	 * @param BUType
	 * @param wc
	 */
	private void addContractType2(String BUType, WhereCondition wc) {
		List<String> contractType2List=new ArrayList<String>();
		if(Enums.OFFlINE.equals(BUType)){
			contractType2List.add(Enums.OFFlINE);
			contractType2List.add(Enums.ContractType2.HYBRID);
			wc.andIn("contractType2",contractType2List);
		}
		if(Enums.ONLINE.equals(BUType)){
			contractType2List.add(Enums.ONLINE);
			contractType2List.add(Enums.ContractType2.HYBRID);
			wc.andIn("contractType2",contractType2List);
		}
	}
	/**
	 * where 时区的时间条件
	 * @param startDateLeave
	 * @param endDateLeave
	 * @param timezoneName
	 * @param field
	 * @return
	 */
	private String startAndEndDateTimezone(String startDateLeave, String endDateLeave, String timezoneName, String field) {
		String sql = " and( ";
		String startDate = "";
		String startTime = "";
		String endDate = "";
		String endTime = "";
		//时区转换
		if(StringUtils.isNotBlank(startDateLeave)){
			List listStart = TimezoneUtil.timezoneChange(startDateLeave, "00:00", timezoneName, "G");
			startDate = listStart.get(2).toString();
			startTime = listStart.get(1).toString();
		}
		if(StringUtils.isNotBlank(endDateLeave)){

			List listEnd= TimezoneUtil.timezoneChange(endDateLeave, "23:59", timezoneName, "G");
			endDate = listEnd.get(2).toString();
			endTime = listEnd.get(1).toString();
		}
		//开始时间与结束都有
		if(StringUtils.isNotBlank(startDateLeave) && StringUtils.isNotBlank(endDateLeave)){
			sql += field +" = '"+startDate+"' and startTime >= '"+startTime+"'" ;    // scheduledDate = '2017-11-01' AND startTime >= '00:00'

			List<String> days = LanDateUtils.getDays(startDate, endDate);
			if(LanDateUtils.getDays(startDate,endDate).size() > 1){
				String startDateNext = LanDateUtils.getNext_Day(startDate,1);
				String endDateNext = LanDateUtils.getNext_Day(endDate,-1);
				sql += " OR  "+field+" >= '"+startDateNext+"' and  "+field +" <= '"+endDateNext+"'"; //or scheduledDate >= '2017-11-02' and scheduledDate <= '2017-11-02'
			}
			sql +=" OR " + field +" = '"+endDate+"' and startTime <= '"+endTime+"'" ; //OR scheduledDate = '2017-11-03' AND startTime <= '00:00'

		}else if(StringUtils.isNotBlank(startDateLeave)){
			sql += field +" = '"+startDate+"' and startTime >= '"+startTime+"'" ;    // scheduledDate = '2017-11-01' AND startTime >= '00:00'
			sql +=" OR " + field +" >= '"+LanDateUtils.getNext_Day(startDate,1)+"' " ; //OR scheduledDate > '2017-11-02' '

		}else if(StringUtils.isNotBlank(endDateLeave)){
			sql += field +" = '"+endDate+"' and startTime <= '"+endTime+"'" ; //OR scheduledDate = '2017-11-03' AND startTime <= '00:00'
			sql +=" OR " + field +" <= '"+LanDateUtils.getNext_Day(endDate,-1)+"' " ; //OR scheduledDate < '2017-11-02' '
		}

		sql +=" ) ";
		return sql;
	}

	public void testEmail() {

		Zidingyi z = new Zidingyi();
		String sqlA = " SELECT ";
		sqlA += " a.content AS content, ";  //模板内容
		sqlA += " a. SUBJECT AS SUBJECT, "; //模板标题
		sqlA += " ea. HOST AS emailHost, "; //邮箱服务器地址
		sqlA += " ea. PORT AS emailPort, "; //邮箱服务器端口
		sqlA += " ea.protocol AS emailProtocol, "; //邮箱服务器类型
		sqlA += " ea.email AS emailFrom, "; //邮箱账户名称
		sqlA += " ea.email AS emailUserName, "; //邮箱地址
		sqlA += " ea. PASSWORD AS emailPassword ";//密码
		sqlA += " FROM ";
		sqlA += " emailtemplate a ";
		sqlA += " JOIN emailaccount ea ON a.email_id = ea.id ";
		sqlA += " WHERE " ;
		sqlA += "a.id='Id_emailtemplate00000005'";
		z.setSql(sqlA);
		List<Map> listMap=tongjiService.query(z);
		if(listMap != null && listMap.size()!= 0) {
			for (Map mstu:listMap) {
				String content=mstu.get("content")!=null?mstu.get("content").toString().trim():"";
				String SUBJECT=mstu.get("SUBJECT")!=null?mstu.get("SUBJECT").toString().trim():"";
				String emailHost=mstu.get("emailHost")!=null?mstu.get("emailHost").toString().trim():"";
				Integer emailPort=mstu.get("emailPort")!=null?Integer.parseInt(mstu.get("emailPort").toString().trim()):null;
				String emailProtocol=mstu.get("emailProtocol")!=null?mstu.get("emailProtocol").toString().trim():"";
				String emailFrom=mstu.get("emailFrom")!=null?mstu.get("emailFrom").toString().trim():"";
				String emailUserName=mstu.get("emailUserName")!=null?mstu.get("emailUserName").toString().trim():"";
				String emailPassword=mstu.get("emailPassword")!=null?mstu.get("emailPassword").toString().trim():"";

				MailSenderInfo mailSenderInfo = new MailSenderInfo();
				mailSenderInfo.setHost(emailHost);
				mailSenderInfo.setPort(emailPort);
				mailSenderInfo.setProtocol(emailProtocol);
				mailSenderInfo.setFrom(emailFrom);
				mailSenderInfo.setUserName(emailUserName);
				mailSenderInfo.setPassword(emailPassword);

				boolean isSuccess =lanmoMailSender.sendMassageAppoint_ssl("<EMAIL>", null, null, "email body here", "email subject", mailSenderInfo);
				System.out.println("isSuccess: " + isSuccess);
			}
		}
	}
}
