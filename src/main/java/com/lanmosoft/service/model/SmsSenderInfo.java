package com.lanmosoft.service.model;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("smsSenderInfo")
public class SmsSenderInfo {
	
	@Value("${sms.smsSvcUrl}")
	private String smsSvcUrl;
	@Value("${sms.cust_code}")
	private String cust_code;
	@Value("${sms.password}")
	private String password;
	
	public String getSmsSvcUrl() {
		return smsSvcUrl;
	}
	public void setSmsSvcUrl(String smsSvcUrl) {
		this.smsSvcUrl = smsSvcUrl;
	}
	public String getCust_code() {
		return cust_code;
	}
	public void setCust_code(String cust_code) {
		this.cust_code = cust_code;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	

}
