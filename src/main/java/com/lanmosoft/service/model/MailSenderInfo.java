package com.lanmosoft.service.model;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("mailSenderInfo")
public class MailSenderInfo {
	
	@Value("${mail.host}")
	private String host;
	@Value("${mail.port}")
	private int port;
	@Value("${mail.transport.protocol}")
	private String protocol;
	@Value("${mail.default.from}")
	private String from;
	@Value("${mail.default.userName}")
	private String userName;
	@Value("${mail.default.password}")
	private String password;
	public String getHost() {
		return host;
	}
	public int getPort() {
		return port;
	}
	public String getProtocol() {
		return protocol;
	}
	public String getFrom() {
		return from;
	}
	public String getUserName() {
		return userName;
	}
	public String getPassword() {
		return password;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public void setPassword(String password) {
		this.password = password;
	}
}
