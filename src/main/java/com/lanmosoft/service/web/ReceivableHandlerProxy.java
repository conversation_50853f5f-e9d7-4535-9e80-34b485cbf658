package com.lanmosoft.service.web;

public class ReceivableHandlerProxy implements com.lanmosoft.service.web.ReceivableHandler {
  private String _endpoint = null;
  private com.lanmosoft.service.web.ReceivableHandler receivableHandler = null;
  
  public ReceivableHandlerProxy() {
    _initReceivableHandlerProxy();
  }
  
  public ReceivableHandlerProxy(String endpoint) {
    _endpoint = endpoint;
    _initReceivableHandlerProxy();
  }
  
  private void _initReceivableHandlerProxy() {
    try {
      receivableHandler = (new com.lanmosoft.service.web.ReceivableHandlerServiceLocator()).getReceivableHandlerPort();
      if (receivableHandler != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)receivableHandler)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)receivableHandler)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (receivableHandler != null)
      ((javax.xml.rpc.Stub)receivableHandler)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public com.lanmosoft.service.web.ReceivableHandler getReceivableHandler() {
    if (receivableHandler == null)
      _initReceivableHandlerProxy();
    return receivableHandler;
  }
  
  public java.lang.String handleReceivable(java.lang.String jsonStr) throws java.rmi.RemoteException{
    if (receivableHandler == null)
      _initReceivableHandlerProxy();
    return receivableHandler.handleReceivable(jsonStr);
  }
  
  
}