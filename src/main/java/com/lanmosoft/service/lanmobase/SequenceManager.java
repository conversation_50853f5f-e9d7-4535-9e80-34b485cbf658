package com.lanmosoft.service.lanmobase;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lanmosoft.dao.model.Xulie;
import com.lanmosoft.service.biz.XulieService;
import com.lanmosoft.util.LanDateUtils;
import com.lanmosoft.util.RandomUtil;
@Service
public class SequenceManager {
	@Autowired
	XulieService xulieService;

	/**
	 * 获取当前序列号
	 * 
	 * @param type
	 * @return
	 */
	public synchronized long currentSeq(String type) {
		if (StringUtils.isEmpty(type)) {
			throw new RuntimeException("序列类型不能为空");
		}
		Xulie xulie = xulieService.loadById(type);
		if (xulie == null) {
			xulie = new Xulie();
			xulie.setId(type);
			xulie.setDangqian(0L);
			xulie.setShijian(LanDateUtils.currentDateStr());
			xulieService.insert(xulie);
			return 0L;
		} else {
			return xulie.getDangqian();
		}
	}

	/**
	 * 获取当前序列号，隔天序列号清零
	 * 
	 * @param type
	 * @return
	 */
	public synchronized long currentSeqLimitDay(String type) {
		if (StringUtils.isEmpty(type)) {
			throw new RuntimeException("序列类型不能为空");
		}
		Xulie xulie = xulieService.loadById(type);
		if (xulie == null) {
			xulie = new Xulie();
			xulie.setId(type);
			xulie.setDangqian(0L);
			xulie.setShijian(LanDateUtils.currentDateStr());
			xulieService.insert(xulie);
			return 0L;
		} else {
			if (StringUtils.equals(LanDateUtils.currentDateStr(),
					xulie.getShijian())) {
				return xulie.getDangqian();
			} else {
				xulie.setDangqian(0L);
				xulie.setShijian(LanDateUtils.currentDateStr());
				xulieService.update(xulie);
				return xulie.getDangqian();
			}

		}
	}

	/**
	 * 获取下一个序列号
	 * 
	 * @param type
	 * @return
	 */
	public synchronized long nextSeq(String type) {
		long a = currentSeq(type);
		a = a + 1;
		Xulie xulie = new Xulie();
		xulie.setId(type);
		xulie.setDangqian(a);
		xulie.setShijian(LanDateUtils.currentDateStr());
		xulieService.update(xulie);
		return a;
	}

	/**
	 * 获取下一个序列号，隔天清零
	 * 
	 * @param type
	 * @return
	 */
	public synchronized long nextSeqLimitDay(String type) {
		long a = currentSeqLimitDay(type);
		a = a + 1;
		Xulie xulie = new Xulie();
		xulie.setId(type);
		xulie.setDangqian(a);
		xulie.setShijian(LanDateUtils.currentDateStr());
		xulieService.update(xulie);
		return a;
	}

	/**
	 * ID生成器
	 * 
	 * @param type
	 * @return
	 */
	public String generateId(String type) {
		if (StringUtils.isEmpty(type)) {
			throw new RuntimeException("序列类型不能为空");
		}
		type = "Id_" + type;
		long idseq = nextSeq(type);
		String id = type+getFixStr(""+idseq,8);
		return id;
	}
	
	private static String getFixStr(String s,int size){
		if(StringUtils.isEmpty(s)){
			return null;
		}
		if(s.length()==size){  
			return s;
		}
		
		if(s.length()<size){
			return "000000000".substring(0, size-s.length())+s;
		}
		
		if(s.length()>size){
			return RandomUtil.produceStringAndNumber(size);
		}
		
		return null;
	}
	public static void main(String[] args) {
		System.out.println(getFixStr("94",8));
	}

	/**
	 * 单据凭证号生成
	 * 
	 * @param type
	 * @param prefix
	 *            前缀
	 * @param suffix
	 *            后缀
	 * @param isLimitDay
	 *            是否隔天清零序列号
	 * @return
	 */
	public String generateNO(String type, String prefix, String suffix,
			boolean isLimitDay) {
		if (StringUtils.isEmpty(prefix)) {
			prefix = "";
		}
		if (StringUtils.isEmpty(suffix)) {
			suffix = "";
		}
		if (isLimitDay) {
			long idseq = nextSeqLimitDay("NO_" + type);
			long b = 1000000 + (1000000 + idseq) % 1000000;
			String s = prefix
					+ (Long.parseLong(LanDateUtils.currentDateStr() + b) - 1000000)
					+ suffix;
			return s;
		} else {
			long idseq = nextSeq("NO_" + type);
			long b = 1000000 + (1000000 + idseq) % 1000000;
			String s = prefix + ((100000000L + b) + suffix).substring(1);
			return s;
		}
	}
	
	public String generateTeCode(String prefix, String suffix){
		String code ="";
		if (StringUtils.isEmpty(prefix)) {
			prefix = "";
		}
		if (StringUtils.isEmpty(suffix)) {
			suffix = "";
		}
		long idseq = nextSeq("teCode");
		if(0<=idseq && idseq<10){
			code = "00"+idseq;
		}else if(10<=idseq && idseq<100){
			code = "0"+idseq;
		}else{
			code = ""+idseq;
		}
		String s = prefix + code + suffix;
		return s;
	}

}
