package com.lanmosoft.model;

import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.springframework.web.multipart.support.DefaultMultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;


@XmlRootElement
public class Restful implements Serializable {
    private static final long serialVersionUID = 1332643889208978231L;

    private String context;
    private HttpServletRequestWrapper request;
    private HttpServletResponseWrapper response;

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public HttpServletRequestWrapper getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = (HttpServletRequestWrapper)request;
    }

    public HttpServletResponseWrapper getResponse() {
        return response;
    }

    public void setResponse(HttpServletResponse response) {
        this.response = (HttpServletResponseWrapper) response;
    }
}
