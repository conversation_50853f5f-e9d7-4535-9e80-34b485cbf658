package com.lanmosoft.model;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.google.gson.annotations.Expose;

public class ContractInfo{

	@Expose
	private String ContractFNumber;// SF合同号 
	@Expose
	private Date ContractFDate;// 业务日期
	@Expose
	private String SalesOrg;// 销售组织
	private String CustName;// 中文名
	@Expose
	private String SFCode;// SF编码
	private Double units;// 数量
	@Expose
	private Double unitPrice;// 不含税单价
	@Expose
	private Double vatRate;// 税率
	@Expose
	private Double withVatPrice;// 含税单价
	@Expose
	private Double vat;// 税额
	@Expose
	private Double taxTotal;// 价税合计
	
	private List <String> consumption_student_contract_ids;//学生耗课合同ID
	private String contract_id;//合同ID
	public String getContractFNumber() {
		return this.ContractFNumber == null ? StringUtils.EMPTY : this.ContractFNumber.trim();
	}
	public void setContractFNumber(String contractFNumber) {
		this.ContractFNumber = contractFNumber == null ? StringUtils.EMPTY : contractFNumber.trim();
	}
	public Date getContractFDate() {
		return ContractFDate;
	}
	public void setContractFDate(Date contractFDate) {
		ContractFDate = contractFDate;
	}
	public String getSalesOrg() {
		return this.SalesOrg == null ? StringUtils.EMPTY : this.SalesOrg.trim();
	}
	public void setSalesOrg(String salesOrg) {
		this.SalesOrg = salesOrg == null ? StringUtils.EMPTY : salesOrg.trim();
	}
	public String getCustName() {
		return this.CustName == null ?  StringUtils.EMPTY : this.CustName.trim();
	}
	public void setCustName(String custName) {
		 this.CustName = custName == null ?  StringUtils.EMPTY : custName.trim();
	}
	public String getSFCode() {
		return this.SFCode == null ?  StringUtils.EMPTY : this.SFCode.trim();
	}
	public void setSFCode(String sFCode) {
		 this.SFCode = sFCode == null ?  StringUtils.EMPTY : sFCode.trim();
	}
	public Double getUnits() {
		return units;
	}
	public void setUnits(Double units) {
		this.units = units;
	}
	public Double getUnitPrice() {
		return unitPrice;
	}
	public void setUnitPrice(Double unitPrice) {
		this.unitPrice = unitPrice;
	}
	public Double getVatRate() {
		return vatRate;
	}
	public void setVatRate(Double vatRate) {
		this.vatRate = vatRate;
	}
	public Double getWithVatPrice() {
		return withVatPrice;
	}
	public void setWithVatPrice(Double withVatPrice) {
		this.withVatPrice = withVatPrice;
	}
	public Double getVat() {
		return vat;
	}
	public void setVat(Double vat) {
		this.vat = vat;
	}
	public Double getTaxTotal() {
		return taxTotal;
	}
	public void setTaxTotal(Double taxTotal) {
		this.taxTotal = taxTotal;
	}
	public List<String> getConsumption_student_contract_ids() {
		return consumption_student_contract_ids;
	}
	public void setConsumption_student_contract_ids(
			List<String> consumption_student_contract_ids) {
		this.consumption_student_contract_ids = consumption_student_contract_ids;
	}
	public String getContract_id() {
		return contract_id;
	}
	public void setContract_id(String contract_id) {
		this.contract_id = contract_id;
	}
}
