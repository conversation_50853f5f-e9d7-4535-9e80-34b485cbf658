/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums.ApprovalStatus;
import com.lanmosoft.enums.Enums.ClassScheduleStatus;
import com.lanmosoft.enums.Enums.LeaveState;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.LanmoMailSender;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.model.PaiKeGuwenInfo;
import com.lanmosoft.util.*;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class View_student_leaveController extends BaseController {

	@Autowired
	View_student_leaveService view_student_leaveService;
	@Autowired
	Student_leaveService student_leaveService;
	@Autowired
	ClassScheduleService classScheduleService;
	@Autowired
	Consumption_studentService consumption_studentService;
	@Autowired
	Consumption_teacherService consumption_teacherService;
	@Autowired
	TongjiService tongjiService;
	@Autowired
	Student_freeTimeService student_freeTimeService;
	@Autowired
	LanmoMailSender lanmoMailSender;
	@Autowired
	PaiKeGuwenInfo paiKeGuwenInfo;
	@Autowired
	StudentService studentService;
	@Autowired
	Teacher_zoneService teacher_zoneService;

	@Autowired
	TimezoneService timezoneService;

	@Autowired
	ZoneService zoneService;
	
	@RequestMapping(value = "/ngres/qingjiaguanli/view_student_leave/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			View_student_leave p = new View_student_leave();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				view_student_leaveService.update(p);
				context+="修改了学生请假数据，修改学生请假"+p.getId();
			} else {
				String id = sequenceManager.generateId("view_student_leave");
				p.setId(id);
				initCreate2(p, request);
				if("1".equals(p.getIsInadvance().trim())){
					p.setGivenClass(0d);
					p.setDeductionClass(0d);
				}
				view_student_leaveService.insert(p);
				context+="新增了学生请假数据，新增学生请假"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/qingjiaguanli/view_student_leave/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<View_student_leave> list = new ArrayList<View_student_leave>();
			for (int i = 0; i < jsonArray.size(); i++) {
				View_student_leave p = new View_student_leave();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (View_student_leave p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			view_student_leaveService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生请假数据："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	

	@RequestMapping(value = "/ngres/qingjiaguanli/view_student_leave/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			String leaveTimeStart = JSONUtils.getStr(jsonObj.getJSONObject("searchItems"), "leaveTimeStart");
			String leaveTimeEnd = JSONUtils.getStr(jsonObj.getJSONObject("searchItems"), "leaveTimeEnd");
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			if(StringUtils.isNotEmpty(leaveTimeStart)){
				wc.andGreaterEquals("leaveDate", leaveTimeStart+" 00:00:00");
			}
			if(StringUtils.isNotEmpty(leaveTimeEnd)){
				wc.andLessEquals("leaveDate", leaveTimeEnd+" 23:59:59");
			}
			initWanNengChaXun(jsonObj, wc);// 万能查询
			String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
			zonePermissionFilter(jsonObj, wc, request, response,zone_id);// 万能查询
			if(!"0".equals(getLogin(request).getUser().getCategory().trim())){
				List<Permission> li=getLogin(request).getPermissionList();
				for(Permission pm:li){
					if("133".equals(pm.getFunction_id())){
						wc.andEquals("tutorId", getLogin(request).getUser().getId());
					}
				}
			}
			List<View_student_leave> list = view_student_leaveService.query(wc);
			//时区转化
			//获取当前时区的名称
			String timezone_id = JSONUtils.getStr(jsonObj, "timezone_id");
			Timezone timezone=timezoneService.loadById(timezone_id);
			for (View_student_leave vst:list) {
				//更改请假时间
				String handleTime = vst.getHandleTime();
				List handle = TimezoneUtil.timezoneChange(handleTime.substring(0, 10), handleTime.substring(11, 16), timezone.getTimezone(), "S");
				vst.setHandleTime(handle.get(2).toString()+" "+handle.get(1).toString());

				//更改上课时间
				List startlist = TimezoneUtil.timezoneChange(vst.getLeaveDate(), vst.getStartTime(), timezone.getTimezone(), "S");
				List endList = TimezoneUtil.timezoneChange(vst.getLeaveDate(), vst.getEndTime(), timezone.getTimezone(), "S");

				String riqi = startlist.get(2).toString();
				Date date = LanDateUtils.stringToDate(riqi);
				vst.setLeaveDate(date);
				vst.setStartTime(startlist.get(1).toString());
				vst.setEndTime(endList.get(1).toString());
			}

			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(view_student_leaveService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/qingjiaguanli/view_student_leave/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			View_student_leave k =  view_student_leaveService.loadById(JSONUtils.getStr(jsonObj, "id"));
			//时区转换
			String timezone_id = JSONUtils.getStr(jsonObj, "timezone_id");
			Timezone timezone=timezoneService.loadById(timezone_id);
			//更改请假时间
			String handleTime = k.getHandleTime();
			List handle = TimezoneUtil.timezoneChange(handleTime.substring(0, 10), handleTime.substring(11, 16), timezone.getTimezone(), "S");
			k.setHandleTime(handle.get(2).toString()+" "+handle.get(1).toString());

			//更改上课时间
			List startlist = TimezoneUtil.timezoneChange(k.getLeaveDate(), k.getStartTime(), timezone.getTimezone(), "S");
			List endList = TimezoneUtil.timezoneChange(k.getLeaveDate(), k.getEndTime(), timezone.getTimezone(), "S");

			String riqi = startlist.get(2).toString();
			Date date = LanDateUtils.stringToDate(riqi);
			k.setLeaveDate(date);
			k.setStartTime(startlist.get(1).toString());
			k.setEndTime(endList.get(1).toString());

			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		
	@RequestMapping(value = "/ngres/qingjiaguanli/view_student_leave/approval")
	public String approval(ModelMap model, @RequestBody final String content,
			final HttpServletRequest request, final HttpServletResponse response) {
		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus arg0) {
				try {
					JSONObject jsonObj = JSONObject.fromObject(content);
					String approvalStatus = JSONUtils.getStr(jsonObj, "approvalStatus");
					List<String> ids = new ArrayList<String>();
					List<String> classSchedule_ids = new ArrayList<String>();
					String creator_id = getLogin(request).getUser().getId();
					JSONArray jArr = jsonObj.getJSONArray("list");
					for(int i=0; i<jArr.size(); i++){
						JSONObject jObj  = jArr.getJSONObject(i);
						String id = JSONUtils.getStr(jObj, "id");
						Student_leave student_leave = student_leaveService.loadById(id);
						ids.add(id);
						String classSchedule_id = JSONUtils.getStr(jObj, "classSchedule_id");
						classSchedule_ids.add(classSchedule_id);
						if(StringUtils.equals(ApprovalStatus.STATUS_END, approvalStatus)){
							ClassSchedule classSchedule = classScheduleService.loadById(classSchedule_id);
							//生成学生耗课信息
							consumption_studentService.generateConsumedClass(student_leave.getStudent_id(), classSchedule_id, 
									student_leave.getDeductionClass(), 0D, null, creator_id);
							
							//生成教师耗课信息
							double wage2=0d;
							String currency_id  = null;
							if(student_leave.getGivenClass()!=null){
								String teachingWay_id = classSchedule.getTeachingWay();
								Map<String,Double> rt=getWage2(classSchedule.getTeacher_id(),classSchedule.getCourse_id(),LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd"),student_leave.getGivenClass(),teachingWay_id);
								wage2=rt.get("wage2");

								if(rt.get("currency_idDou")!=null){ //添加货别id
									Double currency_idDou = rt.get("currency_idDou");
									String currency_idString = currency_idDou.toString();
									String[] v = currency_idString.split("\\.");

									String currency_idStr = currency_idString.split("\\.")[0];
									int index = 8 - currency_idStr.length();
									currency_id = "Id_currency";
									for (int j = 0; j<index;j++){
										currency_id += "0";
									}
									currency_id += currency_idStr;
								}
							}
							consumption_teacherService.generateConsumedClass(classSchedule.getTeacher_id(), classSchedule_id,
									student_leave.getGivenClass(), wage2, 0D, 0D,currency_id);
							
							View_student_leave sl = view_student_leaveService.loadById(student_leave.getId());
							ClassSchedule classSchedule1 = classScheduleService.loadById(sl.getClassSchedule_id());
							User teacher = userService.loadById(classSchedule1.getTeacher_id());
							WhereCondition wc2 = new WhereCondition();
//							wc2.andEquals("teacher_id", teacher.getId());
//							wc2.andEquals("zone_id", classSchedule1.getZone_id());
//							List<Teacher_zone> zone_list = teacher_zoneService.query(wc2);
//							boolean is_SH = false;
//							if(!zone_list.isEmpty()) {
//								for (Teacher_zone c : zone_list) {
//									if (StringUtils.equals("Id_city00000001", c.getCity_id().toString()) //上海地区的发送邮件
//											|| StringUtils.equals("2", classSchedule1.getBUType())	) //online地区的发送邮件
//										is_SH = true;
//								}
//							}
							//发送邮箱
 							boolean isEmail= false;
							String campusMailbox = null;
							if(StringUtils.isNotBlank(classSchedule1.getZone_id())){
								Zone zone = zoneService.loadById(classSchedule1.getZone_id());
								if(StringUtils.isNotBlank(zone.getCampusMailbox())){
									campusMailbox = zone.getCampusMailbox();
									isEmail = true;
								}
							}
							//审核通过,切有校区邮箱给顾问发送学生请假邮件
							if(isEmail) {
								//判断邮箱是否多个
								String[] campusMailboxs = campusMailbox.split(",");
								//内容
								String bodyText = "";
								String subject = "";
								if ("Id_zone00000016".equals(classSchedule1.getZone_id())) {
									subject += "OBT-学生请假-" + sl.getEnglishName() + " " + sl.getChineseName();
								} else {
									subject += "【学生请假】" + sl.getEnglishName() + " " + sl.getChineseName();
								}
								bodyText+="学生"+sl.getEnglishName()+" "+sl.getChineseName()+"请假，"+
										LanDateUtils.format(sl.getLeaveDate(), "yyyy-MM-dd")+"日"+
										sl.getStartTime()+"-"+sl.getEndTime()+  " "+ teacher.getEnglishName()+
										" 的课已取消,请及时告知老师。\r\n"+
										"请假原因："+sl.getReason();

								System.out.println("send Email start..");
								for(int e = 0; e < campusMailboxs.length ;e++){
									String paiKeGuWeiEamil = campusMailboxs[e];
									String bodyText0 ="Dear "+paiKeGuWeiEamil.substring(0,paiKeGuWeiEamil.indexOf("@"))+" \r\n" ;
									bodyText = bodyText0+bodyText;

									boolean isSuccess = lanmoMailSender.sendMassageDefault(paiKeGuWeiEamil, null, null, bodyText, subject);
									if(!isSuccess){
										System.out.println("Failure Sending Email To "+paiKeGuWeiEamil);
									}else{
										System.out.println("send email to "+paiKeGuWeiEamil+" is successful");
									}
								}
								System.out.println("send Email end..");

							}

							// cencel  tencent meeting
							User usr = userService.loadById(classSchedule.getTeacher_id());
							//ClassSchedule cls = classScheduleService.loadById(p.getId());
							String meetingid = classSchedule.getTencentmeetingid();
							if (StringUtils.isNotEmpty(meetingid)) {
								try {
									String contextTencent = "";
									String res = TencentUtil.cancelTencentMeeting(meetingid, usr.getEmail());
									if (StringUtils.equals(res, "error")) {
										contextTencent += "学生请假，删除腾讯会议错误" + classSchedule.getId();
									} else {
										contextTencent += "学生请假，删除腾讯会议成功" + classSchedule.getId();
										classSchedule.setTencenturl("");
										classSchedule.setTencentmeetingid("");
										classSchedule.setTencentmeetingcode("");
									}
									operationLog.setId(sequenceManager.generateId("operationLog"));
									operationLog.setUser_id(getLogin(request).getUser().getId());
									operationLog.setCreateTime(new Date());
									operationLog.setContent(contextTencent);
									operationLogService.insert(operationLog);
								} catch (Exception e) {
									e.printStackTrace();;
								}
							}
							classScheduleService.update(classSchedule);

						}
					}
					WhereCondition wc = new WhereCondition();
					wc.andIn("id", ids);
					Student_leave student_leave = new Student_leave();
					student_leave.setApprovalStatus(approvalStatus);
					student_leaveService.updateByCondition(wc, student_leave);
					ClassSchedule classSchedule = new ClassSchedule();
					String context=getLogin(request).getUser().getUserName();
					if(StringUtils.equals(ApprovalStatus.STATUS_TERMINATE, approvalStatus)){//驳回，把课表请假状态恢复成未请假
						classSchedule.setLeaveState(LeaveState.LEAVE_NONE);
						context+="驳回了学生请假数据："+ids;
					}else{//通过，标记删除课表
						classSchedule.setStatus(ClassScheduleStatus.DELETED);
						context+="审核通过了学生请假数据："+ids;
						updateTeacherFreeTime(classSchedule_ids);
					}
					wc = new WhereCondition();
					wc.andIn("id", classSchedule_ids);
					classScheduleService.updateByCondition(wc, classSchedule);
					
					operationLog.setId(sequenceManager.generateId("operationLog"));
					operationLog.setUser_id(getLogin(request).getUser().getId());
					operationLog.setCreateTime(new Date());
					operationLog.setContent(context);
					operationLogService.insert(operationLog);
					AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0032");
				} catch (Exception e) {
					arg0.setRollbackOnly();
					AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0033");
					e.printStackTrace();
				}
			}
		});
		
		return null;
	}
	
	private void updateTeacherFreeTime(List<String> classSchedule_ids){
		System.out.println("classSchedule_ids===>"+classSchedule_ids);
		for(String id:classSchedule_ids){
			
			ClassSchedule classSchedule=classScheduleService.loadById(id);
			String sql="SELECT a.id as id from student_freeTime a where a.freeDate='"+LanDateUtils.format(classSchedule.getScheduledDate(),"yyyy-MM-dd")+"' "
			+ "and a.startTime>='"+classSchedule.getStartTime()+"' and a.endTime<='"+classSchedule.getEndTime()+"' and a.student_id='"+classSchedule.getStudent_id()+"'";
			Zidingyi z=new Zidingyi();
			z.setSql(sql);
			List<Map> li = tongjiService.query(z);
			for(Map m:li){
				student_freeTimeService.delete(Integer.valueOf(m.get("id").toString()));
			}
			sql="SELECT a.id as id from student_freeTime a where a.freeDate='"+LanDateUtils.format(classSchedule.getScheduledDate(),"yyyy-MM-dd")+"' "
			+ "and a.startTime<'"+classSchedule.getStartTime()+"' and a.endTime>'"+classSchedule.getStartTime()+"' and a.student_id='"+classSchedule.getStudent_id()+"'";
			z.setSql(sql);
			List<Map> li1 = tongjiService.query(z);
			for(Map m:li1){
				Student_freeTime student_freeTime=student_freeTimeService.loadById(m.get("id").toString());
				//student_freeTime.setEndTime(classSchedule.getStartTime());
				student_freeTime.setFreeTimeCode(generateFreeTimeCode(student_freeTime.getStartTime(), classSchedule.getStartTime()));
				student_freeTimeService.update(student_freeTime);
			}
			sql="SELECT a.id as id from student_freeTime a where a.freeDate='"+LanDateUtils.format(classSchedule.getScheduledDate(),"yyyy-MM-dd")+"' "
			+ "and a.startTime<'"+classSchedule.getEndTime()+"' and a.endTime>'"+classSchedule.getEndTime()+"' and a.student_id='"+classSchedule.getStudent_id()+"'";
			z.setSql(sql);
			List<Map> li2 = tongjiService.query(z);
			for(Map m:li2){
				Student_freeTime student_freeTime=student_freeTimeService.loadById(m.get("id").toString());
				//student_freeTime.setStartTime(classSchedule.getEndTime());
				student_freeTime.setFreeTimeCode(generateFreeTimeCode(classSchedule.getEndTime(), student_freeTime.getEndTime()));
				student_freeTimeService.update(student_freeTime);
			}
		}
	}
	
	/**
	 * 封装占位符
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	private String generateFreeTimeCode(String startTime, String endTime){
		String freeTimeCode = "";
		int startMin = Integer.valueOf(startTime.split(":")[1]);
		int endMin = Integer.valueOf(endTime.split(":")[1]);
		if(startMin==0&&endMin==15){
			freeTimeCode +="1";
		}else if(startMin==0&&endMin==30){
			freeTimeCode +="12";
		}else if(startMin==0&&endMin==45){
			freeTimeCode +="123";
		}else if(startMin==15&&endMin==0){
			freeTimeCode +="234";
		}else if(startMin==15&&endMin==30){
			freeTimeCode +="2";
		}else if(startMin==15&&endMin==45){
			freeTimeCode +="23";
		}else if(startMin==30&&endMin==0){
			freeTimeCode +="34";
		}else if(startMin==30&&endMin==45){
			freeTimeCode +="3";
		}else if(startMin==45&&endMin==0){
			freeTimeCode +="4";
		}
		return freeTimeCode;
	}
	
	
	private Map<String,Double> getWage2(String teacherId,String courseId,String day,Double xs,String teachingWay_id){
		Map<String,Double> rt=new HashMap<String,Double>();
		double d=0d;
		if(xs==0){
			rt.put("wage2", d);
			return rt;
		}
//		String sql="select a.periodLowerLimit as min,a.periodUpperLimit as max,a.intervalType as type,a.unitPrice as price from teacher_course a ";
//		sql+=" where a.teacher_id='"+teacherId+"' and a.course_id='"+courseId+"' and a.startDate<='"+day+"' and (a.endDate>'"+day+"' or a.endDate is null)";
		String sqljiage=" ";
		sqljiage += " SELECT DISTINCT hd.currency_id , hd.periodLowerLimit AS min, hd.periodUpperLimit AS max, hd.intervalType AS type, hd.unitPrice AS price ";
		sqljiage += " FROM teacher_course tc  JOIN teacher_hourlyratescheme th on th.id = tc.scheme_id JOIN hourlyratescheme_details hd on th.id = hd.scheme_id JOIN teacher_course_teachingway tct on tct.schemeDetails_id = hd.id ";
		sqljiage += " WHERE tc.course_id = '"+courseId+"' AND tc.teacher_id = '"+teacherId+"' and tct.teachingWay_id ='"+teachingWay_id+"' ";
		sqljiage += " and (  (hd.startDate <= '"+day+"' and hd.endDate> '"+day+"')or (hd.startDate <= '"+day+"' and hd.endDate is null) ) ";
		Zidingyi z=new Zidingyi();
		z.setSql(sqljiage);
		List<Map> li = tongjiService.query(z);
		if(li.isEmpty()){
			rt.put("wage2", d);
			return rt;
		}
		String type=li.get(0).get("type").toString();
		String startDate=LanDateUtils.getFirstDayOfMonth(LanDateUtils.stringToDate(day), 0);
		String endDate=LanDateUtils.getLastDayOfMonth(LanDateUtils.stringToDate(day), 0);
		if(!"1".equals(type)){
			startDate=LanDateUtils.format(LanDateUtils.getFirstDayOfWeek(LanDateUtils.stringToDate(day)), "yyyy-MM-dd");
			endDate=LanDateUtils.format(LanDateUtils.getLastDayOfWeek(LanDateUtils.stringToDate(day)), "yyyy-MM-dd");
		}
		String sql1="select sum(a.wage) as keshi,sum(a.wageDeduction) as koushi from consumption_teacher a LEFT JOIN classSchedule b on a.classSchedule_id=b.id";
		sql1+=" where a.teacher_id='"+teacherId+"' and b.scheduledDate>='"+startDate+"' and b.scheduledDate<='"+endDate+"'";
		z.setSql(sql1);
		List<Map> li1 = tongjiService.query(z);
		double ks=xs;
		if(!li1.isEmpty()&&li1.get(0)!=null){
			String keshi=li1.get(0).get("keshi")!=null?li1.get(0).get("keshi").toString():"0";
			String koushi=li1.get(0).get("koushi")!=null?li1.get(0).get("koushi").toString():"0";
			if(Double.valueOf(koushi)<Double.valueOf(keshi)+xs){
				ks=Double.valueOf(keshi)+xs-Double.valueOf(koushi);
			}
		}
		User u=userService.loadById(teacherId);
		for(Map m:li){

			//货币id,返回值为double需要转换下
			if(m.get("currency_id")!= null){
				String currency_id = m.get("currency_id").toString();
				Double currency_idDou = Double.valueOf(currency_id.substring(11));
				rt.put("currency_idDou",currency_idDou );
			}
			//总课时开始计算,总课时为0,课时小于1
			if(ks == xs && Double.valueOf(m.get("min").toString()) > ks ){
				d+=Double.valueOf(m.get("price").toString())*xs;
				rt.put("price", Double.valueOf(m.get("price").toString()));
			}
			if(Double.valueOf(m.get("min").toString())<=ks&&Double.valueOf(m.get("max").toString())>=ks){
				if(Double.valueOf(m.get("min").toString())==1){
//					if("1".equals(u.getType())||"3".equals(u.getType())){
//						d=0d;
//					}else{
						d+=Double.valueOf(m.get("price").toString())*xs;
						rt.put("price", Double.valueOf(m.get("price").toString()));
//					}
				}else{
					d+=Double.valueOf(m.get("price").toString())*xs;
					rt.put("price", Double.valueOf(m.get("price").toString()));
				}
			}
			if(Double.valueOf(m.get("max").toString())<ks&&ks-Double.valueOf(m.get("max").toString())<xs){
				d+=Double.valueOf(m.get("price").toString())*(xs-(ks-Double.valueOf(m.get("max").toString())));
				System.out.println("d1=="+d);
				System.out.println("xs1=="+xs);
				xs=ks-Double.valueOf(m.get("max").toString());
				System.out.println("xs2=="+xs);
				rt.put("price", Double.valueOf(m.get("price").toString()));
				rt.put("next", xs);
			}
			System.out.println("d=="+d);
		}
		rt.put("wage2", d);
		System.out.println("wage2=="+rt.get("wage2"));
		System.out.println("price=="+rt.get("price"));
		System.out.println("next=="+rt.get("next"));
		return rt;
	}
}
