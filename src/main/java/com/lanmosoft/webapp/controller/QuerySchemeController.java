package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.City;
import com.lanmosoft.dao.model.QueryScheme;
import com.lanmosoft.dao.model.StudentReturns;
import com.lanmosoft.dao.model.Teacherview;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.QuerySchemeService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.BeanTranUtil;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.PinyinUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Controller
public class QuerySchemeController extends BaseController {
    @Autowired
    QuerySchemeService querySchemeService;


    @RequestMapping(value = "/ngres/tongjibaobiao/queryScheme/delete")
    public String execute3(ModelMap model, @RequestBody String content, HttpServletRequest request, HttpServletResponse response) {
        try {

            String id = content;
            if(StringUtils.isBlank(id)){
                AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
                return null;
            }
            //删除
            querySchemeService.delete(id);
            //日志
            String context=getLogin(request).getUser().getUserName()+"删除了查询方案数据："+id;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/tongjibaobiao/queryScheme/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            wc.setLength(page.getItemsperpage());
            wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
            wc.setOrderBy(orderGuize);
            initWanNengChaXun(jsonObj, wc);// 万能查询
            LoginModel login = (LoginModel)request.getSession().getAttribute("login");
            wc.andEquals("userId",login.getUser().getId());

            List<QueryScheme> list = querySchemeService.query(wc);
            int count = querySchemeService.count(wc);

            JSONArray ja = JSONArray.fromObject(list,jsonConfig);
            page.setTotalItems(count);
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s=JSONObject.fromObject(map,jsonConfig).toString();
            AjaxUtils.ajaxJson(response,s );
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 保存
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/tongjibaobiao/queryScheme/createOrUpdate")
    public String execute(ModelMap model, @RequestBody String content,HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginModel login = (LoginModel)request.getSession().getAttribute("login");
            JSONObject jsonObj = JSONObject.fromObject(content);
            QueryScheme queryScheme = new QueryScheme();
            JSONObject.toBean(jsonObj,queryScheme,jsonConfig);
            queryScheme.setId(sequenceManager.generateId("queryScheme"));
            queryScheme.setUserId(login.getUser().getId());
            queryScheme.setStudentIds(JSONUtils.getStr(jsonObj, "studentIds"));
            queryScheme.setTeacherIds(JSONUtils.getStr(jsonObj, "teacherIds"));
            //保存
            querySchemeService.insert(queryScheme);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
            e.printStackTrace();
        }
        return null;
    }
}
