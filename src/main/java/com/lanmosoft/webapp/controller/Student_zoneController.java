/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.LanDateUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class Student_zoneController extends BaseController {

	@Autowired
	Student_zoneService student_zoneService;
	@Autowired
	StudentService studentService;
	@Autowired
	TutorService tutorService;
	@Autowired
	ContractService contractService;
	@Autowired
	Consumption_studentService consumption_studentService;
	@Autowired
	View_consumption_student_contractService view_consumption_student_contractService;

	@RequestMapping(value = "/ngres/xueshengguanli/student_zone/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			String context=getLogin(request).getUser().getUserName();
			Student_zone p = new Student_zone();
			JSONObject.toBean(job,p,jsonConfig);
			if (StringUtils.isNotEmpty(p.getStudent_id())) {
				Student old = studentService.loadById(p.getStudent_id());
				Student newStu=new Student();
				newStu.setId(p.getStudent_id());
				Date nowDate = LanDateUtils.getNowDate();
				//是否已经有code
				if(StringUtils.isEmpty(old.getCode())){
					if(p.getCity_id()!=null && p.getZone_id()!=null){
						newStu.setCode(sequenceManager.generateNO("student", "", "", false));
						studentService.update(newStu);
						context="修改了学生信息";
						operationLog.setId(sequenceManager.generateId("operationLog"));
						operationLog.setUser_id(getLogin(request).getUser().getId());
						operationLog.setCreateTime(new Date());
						operationLog.setContent(context);
						operationLogService.insert(operationLog);
					}
					String id = sequenceManager.generateId("student_zone");
					p.setId(id);
					p.setClassStatus("2");//设置默认的上课状态
					student_zoneService.insert(p);
					//更新学生上课状态
					student_zoneService.updateClassStatusById(p.getStudent_id());
					context+="新增了学生城市和校区设置，新增学生城市和校区"+p.getId();

					//添加历史助教的信息
					if(p.getTutorId() != null){
						String tutorId = tutorInsert(p);
						context+="新增了学生的历史助教信息"+tutorId;
					}
					operationLog.setId(sequenceManager.generateId("operationLog"));
					operationLog.setUser_id(getLogin(request).getUser().getId());
					operationLog.setCreateTime(new Date());
					operationLog.setContent(context);
					operationLogService.insert(operationLog);
					AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
				}
				else{
					List<Student_zone> querycheckZone = student_zoneService.querycheckZone(p);
					if(querycheckZone.size()!=0){
						AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0074");
					}
					else{
						if (StringUtils.isNotEmpty(p.getId())) {
							student_zoneService.update(p);
							context+="修改了学生城市和校区设置，修改学生城市和校区"+p.getId();

							if(StringUtils.isNotBlank(p.getTutorId())){
								//修改历史助教的信息
								String tutorId = tutorUpdate(p,"update");
								if(!"error".equals(tutorId)){
									context+="修改了学生的历史助教信息"+tutorId;
									String tutorInsertID = tutorInsert(p);
									context+="新增了学生的历史助教信息"+tutorInsertID;
								}
							}


						} else {
							String id = sequenceManager.generateId("student_zone");
							p.setId(id);
							p.setClassStatus("2");//设置默认的上课状态
							student_zoneService.insert(p);
							//更新学生上课状态
							student_zoneService.updateClassStatusById(p.getStudent_id());
							context+="新增了学生城市和校区设置，新增学生城市和校区"+p.getId();
							if(StringUtils.isNotBlank(p.getTutorId())){
								//添加历史助教的信息
								String tutorId = tutorInsert(p);
								context+="新增了学生的历史助教信息"+tutorId;
							}

						}
						operationLog.setId(sequenceManager.generateId("operationLog"));
						operationLog.setUser_id(getLogin(request).getUser().getId());
						operationLog.setCreateTime(new Date());
						operationLog.setContent(context);
						operationLogService.insert(operationLog);
						AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
					 // }
					}
				}
				
				
			}
			
			
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 修改历史助教信息,添加结束时间
	 * @param p
	 * @return
	 */
	private String tutorUpdate(Student_zone p,String  falg) {
		//查询出上一次的历史助教信息,添加他的结束时间
		WhereCondition wc = new WhereCondition();
		wc.andEquals("student_id",p.getStudent_id());
		wc.andEquals("zone_id",p.getZone_id());
		wc.andIsNull("endDate");
		String tutorUpdateId = "";
		List<Tutor> listTutor = tutorService.query(wc);
		if(!listTutor.isEmpty()&&listTutor.size()==1){
			Tutor tutor = listTutor.get(0);
			if("delete".equals(falg)){
				tutor.setEndDate(new Date());
				tutorService.update(tutor);
				tutorUpdateId = tutor.getId();
			}else{
				if(!tutor.getTutor_id().equals(p.getTutorId())){
					tutor.setEndDate(new Date());
					tutorService.update(tutor);
					tutorUpdateId = tutor.getId();
				}else {
					tutorUpdateId = "error";
				}

			}
		}
		return tutorUpdateId;
	}

	/**
	 * 添加历史助教信息,没有结束时间
	 * @param p
	 * @return
	 */
	private String tutorInsert(Student_zone p) {

		Tutor tutor = new Tutor();

		String tutorId = sequenceManager.generateId("tutor");
		tutor.setId(tutorId);
		tutor.setStudent_id(p.getStudent_id());
		tutor.setTutor_id(p.getTutorId());
		tutor.setZone_id(p.getZone_id());
		tutor.setStartDate(new Date());
		tutorService.insert(tutor);

		return tutorId;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/student_zone/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Student_zone> list = new ArrayList<Student_zone>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Student_zone p = new Student_zone();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Student_zone p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			student_zoneService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生城市和校区："+ids;
			//修改历史助教的信息
			for (Student_zone p : list) {
				if(p.getTutorId() != null) {
					String tutorId = tutorUpdate(p,"delete");
					context += "修改了学生的历史助教信息" + tutorId;
				}
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/xueshengguanli/student_zone/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Student_zone> list = new ArrayList<Student_zone>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Student_zone p = new Student_zone();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Student_zone p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			student_zoneService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生城市和校区："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/student_zone/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List<Student_zone> list = student_zoneService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(student_zoneService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/xueshengguanli/student_zone/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Student_zone k =  student_zoneService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}

	public void updateClassStatusById(final String student_id){

		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
				try {
					WhereCondition wc = new WhereCondition();
					wc.andNotEquals("classStatus","5");
					wc.andEquals("student_id",student_id);
					//查询出所有学生的上课状态,除了冻结的
					List<Student_zone> listSz = student_zoneService.query(wc);
					for (Student_zone sz:listSz) {
						//查询执行中的合同
						wc = new WhereCondition();
						wc.andEquals("status", "2");
						wc.andEquals("studentId", sz.getStudent_id());
						//判断合同类型
						addContractType2(sz.getBUType(),wc);
						List<Contract> listExecute=contractService.query(wc);
						if(listExecute.isEmpty()){
							//查询待确认合同
							wc.remove("status");
							wc.andEquals("status","1");
							List<Contract> listConfirmed = contractService.query(wc);
							if(listConfirmed.isEmpty()){
								boolean isNull = true;
								wc = new WhereCondition();
								wc.andEquals("student_id", sz.getStudent_id());
								List<Consumption_student> listCs = consumption_studentService.query(wc);
								if (!listCs.isEmpty()) {
									our:for (Consumption_student cs : listCs) {
										wc = new WhereCondition();
										wc.andEquals("consumption_student_id", cs.getId());
										wc.andIsNull("contract_id");
										List<View_consumption_student_contract> listVCsc = view_consumption_student_contractService.query(wc);
										if (!listVCsc.isEmpty()) {
											for (View_consumption_student_contract vcsc:listVCsc) {
												if(sz.getBUType().equals(vcsc.getBUType())){
													isNull = false;
													break our;
												}
											}
										}
									}
									if (isNull) {
										sz.setClassStatus("4");
									} else {
										sz.setClassStatus("1");
									}
								}
							}else{
								sz.setClassStatus("2");//设置待确认
							}
						}else{
							sz.setClassStatus("3");//设置执行中
						}
						student_zoneService.update(sz);
					}
				} catch (Exception e) {
					transactionStatus.setRollbackOnly();//回滚
					e.printStackTrace();
				}
			}
		});

	}

	/**
	 * 判断合同类型,添加对应的条件
	 * @param BUType
	 * @param wc
	 */
	private void addContractType2(String BUType, WhereCondition wc) {
		List<String> contractType2List=new ArrayList<String>();
		if(Enums.OFFlINE.equals(BUType)){
			contractType2List.add(Enums.OFFlINE);
			contractType2List.add(Enums.ContractType2.HYBRID);
			wc.andIn("contractType2",contractType2List);
		}
		if(Enums.ONLINE.equals(BUType)){
			contractType2List.add(Enums.ONLINE);
			contractType2List.add(Enums.ContractType2.HYBRID);
			wc.andIn("contractType2",contractType2List);
		}
	}
}
