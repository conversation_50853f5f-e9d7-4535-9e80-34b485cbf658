/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.lanmosoft.util.FastDFSTool;
import com.lanmosoft.util.LanDateUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.lanmosoft.dao.model.Filerecord;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.FilerecordService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class FilerecordController extends BaseController {

	private String fileDate = "2018/02/05 18:25";//限制时间

	@Autowired
	FilerecordService filerecordService;
	@RequestMapping(value = "/ngres/gonggong/filerecord/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			Filerecord p = new Filerecord();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				filerecordService.update(p);
				context+="修改了文件记录"+p.getId();
			} else {
				String id = sequenceManager.generateId("filerecord");
				p.setId(id);
				initCreate2(p, request);
				filerecordService.insert(p);
				context+="新增了文件记录"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/gonggong/filerecord/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Filerecord> list = new ArrayList<Filerecord>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Filerecord p = new Filerecord();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Filerecord p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			filerecordService.deleteByCondition(wc);

			//删除文件
			for (Filerecord p : list) {
				Boolean flagDate =	p.getLogicPath().contains("/lanmo/upload");
				if(!flagDate){
					int i = FastDFSTool.deleteFile(p.getLogicPath());
					System.out.println("FastDFS文件删除,状态:"+i+",path:"+p.getLogicPath());
				}
			}

			String context=getLogin(request).getUser().getUserName()+"删除了文件记录"+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}


	/**
	 * 判断文件创建时间是否在限制时间后,是就用fastDFS文件上传,不是用就原来的
	 * @param fileDate
	 * @param createTime
	 * @return
	 */
	private Boolean compareDate(String fileDate, Date createTime) {

		try {
			Date date = LanDateUtils.convertStringToDate("yyyy/MM/dd HH:mm", fileDate);
			if (date.getTime() < createTime.getTime()){
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	@RequestMapping(value = "/ngres/gonggong/filerecord/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			System.out.println(jsonObj);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = filerecordService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(filerecordService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/gonggong/filerecord/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Filerecord k =  filerecordService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
}
