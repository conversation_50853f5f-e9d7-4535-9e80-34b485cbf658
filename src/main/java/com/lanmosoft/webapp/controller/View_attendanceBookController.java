/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Currency;
import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.enums.Enums.*;
import com.lanmosoft.enums.LanConstants;
import com.lanmosoft.model.ContractInfo;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.web.erp.ErpServiceImpl;
import com.lanmosoft.util.*;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class View_attendanceBookController extends BaseController {

    @Autowired
    View_attendanceBookService view_attendanceBookService;
    @Autowired
    AttendanceBookService attendanceBookService;
    @Autowired
    Consumption_studentService consumption_studentService;
    @Autowired
    Consumption_student_contractService consumption_student_contractService;
    @Autowired
    Consumption_teacherService consumption_teacherService;
    @Autowired
    UserService userService;
    @Autowired
    ContractService contractService;
    @Autowired
    StudentService studentService;
    @Autowired
    Teacher_courseService teacher_courseService;
    @Autowired
    View_teacher_courseService view_teacher_courseService;
    @Autowired
    ClassScheduleService classScheduleService;
    @Autowired
    ErpServiceImpl erpServiceImpl;
    @Autowired
    TongjiService tongjiService;
    @Autowired
    LockDataService lockDataService;
    @Autowired
    Teacher_freeTimeService teacher_freeTimeService;
    @Autowired
    Student_freeTimeService student_freeTimeService;
    @Autowired
    Hourlyratescheme_detailsService hourlyratescheme_detailsService;
    @Autowired
    View_consumption_student_contractService view_consumption_student_contractService;
    @Autowired
    TimezoneService timezoneService;
    @Autowired
    Student_zoneService student_zoneService;
    @Autowired
    CurrencyService currencyService;

    @Autowired
    ContractviewService contractviewService;
    //初始化历史数据
//	private void inti_student_contract(){
//		WhereCondition wc=new WhereCondition();
//		//wc.andEquals("id", "Id_consumption_student00000042");
//		wc.andIsNull("flag");
//		List<Consumption_student> list=consumption_studentService.query(wc);
//		for(Consumption_student cs:list){
//			wc=new WhereCondition();
//			wc.andEquals("consumption_student_id", cs.getId());
//			List<Consumption_student_contract> list1=consumption_student_contractService.query(wc);
//			double dc=0d;
//			double consumedClass=cs.getConsumedClass();
//			for(Consumption_student_contract csc:list1){
//				dc+=csc.getConsumedClass()!=null?csc.getConsumedClass():0d;
//			}
//			System.out.println("consumedClass-->"+consumedClass+"---dc-->"+dc);
//			if(consumedClass>dc){
//				consumedClass=consumedClass-dc;
//				List<Contract> contracts = contractService.getContractByClass(cs.getStudent_id());
//				for(Contract item : contracts){
//					if(item.getAmount()*2-item.getConsumedClass()>=consumedClass){
//						Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
//						consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
//						consumption_student_contract.setConsumption_student_id(cs.getId());// 学生耗课ID
//						consumption_student_contract.setContract_id(item.getId());//合同ID
//						consumption_student_contract.setConsumedClass(consumedClass);
//						consumption_student_contract.setUnitPrice(item.getTuitionFee()/item.getAmount());// 单价
//						consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
//						consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
//						consumption_student_contractService.insert(consumption_student_contract);
//						if(item.getAmount()*2-item.getConsumedClass()>consumedClass){
//							item.setStatus(ContractStatus.STAUS_2);
//						}else{
//							item.setStatus(ContractStatus.STAUS_3);
//						}
//						item.setConsumedClass(item.getConsumedClass()+consumedClass);
//						contractService.update(item);
//						updateStudentShangke(cs.getStudent_id(),item.getStatus());
//						consumedClass=0;
//						break;
//					}else{
//						double consumedClass1=item.getAmount()*2-item.getConsumedClass();
//						Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
//						consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
//						consumption_student_contract.setConsumption_student_id(cs.getId());// 学生耗课ID
//						consumption_student_contract.setContract_id(item.getId());//合同ID
//						consumption_student_contract.setConsumedClass(consumedClass1);
//						consumption_student_contract.setUnitPrice(item.getTuitionFee()/item.getAmount());// 单价
//						consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
//						consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
//						consumption_student_contractService.insert(consumption_student_contract);
//						item.setStatus(ContractStatus.STAUS_3);
//						item.setConsumedClass(item.getAmount()*2);
//						contractService.update(item);
//						updateStudentShangke(cs.getStudent_id(),item.getStatus());
//						consumedClass=consumedClass-consumedClass1;
//					}
//				}
//				if(consumedClass>0){
//					Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
//					consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
//					consumption_student_contract.setConsumption_student_id(cs.getId());// 学生耗课ID
//					consumption_student_contract.setConsumedClass(consumedClass);
//					consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
//					consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
//					consumption_student_contractService.insert(consumption_student_contract);
//					updateStudentShangke(cs.getStudent_id(),"");
//				}
//			}
//			cs.setFlag("1");
//			if(consumedClass<dc){
//				cs.setFlag("0");
//			}
//			consumption_studentService.update(cs);
//		}
//	}


    /**
     * 签到
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/sign")
    public String execute(ModelMap model, @RequestBody String content,
                          final HttpServletRequest request, final HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            final JSONObject job = jsonObj.getJSONObject("item");
            final View_attendanceBook p = new View_attendanceBook();
            JSONObject.toBean(job, p, jsonConfig);
            ClassSchedule classSchedule = classScheduleService.loadById(p.getClassSchedule_id());
            final String BUType = classSchedule.getBUType();
            final String course_id = classSchedule.getCourse_id();
            if ("0".equals(classSchedule.getLeaveState()) && "1".equals(classSchedule.getStatus())) {
                transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                    @Override
                    protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                        try {
                            // 签到表
                            AttendanceBook attendanceBook = new AttendanceBook();
                            attendanceBook.setId(sequenceManager.generateId("attendanceBook"));
                            attendanceBook.setClassSchedule_id(p.getClassSchedule_id());//课表ID
                            attendanceBook.setAttendanceStatus(AttendanceStatus.NORMAL);//出勤状态
                            attendanceBook.setApprovalStatus(ApprovalStatus.STATUS_NOPROCESS);//审批状态
                            attendanceBook.setApprover(null);//审批人
                            initCreate2(attendanceBook, request);
//							attendanceBook.setContract_id(contract_id);//合同
                            attendanceBook.setLockStatus(Enums.FALSE_STRING);//锁定状态
                            attendanceBookService.insert(attendanceBook);
                            String context = getLogin(request).getUser().getUserName() + "为" + p.getClassSchedule_id() + "正常签到,签到信息:" + attendanceBook.getId();
                            operationLog.setId(sequenceManager.generateId("operationLog"));
                            operationLog.setUser_id(getLogin(request).getUser().getId());
                            operationLog.setCreateTime(new Date());
                            operationLog.setContent(context);
                            operationLogService.insert(operationLog);
                            // 获取学生合同，生成耗课合同信息
                            if (StringUtils.equals(CourseType.COURSETYPE_1, p.getCourseType())
                                    || StringUtils.equals(CourseType.COURSETYPE_2, p.getCourseType())) { //发工资，扣学时
                                // 学生耗课信息
                                Consumption_student consumption_student = new Consumption_student();
                                consumption_student.setId(sequenceManager.generateId("consumption_student"));
                                consumption_student.setStudent_id(p.getStudent_id());//学生
                                consumption_student.setClassSchedule_id(p.getClassSchedule_id());//课表
                                consumption_student.setConsumedClass(Double.valueOf(p.getTime()));//消耗课时数
                                consumption_student.setGivenClass(0D);//赠送课时
                                consumption_student.setLockStatus(Enums.FALSE_STRING);//锁定状态
                                consumption_studentService.insert(consumption_student);
//								List<Contract> contracts = contractService.getContractByClass(consumption_student.getStudent_id(), consumption_student.getConsumedClass());
//								for(Contract item : contracts){
//									Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
//									consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
//									consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
//									if(StringUtils.isNotEmpty(item.getId())){
//										consumption_student_contract.setContract_id(item.getId());//合同ID
//										consumption_student_contract.setConsumedClass(item.getCurrentConsumedClass());
//										consumption_student_contract.setUnitPrice(item.getTuitionFee()/item.getAmount());// 单价
//									}
//									consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
//									consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
//									consumption_student_contractService.insert(consumption_student_contract);
//									//更新合同已消耗课时数
//									if(StringUtils.isNotEmpty(item.getId())){
//										Contract contract = new Contract();
//										contract.setId(item.getId());
//										contract.setConsumedClass(item.getConsumedClass());
//										if(item.getAmount()>contract.getConsumedClass()/2){
//											contract.setStatus(ContractStatus.STAUS_2);
//										}else{
//											contract.setStatus(ContractStatus.STAUS_3);
//										}
//										contractService.update(contract);
//										updateStudentShangke(p.getStudent_id(),contract.getStatus());
//									}else{
//										updateStudentShangke(p.getStudent_id(),"");
//									}
//								}
                                //LoginModel login = (LoginModel)request.getSession().getAttribute("login");
                                //String BUType=classSchedule.getBUType();
                                List<String> contractType2List = new ArrayList<String>();
                                if (Enums.OFFlINE.equals(BUType)) {
                                    contractType2List.add(Enums.OFFlINE);
                                    contractType2List.add(Enums.ContractType2.HYBRID);
                                }
                                if (Enums.ONLINE.equals(BUType)) {
                                    contractType2List.add(Enums.ONLINE);
                                    contractType2List.add(Enums.ContractType2.HYBRID);
                                }
                                if (Enums.SPE.equals(BUType)) {
                                    contractType2List.add(Enums.ContractType2.SPE);
                                }
                                if (Enums.ONLINETE.equals(BUType)) {
                                    contractType2List.add(Enums.ContractType2.OBT);
                                }
                                List<Contract> contracts = new ArrayList<Contract>();
                                if (contractType2List.size() > 0) {
                                    if (Enums.ONLINETE.equals(BUType)) {
                                        // obt, add course id
                                        contracts = contractService.getContractByClass(consumption_student.getStudent_id(), contractType2List, course_id);
                                    } else {
                                        contracts = contractService.getContractByClass(consumption_student.getStudent_id(), contractType2List);
                                    }
                                } else {
                                    contracts = contractService.getContractByClass(consumption_student.getStudent_id());
                                }

                                double consumedClass = consumption_student.getConsumedClass();
                                for (Contract item : contracts) {
                                    if (item.getAmount() * 2 - item.getConsumedClass() >= consumedClass) {
                                        Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                        consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                        consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                        consumption_student_contract.setContract_id(item.getId());//合同ID
                                        consumption_student_contract.setConsumedClass(consumedClass);
                                        consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                        consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                        consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                        consumption_student_contractService.insert(consumption_student_contract);
                                        if (item.getAmount() * 2 - item.getConsumedClass() > consumedClass) {
                                            item.setStatus(ContractStatus.STAUS_2);
                                        } else {
                                            item.setStatus(ContractStatus.STAUS_3);
                                        }
                                        item.setConsumedClass(item.getConsumedClass() + consumedClass);
                                        contractService.update(item);
                                        updateStudentShangke(p.getStudent_id(), item.getStatus());
                                        consumedClass = 0;
                                        break;
                                    } else {
                                        double consumedClass1 = item.getAmount() * 2 - item.getConsumedClass();
                                        Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                        consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                        consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                        consumption_student_contract.setContract_id(item.getId());//合同ID
                                        consumption_student_contract.setConsumedClass(consumedClass1);
                                        consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                        consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                        consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                        consumption_student_contractService.insert(consumption_student_contract);
                                        item.setStatus(ContractStatus.STAUS_3);
                                        item.setConsumedClass(item.getAmount() * 2);
                                        contractService.update(item);
                                        updateStudentShangke(p.getStudent_id(), item.getStatus());
                                        consumedClass = consumedClass - consumedClass1;
                                    }
//
//									//更新合同已消耗课时数
//									if(StringUtils.isNotEmpty(item.getId())){
//										Contract contract = new Contract();
//										contract.setId(item.getId());
//										contract.setConsumedClass(item.getConsumedClass());
//										if(item.getAmount()>contract.getConsumedClass()/2){
//											contract.setStatus(ContractStatus.STAUS_2);
//										}else{
//											contract.setStatus(ContractStatus.STAUS_3);
//										}
//										contractService.update(contract);
//										updateStudentShangke(p.getStudent_id(),contract.getStatus());
//									}else{
//										updateStudentShangke(p.getStudent_id(),"");
//									}
                                }
                                if (consumedClass > 0) {
                                    Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                    consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                    consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                    consumption_student_contract.setConsumedClass(consumedClass);
                                    consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                    consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                    consumption_student_contractService.insert(consumption_student_contract);
                                    updateStudentShangke(p.getStudent_id(), "");
                                }

                                //教师耗课信息
                                Consumption_teacher consumption_teacher = new Consumption_teacher();
                                consumption_teacher.setId(sequenceManager.generateId("consumption_teacher"));
                                consumption_teacher.setTeacher_id(p.getTeacher_id());// 教师ID
                                consumption_teacher.setClassSchedule_id(p.getClassSchedule_id());// 课表
                                consumption_teacher.setWage(Double.valueOf(p.getTime()));// 工资（小时计）
                                String teachingWay = JSONUtils.getStr(job, "teachingWay");//上课方式
                                Map<String, Double> rt = getWage2(p.getTeacher_id(), p.getCourse_id(), LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd"), consumption_teacher.getWage(), teachingWay);
                                consumption_teacher.setWage2(rt.get("wage2"));
                                if (rt.get("next") != null) {
                                    consumption_teacher.setNext(rt.get("next"));
                                } else {
                                    consumption_teacher.setNext(0D);
                                }
                                if (rt.get("price") != null) {
                                    consumption_teacher.setNowprice(rt.get("price"));
                                } else {
                                    consumption_teacher.setNowprice(0D);
                                }
                                if (rt.get("currency_idDou") != null) { //添加货别id
                                    Double currency_idDou = rt.get("currency_idDou");
                                    String currency_idString = currency_idDou.toString();
                                    String[] v = currency_idString.split("\\.");

                                    String currency_idStr = currency_idString.split("\\.")[0];
                                    int index = 8 - currency_idStr.length();
                                    String currency_id = "Id_currency";
                                    for (int i = 0; i < index; i++) {
                                        currency_id += "0";
                                    }
                                    currency_id += currency_idStr;
                                    consumption_teacher.setCurrency_id(currency_id);
                                }
                                consumption_teacher.setWageDeduction(0D);// 扣除工资（小时计）
                                consumption_teacher.setIncidental(0D);// 杂费
                                consumption_teacher.setLockStatus(Enums.FALSE_STRING);
                                consumption_teacherService.insert(consumption_teacher);
                            } else if (StringUtils.equals(CourseType.COURSETYPE_3, p.getCourseType())) {//不发工资，不扣学时

                            } else if (StringUtils.equals(CourseType.COURSETYPE_4, p.getCourseType())) {//发工资，不扣学时
                                //教师耗课信息
                                Consumption_teacher consumption_teacher = new Consumption_teacher();
                                consumption_teacher.setId(sequenceManager.generateId("consumption_teacher"));
                                consumption_teacher.setTeacher_id(p.getTeacher_id());// 教师ID
                                consumption_teacher.setClassSchedule_id(p.getClassSchedule_id());// 课表
                                consumption_teacher.setWage(Double.valueOf(p.getTime()));// 工资（小时计）
                                String teachingWay = JSONUtils.getStr(job, "teachingWay");//上课方式
                                Map<String, Double> rt = getWage2(p.getTeacher_id(), p.getCourse_id(), LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd"), consumption_teacher.getWage(), teachingWay);
                                consumption_teacher.setWage2(rt.get("wage2"));
                                if (rt.get("next") != null) {
                                    consumption_teacher.setNext(rt.get("next"));
                                } else {
                                    consumption_teacher.setNext(0D);
                                }
                                if (rt.get("price") != null) {
                                    consumption_teacher.setNowprice(rt.get("price"));
                                } else {
                                    consumption_teacher.setNowprice(0D);
                                }
                                if (rt.get("currency_idDou") != null) { //添加货别id
                                    Double currency_idDou = rt.get("currency_idDou");
                                    String currency_idString = currency_idDou.toString();
                                    String[] v = currency_idString.split("\\.");

                                    String currency_idStr = currency_idString.split("\\.")[0];
                                    int index = 8 - currency_idStr.length();
                                    String currency_id = "Id_currency";
                                    for (int i = 0; i < index; i++) {
                                        currency_id += "0";
                                    }
                                    currency_id += currency_idStr;
                                    consumption_teacher.setCurrency_id(currency_id);
                                }
                                consumption_teacher.setWageDeduction(0D);// 扣除工资（小时计）
                                consumption_teacher.setIncidental(0D);// 杂费
                                consumption_teacher.setLockStatus(Enums.FALSE_STRING);
                                consumption_teacherService.insert(consumption_teacher);
                            }
                            //inti_student_contract();
                        } catch (Exception e) {
                            arg0.setRollbackOnly();
                            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0028");
                            e.printStackTrace();
                        }
                    }
                });
                AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0027");

                //更新上课状态
                student_zoneService.updateClassStatusById(classSchedule.getStudent_id());
            } else {
                AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0028");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void updateStudentShangke(String id, String flag) {
        Student student = studentService.loadById(id);
        if (student != null && student.getId() != null) {
            if ("2".equals(flag)) {
                student.setStatus("3");
            } else if ("1".equals(flag.trim())) {
                student.setStatus("2");
            } else if ("".equals(flag.trim())) {
                student.setStatus("1");
            } else {
                student.setStatus("4");
            }
            studentService.update(student);
        }
    }

    /**
     * 计算价格
     *
     * @param teacherId
     * @param courseId
     * @param day
     * @param xs
     * @param teachingWay_id
     * @return
     */
    private Map<String, Double> getWage2(String teacherId, String courseId, String day, Double xs, String teachingWay_id) {

        Map<String, Double> rt = new HashMap<String, Double>();
        double d = 0d;
        if (xs == 0) {
            rt.put("wage2", d);
            return rt;
        }

        //获取价格,上下限,时间间隔
        Zidingyi z = new Zidingyi();
        String sqljiage = " ";
        sqljiage += " SELECT DISTINCT hd.currency_id , hd.periodLowerLimit AS min, hd.periodUpperLimit AS max, hd.intervalType AS type, hd.unitPrice AS price ";
        sqljiage += " FROM teacher_course tc  JOIN teacher_hourlyratescheme th on th.id = tc.scheme_id JOIN hourlyratescheme_details hd on th.id = hd.scheme_id JOIN teacher_course_teachingway tct on tct.schemeDetails_id = hd.id ";
        sqljiage += " WHERE tc.course_id = '" + courseId + "' AND tc.teacher_id = '" + teacherId + "' and tct.teachingWay_id ='" + teachingWay_id + "' ";
        sqljiage += " and (  (hd.startDate <= '" + day + "' and hd.endDate> '" + day + "')or (hd.startDate <= '" + day + "' and hd.endDate is null) ) ";
        z.setSql(sqljiage);
        List<Map> li = tongjiService.query(z);
        if (li.isEmpty()) {
            rt.put("wage2", d);
            return rt;
        }
        String type = li.get(0).get("type").toString();
        String startDate = LanDateUtils.getFirstDayOfMonth(LanDateUtils.stringToDate(day), 0);
        String endDate = LanDateUtils.getLastDayOfMonth(LanDateUtils.stringToDate(day), 0);
        if (!"1".equals(type)) {
            startDate = LanDateUtils.format(LanDateUtils.getFirstDayOfWeek(LanDateUtils.stringToDate(day)), "yyyy-MM-dd");
            endDate = LanDateUtils.format(LanDateUtils.getLastDayOfWeek(LanDateUtils.stringToDate(day)), "yyyy-MM-dd");
        }
        String sql1 = "select sum(a.wage) as keshi,sum(a.wageDeduction) as koushi from consumption_teacher a LEFT JOIN classSchedule b on a.classSchedule_id=b.id";
        sql1 += " where a.teacher_id='" + teacherId + "' and b.scheduledDate>='" + startDate + "' and b.scheduledDate<='" + endDate + "'";
        z.setSql(sql1);
        List<Map> li1 = tongjiService.query(z);
        double ks = xs;
        System.out.println("ks1==" + ks);
        if (!li1.isEmpty() && li1.get(0) != null) {
            String keshi = li1.get(0).get("keshi") != null ? li1.get(0).get("keshi").toString() : "0";
            String koushi = li1.get(0).get("koushi") != null ? li1.get(0).get("koushi").toString() : "0";
            if (Double.valueOf(koushi) < Double.valueOf(keshi) + xs) {
                ks = Double.valueOf(keshi) + xs - Double.valueOf(koushi);
            }
        }
        System.out.println("ks2==" + ks);
        User u = userService.loadById(teacherId);
        for (Map m : li) {

            //货币id,返回值为double需要转换下
            if (m.get("currency_id") != null) {
                String currency_id = m.get("currency_id").toString();
                Double currency_idDou = Double.valueOf(currency_id.substring(11));
                rt.put("currency_idDou", currency_idDou);
            }

            //总课时开始计算,总课时为0,课时小于1
            if (ks == xs && Double.valueOf(m.get("min").toString()) > ks) {
                d += Double.valueOf(m.get("price").toString()) * xs;
                rt.put("price", Double.valueOf(m.get("price").toString()));
            }

            if (Double.valueOf(m.get("min").toString()) <= ks && Double.valueOf(m.get("max").toString()) >= ks) {
                if (Double.valueOf(m.get("min").toString()) == 1) {
//					if("1".equals(u.getType())||"3".equals(u.getType())){
//						d=0d;
//					}else{
                    d += Double.valueOf(m.get("price").toString()) * xs;
                    rt.put("price", Double.valueOf(m.get("price").toString()));
//					}
                } else {
                    d += Double.valueOf(m.get("price").toString()) * xs;
                    rt.put("price", Double.valueOf(m.get("price").toString()));
                }
            }
            if (Double.valueOf(m.get("max").toString()) < ks && ks - Double.valueOf(m.get("max").toString()) < xs) {
                d += Double.valueOf(m.get("price").toString()) * (xs - (ks - Double.valueOf(m.get("max").toString())));
                System.out.println("d1==" + d);
                System.out.println("xs1==" + xs);
                xs = ks - Double.valueOf(m.get("max").toString());
                System.out.println("xs2==" + xs);
                rt.put("price", Double.valueOf(m.get("price").toString()));
                rt.put("next", xs);
            }
            System.out.println("d==" + d);
        }
        rt.put("wage2", d);
        System.out.println("wage2==" + rt.get("wage2"));
        System.out.println("price==" + rt.get("price"));
        System.out.println("next==" + rt.get("next"));
        return rt;
    }
//	private Map<String,Double> getWage2(String teacherId,String courseId,String day,Double xs){
//		//teacher_course BUType ,course_id,拿到scheme_id
//		//scheme_id==hourlyratescheme_details中的scheme_id，拿到unitPrice
//		//String BUType="";
//		//String teachingWay_id="";
//
//		Map<String,Double> rt=new HashMap<String,Double>();
//		double d=0d;
//		if(xs==0){
//			rt.put("wage2", d);
//			return rt;
//		}
//		String sql="select a.periodLowerLimit as min,a.periodUpperLimit as max,a.intervalType as type,a.unitPrice as price from teacher_course a ";
//		sql+=" where a.teacher_id='"+teacherId+"' and a.course_id='"+courseId+"' and a.startDate<='"+day+"' and (a.endDate>'"+day+"' or a.endDate is null)";
//
//		//String sql="select b.periodLowerLimit as min,b.periodUpperLimit as max,b.intervalType as type,b.unitPrice as price from hourlyratescheme_details b,teacher_course a, teacher_course_teachingway c ";
//		//sql+=" where a.teacher_id='"+teacherId+"' and a.BUType='"+BUType+"' a.course_id='"+courseId+"' and  (a.endDate>'"+day+"' or a.endDate is null) and a.scheme_id=b.scheme_id and c.scheme_id=b.scheme_id and c.teachingWay_id='"+teachingWay_id+"'";
//
//		Zidingyi z=new Zidingyi();
//		z.setSql(sql);
//		List<Map> li = tongjiService.query(z);
//		if(li.isEmpty()){
//			rt.put("wage2", d);
//			return rt;
//		}
//		String type=li.get(0).get("type").toString();
//		String startDate=LanDateUtils.getFirstDayOfMonth(LanDateUtils.stringToDate(day), 0);
//		String endDate=LanDateUtils.getLastDayOfMonth(LanDateUtils.stringToDate(day), 0);
//		if(!"1".equals(type)){
//			startDate=LanDateUtils.format(LanDateUtils.getFirstDayOfWeek(LanDateUtils.stringToDate(day)), "yyyy-MM-dd");
//			endDate=LanDateUtils.format(LanDateUtils.getLastDayOfWeek(LanDateUtils.stringToDate(day)), "yyyy-MM-dd");
//		}
//		String sql1="select sum(a.wage) as keshi,sum(a.wageDeduction) as koushi from consumption_teacher a LEFT JOIN classSchedule b on a.classSchedule_id=b.id";
//		sql1+=" where a.teacher_id='"+teacherId+"' and b.scheduledDate>='"+startDate+"' and b.scheduledDate<='"+endDate+"'";
//		z.setSql(sql1);
//		List<Map> li1 = tongjiService.query(z);
//		double ks=xs;
//		System.out.println("ks1=="+ks);
//		if(!li1.isEmpty()&&li1.get(0)!=null){
//			String keshi=li1.get(0).get("keshi")!=null?li1.get(0).get("keshi").toString():"0";
//			String koushi=li1.get(0).get("koushi")!=null?li1.get(0).get("koushi").toString():"0";
//			if(Double.valueOf(koushi)<Double.valueOf(keshi)+xs){
//				ks=Double.valueOf(keshi)+xs-Double.valueOf(koushi);
//			}
//		}
//		System.out.println("ks2=="+ks);
//		User u=userService.loadById(teacherId);
//		for(Map m:li){
//			if(Double.valueOf(m.get("min").toString())<=ks&&Double.valueOf(m.get("max").toString())>=ks){
//				if(Double.valueOf(m.get("min").toString())==1){
//					if("1".equals(u.getType())||"3".equals(u.getType())){
//						d=0d;
//					}else{
//						d+=Double.valueOf(m.get("price").toString())*xs;
//						rt.put("price", Double.valueOf(m.get("price").toString()));
//					}
//				}else{
//					d+=Double.valueOf(m.get("price").toString())*xs;
//					rt.put("price", Double.valueOf(m.get("price").toString()));
//				}
//			}
//			if(Double.valueOf(m.get("max").toString())<ks&&ks-Double.valueOf(m.get("max").toString())<xs){
//				d+=Double.valueOf(m.get("price").toString())*(xs-(ks-Double.valueOf(m.get("max").toString())));
//				System.out.println("d1=="+d);
//				System.out.println("xs1=="+xs);
//				xs=ks-Double.valueOf(m.get("max").toString());
//				System.out.println("xs2=="+xs);
//				rt.put("price", Double.valueOf(m.get("price").toString()));
//				rt.put("next", xs);
//			}
//			System.out.println("d=="+d);
//		}
//		rt.put("wage2", d);
//		System.out.println("wage2=="+rt.get("wage2"));
//		System.out.println("price=="+rt.get("price"));
//		System.out.println("next=="+rt.get("next"));
//		return rt;
//	}
//

    /**
     * 批量签到
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/batchSign")
    public String batchSign(ModelMap model, @RequestBody String content,
                            final HttpServletRequest request, final HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONArray ja = jsonObj.getJSONArray("list");
            for (int i = 0; i < ja.size(); i++) {
                JSONObject jo = ja.getJSONObject(i);
                final View_attendanceBook p = new View_attendanceBook();
                JSONObject.toBean(jo, p, jsonConfig);
                final String teachingWay = JSONUtils.getStr(jo, "teachingWay");//上课方式
                if (StringUtils.isNotEmpty(p.getAttendanceStatus())) {//  跳过已签到课表
                    continue;
                }
                ClassSchedule classSchedule = classScheduleService.loadById(p.getClassSchedule_id());
                final String BUType = classSchedule.getBUType();
                final String course_id = classSchedule.getCourse_id();
                if (!"0".equals(classSchedule.getLeaveState()) || !"1".equals(classSchedule.getStatus())) {//跳过请假和不是确认的课程
                    continue;
                }
                if (classSchedule.getScheduledDate().getTime() > (new Date()).getTime()) {//跳过上课日期大于今天的课程
                    continue;
                }
                transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                    @Override
                    protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                        try {
                            // 签到表
                            AttendanceBook attendanceBook = new AttendanceBook();
                            attendanceBook.setId(sequenceManager.generateId("attendanceBook"));
                            attendanceBook.setClassSchedule_id(p.getClassSchedule_id());//课表ID
                            attendanceBook.setAttendanceStatus(AttendanceStatus.NORMAL);//出勤状态
                            attendanceBook.setApprovalStatus(ApprovalStatus.STATUS_NOPROCESS);//审批状态
                            attendanceBook.setApprover(null);//审批人
                            initCreate2(attendanceBook, request);
//							attendanceBook.setContract_id(contract_id);//合同
                            attendanceBook.setLockStatus(Enums.FALSE_STRING);//锁定状态
                            attendanceBookService.insert(attendanceBook);
                            String context = getLogin(request).getUser().getUserName() + "为" + p.getClassSchedule_id() + "正常签到,签到信息：" + attendanceBook.getId();
                            operationLog.setId(sequenceManager.generateId("operationLog"));
                            operationLog.setUser_id(getLogin(request).getUser().getId());
                            operationLog.setCreateTime(new Date());
                            operationLog.setContent(context);
                            operationLogService.insert(operationLog);
                            if (StringUtils.equals(CourseType.COURSETYPE_1, p.getCourseType())
                                    || StringUtils.equals(CourseType.COURSETYPE_2, p.getCourseType())) {
                                // 学生耗课信息
                                Consumption_student consumption_student = new Consumption_student();
                                consumption_student.setId(sequenceManager.generateId("consumption_student"));
                                consumption_student.setStudent_id(p.getStudent_id());//学生
                                consumption_student.setClassSchedule_id(p.getClassSchedule_id());//课表
                                consumption_student.setConsumedClass(Double.valueOf(p.getTime()));//消耗课时数
                                consumption_student.setGivenClass(0D);//赠送课时
                                consumption_student.setLockStatus(Enums.FALSE_STRING);//锁定状态
                                consumption_studentService.insert(consumption_student);

                                // 获取学生合同，生成耗课合同信息
//								List<Contract> contracts = contractService.getContractByClass(consumption_student.getStudent_id(), consumption_student.getConsumedClass());
//								for(Contract item : contracts){
//									Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
//									consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
//									consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
//									consumption_student_contract.setConsumedClass(item.getCurrentConsumedClass());
//									if(StringUtils.isNotEmpty(item.getId())){
//										consumption_student_contract.setContract_id(item.getId());//合同ID
//										consumption_student_contract.setUnitPrice(item.getTuitionFee()/item.getAmount());// 单价
//									}
//
//									consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
//									consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
//									consumption_student_contractService.insert(consumption_student_contract);
//									//更新合同已消耗课时数
//									if(StringUtils.isNotEmpty(item.getId())){
//										Contract contract = new Contract();
//										contract.setId(item.getId());
//										contract.setConsumedClass(item.getConsumedClass());
//										if(item.getAmount()>contract.getConsumedClass()/2){
//											contract.setStatus(ContractStatus.STAUS_2);
//										}else{
//											contract.setStatus(ContractStatus.STAUS_3);
//										}
//										contractService.update(contract);
//										updateStudentShangke(p.getStudent_id(),contract.getStatus());
//									}else{
//										updateStudentShangke(p.getStudent_id(),"");
//									}
//								}
                                List<String> contractType2List = new ArrayList<String>();
                                if (Enums.OFFlINE.equals(BUType)) {
                                    contractType2List.add(Enums.OFFlINE);
                                    contractType2List.add(Enums.ContractType2.HYBRID);
                                }
                                if (Enums.ONLINE.equals(BUType)) {
                                    contractType2List.add(Enums.ONLINE);
                                    contractType2List.add(Enums.ContractType2.HYBRID);
                                }
                                if (Enums.SPE.equals(BUType)) {
                                    contractType2List.add(Enums.ContractType2.SPE);
                                }
                                if (Enums.ONLINETE.equals(BUType)) {
                                    contractType2List.add(Enums.ContractType2.OBT);
                                }
                                List<Contract> contracts = new ArrayList<Contract>();
                                if (contractType2List.size() > 0) {
                                    if (Enums.ONLINETE.equals(BUType)) {
                                        //obt, get course id
                                        contracts = contractService.getContractByClass(consumption_student.getStudent_id(), contractType2List, course_id);
                                    } else {
                                        contracts = contractService.getContractByClass(consumption_student.getStudent_id(), contractType2List);
                                    }
                                } else {
                                    contracts = contractService.getContractByClass(consumption_student.getStudent_id());
                                }
                                //List<Contract> contracts = contractService.getContractByClass(consumption_student.getStudent_id());
                                double consumedClass = consumption_student.getConsumedClass();
                                for (Contract item : contracts) {
                                    if (item.getAmount() * 2 - item.getConsumedClass() >= consumedClass) {
                                        Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                        consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                        consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                        consumption_student_contract.setContract_id(item.getId());//合同ID
                                        consumption_student_contract.setConsumedClass(consumedClass);
                                        consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                        consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                        consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                        consumption_student_contractService.insert(consumption_student_contract);
                                        if (item.getAmount() * 2 - item.getConsumedClass() > consumedClass) {
                                            item.setStatus(ContractStatus.STAUS_2);
                                        } else {
                                            item.setStatus(ContractStatus.STAUS_3);
                                        }
                                        item.setConsumedClass(item.getConsumedClass() + consumedClass);
                                        contractService.update(item);
                                        updateStudentShangke(p.getStudent_id(), item.getStatus());
                                        consumedClass = 0;
                                        break;
                                    } else {
                                        double consumedClass1 = item.getAmount() * 2 - item.getConsumedClass();
                                        Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                        consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                        consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                        consumption_student_contract.setContract_id(item.getId());//合同ID
                                        consumption_student_contract.setConsumedClass(consumedClass1);
                                        consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                        consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                        consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                        consumption_student_contractService.insert(consumption_student_contract);
                                        item.setStatus(ContractStatus.STAUS_3);
                                        item.setConsumedClass(item.getAmount() * 2);
                                        contractService.update(item);
                                        updateStudentShangke(p.getStudent_id(), item.getStatus());
                                        consumedClass = consumedClass - consumedClass1;
                                    }
//
//									//更新合同已消耗课时数
//									if(StringUtils.isNotEmpty(item.getId())){
//										Contract contract = new Contract();
//										contract.setId(item.getId());
//										contract.setConsumedClass(item.getConsumedClass());
//										if(item.getAmount()>contract.getConsumedClass()/2){
//											contract.setStatus(ContractStatus.STAUS_2);
//										}else{
//											contract.setStatus(ContractStatus.STAUS_3);
//										}
//										contractService.update(contract);
//										updateStudentShangke(p.getStudent_id(),contract.getStatus());
//									}else{
//										updateStudentShangke(p.getStudent_id(),"");
//									}
                                }
                                if (consumedClass > 0) {
                                    Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                    consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                    consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                    consumption_student_contract.setConsumedClass(consumedClass);
                                    consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                    consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                    consumption_student_contractService.insert(consumption_student_contract);
                                    updateStudentShangke(p.getStudent_id(), "");
                                }

                                //教师耗课信息
                                Consumption_teacher consumption_teacher = new Consumption_teacher();
                                consumption_teacher.setId(sequenceManager.generateId("consumption_teacher"));
                                consumption_teacher.setTeacher_id(p.getTeacher_id());// 教师ID
                                consumption_teacher.setClassSchedule_id(p.getClassSchedule_id());// 课表
                                consumption_teacher.setWage(Double.valueOf(p.getTime()));// 工资（小时计）
                                Map<String, Double> rt = getWage2(p.getTeacher_id(), p.getCourse_id(), LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd"), consumption_teacher.getWage(), teachingWay);
                                consumption_teacher.setWage2(rt.get("wage2"));
                                if (rt.get("next") != null) {
                                    consumption_teacher.setNext(rt.get("next"));
                                } else {
                                    consumption_teacher.setNext(0D);
                                }
                                if (rt.get("price") != null) {
                                    consumption_teacher.setNowprice(rt.get("price"));
                                } else {
                                    consumption_teacher.setNowprice(0D);
                                }
                                if (rt.get("currency_idDou") != null) { //添加货别id
                                    Double currency_idDou = rt.get("currency_idDou");
                                    String currency_idString = currency_idDou.toString();
                                    String[] v = currency_idString.split("\\.");

                                    String currency_idStr = currency_idString.split("\\.")[0];
                                    int index = 8 - currency_idStr.length();
                                    String currency_id = "Id_currency";
                                    for (int i = 0; i < index; i++) {
                                        currency_id += "0";
                                    }
                                    currency_id += currency_idStr;
                                    consumption_teacher.setCurrency_id(currency_id);
                                }
                                consumption_teacher.setWageDeduction(0D);// 扣除工资（小时计）
                                consumption_teacher.setIncidental(0D);// 杂费
                                consumption_teacher.setLockStatus(Enums.FALSE_STRING);
                                consumption_teacherService.insert(consumption_teacher);
                            } else if (StringUtils.equals(CourseType.COURSETYPE_3, p.getCourseType())) {//不发工资，不扣学时

                            } else if (StringUtils.equals(CourseType.COURSETYPE_4, p.getCourseType())) {//发工资，不扣学时
                                //教师耗课信息
                                Consumption_teacher consumption_teacher = new Consumption_teacher();
                                consumption_teacher.setId(sequenceManager.generateId("consumption_teacher"));
                                consumption_teacher.setTeacher_id(p.getTeacher_id());// 教师ID
                                consumption_teacher.setClassSchedule_id(p.getClassSchedule_id());// 课表
                                consumption_teacher.setWage(Double.valueOf(p.getTime()));// 工资（小时计）
                                Map<String, Double> rt = getWage2(p.getTeacher_id(), p.getCourse_id(), LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd"), consumption_teacher.getWage(), teachingWay);
                                consumption_teacher.setWage2(rt.get("wage2"));
                                if (rt.get("next") != null) {
                                    consumption_teacher.setNext(rt.get("next"));
                                } else {
                                    consumption_teacher.setNext(0D);
                                }
                                if (rt.get("price") != null) {
                                    consumption_teacher.setNowprice(rt.get("price"));
                                } else {
                                    consumption_teacher.setNowprice(0D);
                                }
                                if (rt.get("currency_idDou") != null) { //添加货别id
                                    Double currency_idDou = rt.get("currency_idDou");
                                    String currency_idString = currency_idDou.toString();
                                    String[] v = currency_idString.split("\\.");

                                    String currency_idStr = currency_idString.split("\\.")[0];
                                    int index = 8 - currency_idStr.length();
                                    String currency_id = "Id_currency";
                                    for (int i = 0; i < index; i++) {
                                        currency_id += "0";
                                    }
                                    currency_id += currency_idStr;
                                    consumption_teacher.setCurrency_id(currency_id);
                                }
                                consumption_teacher.setWageDeduction(0D);// 扣除工资（小时计）
                                consumption_teacher.setIncidental(0D);// 杂费
                                consumption_teacher.setLockStatus(Enums.FALSE_STRING);
                                consumption_teacherService.insert(consumption_teacher);
                            }
                        } catch (Exception e) {
                            arg0.setRollbackOnly();
                            e.printStackTrace();
                        }
                    }
                });
            }
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0029");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 异常签到
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/abnormalSign")
    public String abnormalSign(ModelMap model, @RequestBody String content,
                               final HttpServletRequest request, final HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            final JSONObject job = jsonObj.getJSONObject("item");

            JSONObject view_attendanceBook = jsonObj.getJSONObject("view_attendanceBook");
            final View_attendanceBook p = new View_attendanceBook();
            final String teachingWay = JSONUtils.getStr(view_attendanceBook, "teachingWay");//上课方式
            final String currency_id0 = JSONUtils.getStr(job, "currency_id");//货币id,课时为0
            JSONObject.toBean(view_attendanceBook, p, jsonConfig);
            final ClassSchedule classSchedule = classScheduleService.loadById(p.getClassSchedule_id());
            final String BUType = classSchedule.getBUType();//课程的类型
            final String course_id = classSchedule.getCourse_id();
            final List listBUType = new ArrayList();
            listBUType.add(BUType);
            //LeaveState：0未请假，1学生请假，2老师请假 Status课程状态，0初始化，1已确认，2已删除
            if ("0".equals(classSchedule.getLeaveState()) && "1".equals(classSchedule.getStatus())) {
                transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                    @Override
                    protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                        try {
                            double consumedClass = job.getDouble("consumedClass");// 学生耗课
                            double givenClass = job.getDouble("givenClass");// 学生获赠
                            double wage = job.getDouble("wage");// 老师工资
                            String teacherType = JSONUtils.getStr(job, "teacherType");//教师类型，兼职，全职
                            System.out.println(teacherType);
                            double wageDeduction = 0;
                            if (StringUtils.equals(TeacherType.FULL_TIME, teacherType)) {
                                wageDeduction = job.getDouble("wageDeduction2");// 倒扣老师工资
                            } else {
                                wageDeduction = job.getDouble("wageDeduction");// 倒扣老师工资
                            }


                            boolean hasIncidental = job.getBoolean("hasIncidental");//
                            boolean isChanged = job.getBoolean("isChanged");

                            String reason = JSONUtils.getStr(job, "reason"); // 修改原因

                            double incidental = job.getDouble("incidental");// 杂费
                            String description = JSONUtils.getStr(job, "description"); // 杂费摘要
                            // 签到表
                            AttendanceBook attendanceBook = new AttendanceBook();
                            attendanceBook.setId(sequenceManager.generateId("attendanceBook"));
                            attendanceBook.setClassSchedule_id(p.getClassSchedule_id());//课表ID
                            attendanceBook.setAttendanceStatus(JSONUtils.getStr(job, "attendanceStatus"));//出勤状态
                            attendanceBook.setReason(reason);// 修改原因
                            if (isChanged) {
                                attendanceBook.setApprovalStatus(ApprovalStatus.STATUS_NEW);// 审批状态，待审批
                            } else {
                                attendanceBook.setApprovalStatus(ApprovalStatus.STATUS_NOPROCESS);//审批状态，无需审批
                            }
                            attendanceBook.setApprover(null);//审批人
                            initCreate2(attendanceBook, request);
                            attendanceBook.setLockStatus(Enums.FALSE_STRING);//锁定状态

                            if (StringUtils.equals(CourseType.COURSETYPE_1, p.getCourseType())) {
                                // 学生耗课信息
                                Consumption_student consumption_student = new Consumption_student();
                                consumption_student.setId(sequenceManager.generateId("consumption_student"));
                                consumption_student.setStudent_id(p.getStudent_id());//学生
                                consumption_student.setClassSchedule_id(p.getClassSchedule_id());//课表
                                consumption_student.setConsumedClass(consumedClass);//消耗课时数
                                consumption_student.setGivenClass(givenClass);//赠送课时
                                consumption_student.setLockStatus(Enums.FALSE_STRING);//锁定状态
                                consumption_studentService.insert(consumption_student);
                                // 获取学生合同，生成耗课合同信息
                                if (consumedClass > 0) {
                                    System.out.println("consumedClass1---->" + consumedClass);
//									List<Contract> contracts = contractService.getContractByClass(consumption_student.getStudent_id(), consumption_student.getConsumedClass());
//									for(Contract item : contracts){
//										Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
//										consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
//										consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
//										consumption_student_contract.setConsumedClass(item.getCurrentConsumedClass());
//										if(StringUtils.isNotEmpty(item.getId())){
//											consumption_student_contract.setContract_id(item.getId());//合同ID
//											consumption_student_contract.setUnitPrice(item.getTuitionFee()/item.getAmount());// 单价
//										}
//
//										consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
//										consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
//										consumption_student_contractService.insert(consumption_student_contract);
//										//更新合同已消耗课时数
//										if(StringUtils.isNotEmpty(item.getId())){
//											Contract contract = new Contract();
//											contract.setId(item.getId());
//											contract.setConsumedClass(item.getConsumedClass());
//											if(item.getAmount()>contract.getConsumedClass()/2){
//												contract.setStatus(ContractStatus.STAUS_2);
//											}else{
//												contract.setStatus(ContractStatus.STAUS_3);
//											}
//											contractService.update(contract);
//											updateStudentShangke(p.getStudent_id(),contract.getStatus());
//										}else{
//											updateStudentShangke(p.getStudent_id(),"");
//										}
//									}
                                    List<String> contractType2List = new ArrayList<String>();
                                    if (Enums.OFFlINE.equals(BUType)) {
                                        contractType2List.add(Enums.OFFlINE);
                                        contractType2List.add(Enums.ContractType2.HYBRID);
                                    }
                                    if (Enums.ONLINE.equals(BUType)) {
                                        contractType2List.add(Enums.ONLINE);
                                        contractType2List.add(Enums.ContractType2.HYBRID);
                                    }
                                    if (Enums.SPE.equals(BUType)) {
                                        contractType2List.add(Enums.ContractType2.SPE);
                                    }
                                    if (Enums.ONLINETE.equals(BUType)) {
                                        contractType2List.add(Enums.ContractType2.OBT);
                                    }
                                    List<Contract> contracts = new ArrayList<Contract>();//学生相同状态所有未消耗完的合同
                                    if (contractType2List.size() > 0) {
                                        if (Enums.ONLINETE.equals(BUType)) {
                                            //obt ,get course id
                                            contracts = contractService.getContractByClass(consumption_student.getStudent_id(), contractType2List, course_id);
                                        } else {
                                            contracts = contractService.getContractByClass(consumption_student.getStudent_id(), contractType2List);
                                        }
                                    } else {
                                        contracts = contractService.getContractByClass(consumption_student.getStudent_id());
                                    }
                                    //List<Contract> contracts = contractService.getContractByClass(consumption_student.getStudent_id());
                                    for (Contract item : contracts) {
                                        if (item.getAmount() * 2 - item.getConsumedClass() >= consumedClass) {
                                            Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                            consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                            consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                            consumption_student_contract.setContract_id(item.getId());//合同ID
                                            consumption_student_contract.setConsumedClass(consumedClass);
                                            consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                            consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                            consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                            consumption_student_contractService.insert(consumption_student_contract);
                                            if (item.getAmount() * 2 - item.getConsumedClass() > consumedClass) {
                                                item.setStatus(ContractStatus.STAUS_2);
                                            } else {
                                                item.setStatus(ContractStatus.STAUS_3);
                                            }
                                            item.setConsumedClass(item.getConsumedClass() + consumedClass);
                                            contractService.update(item);
                                            updateStudentShangke(p.getStudent_id(), item.getStatus());
                                            consumedClass = 0;
                                            System.out.println("consumedClass2---->" + consumedClass);
                                            break;
                                        } else {
                                            double consumedClass1 = item.getAmount() * 2 - item.getConsumedClass();
                                            Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                            consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                            consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                            consumption_student_contract.setContract_id(item.getId());//合同ID
                                            consumption_student_contract.setConsumedClass(consumedClass1);
                                            consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                            consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                            consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                            consumption_student_contractService.insert(consumption_student_contract);
                                            item.setStatus(ContractStatus.STAUS_3);
                                            item.setConsumedClass(item.getAmount() * 2);
                                            contractService.update(item);
                                            updateStudentShangke(p.getStudent_id(), item.getStatus());
                                            System.out.println("consumedClass311---->" + consumedClass);
                                            System.out.println("consumedClass312---->" + consumedClass1);
                                            consumedClass = consumedClass - consumedClass1;
                                            System.out.println("consumedClass32---->" + consumedClass);
                                        }
//
//										//更新合同已消耗课时数
//										if(StringUtils.isNotEmpty(item.getId())){
//											Contract contract = new Contract();
//											contract.setId(item.getId());
//											contract.setConsumedClass(item.getConsumedClass());
//											if(item.getAmount()>contract.getConsumedClass()/2){
//												contract.setStatus(ContractStatus.STAUS_2);
//											}else{
//												contract.setStatus(ContractStatus.STAUS_3);
//											}
//											contractService.update(contract);
//											updateStudentShangke(p.getStudent_id(),contract.getStatus());
//										}else{
//											updateStudentShangke(p.getStudent_id(),"");
//										}
                                    }
                                    if (consumedClass > 0) {
                                        Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                        consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                        consumption_student_contract.setConsumption_student_id(consumption_student.getId());// 学生耗课ID
                                        consumption_student_contract.setConsumedClass(consumedClass);
                                        consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                        consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                        consumption_student_contractService.insert(consumption_student_contract);
                                        updateStudentShangke(p.getStudent_id(), "");
                                    }

                                }
                                // 无需审批
                                if (StringUtils.equals(ApprovalStatus.STATUS_NOPROCESS, attendanceBook.getApprovalStatus())) {
                                    /* 生成虚拟合同 */
                                    if (consumption_student.getGivenClass() != null && consumption_student.getGivenClass() > 0) {
                                        //取有钱合同的时间(添加合同类别),
                                        List<Contract> useableContracts = contractService.getUseableContract(p.getStudent_id(), false, listBUType);
                                        /* 合同 */
                                        Contract contract = new Contract();
                                        contract.setId(sequenceManager.generateId("contract"));
                                        attendanceBook.setContract_id(contract.getId());
                                        // 合同开始日期
                                        if (CollectionUtils.isEmpty(useableContracts)) {
                                            //contract.setStartDate(LanDateUtils.convertStringToDate("yyyy-MM-dd", "9999-12-31"));
                                            contract.setStartDate(new Date());
                                        } else {
                                            contract.setStartDate(LanDateUtils.getNextDate_month(
                                                    useableContracts.get(useableContracts.size() - 1).getStartDate(), 1));
                                        }
                                        contract.setEndDate(null);//
                                        // 赠送类型
                                        if (StringUtils.equals(AttendanceStatus.TEACHER_LATE, p.getAttendanceStatus())) {
                                            contract.setPersentType(PersentType.TE_TEACHER_LATE);// 教师迟到
                                        } else if (StringUtils.equals(AttendanceStatus.TEACHER_ABSENTEEISM, p.getAttendanceStatus())) {
                                            contract.setPersentType(PersentType.TE_TEACHER_ABSENTEEISM);// 教师缺勤
                                        } else {
                                            contract.setPersentType(PersentType.TE_OTHER);// 其他
                                        }
                                        contract.setStudentId(p.getStudent_id());// 学员
                                        contract.setUnits(consumption_student.getGivenClass() / 2);// 单元数
                                        contract.setAmount(contract.getUnits());// 数量
                                        contract.setTuitionFee(0D);// 学费
                                        contract.setProductType(ProductType.ONE_TO_ONE); // 产品类型
                                        contract.setConsumedClass(0D);// 已消耗课时数
                                        if (Enums.SPE.equals(BUType)) {
                                            contract.setContractType2(Enums.ContractType2.SPE);
                                        } else if (Enums.ONLINETE.equals(BUType)) {
                                            contract.setContractType2(Enums.ContractType2.OBT);
                                            //obt course id (zoho course id)
                                            WhereCondition wc = new WhereCondition();
                                            wc.andEquals("course_id", course_id);
                                            wc.andEquals("studentId", p.getStudent_id());
                                            wc.andEquals("contractType2", Enums.ContractType2.OBT);
                                            List<Contractview> contracts = contractviewService.query(wc);
                                            if(contracts.size() > 0) {
                                                contract.setCourse_id(contracts.get(0).getObt_course_id());
                                            }
                                        } else {
                                            contract.setContractType2(BUType);
                                        }
                                        contract.setCompensateStudent(classSchedule.getId());//补偿合同对应的课程id
                                        if (StringUtils.equals(AttendanceStatus.TEACHER_LATE, attendanceBook.getAttendanceStatus())) {
                                            contract.setProductName(ProductName.TE_TEACHER_LATE);
                                        } else if (StringUtils.equals(AttendanceStatus.TEACHER_ABSENTEEISM, attendanceBook.getAttendanceStatus())) {
                                            contract.setProductName(ProductName.TE_TEACHER_ABSENTEEISM);
                                        } else {
                                            contract.setProductName(ProductName.TE_OTHER);
                                        }

                                        initCreate2(contract, request);
                                        contractService.insert(contract);
                                    }
                                }

                                //教师耗课信息
                                Consumption_teacher consumption_teacher = new Consumption_teacher();
                                consumption_teacher.setId(sequenceManager.generateId("consumption_teacher"));
                                consumption_teacher.setTeacher_id(p.getTeacher_id());// 教师ID
                                consumption_teacher.setClassSchedule_id(p.getClassSchedule_id());// 课表
                                consumption_teacher.setWage(wage);// 工资（小时计）

                                if (StringUtils.equals(TeacherType.FULL_TIME, teacherType)) {
                                    consumption_teacher.setWageDeduction2(wageDeduction);// 扣除工资（元）
                                    consumption_teacher.setWageDeduction(0d);// 扣除工资（小时计）
                                } else {
                                    consumption_teacher.setWageDeduction(wageDeduction);// 扣除工资（小时计）
                                    consumption_teacher.setWageDeduction2(0d);// 扣除工资（元）
                                }

                                if (hasIncidental) {
                                    consumption_teacher.setIncidental(incidental);// 杂费
                                }
                                double ks = 0d;
                                boolean flag = false;
                                if (consumption_teacher.getWage() - consumption_teacher.getWageDeduction() > 0) {
                                    ks = consumption_teacher.getWage() - consumption_teacher.getWageDeduction();
                                } else {
                                    flag = true;
                                    ks = -1 * (consumption_teacher.getWage() - consumption_teacher.getWageDeduction());
                                }
                                Map<String, Double> rt = getWage2(p.getTeacher_id(), p.getCourse_id(), LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd"), ks, teachingWay);
                                if (flag) {
                                    consumption_teacher.setWage2(-1 * rt.get("wage2"));
                                } else {
                                    consumption_teacher.setWage2(rt.get("wage2"));
                                }
                                if (rt.get("next") != null) {
                                    consumption_teacher.setNext(rt.get("next"));
                                } else {
                                    consumption_teacher.setNext(0D);
                                }
                                if (rt.get("price") != null) {
                                    consumption_teacher.setNowprice(rt.get("price"));
                                } else {
                                    consumption_teacher.setNowprice(0D);
                                }
                                if (rt.get("currency_idDou") != null) { //添加货别id
                                    Double currency_idDou = rt.get("currency_idDou");
                                    String currency_idString = currency_idDou.toString();
                                    String[] v = currency_idString.split("\\.");

                                    String currency_idStr = currency_idString.split("\\.")[0];
                                    int index = 8 - currency_idStr.length();
                                    String currency_id = "Id_currency";
                                    for (int i = 0; i < index; i++) {
                                        currency_id += "0";
                                    }
                                    currency_id += currency_idStr;
                                    consumption_teacher.setCurrency_id(currency_id);
                                } else {
                                    consumption_teacher.setCurrency_id(currency_id0);
                                }
                                consumption_teacher.setDescription(description);// 杂费摘要
                                consumption_teacher.setLockStatus(Enums.FALSE_STRING);
                                consumption_teacherService.insert(consumption_teacher);
                            } else if (StringUtils.equals(CourseType.COURSETYPE_3, p.getCourseType())) {//不发工资，不扣学时 TODO

                            } else if (StringUtils.equals(CourseType.COURSETYPE_4, p.getCourseType())) {//发工资，不扣学时 TODO

                            }
                            attendanceBookService.insert(attendanceBook);
                            //release classschedule by changing status to 'deleted'
                            //老师缺勤 && 无需审批 || 审批通过
                            if (StringUtils.equals(AttendanceStatus.TEACHER_ABSENTEEISM, attendanceBook.getAttendanceStatus()) &&
                                    ((StringUtils.equals(ApprovalStatus.STATUS_NOPROCESS, attendanceBook.getApprovalStatus())) ||
                                            (StringUtils.equals(ApprovalStatus.STATUS_END, attendanceBook.getApprovalStatus())))) {
                                classSchedule.setStatus(ClassScheduleStatus.DELETED);
                                classSchedule.setDeleteFlag("2");
                                classScheduleService.update(classSchedule);
                                //delete teacher freetime_code
                                WhereCondition wc = new WhereCondition();
                                int startMin = Integer.valueOf(classSchedule.getStartTime().split(":")[1]);
                                int endMin = Integer.valueOf(classSchedule.getEndTime().split(":")[1]);
                                String startTime = classSchedule.getStartTime();
                                String endTime = classSchedule.getEndTime();
                                boolean s_Clock = true;
                                boolean e_Clock = true;
                                if (startMin != 0) {
                                    //非整点开始
                                    startTime = String.format("%02d", Integer.valueOf(classSchedule.getStartTime().split(":")[0])) + ":00";
                                    s_Clock = false;
                                }

                                if (endMin != 0) {
                                    endTime = String.format("%02d", Integer.valueOf(classSchedule.getEndTime().split(":")[0]) + 1) + ":00";
                                    //非整点结束
                                    e_Clock = false;
                                }

                                wc.andEquals("teacher_id", classSchedule.getTeacher_id());
                                wc.andEquals("freeDate", classSchedule.getScheduledDate());
                                wc.andGreaterEquals("startTime", startTime);
                                wc.andLessEquals("endTime", endTime);
                                wc.setOrderBy("startTime");

                                List<Teacher_freeTime> list = teacher_freeTimeService.query(wc);

                                if (CollectionUtils.isNotEmpty(list)) {
                                    String freeTimeCode = "";
                                    int count = 1;

                                    if (s_Clock && e_Clock) { //整点开始结束 teacher freetime全部删除
                                        teacher_freeTimeService.deleteByCondition(wc);
                                    } else if (!s_Clock && e_Clock) { //非整点开始但整点结束
                                        for (Teacher_freeTime t_ft : list) {
                                            Teacher_freeTime t = teacher_freeTimeService.loadById(t_ft.getId().toString());
                                            if (count == 1) { //第一条
                                                if (startMin == 15) {
                                                    freeTimeCode = "1";
                                                } else if (startMin == 30) {
                                                    freeTimeCode = "12";
                                                } else if (startMin == 45) {
                                                    freeTimeCode = "123";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                teacher_freeTimeService.update(t);
                                            } else {
                                                teacher_freeTimeService.delete(t_ft.getId());
                                            }
                                            count += 1;
                                        }
                                    } else if (s_Clock && !e_Clock) { //整点开始但非整点结束
                                        for (Teacher_freeTime t_ft : list) {
                                            Teacher_freeTime t = teacher_freeTimeService.loadById(t_ft.getId().toString());
                                            if (count == list.size()) {
                                                if (endMin == 15) {
                                                    freeTimeCode = "234";
                                                } else if (endMin == 30) {
                                                    freeTimeCode = "34";
                                                } else if (endMin == 45) {
                                                    freeTimeCode = "4";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                teacher_freeTimeService.update(t);
                                            } else {
                                                teacher_freeTimeService.delete(t_ft.getId());
                                            }
                                            count += 1;
                                        }
                                    } else if (!s_Clock && !e_Clock) { //非整点开始非整点结束
                                        for (Teacher_freeTime t_ft : list) {
                                            Teacher_freeTime t = teacher_freeTimeService.loadById(t_ft.getId().toString());
                                            if (count == 1) { //第一条
                                                if (startMin == 15) {
                                                    freeTimeCode = "1";
                                                } else if (startMin == 30) {
                                                    freeTimeCode = "12";
                                                } else if (startMin == 45) {
                                                    freeTimeCode = "123";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                teacher_freeTimeService.update(t);
                                            } else if (count == list.size()) { //最后一条
                                                if (endMin == 15) {
                                                    freeTimeCode = "234";
                                                } else if (endMin == 30) {
                                                    freeTimeCode = "34";
                                                } else if (endMin == 45) {
                                                    freeTimeCode = "4";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                teacher_freeTimeService.update(t);
                                            } else { //中间各条
                                                teacher_freeTimeService.delete(t_ft.getId());
                                            }
                                            count += 1;
                                        }
                                    }
                                }
                            } else if (StringUtils.equals(AttendanceStatus.STUDENT_ABSENTEEISM, attendanceBook.getAttendanceStatus()) &&
                                    ((StringUtils.equals(ApprovalStatus.STATUS_NOPROCESS, attendanceBook.getApprovalStatus())) ||
                                            (StringUtils.equals(ApprovalStatus.STATUS_END, attendanceBook.getApprovalStatus())))) {
                                classSchedule.setStatus(ClassScheduleStatus.DELETED);
                                classSchedule.setDeleteFlag("2");
                                classScheduleService.update(classSchedule);
                                //delete student freetime_code
                                WhereCondition wc = new WhereCondition();
                                int startMin = Integer.valueOf(classSchedule.getStartTime().split(":")[1]);
                                int endMin = Integer.valueOf(classSchedule.getEndTime().split(":")[1]);
                                String startTime = classSchedule.getStartTime();
                                String endTime = classSchedule.getEndTime();
                                boolean s_Clock = true;
                                boolean e_Clock = true;
                                if (startMin != 0) {
                                    //非整点开始
                                    startTime = String.format("%02d", Integer.valueOf(classSchedule.getStartTime().split(":")[0])) + ":00";
                                    s_Clock = false;
                                }

                                if (endMin != 0) {
                                    endTime = String.format("%02d", Integer.valueOf(classSchedule.getEndTime().split(":")[0]) + 1) + ":00";
                                    //非整点结束
                                    e_Clock = false;
                                }

                                wc.andEquals("student_id", classSchedule.getStudent_id());
                                wc.andEquals("freeDate", classSchedule.getScheduledDate());
                                wc.andGreaterEquals("startTime", startTime);
                                wc.andLessEquals("endTime", endTime);
                                wc.setOrderBy("startTime");

                                List<Student_freeTime> list = student_freeTimeService.query(wc);

                                if (CollectionUtils.isNotEmpty(list)) {
                                    String freeTimeCode = "";
                                    int count = 1;

                                    if (s_Clock && e_Clock) { //整点开始结束 teacher freetime全部删除
                                        student_freeTimeService.deleteByCondition(wc);
                                    } else if (!s_Clock && e_Clock) { //非整点开始但整点结束
                                        for (Student_freeTime t_ft : list) {
                                            Student_freeTime t = student_freeTimeService.loadById(t_ft.getId().toString());
                                            if (count == 1) { //第一条
                                                if (startMin == 15) {
                                                    freeTimeCode = "1";
                                                } else if (startMin == 30) {
                                                    freeTimeCode = "12";
                                                } else if (startMin == 45) {
                                                    freeTimeCode = "123";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                student_freeTimeService.update(t);
                                            } else {
                                                student_freeTimeService.delete(t_ft.getId());
                                            }
                                            count += 1;
                                        }
                                    } else if (s_Clock && !e_Clock) { //整点开始但非整点结束
                                        for (Student_freeTime t_ft : list) {
                                            Student_freeTime t = student_freeTimeService.loadById(t_ft.getId().toString());
                                            if (count == list.size()) {
                                                if (endMin == 15) {
                                                    freeTimeCode = "234";
                                                } else if (endMin == 30) {
                                                    freeTimeCode = "34";
                                                } else if (endMin == 45) {
                                                    freeTimeCode = "4";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                student_freeTimeService.update(t);
                                            } else {
                                                student_freeTimeService.delete(t_ft.getId());
                                            }
                                            count += 1;
                                        }
                                    } else if (!s_Clock && !e_Clock) { //非整点开始非整点结束
                                        for (Student_freeTime t_ft : list) {
                                            Student_freeTime t = student_freeTimeService.loadById(t_ft.getId().toString());
                                            if (count == 1) { //第一条
                                                if (startMin == 15) {
                                                    freeTimeCode = "1";
                                                } else if (startMin == 30) {
                                                    freeTimeCode = "12";
                                                } else if (startMin == 45) {
                                                    freeTimeCode = "123";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                student_freeTimeService.update(t);
                                            } else if (count == list.size()) { //最后一条
                                                if (endMin == 15) {
                                                    freeTimeCode = "234";
                                                } else if (endMin == 30) {
                                                    freeTimeCode = "34";
                                                } else if (endMin == 45) {
                                                    freeTimeCode = "4";
                                                }
                                                t.setFreeTimeCode(freeTimeCode);
                                                student_freeTimeService.update(t);
                                            } else { //中间各条
                                                student_freeTimeService.delete(t_ft.getId());
                                            }
                                            count += 1;
                                        }
                                    }
                                }

                            }
                            String context = getLogin(request).getUser().getUserName() + "为" + p.getClassSchedule_id() + "异常签到，签到信息" + attendanceBook.getId();
                            operationLog.setId(sequenceManager.generateId("operationLog"));
                            operationLog.setUser_id(getLogin(request).getUser().getId());
                            operationLog.setCreateTime(new Date());
                            operationLog.setContent(context);
                            operationLogService.insert(operationLog);
                        } catch (Exception e) {
                            arg0.setRollbackOnly();
                            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0028");
                            e.printStackTrace();
                        }
                    }
                });
                AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0027");

                //更新上课状态
                student_zoneService.updateClassStatusById(classSchedule.getStudent_id());
            } else {
                AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0028");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/getBaseData")
    public String getBaseData(ModelMap model, @RequestBody String content,
                              HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String teacher_id = JSONUtils.getStr(jsonObj, "teacher_id");//教师ID
            String course_id = JSONUtils.getStr(jsonObj, "course_id");
            String BUType = JSONUtils.getStr(jsonObj, "BUType");
            String scheduledDateStr = JSONUtils.getStr(jsonObj, "scheduledDate");//上课日期
            User teacher = userService.loadById(teacher_id);
            Date scheduledDate = LanDateUtils.convertStringToDate("yyyy-MM-dd", scheduledDateStr);
            model.put("teacherType", teacher.getType());//教师性质
            if (StringUtils.equals(TeacherType.FULL_TIME, teacher.getType())) {// 全职
                WhereCondition wc = new WhereCondition();
                wc.andEquals("consumption_teacher.teacher_id", teacher_id);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(scheduledDate);
                wc.andGreaterEquals("scheduledDate", LanDateUtils.getFirstDayOfMonth(scheduledDate, 0));
                wc.andLessEquals("scheduledDate", LanDateUtils.getLastDayOfMonth(scheduledDate, 0));
                Double consumedClass_teacher = consumption_teacherService.getConsumedClass(wc);
//				wc = new WhereCondition();
//				wc.andEquals("teacher_id", teacher_id);
//				wc.andEquals("course_id", course_id);
//				wc.andEquals("BUType", BUType);
//				//wc.andEquals("isActivated", "1");
//				List<View_teacher_course> ListTc=view_teacher_courseService.query(wc);
//				double d=0d;
//				//PeriodLowerLimit移植到了新的表中从新的表中取
//				for(View_teacher_course tc:ListTc){
//					String scheme_id=tc.getScheme_id();
//					wc = new WhereCondition();
//					wc.andEquals("scheme_id", scheme_id);
//					List<Hourlyratescheme_details> hdList=hourlyratescheme_detailsService.query(wc);
//					for(Hourlyratescheme_details hds:hdList){
//						//if(hds.getPeriodLowerLimit()!=null){
//							if(hds.getPeriodLowerLimit()==1){
//							d=hds.getPeriodUpperLimit();
//							break;
//							}
//						//}
//					}
//
//
//				}
                if (consumedClass_teacher != null && consumedClass_teacher > 80) {//是否超过80小时
                    model.put("over80", true);
                } else {
                    model.put("over80", false);
                }
                model.put("consumedclass_full_21", LanConstants.CONSUMEDCLASS_FULL_21);
                model.put("givenclass_full_21", LanConstants.GIVENCLASS_FULL_21);
                model.put("wage_full_21", LanConstants.WAGE_FULL_21);
                model.put("wagededuction_under80_full_21", LanConstants.WAGEDEDUCTION_UNDER80_FULL_21);
                model.put("wagededuction_over80_full_21", LanConstants.WAGEDEDUCTION_OVER80_FULL_21);

                model.put("consumedclass_full_22", LanConstants.CONSUMEDCLASS_FULL_22);
                model.put("givenclass_full_22", LanConstants.GIVENCLASS_FULL_22);
                model.put("wage_full_22", LanConstants.WAGE_FULL_22);
                model.put("wagededuction_full_22", LanConstants.WAGEDEDUCTION_FULL_22);

                model.put("givenclass_full_11", LanConstants.GIVENCLASS_FULL_11);
                model.put("wage_full_11", LanConstants.WAGE_FULL_11);
                model.put("wagededuction_full_11", LanConstants.WAGEDEDUCTION_FULL_11);

                model.put("givenclass_full_12", LanConstants.GIVENCLASS_FULL_12);
                model.put("wage_full_12", LanConstants.WAGE_FULL_12);
                model.put("wagededuction_full_12", LanConstants.WAGEDEDUCTION_FULL_12);

            } else {
                //教师迟到 二分之一
                model.put("consumedclass_part_21_half", LanConstants.CONSUMEDCLASS_PART_21_HALF);//1  学员耗课
                model.put("givenclass_part_21_half", LanConstants.GIVENCLASS_PART_21_HALF);//1  学员获赠
                model.put("wage_part_21_half", LanConstants.WAGE_PART_21_HALF);//1  教师工资
                model.put("wagededuction_part_21_half", LanConstants.WAGEDEDUCTION_PART_21_HALF);//1 倒扣工资
                //教师迟到 一单元
                model.put("consumedclass_part_21_one", LanConstants.CONSUMEDCLASS_PART_21_ONE);//0 学员耗课
                model.put("givenclass_part_21_one", LanConstants.GIVENCLASS_PART_21_ONE);//1 学员获赠
                model.put("wage_part_21_one", LanConstants.WAGE_PART_21_ONE);//0 教师工资
                model.put("wagededuction_part_21_one", LanConstants.WAGEDEDUCTION_PART_21_ONE);//1 倒扣工资
                //教师缺勤
                model.put("consumedclass_part_22", LanConstants.CONSUMEDCLASS_PART_22);//0 学员耗课
                model.put("givenclass_part_22", LanConstants.GIVENCLASS_PART_22);//1 学员获赠
                model.put("wage_part_22", LanConstants.WAGE_PART_22);//0 教师工资
                model.put("wagededuction_part_22", LanConstants.WAGEDEDUCTION_PART_22);//1 倒扣工资
                //学员迟到
                model.put("givenclass_part_11", LanConstants.GIVENCLASS_PART_11);//0 学员获赠
                model.put("wagededuction_part_11", LanConstants.WAGEDEDUCTION_PART_11);//0 倒扣工资

                model.put("givenclass_part_12", LanConstants.GIVENCLASS_PART_12);//0 学员获赠
                model.put("wage_part_12", LanConstants.WAGE_PART_12);//1 教师工资
                model.put("wagededuction_part_12", LanConstants.WAGEDEDUCTION_PART_12);//0 倒扣工资
            }
            String s = JSONObject.fromObject(model, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 取消已审核签到
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/cancelSignActived")
    public String execute3Actived(ModelMap model, @RequestBody final String content,
                                  final HttpServletRequest request, final HttpServletResponse response) {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                try {
                    JSONObject jsonobject = JSONObject.fromObject(content);
                    String attendanceBook_id = JSONUtils.getStr(jsonobject, "attendanceBook_id");
                    String classSchedule_id = JSONUtils.getStr(jsonobject, "classSchedule_id");
                    String approvalStatus = JSONUtils.getStr(jsonobject, "approvalStatus");//审核状态
                    String status = JSONUtils.getStr(jsonobject, "status");//课程状态
                    String student_id = "";
                    if (StringUtils.isNotEmpty(attendanceBook_id)) {
                        WhereCondition wc = new WhereCondition();
                        /* ====更新合同产品==== */
                        //取消签到的课程
                        List<Consumption_student_contract> cscList = consumption_student_contractService.getByclassSchedule(classSchedule_id);
                        ClassSchedule classSchedule = classScheduleService.loadById(classSchedule_id);

                        //学生缺勤和老师缺勤的课是删除状态,需要恢复
                        if (StringUtils.equals("2", status)) {
                            //ClassSchedule classSchedule = classScheduleService.loadById(classSchedule_id);
                            //因为是审核的签到,status是删除状体,所以我们需要判断该时间段的学生和老师是否被占用了
                            boolean flag = isClassScheduleEmploy(classSchedule, response);
                            if (!flag) {
                                return;
                            }
                            classSchedule.setStatus("1");
                            classSchedule.setLeaveState("0");
                            classSchedule.setNotes("");
                            classScheduleService.update(classSchedule);
                        }
                        //String BUType=classSchedule.getBUType();

                        String contract_id_null = attendanceBookService.loadById(attendanceBook_id).getContract_id();
                        //产生的免费合同已消耗，要将consumption_student_contract中对应的contract_id置为null
                        if (StringUtils.isNotBlank(contract_id_null)) {
                            wc = new WhereCondition();
                            wc.andEquals("contract_id", contract_id_null);
                            List<Consumption_student_contract> listcsc = consumption_student_contractService.query(wc);
                            if (!listcsc.isEmpty()) {
                                for (Consumption_student_contract csc : listcsc) {
                                    csc.setContract_id(null);
                                    consumption_student_contractService.updateForce(csc);
                                }
                            }
                        }
                        //先去将合同的内容更新，取消签到的耗课加入合同
                        if (CollectionUtils.isNotEmpty(cscList)) {
                            List<Map> contract_list = new ArrayList();
                            for (Consumption_student_contract csc : cscList) {
                                if (StringUtils.isNotEmpty(csc.getContract_id())) {
                                    Contract contract = contractService.loadById(csc.getContract_id());
                                    Map contract_set = new HashMap();
                                    student_id = contract.getStudentId();

                                    contract_set.put("contract", contract.getId());
                                    contract_set.put("diff", String.valueOf(contract.getConsumedClass() - csc.getConsumedClass()));
                                    contract_list.add(contract_set);

                                    contract.setConsumedClass(contract.getConsumedClass() - csc.getConsumedClass());
                                    if (contract.getConsumedClass() == 0) {
                                        contract.setStatus(ContractStatus.STAUS_1);
                                    } else {
                                        if (contract.getAmount() > contract.getConsumedClass() / 2) {
                                            contract.setStatus(ContractStatus.STAUS_2);
                                        } else {
                                            contract.setStatus(ContractStatus.STAUS_3);
                                        }
                                    }
                                    contractService.update(contract);
                                    updateStudentShangke(contract.getStudentId(), contract.getStatus());
                                }
                            }

                            if (!contract_list.isEmpty()) {
                                for (Map ctt_set : contract_list) {
                                    String contract_id = ctt_set.get("contract").toString();
                                    Contract contract2 = new Contract();
                                    contract2 = contractService.loadById(contract_id);
                                    Double diff = Double.parseDouble(ctt_set.get("diff").toString()); //???
                                    List<Consumption_student_contract> cscList2 = consumption_student_contractService.getByStudent(contract2.getStudentId(), classSchedule_id, contract2.getContractType2());
                                    if (!cscList2.isEmpty()) {
                                        for (Consumption_student_contract csc : cscList2) {
                                            if (contract2.getAmount() * 2 - contract2.getConsumedClass() >= csc.getConsumedClass()
                                                    && (contract2.getAmount() * 2 - contract2.getConsumedClass() > 0)) {
                                                //寻找学生耗课中，所消耗课程类型和合同类型一致
                                                csc.setContract_id(contract2.getId());
                                                contract2.setConsumedClass(contract2.getConsumedClass() + csc.getConsumedClass());
                                            } else if (contract2.getAmount() * 2 - contract2.getConsumedClass() > 0) {
                                                //这个位置也加入判断

                                                csc.setContract_id(contract2.getId());
                                                Double diff_hours = csc.getConsumedClass() - (contract2.getAmount() * 2 - contract2.getConsumedClass());
                                                csc.setConsumedClass(contract2.getAmount() * 2 - contract2.getConsumedClass());
                                                contract2.setConsumedClass(contract2.getAmount() * 2);
                                                // New another csc with contract_id is null
                                                Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                                consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                                consumption_student_contract.setConsumption_student_id(csc.getConsumption_student_id());
                                                consumption_student_contract.setContract_id(null);
                                                consumption_student_contract.setConsumedClass(diff_hours);
                                                consumption_student_contract.setUnitPrice(null);
                                                consumption_student_contract.setLockStatus(csc.getLockStatus());
                                                consumption_student_contract.setIsUploaded(csc.getIsUploaded());
                                                consumption_student_contractService.insert(consumption_student_contract);
                                            }
                                            contractService.update(contract2);
                                            consumption_student_contractService.update(csc);
                                        }
                                    }
                                }
                            }
                        }
                        /* 删除虚拟合同 */

                        if (StringUtils.isNotEmpty(contract_id_null)) {
                            contractService.delete(contract_id_null);
                        }
                        /* 删除学生耗课_合同 */
                        consumption_student_contractService.deleteByclassSchedule(classSchedule_id);

                        wc = new WhereCondition();
                        wc.andEquals("classSchedule_id", classSchedule_id);

                        /* 删除学生耗课信息 */
                        consumption_studentService.deleteByCondition(wc);

                        /* 删除教师耗课信息 */
                        consumption_teacherService.deleteByCondition(wc);

                        /* 删除签到信息 */
                        attendanceBookService.delete(attendanceBook_id);

                        String context = getLogin(request).getUser().getUserName() + "取消了" + classSchedule_id + "签到：";
                        operationLog.setId(sequenceManager.generateId("operationLog"));
                        operationLog.setUser_id(getLogin(request).getUser().getId());
                        operationLog.setCreateTime(new Date());
                        operationLog.setContent(context);
                        operationLogService.insert(operationLog);

                    }
                    AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0030");
                } catch (Exception e) {
                    arg0.setRollbackOnly();
                    AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0031");
                    e.printStackTrace();
                }
            }
        });

        return null;
    }

    /**
     * 判断此段时间老师和学生是否被占用
     *
     * @param classSchedule
     * @param response
     * @return
     */
    private boolean isClassScheduleEmploy(ClassSchedule classSchedule, HttpServletResponse response) {
        String select = " SELECT  count(*) FROM classschedulelong ";
        String where = " WHERE STATUS <> 2 AND courseType <> 2 ";
        where += classScheduleService.getQueryWhereClassroomBytime(classSchedule.getStartTime(), classSchedule.getEndTime(), LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd"));
        where += " and (teacher_id='" + classSchedule.getTeacher_id() + "' or student_id ='" + classSchedule.getStudent_id() + "' ) ";
        where += " and id <> '" + classSchedule.getId() + "' ";
        Zidingyi zi = new Zidingyi();
        zi.setSql(select + where);
        int count = tongjiService.count(zi);
        if (count > 0) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041_2");//取消签到失败,学生或者老师被占用！
            return false;
        }
        return true;
    }

    /**
     * 取消签到
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/cancelSign")
    public String execute3(ModelMap model, @RequestBody final String content,
                           final HttpServletRequest request, final HttpServletResponse response) {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                try {
                    JSONObject jsonobject = JSONObject.fromObject(content);
                    String attendanceBook_id = JSONUtils.getStr(jsonobject, "attendanceBook_id");
                    String classSchedule_id = JSONUtils.getStr(jsonobject, "classSchedule_id");
                    String approvalStatus = JSONUtils.getStr(jsonobject, "approvalStatus");//审核状态
                    String student_id = "";
                    if (StringUtils.isNotEmpty(attendanceBook_id)) {
                        WhereCondition wc = new WhereCondition();
                        /* ====更新合同产品==== */
                        //取消签到的课程
                        List<Consumption_student_contract> cscList = consumption_student_contractService.getByclassSchedule(classSchedule_id);
                        ClassSchedule classSchedule = classScheduleService.loadById(classSchedule_id);
                        //String BUType=classSchedule.getBUType();
                        //先去将合同的内容更新，取消签到的耗课加入合同
                        if (CollectionUtils.isNotEmpty(cscList)) {
                            List<Map> contract_list = new ArrayList();
                            for (Consumption_student_contract csc : cscList) {
                                if (StringUtils.isNotEmpty(csc.getContract_id())) {
                                    Contract contract = contractService.loadById(csc.getContract_id());
                                    Map contract_set = new HashMap();
                                    student_id = contract.getStudentId();

                                    contract_set.put("contract", contract.getId());
                                    contract_set.put("diff", String.valueOf(contract.getConsumedClass() - csc.getConsumedClass()));
                                    contract_list.add(contract_set);

                                    contract.setConsumedClass(contract.getConsumedClass() - csc.getConsumedClass());
                                    if (contract.getConsumedClass() == 0) {
                                        contract.setStatus(ContractStatus.STAUS_1);
                                    } else {
                                        if (contract.getAmount() > contract.getConsumedClass() / 2) {
                                            contract.setStatus(ContractStatus.STAUS_2);
                                        } else {
                                            contract.setStatus(ContractStatus.STAUS_3);
                                        }
                                    }
                                    contractService.update(contract);
                                    updateStudentShangke(contract.getStudentId(), contract.getStatus());
                                }
                            }

                            if (!contract_list.isEmpty()) {
                                for (Map ctt_set : contract_list) {
                                    String contract_id = ctt_set.get("contract").toString();
                                    Contract contract2 = new Contract();
                                    contract2 = contractService.loadById(contract_id);
                                    //String contractType2=contract2.getContractType2();
                                    Double diff = Double.parseDouble(ctt_set.get("diff").toString()); //???
                                    // get csclist that contract_id is null
                                    //cscList2合同号为空的记录
                                    List<Consumption_student_contract> cscList2 = consumption_student_contractService.getByStudent(contract2.getStudentId(), classSchedule_id, contract2.getContractType2());
                                    //学生排课合同的耗课记录
                                    //List<View_consumption_student_contract> cscList2=view_consumption_student_contractService.query(wc);
                                    if (!cscList2.isEmpty()) {
                                        for (Consumption_student_contract csc : cscList2) {

                                            if (contract2.getAmount() * 2 - contract2.getConsumedClass() >= csc.getConsumedClass()
                                                    && (contract2.getAmount() * 2 - contract2.getConsumedClass() > 0)) {
                                                //寻找学生耗课中，所消耗课程类型和合同类型一致
                                                csc.setContract_id(contract2.getId());
                                                contract2.setConsumedClass(contract2.getConsumedClass() + csc.getConsumedClass());
                                            } else if (contract2.getAmount() * 2 - contract2.getConsumedClass() > 0) {
                                                //这个位置也加入判断

                                                csc.setContract_id(contract2.getId());
                                                Double diff_hours = csc.getConsumedClass() - (contract2.getAmount() * 2 - contract2.getConsumedClass());
                                                csc.setConsumedClass(contract2.getAmount() * 2 - contract2.getConsumedClass());
                                                contract2.setConsumedClass(contract2.getAmount() * 2);
                                                // New another csc with contract_id is null
                                                Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                                consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                                consumption_student_contract.setConsumption_student_id(csc.getConsumption_student_id());
                                                consumption_student_contract.setContract_id(null);
                                                consumption_student_contract.setConsumedClass(diff_hours);
                                                consumption_student_contract.setUnitPrice(null);
                                                consumption_student_contract.setLockStatus(csc.getLockStatus());
                                                consumption_student_contract.setIsUploaded(csc.getIsUploaded());
                                                consumption_student_contractService.insert(consumption_student_contract);
                                            }
                                            contractService.update(contract2);
                                            consumption_student_contractService.update(csc);
                                        }
                                    }
                                }
                            }

//								}
//							}


                        }
                        /* 删除虚拟合同 */
                        String contract_id = attendanceBookService.loadById(attendanceBook_id).getContract_id();
                        if (StringUtils.isNotEmpty(contract_id)) {
                            contractService.delete(contract_id);
                        }
                        /* 删除学生耗课_合同 */
                        consumption_student_contractService.deleteByclassSchedule(classSchedule_id);

                        wc = new WhereCondition();
                        wc.andEquals("classSchedule_id", classSchedule_id);

                        /* 删除学生耗课信息 */
                        consumption_studentService.deleteByCondition(wc);

                        /* 删除教师耗课信息 */
                        consumption_teacherService.deleteByCondition(wc);

                        /* 删除签到信息 */
                        attendanceBookService.delete(attendanceBook_id);

                        //产生的免费合同已消耗，要将consumption_student_contract中对应的contract_id置为null
                        if (StringUtils.isNotBlank(contract_id)) {
                            wc = new WhereCondition();
                            wc.andEquals("contract_id", contract_id);
                            List<Consumption_student_contract> listcsc = consumption_student_contractService.query(wc);
                            if (!listcsc.isEmpty()) {
                                for (Consumption_student_contract csc : listcsc) {
                                    csc.setContract_id(null);
                                    consumption_student_contractService.updateForce(csc);
                                }
                            }
                        }


                        String context = getLogin(request).getUser().getUserName() + "取消了" + classSchedule_id + "签到：";
                        operationLog.setId(sequenceManager.generateId("operationLog"));
                        operationLog.setUser_id(getLogin(request).getUser().getId());
                        operationLog.setCreateTime(new Date());
                        operationLog.setContent(context);
                        operationLogService.insert(operationLog);
                    }
                    AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0030");
                } catch (Exception e) {
                    arg0.setRollbackOnly();
                    AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0031");
                    e.printStackTrace();
                }
            }
        });

        return null;
    }

    /**
     * 审批
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/approval")
    public String approval(ModelMap model, @RequestBody String content,
                           final HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObject = JSONObject.fromObject(content);
            JSONArray jsonArray = jsonObject.getJSONArray("list");
            final String approvalStatus = JSONUtils.getStr(jsonObject, "approvalStatus");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jo = jsonArray.getJSONObject(i);
                final View_attendanceBook p = new View_attendanceBook();

                JSONObject.toBean(jo, p, jsonConfig);
                final ClassSchedule classSchedule = classScheduleService.loadById(p.getClassSchedule_id());
                final List listBUType = new ArrayList();
                final String BUType = classSchedule.getBUType();
                final String course_id = classSchedule.getCourse_id();
                listBUType.add(BUType);
                if (!StringUtils.equals(ApprovalStatus.STATUS_NEW, p.getApprovalStatus())) {//  跳过非待审批课表
                    continue;
                }
                transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                    @Override
                    protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                        try {
                            Contract contract = null;
                            System.out.println("approvalStatus----" + approvalStatus);
                            /* 生成虚拟合同 *///审批通过
                            if (StringUtils.equals(ApprovalStatus.STATUS_END, approvalStatus)) {
                                WhereCondition wc = new WhereCondition();
                                wc.andEquals("classSchedule_id", p.getClassSchedule_id());
                                List<Consumption_student> csList = consumption_studentService.query(wc);
                                if (CollectionUtils.isNotEmpty(csList) && csList.get(0).getGivenClass() > 0) {
                                    List<Contract> useableContracts = new ArrayList<>();
                                    if (Enums.ONLINETE.equals(BUType)) {
                                        useableContracts = contractService.getUseableContract(p.getStudent_id(), false, listBUType, course_id);
                                    } else {
                                        useableContracts = contractService.getUseableContract(p.getStudent_id(), false, listBUType);
                                    }

                                    /* 虚拟合同 */
                                    contract = new Contract();
                                    contract.setId(sequenceManager.generateId("contract"));
                                    // 合同开始日期
                                    if (CollectionUtils.isEmpty(useableContracts)) {
                                        contract.setStartDate(new Date());
                                    } else {
                                        contract.setStartDate(LanDateUtils.getNextDate_month(
                                                useableContracts.get(useableContracts.size() - 1).getStartDate(), 1));
                                    }
                                    contract.setEndDate(null);//
                                    // 赠送类型
                                    if (StringUtils.equals(AttendanceStatus.TEACHER_LATE, p.getAttendanceStatus())) {
                                        contract.setPersentType(PersentType.TE_TEACHER_LATE);// 教师迟到
                                    } else if (StringUtils.equals(AttendanceStatus.TEACHER_ABSENTEEISM, p.getAttendanceStatus())) {
                                        contract.setPersentType(PersentType.TE_TEACHER_ABSENTEEISM);// 教师缺勤
                                    } else {
                                        contract.setPersentType(PersentType.TE_OTHER);// 其他
                                    }
                                    contract.setStudentId(p.getStudent_id());// 学员
                                    contract.setUnits(csList.get(0).getGivenClass() / 2);// 单元数
                                    contract.setAmount(contract.getUnits());// 数量
                                    contract.setTuitionFee(0D);// 学费
                                    contract.setProductType(ProductType.ONE_TO_ONE); // 产品类型
                                    contract.setConsumedClass(0D);// 已消耗课时数
                                    if (Enums.SPE.equals(BUType)) {
                                        contract.setContractType2(Enums.ContractType2.SPE);
                                    } else if (Enums.ONLINETE.equals(BUType)) {
                                        contract.setContractType2(Enums.ContractType2.OBT);
                                        //obt course id (zoho course id)
                                        wc = new WhereCondition();
                                        wc.andEquals("course_id", course_id);
                                        wc.andEquals("studentId", p.getStudent_id());
                                        wc.andEquals("contractType2", Enums.ContractType2.OBT);
                                        List<Contractview> contracts = contractviewService.query(wc);
                                        if(contracts.size() > 0) {
                                            contract.setCourse_id(contracts.get(0).getObt_course_id());
                                        }
                                    } else {
                                        contract.setContractType2(BUType);
                                    }
                                    contract.setCompensateStudent(classSchedule.getId());//补偿合同对应的课程id
                                    if (StringUtils.equals(AttendanceStatus.TEACHER_LATE, p.getAttendanceStatus())) {
                                        contract.setProductName(ProductName.TE_TEACHER_LATE);
                                    } else if (StringUtils.equals(AttendanceStatus.TEACHER_ABSENTEEISM, p.getAttendanceStatus())) {
                                        contract.setProductName(ProductName.TE_TEACHER_ABSENTEEISM);
                                    } else {
                                        contract.setProductName(ProductName.TE_OTHER);
                                    }
                                    initCreate2(contract, request);
                                    contractService.insert(contract);
                                }
                                /* 更新签到信息 */
                                AttendanceBook attendanceBook = new AttendanceBook();
                                attendanceBook.setId(p.getAttendanceBook_id());
                                attendanceBook.setApprovalStatus(approvalStatus);
                                if (contract != null) {
                                    attendanceBook.setContract_id(contract.getId());
                                }
                                attendanceBookService.update(attendanceBook);
                                //release classschedule by changing status to 'deleted'
                                if (StringUtils.equals(AttendanceStatus.TEACHER_ABSENTEEISM, p.getAttendanceStatus())) {
                                    classSchedule.setStatus(ClassScheduleStatus.DELETED);
                                    classSchedule.setDeleteFlag("2");
                                    classScheduleService.update(classSchedule);
                                    //delete teacher freetime_code
                                    WhereCondition wc1 = new WhereCondition();
                                    int startMin = Integer.valueOf(classSchedule.getStartTime().split(":")[1]);
                                    int endMin = Integer.valueOf(classSchedule.getEndTime().split(":")[1]);
                                    String startTime = classSchedule.getStartTime();
                                    String endTime = classSchedule.getEndTime();
                                    boolean s_Clock = true;
                                    boolean e_Clock = true;
                                    if (startMin != 0) {
                                        //非整点开始
                                        startTime = String.format("%02d", Integer.valueOf(classSchedule.getStartTime().split(":")[0])) + ":00";
                                        s_Clock = false;
                                    }

                                    if (endMin != 0) {
                                        endTime = String.format("%02d", Integer.valueOf(classSchedule.getEndTime().split(":")[0]) + 1) + ":00";
                                        //非整点结束
                                        e_Clock = false;
                                    }

                                    wc1.andEquals("teacher_id", classSchedule.getTeacher_id());
                                    wc1.andEquals("freeDate", classSchedule.getScheduledDate());
                                    wc1.andGreaterEquals("startTime", startTime);
                                    wc1.andLessEquals("endTime", endTime);
                                    wc1.setOrderBy("startTime");

                                    List<Teacher_freeTime> list = teacher_freeTimeService.query(wc1);

                                    if (CollectionUtils.isNotEmpty(list)) {
                                        String freeTimeCode = "";
                                        int count = 1;

                                        if (s_Clock && e_Clock) { //整点开始结束 teacher freetime全部删除
                                            teacher_freeTimeService.deleteByCondition(wc1);
                                        } else if (!s_Clock && e_Clock) { //非整点开始但整点结束
                                            for (Teacher_freeTime t_ft : list) {
                                                Teacher_freeTime t = teacher_freeTimeService.loadById(t_ft.getId().toString());
                                                if (count == 1) { //第一条
                                                    if (startMin == 15) {
                                                        freeTimeCode = "1";
                                                    } else if (startMin == 30) {
                                                        freeTimeCode = "12";
                                                    } else if (startMin == 45) {
                                                        freeTimeCode = "123";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    teacher_freeTimeService.update(t);
                                                } else {
                                                    teacher_freeTimeService.delete(t_ft.getId());
                                                }
                                                count += 1;
                                            }
                                        } else if (s_Clock && !e_Clock) { //整点开始但非整点结束
                                            for (Teacher_freeTime t_ft : list) {
                                                Teacher_freeTime t = teacher_freeTimeService.loadById(t_ft.getId().toString());
                                                if (count == list.size()) {
                                                    if (endMin == 15) {
                                                        freeTimeCode = "234";
                                                    } else if (endMin == 30) {
                                                        freeTimeCode = "34";
                                                    } else if (endMin == 45) {
                                                        freeTimeCode = "4";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    teacher_freeTimeService.update(t);
                                                } else {
                                                    teacher_freeTimeService.delete(t_ft.getId());
                                                }
                                                count += 1;
                                            }
                                        } else if (!s_Clock && !e_Clock) { //非整点开始非整点结束
                                            for (Teacher_freeTime t_ft : list) {
                                                Teacher_freeTime t = teacher_freeTimeService.loadById(t_ft.getId().toString());
                                                if (count == 1) { //第一条
                                                    if (startMin == 15) {
                                                        freeTimeCode = "1";
                                                    } else if (startMin == 30) {
                                                        freeTimeCode = "12";
                                                    } else if (startMin == 45) {
                                                        freeTimeCode = "123";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    teacher_freeTimeService.update(t);
                                                } else if (count == list.size()) { //最后一条
                                                    if (endMin == 15) {
                                                        freeTimeCode = "234";
                                                    } else if (endMin == 30) {
                                                        freeTimeCode = "34";
                                                    } else if (endMin == 45) {
                                                        freeTimeCode = "4";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    teacher_freeTimeService.update(t);
                                                } else { //中间各条
                                                    teacher_freeTimeService.delete(t_ft.getId());
                                                }
                                                count += 1;
                                            }
                                        }
                                    }

                                } else if (StringUtils.equals(AttendanceStatus.STUDENT_ABSENTEEISM, p.getAttendanceStatus())) {
                                    classSchedule.setStatus(ClassScheduleStatus.DELETED);
                                    classSchedule.setDeleteFlag("2");
                                    classScheduleService.update(classSchedule);
                                    //delete student freetime_code
                                    WhereCondition wc1 = new WhereCondition();
                                    int startMin = Integer.valueOf(classSchedule.getStartTime().split(":")[1]);
                                    int endMin = Integer.valueOf(classSchedule.getEndTime().split(":")[1]);
                                    String startTime = classSchedule.getStartTime();
                                    String endTime = classSchedule.getEndTime();
                                    boolean s_Clock = true;
                                    boolean e_Clock = true;
                                    if (startMin != 0) {
                                        //非整点开始
                                        startTime = String.format("%02d", Integer.valueOf(classSchedule.getStartTime().split(":")[0])) + ":00";
                                        s_Clock = false;
                                    }

                                    if (endMin != 0) {
                                        endTime = String.format("%02d", Integer.valueOf(classSchedule.getEndTime().split(":")[0]) + 1) + ":00";
                                        //非整点结束
                                        e_Clock = false;
                                    }

                                    wc1.andEquals("student_id", classSchedule.getStudent_id());
                                    wc1.andEquals("freeDate", classSchedule.getScheduledDate());
                                    wc1.andGreaterEquals("startTime", startTime);
                                    wc1.andLessEquals("endTime", endTime);
                                    wc1.setOrderBy("startTime");

                                    List<Student_freeTime> list = student_freeTimeService.query(wc1);

                                    if (CollectionUtils.isNotEmpty(list)) {
                                        String freeTimeCode = "";
                                        int count = 1;

                                        if (s_Clock && e_Clock) { //整点开始结束 teacher freetime全部删除
                                            student_freeTimeService.deleteByCondition(wc1);
                                        } else if (!s_Clock && e_Clock) { //非整点开始但整点结束
                                            for (Student_freeTime t_ft : list) {
                                                Student_freeTime t = student_freeTimeService.loadById(t_ft.getId().toString());
                                                if (count == 1) { //第一条
                                                    if (startMin == 15) {
                                                        freeTimeCode = "1";
                                                    } else if (startMin == 30) {
                                                        freeTimeCode = "12";
                                                    } else if (startMin == 45) {
                                                        freeTimeCode = "123";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    student_freeTimeService.update(t);
                                                } else {
                                                    student_freeTimeService.delete(t_ft.getId());
                                                }
                                                count += 1;
                                            }
                                        } else if (s_Clock && !e_Clock) { //整点开始但非整点结束
                                            for (Student_freeTime t_ft : list) {
                                                Student_freeTime t = student_freeTimeService.loadById(t_ft.getId().toString());
                                                if (count == list.size()) {
                                                    if (endMin == 15) {
                                                        freeTimeCode = "234";
                                                    } else if (endMin == 30) {
                                                        freeTimeCode = "34";
                                                    } else if (endMin == 45) {
                                                        freeTimeCode = "4";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    student_freeTimeService.update(t);
                                                } else {
                                                    student_freeTimeService.delete(t_ft.getId());
                                                }
                                                count += 1;
                                            }
                                        } else if (!s_Clock && !e_Clock) { //非整点开始非整点结束
                                            for (Student_freeTime t_ft : list) {
                                                Student_freeTime t = student_freeTimeService.loadById(t_ft.getId().toString());
                                                if (count == 1) { //第一条
                                                    if (startMin == 15) {
                                                        freeTimeCode = "1";
                                                    } else if (startMin == 30) {
                                                        freeTimeCode = "12";
                                                    } else if (startMin == 45) {
                                                        freeTimeCode = "123";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    student_freeTimeService.update(t);
                                                } else if (count == list.size()) { //最后一条
                                                    if (endMin == 15) {
                                                        freeTimeCode = "234";
                                                    } else if (endMin == 30) {
                                                        freeTimeCode = "34";
                                                    } else if (endMin == 45) {
                                                        freeTimeCode = "4";
                                                    }
                                                    t.setFreeTimeCode(freeTimeCode);
                                                    student_freeTimeService.update(t);
                                                } else { //中间各条
                                                    student_freeTimeService.delete(t_ft.getId());
                                                }
                                                count += 1;
                                            }
                                        }
                                    }

                                }
                                String context = getLogin(request).getUser().getUserName() + "审批通过了" + p.getClassSchedule_id() + "的签到，签到信息" + p.getAttendanceBook_id();
                                operationLog.setId(sequenceManager.generateId("operationLog"));
                                operationLog.setUser_id(getLogin(request).getUser().getId());
                                operationLog.setCreateTime(new Date());
                                operationLog.setContent(context);
                                operationLogService.insert(operationLog);
                            } else if (StringUtils.equals(ApprovalStatus.STATUS_TERMINATE, approvalStatus)) {
                                WhereCondition wc = new WhereCondition();
                                /* 删除学生耗课_合同 */
                                consumption_student_contractService.deleteByclassSchedule(p.getClassSchedule_id());

                                wc = new WhereCondition();
                                wc.andEquals("classSchedule_id", p.getClassSchedule_id());

                                /* 删除学生耗课信息 */
                                consumption_studentService.deleteByCondition(wc);

                                /* 删除教师耗课信息 */
                                consumption_teacherService.deleteByCondition(wc);

                                /* 删除签到信息 */
                                attendanceBookService.delete(p.getAttendanceBook_id());

                                String context = getLogin(request).getUser().getUserName() + "驳回了" + p.getClassSchedule_id() + "的签到，删除签到信息" + p.getAttendanceBook_id();
                                operationLog.setId(sequenceManager.generateId("operationLog"));
                                operationLog.setUser_id(getLogin(request).getUser().getId());
                                operationLog.setCreateTime(new Date());
                                operationLog.setContent(context);
                                operationLogService.insert(operationLog);
                            }

                        } catch (Exception e) {
                            arg0.setRollbackOnly();
                            e.printStackTrace();
                        }
                    }


                });
                //更新学员上课状态
                student_zoneService.updateClassStatusById(classSchedule.getStudent_id());
            }
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0032");

        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0033");
            e.printStackTrace();
        }


        return null;
    }

    /**
     * 同步ERP
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/tongbu")
    public String executeTongbu(ModelMap model, @RequestBody String content,
                                HttpServletRequest request, HttpServletResponse response) {
        try {
            String firstDay = "";
            String lastDay = "";
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject jsonObjsearchItems = jsonObj.getJSONObject("item");
            Set set = jsonObjsearchItems.keySet();
            for (Object o : set) {
                String key = o.toString();
                String value = jsonObjsearchItems.getString(key);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                System.out.println(key + "---" + value);
                if ("startDate".equals(key.trim())) {
                    firstDay = value;
                }
                if ("endDate".equals(key.trim())) {
                    lastDay = value;
                }
            }
            System.out.println("firstDay:" + firstDay);
            System.out.println("lastDay:" + lastDay);
            Zidingyi z = new Zidingyi();
            String sql = "select count(1) from student_leave a,classSchedule b where a.classSchedule_id=b.id "
                    + "and a.approvalStatus='1' and b.scheduledDate>='" + firstDay + "' and b.scheduledDate<='" + lastDay + "'";
            z.setSql(sql);
            if (tongjiService.count(z) > 0) {
                AjaxUtils.ajaxJsonErrorMessage(response, "所选时间段内，有待审核的学生请假数据，无法锁定数据");
                return null;
            }
            sql = "select count(1) from teacher_leave a,classSchedule b where a.classSchedule_id=b.id "
                    + "and a.approvalStatus='1' and b.scheduledDate>='" + firstDay + "' and b.scheduledDate<='" + lastDay + "'";
            z.setSql(sql);
            if (tongjiService.count(z) > 0) {
                AjaxUtils.ajaxJsonErrorMessage(response, "所选时间段内，有待审核的老师请假数据，无法锁定数据");
                return null;
            }
            sql = "select count(1) from attendanceBook a,classSchedule b where a.classSchedule_id=b.id "
                    + "and a.approvalStatus='1' and b.scheduledDate>='" + firstDay + "' and b.scheduledDate<='" + lastDay + "'";
            z.setSql(sql);
            if (tongjiService.count(z) > 0) {
                AjaxUtils.ajaxJsonErrorMessage(response, "所选时间段内，有待审核的签到数据，无法锁定数据");
                return null;
            }
            sql = "select count(1) from classSchedule b LEFT JOIN attendanceBook a ON a.classSchedule_id=b.id where b.`status`='1' "
                    + " and a.approvalStatus is null and b.scheduledDate>='" + firstDay + "' and b.scheduledDate<='" + lastDay + "'";
            z.setSql(sql);
            if (tongjiService.count(z) > 0) {
                AjaxUtils.ajaxJsonErrorMessage(response, "所选时间段内，有未签到数据，无法锁定数据");
                return null;
            }
            WhereCondition wc = new WhereCondition();
            wc.andEquals("startDate", firstDay);
            List<LockData> list = lockDataService.query(wc);
            for (LockData l : list) {
                l.setDelStatus("1");
                l.setEndDate(LanDateUtils.stringToDate(lastDay));
                l.setLockTime(new Date());
                lockDataService.update(l);
            }
            LockData l = new LockData();
            l.setCreateTime(new Date());
            l.setDelStatus("0");
            String id = sequenceManager.generateId("lockData");
            l.setId(id);
            l.setStartDate(LanDateUtils.stringToDate(LanDateUtils.getNext_Day(lastDay, 1)));
            lockDataService.insert(l);
            String ids1 = "";
            List<String> ids = new ArrayList<String>();
            wc = new WhereCondition();
            wc.andGreaterEquals("scheduledDate", firstDay);
            wc.andLessEquals("scheduledDate", lastDay);
            List<ClassSchedule> li0 = classScheduleService.query(wc);

            sql = " select b.id as id from attendanceBook a,classSchedule b where a.classSchedule_id=b.id "
                    + "and a.approvalStatus in(0,2) and b.scheduledDate>='" + firstDay + "' and b.scheduledDate<='" + lastDay + "'";
            z.setSql(sql);
            List<Map> li1 = tongjiService.query(z);
            for (Map m : li1) {
                ids1 += m.get("id").toString() + ",";
            }
            sql = " select b.id as id from student_leave a,classSchedule b where a.classSchedule_id=b.id "
                    + "and a.approvalStatus in(0,2) and b.scheduledDate>='" + firstDay + "' and b.scheduledDate<='" + lastDay + "'";
            z.setSql(sql);
            List<Map> li2 = tongjiService.query(z);
            for (Map m : li2) {
                ids1 += m.get("id").toString() + ",";
            }
            sql = " select b.id as id from teacher_leave a,classSchedule b where a.classSchedule_id=b.id "
                    + "and a.approvalStatus in(0,2) and b.scheduledDate>='" + firstDay + "' and b.scheduledDate<='" + lastDay + "'";
            z.setSql(sql);
            List<Map> li3 = tongjiService.query(z);
            for (Map m : li3) {
                ids1 += m.get("id").toString() + ",";
            }
            for (ClassSchedule cs : li0) {
                if (!ids1.contains(cs.getId())) {
                    cs.setDelStatus("1");
                    cs.setDescription("bkhf");//锁定数据，标记删除未签到、请假的数据
                    classScheduleService.update(cs);
                }
            }
            updateConsumption_teacher_Wage2(firstDay, lastDay);
            //查询数据
            List<ContractInfo> contractInfos = erpServiceImpl.generateContractInfo(firstDay, lastDay);
            if (CollectionUtils.isNotEmpty(contractInfos)) {
                //发送数据
                erpServiceImpl.sendContractInfo1(contractInfos, firstDay, lastDay);
            }
            String context = getLogin(request).getUser().getUserName() + "锁定" + firstDay + "--" + lastDay + "的数据";
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0068");
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0069");
            e.printStackTrace();
        }
        return null;
    }

    private void updateConsumption_teacher_Wage2(String startDate, String endDate) {
        String sql = "select b.teachingWay, a.id as id,a.teacher_id as teacherId,b.course_id as courseId,b.scheduledDate as day,a.wage as xiaoshi,b.startTime as startTime from consumption_teacher a";
        sql += " LEFT JOIN classSchedule b on a.classSchedule_id=b.id where b.scheduledDate>='" + startDate + "' and b.scheduledDate<='" + endDate + "' order by b.scheduledDate,b.startTime asc";
        Zidingyi z = new Zidingyi();
        z.setSql(sql);
        List<Map> li = tongjiService.query(z);
        for (Map m : li) {
            Consumption_teacher ct = consumption_teacherService.loadById(m.get("id").toString());
            double xh = ct.getWage();
            if (ct.getWageDeduction() != null) {
                xh = ct.getWage() - ct.getWageDeduction();
            }
            Map<String, Double> rt = getWageAll(m.get("teacherId").toString(), m.get("courseId").toString(), m.get("day").toString(), xh, m.get("startTime").toString(), m.get("teachingWay").toString());
            double wage2 = rt.get("wage2");
            if (rt.get("currency_idDou") != null) { //添加货别id
                Double currency_idDou = rt.get("currency_idDou");
                String currency_idString = currency_idDou.toString();
                String[] v = currency_idString.split("\\.");

                String currency_idStr = currency_idString.split("\\.")[0];
                int index = 8 - currency_idStr.length();
                String currency_id = "Id_currency";
                for (int i = 0; i < index; i++) {
                    currency_id += "0";
                }
                currency_id += currency_idStr;
                ct.setCurrency_id(currency_id);
            }
            ct.setWage2(wage2);
            consumption_teacherService.update(ct);
        }
    }

    private Map<String, Double> getWageAll(String teacherId, String courseId, String day, Double xs, String startTime, String teachingWay_id) {
        Map<String, Double> rt = new HashMap<String, Double>();
        double d = 0d;
        if (xs == 0) {
            rt.put("wage2", d);
            return rt;
        }
//		String sql="select a.periodLowerLimit as min,a.periodUpperLimit as max,a.intervalType as type,a.unitPrice as price from teacher_course a ";
//		sql+=" where a.teacher_id='"+teacherId+"' and a.course_id='"+courseId+"' and a.startDate<='"+day+"' and (a.endDate>'"+day+"' or a.endDate is null)";
        String sqljiage = " ";
        sqljiage += " SELECT DISTINCT hd.currency_id , hd.periodLowerLimit AS min, hd.periodUpperLimit AS max, hd.intervalType AS type, hd.unitPrice AS price ";
        sqljiage += " FROM teacher_course tc  JOIN teacher_hourlyratescheme th on th.id = tc.scheme_id JOIN hourlyratescheme_details hd on th.id = hd.scheme_id JOIN teacher_course_teachingway tct on tct.schemeDetails_id = hd.id ";
        sqljiage += " WHERE tc.course_id = '" + courseId + "' AND tc.teacher_id = '" + teacherId + "' and tct.teachingWay_id ='" + teachingWay_id + "' ";
        sqljiage += " and (  (hd.startDate <= '" + day + "' and hd.endDate> '" + day + "')or (hd.startDate <= '" + day + "' and hd.endDate is null) ) ";
        Zidingyi z = new Zidingyi();
        z.setSql(sqljiage);
        List<Map> li = tongjiService.query(z);
        if (li.isEmpty()) {
            rt.put("wage2", d);
            return rt;
        }
        String type = li.get(0).get("type").toString();
        String startDate = LanDateUtils.getFirstDayOfMonth(LanDateUtils.stringToDate(day), 0);
        String endDate = day;
        if (!"1".equals(type)) {
            startDate = LanDateUtils.format(LanDateUtils.getFirstDayOfWeek(LanDateUtils.stringToDate(day)), "yyyy-MM-dd");
            endDate = day;
        }
        String sql1 = "select sum(a.wage) as keshi,sum(a.wageDeduction) as koushi from consumption_teacher a LEFT JOIN classSchedule b on a.classSchedule_id=b.id";
        sql1 += " where a.teacher_id='" + teacherId + "' and b.scheduledDate>='" + startDate + "' and b.scheduledDate<'" + endDate + "'";
        z.setSql(sql1);
        List<Map> li1 = tongjiService.query(z);

        String sql2 = "select sum(a.wage) as keshi,sum(a.wageDeduction) as koushi from consumption_teacher a LEFT JOIN classSchedule b on a.classSchedule_id=b.id";
        sql2 += " where a.teacher_id='" + teacherId + "' and b.scheduledDate='" + endDate + "' and b.startTime<'" + startTime + "'";
        z.setSql(sql2);
        List<Map> li2 = tongjiService.query(z);

        double ks = xs;
        System.out.println("ks1==" + ks);
        if (!li1.isEmpty() && li1.get(0) != null) {
            String keshi = li1.get(0).get("keshi") != null ? li1.get(0).get("keshi").toString() : "0";
            String koushi = li1.get(0).get("koushi") != null ? li1.get(0).get("koushi").toString() : "0";
            if (Double.valueOf(koushi) < Double.valueOf(keshi) + ks) {
                ks = Double.valueOf(keshi) + ks - Double.valueOf(koushi);
            }
        }
        if (!li2.isEmpty() && li2.get(0) != null) {
            String keshi = li2.get(0).get("keshi") != null ? li2.get(0).get("keshi").toString() : "0";
            String koushi = li2.get(0).get("koushi") != null ? li2.get(0).get("koushi").toString() : "0";
            if (Double.valueOf(koushi) < Double.valueOf(keshi) + ks) {
                ks = Double.valueOf(keshi) + ks - Double.valueOf(koushi);
            }
        }


        System.out.println("ks2==" + ks);
        User u = userService.loadById(teacherId);
        for (Map m : li) {

            //货币id,返回值为double需要转换下
            if (m.get("currency_id") != null) {
                String currency_id = m.get("currency_id").toString();
                Double currency_idDou = Double.valueOf(currency_id.substring(11));
                rt.put("currency_idDou", currency_idDou);
            }
            //总课时开始计算,总课时为0,课时小于1
            if (ks == xs && Double.valueOf(m.get("min").toString()) > ks) {
                d += Double.valueOf(m.get("price").toString()) * xs;
                rt.put("price", Double.valueOf(m.get("price").toString()));
            }
            if (Double.valueOf(m.get("min").toString()) <= ks && Double.valueOf(m.get("max").toString()) >= ks) {
                if (Double.valueOf(m.get("min").toString()) == 1) {
//					if("1".equals(u.getType())||"3".equals(u.getType())){
//						d=0d;
//					}else{
                    d += Double.valueOf(m.get("price").toString()) * xs;
                    rt.put("price", Double.valueOf(m.get("price").toString()));
//					}
                } else {
                    d += Double.valueOf(m.get("price").toString()) * xs;
                    rt.put("price", Double.valueOf(m.get("price").toString()));
                }
            }
            if (Double.valueOf(m.get("max").toString()) < ks && ks - Double.valueOf(m.get("max").toString()) < xs) {
                d += Double.valueOf(m.get("price").toString()) * (xs - (ks - Double.valueOf(m.get("max").toString())));
                System.out.println("d1==" + d);
                System.out.println("xs1==" + xs);
                xs = ks - Double.valueOf(m.get("max").toString());
                System.out.println("xs2==" + xs);
                rt.put("price", Double.valueOf(m.get("price").toString()));
                rt.put("next", xs);
            }
        }
        rt.put("wage2", d);
        System.out.println("type==" + u.getType());
        System.out.println("wage2==" + rt.get("wage2"));
        System.out.println("price==" + rt.get("price"));
        System.out.println("next==" + rt.get("next"));
        return rt;
    }

    /**
     * 今日签到列表
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            String select = " SELECT  scheduledDate >=  ( SELECT startDate FROM lockData WHERE delStatus = '0' ) islockDate, status, classSchedule_id, courseType, student_id, course_id, scheduledDate, startTime, endTime, time, freeTimeCode, teacher_id, teachingWay, ";
            select += " classroom_id, description, notes, leaveState, isAuto, realDate, realStartTime, realEndTime, realTime, attendanceBook_id, attendanceStatus, ";
            select += " reason, approvalStatus, approver, creator_id, createTime, lastModifier_id, lastModifiedTime, delStatus, contract_id, lockStatus, s_chineseName, ";
            select += " s_englishName, zone_id, t_chineseName, t_englishName, c_name, z_name, cr_name, tutorId, tutorenglishName, teachingWayName, BUType ";
            String selectCount = " select count(1) ";
            String from = " FROM view_attendanceBook ";
            String where = " WHERE 1=1 ";
            // 组装查询条件
            JSONObject jsonObjsearchItems = jsonObj.getJSONObject("searchItems");
            Set set = jsonObjsearchItems.keySet();
            String leaveTimeStart = "";
            String leaveTimeEnd = "";
            String payDate = "";

            boolean tutorFlag = true;
            boolean activedflag = false;
            if (!"0".equals(getLogin(request).getUser().getCategory().trim())) {
                List<Permission> li = getLogin(request).getPermissionList();
                for (Permission pm : li) {
                    if ("133".equals(pm.getFunction_id())) {
                        where += " and tutorId = '" + getLogin(request).getUser().getId() + "' ";
                        tutorFlag = false;
                    }
                    //判断是否有取消已审核签到
                    if ("505a".equals(pm.getFunction_id())) {
                        activedflag = true;
                    }
                }
            } else {
                //超级管理员也能看见
                activedflag = true;
            }
            String where1 = " AND  (STATUS = '1' ";
            for (Object o : set) {
                String key = o.toString();
                String value = jsonObjsearchItems.getString(key);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                if ("scheduledDate_end".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        leaveTimeEnd = value;
                    }
                }
                if ("scheduledDate_start".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        leaveTimeStart = value;
                    }
                }
                if ("approvalStatus_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and approvalStatus = '" + value + "'";
                    }
                }
                if ("s_englishName_lk".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and s_englishName like '%" + value + "%'";
                    }
                }
                if ("t_englishName_lk".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and t_englishName like '%" + value + "%'";
                    }
                }
                if ("attendanceStatus_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        if (StringUtils.equalsIgnoreCase("isnull", value)) {
                            where1 += " and attendanceStatus is null ";
                        } else {
                            where1 += " and attendanceStatus = '" + value + "'";
                        }

                    }
                }
                if ("isApproval".equals(key.trim())) {
                    if (value != null && !"".equals(value) && value.equals("0")) {
                        //如果有取消已审核权限,需要把删除的也加上
                        if (activedflag) {
                            //where+= " or `status`='2' and approvalStatus='2' ";
                            where1 += " OR  attendanceStatus ='12' OR attendanceStatus='22' ";
                        }
                    }
                }
            }

            where1 += " ) ";

            where += where1;
            String BUType = jsonObj.getString("BUType");
            if (StringUtils.isNotEmpty(BUType) && tutorFlag) {
                where += " and BUType = '" + BUType + "' ";
            }
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
            if (StringUtils.isNotBlank(zone_id) && !"all".equalsIgnoreCase(zone_id)) {
                where += " and zone_id = '" + zone_id + "' ";
            } else {
                //数据级权限受当前登录用户的权限限制。如果有 所有校区 权限，则课显示全部校区课表；否则显示用户有权限的校区的课
                List<String> listZoneId = ZoneUtils.findZoneIdByUser(request, null);
                String zondids = ZoneUtils.changeString(listZoneId);
                if (StringUtils.isNotBlank(zondids)) {
                    where += " and zone_id in (" + zondids + ")";
                }
            }


            where += "  and leaveState = '" + LeaveState.LEAVE_NONE + "' ";

            //String order = "ORDER BY scheduledDate ASC, startTime ASC ";
            String order = "ORDER BY confirmedTime , scheduledDate , startTime desc ";
            String limit = " limit " + (page.getCurrentPage() - 1) * page.getItemsperpage() + " , " + page.getItemsperpage();


            Calendar now = Calendar.getInstance();

            //时区转化
            //获取当前时区的名称
            String timezone_id = JSONUtils.getStr(jsonObj, "timezone_id");
            if (timezone_id == null) {
                timezone_id = "Id_timeZone00000001";
            }
            Timezone timezone = timezoneService.loadById(timezone_id);
            String timezoneName = "";
            if (timezone != null) {
                timezoneName = timezone.getTimezone();
            } else {
                timezoneName = "Asia/Shanghai";
            }

            Boolean timeFlagSH = "Asia/Shanghai".equals(timezoneName);
            String toDay = now.get(Calendar.YEAR) + "-" + String.format("%02d", Integer.valueOf(now.get(Calendar.MONTH) + 1)) + "-" + String.format("%02d", Integer.valueOf(now.get(Calendar.DAY_OF_MONTH))); //当天时间

            //没有填时间默认当天时间
            if (StringUtils.isEmpty(leaveTimeStart) && StringUtils.isEmpty(leaveTimeEnd)) {
                leaveTimeStart = toDay;
                leaveTimeEnd = toDay;
            }
            String sqlDate = startAndEndDateTimezone(leaveTimeStart, leaveTimeEnd, timezoneName, "scheduledDate");

            where += sqlDate;

            Zidingyi zi = new Zidingyi();
            String sql = select + from + where + order + limit;
            String sqlcount = selectCount + from + where;

            zi.setSql(sql);
            List<Map> listMap = tongjiService.query(zi);
            zi.setSql(sqlcount);
            int count = tongjiService.count(zi);
            List<View_attendanceBook> list = new ArrayList<View_attendanceBook>();

            listMapFromBean(listMap, list);

            for (View_attendanceBook vst : list) {

                //更改上课时间
                List startlist = TimezoneUtil.timezoneChange(vst.getScheduledDate(), vst.getStartTime(), timezoneName, "S");
                Date riqiDate = TimezoneUtil.changRiqi(vst.getScheduledDate(), vst.getStartTime(), vst.getEndTime());
                List endList = TimezoneUtil.timezoneChange(riqiDate, vst.getEndTime(), timezoneName, "S");

                String riqi = startlist.get(2).toString();
                Date date = LanDateUtils.stringToDate(riqi);
                vst.setScheduledDate(date);
                vst.setStartTime(startlist.get(1).toString());
                vst.setEndTime(endList.get(1).toString());
                vst.setRiqiEnd(endList.get(2).toString());
            }


            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(count);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 数据封装
     *
     * @param listMap
     * @param list
     */
    private void listMapFromBean(List<Map> listMap, List<View_attendanceBook> list) {
        if (CollectionUtils.isNotEmpty(listMap)) {
            for (Map m : listMap) {
                View_attendanceBook v = new View_attendanceBook();
                BeanTranUtil.transMap2Bean1(m, v);
                list.add(v);
            }
        }
    }

    /**
     * where 时区的时间条件
     *
     * @param startDateLeave
     * @param endDateLeave
     * @param timezoneName
     * @param field
     * @return
     */
    private String startAndEndDateTimezone(String startDateLeave, String endDateLeave, String timezoneName, String field) {
        String sql = " and( ";
        String startDate = "";
        String startTime = "";
        String endDate = "";
        String endTime = "";
        //时区转换
        if (StringUtils.isNotBlank(startDateLeave)) {
            List listStart = TimezoneUtil.timezoneChange(startDateLeave, "00:00", timezoneName, "G");
            startDate = listStart.get(2).toString();
            startTime = listStart.get(1).toString();
        }
        if (StringUtils.isNotBlank(endDateLeave)) {

            List listEnd = TimezoneUtil.timezoneChange(endDateLeave, "23:59", timezoneName, "G");
            endDate = listEnd.get(2).toString();
            endTime = listEnd.get(1).toString();
        }
        //开始时间与结束都有
        if (StringUtils.isNotBlank(startDateLeave) && StringUtils.isNotBlank(endDateLeave)) {
            sql += field + " = '" + startDate + "' and startTime >= '" + startTime + "'";    // scheduledDate = '2017-11-01' AND startTime >= '00:00'

            List<String> days = LanDateUtils.getDays(startDate, endDate);
            if (LanDateUtils.getDays(startDate, endDate).size() > 1) {
                String startDateNext = LanDateUtils.getNext_Day(startDate, 1);
                String endDateNext = LanDateUtils.getNext_Day(endDate, -1);
                sql += " OR  " + field + " >= '" + startDateNext + "' and  " + field + " <= '" + endDateNext + "'"; //or scheduledDate >= '2017-11-02' and scheduledDate <= '2017-11-02'
            }
            sql += " OR " + field + " = '" + endDate + "' and startTime <= '" + endTime + "'"; //OR scheduledDate = '2017-11-03' AND startTime <= '00:00'

        } else if (StringUtils.isNotBlank(startDateLeave)) {
            sql += field + " = '" + startDate + "' and startTime >= '" + startTime + "'";    // scheduledDate = '2017-11-01' AND startTime >= '00:00'
            sql += " OR " + field + " >= '" + LanDateUtils.getNext_Day(startDate, 1) + "' "; //OR scheduledDate > '2017-11-02' '

        } else if (StringUtils.isNotBlank(endDateLeave)) {
            sql += field + " = '" + endDate + "' and startTime <= '" + endTime + "'"; //OR scheduledDate = '2017-11-03' AND startTime <= '00:00'
            sql += " OR " + field + " <= '" + LanDateUtils.getNext_Day(endDate, -1) + "' "; //OR scheduledDate < '2017-11-02' '
        }

        sql += " ) ";
        return sql;
    }


    /**
     * 因为时区问题,所以搜索条的时间转为对应的上海时间
     *
     * @param date
     * @param wc
     * @param timezone
     * @param time
     */
    private void wcAddDateTimezone(String date, WhereCondition wc, String timezone, String time, String field) {

        //把时间转为上海时间
        List list = TimezoneUtil.timezoneChange(date, time, timezone, "G");
        String dateS = list.get(2).toString();
        String timeS = list.get(1).toString();
        if (StringUtils.equals("00:00", time)) {
            wc.andGreaterEquals(field, dateS);
        }
        if (StringUtils.equals("24:00", time)) {
            wc.andLessEquals(field, dateS);
        }
    }

    /**
     * 历史签到列表
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBookHis/list")
    public String attendanceBookHis(ModelMap model, @RequestBody String content,
                                    HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            String select = " SELECT classSchedule_id, courseType, consumedClass, student_id, course_id, scheduledDate, startTime, endTime, time, freeTimeCode, teacher_id, teachingWay, ";
            select += " classroom_id, description, notes, leaveState, isAuto, realDate, realStartTime, realEndTime, realTime, attendanceBook_id, attendanceStatus, ";
            select += " reason, approvalStatus, approver, creator_id, createTime, lastModifier_id, lastModifiedTime, delStatus, contract_id, lockStatus, s_chineseName, ";
            select += " s_englishName, zone_id, t_chineseName, t_englishName, c_name, z_name, cr_name, tutorId, teachingWayName, tutorenglishName, BUType ";
            String selectCount = " select count(1) ";
            String from = " FROM view_attendanceBook ";
            String where = " WHERE 1=1 ";
            // 组装查询条件
            JSONObject jsonObjsearchItems = jsonObj.getJSONObject("searchItems");
            Set set = jsonObjsearchItems.keySet();
            String leaveTimeStart = "";
            String leaveTimeEnd = "";
            String payDate = "";
            for (Object o : set) {
                String key = o.toString();
                String value = jsonObjsearchItems.getString(key);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                if ("scheduledDate_end".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        leaveTimeEnd = value;
                    }
                }
                if ("scheduledDate_start".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        leaveTimeStart = value;
                    }
                }
                if ("approvalStatus_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and approvalStatus = '" + value + "'";
                    }
                }
                if ("s_englishName_lk".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and s_englishName like '%" + value + "%'";
                    }
                }
                if ("t_englishName_lk".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and t_englishName like '%" + value + "%'";
                    }
                }
                if ("attendanceStatus_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and attendanceStatus = '" + value + "'";
                    }
                }
            }

            boolean tutorFlag = true;
            if (!"0".equals(getLogin(request).getUser().getCategory().trim())) {
                List<Permission> li = getLogin(request).getPermissionList();
                for (Permission pm : li) {
                    if ("133".equals(pm.getFunction_id())) {
                        where += " and tutorId = '" + getLogin(request).getUser().getId() + "' ";
                        tutorFlag = false;
                    }
                }
            }
            String BUType = jsonObj.getString("BUType");
            if (StringUtils.isNotEmpty(BUType) && tutorFlag) {
                where += " and BUType = '" + BUType + "' ";
            }
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
            if (StringUtils.isNotBlank(zone_id) && !"all".equalsIgnoreCase(zone_id)) {
                where += " and zone_id = '" + zone_id + "' ";
            }
            String order = "ORDER BY scheduledDate DESC, endTime ASC ";
            String limit = " limit " + (page.getCurrentPage() - 1) * page.getItemsperpage() + " , " + page.getItemsperpage();

            where += " and attendanceStatus IS NOT NULL ";

            //时区转化
            //获取当前时区的名称
            String timezone_id = JSONUtils.getStr(jsonObj, "timezone_id");
            Timezone timezone = timezoneService.loadById(timezone_id);
            String timezoneName = timezone.getTimezone();
            //搜索时间的时区转换

            if (StringUtils.isNotBlank(leaveTimeStart) || StringUtils.isNotBlank(leaveTimeEnd)) {
                String sqlDate = startAndEndDateTimezone(leaveTimeStart, leaveTimeEnd, timezoneName, "scheduledDate");
                where += sqlDate;
            }
            Zidingyi zi = new Zidingyi();
            String sql = select + from + where + order + limit;
            String sqlcount = selectCount + from + where;

            zi.setSql(sql);
            List<Map> listMap = tongjiService.query(zi);
            zi.setSql(sqlcount);
            int count = tongjiService.count(zi);

            List<View_attendanceBook> list = new ArrayList<View_attendanceBook>();
            listMapFromBean(listMap, list);

            for (View_attendanceBook vst : list) {

                //更改上课时间
                List startlist = TimezoneUtil.timezoneChange(vst.getScheduledDate(), vst.getStartTime(), timezone.getTimezone(), "S");
                Date riqiDate = TimezoneUtil.changRiqi(vst.getScheduledDate(), vst.getStartTime(), vst.getEndTime());
                List endList = TimezoneUtil.timezoneChange(riqiDate, vst.getEndTime(), timezone.getTimezone(), "S");

                String riqi = startlist.get(2).toString();
                Date date = LanDateUtils.stringToDate(riqi);
                vst.setScheduledDate(date);
                vst.setStartTime(startlist.get(1).toString());
                vst.setEndTime(endList.get(1).toString());
                vst.setRiqiEnd(endList.get(2).toString());
            }


            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(count);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/list/id")
    public String executesd1(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            System.out.println("-----------" + JSONUtils.getStr(jsonObj, "id"));
            View_attendanceBook k = view_attendanceBookService.loadById(JSONUtils.getStr(jsonObj, "id"));
            //获取老师状态 全职 兼职
            User teacher = userService.loadById(k.getTeacher_id());
            k.setTeacherType(teacher.getType());
            WhereCondition wc = new WhereCondition();
            wc.andEquals("classSchedule_id", k.getClassSchedule_id());
            List<Consumption_teacher> li = consumption_teacherService.query(wc);
            if (!li.isEmpty()) {
                k.setWage(li.get(0).getWage() + "");
                k.setWageDeduction(li.get(0).getWageDeduction() + "");
                String wageDeduction2 = li.get(0).getWageDeduction2() + "";
                String currency_id = li.get(0).getCurrency_id();
                if (StringUtils.equals("1", k.getTeacherType()) && StringUtils.isNotBlank(currency_id)) {
                    //全职老师倒扣工资是金额所以有币别
                    Currency currency = currencyService.loadById(currency_id);
                    wageDeduction2 += "(" + currency.getCurrencyCode() + ")";
                }
                k.setWageDeduction2(wageDeduction2);
            }
            List<Consumption_student> li1 = consumption_studentService.query(wc);
            if (!li1.isEmpty()) {
                k.setGivenClass(li1.get(0).getGivenClass() + "");
                k.setConsumedClass(li1.get(0).getConsumedClass() + "");
            }
            JSONObject jo = JSONObject.fromObject(k, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/qiandaoguanli/view_attendanceBook/list/lockData")
    public String executeByLockData(ModelMap model, @RequestBody String content,
                                    HttpServletRequest request, HttpServletResponse response) {
        try {
            WhereCondition wc = new WhereCondition();
            wc.andEquals("delStatus", "0");
            List<LockData> li = lockDataService.query(wc);
            JSONObject jo = JSONObject.fromObject(li.get(0), jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }
}
