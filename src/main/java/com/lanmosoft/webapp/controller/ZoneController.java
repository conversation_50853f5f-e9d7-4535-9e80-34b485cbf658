/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.View_ZonePermission;
import com.lanmosoft.dao.model.View_zone;
import com.lanmosoft.dao.model.Zone;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.ZoneService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.PinyinUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class ZoneController extends BaseController {

	@Autowired
	ZoneService zoneService;
	
	@RequestMapping(value = "/ngres/jichushuju/zone/findAll")
	public String executeww(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
			try {
				JSONObject jsonObj = JSONObject.fromObject(content);
				String city_id = JSONUtils.getStr(jsonObj, "city_id");
				WhereCondition wc=new WhereCondition();
				wc.andEquals("city_id", city_id);
				List<Zone> query = new ArrayList<Zone>();


				LoginModel login = getLogin(request);
				if(!"0".equals(getLogin(request).getUser().getCategory().trim())){
					String flag = login.getUser().getFlag();//判断是否是所有校区权限 1是所有校区
					if("0".equals(flag)){//权限校区对应的城市
						List<View_ZonePermission> zonePermissionList = login.getZonePermissionList();
						if(!zonePermissionList.isEmpty()){
							for (View_ZonePermission z: zonePermissionList  ) {

								if(StringUtils.isBlank(city_id)){
									Zone zone = zoneService.loadById(z.getZone_id());
									query.add(zone);
								}else{
									if(city_id.equalsIgnoreCase(z.getCity_id())){
										Zone zone = zoneService.loadById(z.getZone_id());
										query.add(zone);
									}
								}
							}
						}
					}else { //所有校区
						query = 	zoneService.query(wc);
					}
				}else {//管理员权限
					query = zoneService.query(wc);
				}
				
				Map map=new HashMap();
				map.put("list",query);
				String string = JSONObject.fromObject(map, jsonConfig).toString();
				AjaxUtils.ajaxJson(response, string);
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
				e.printStackTrace();
			}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/zone/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			LoginModel login = (LoginModel)request.getSession().getAttribute("login");
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			Zone p = new Zone();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				p.setLastModifier_id(login.getUser().getId());
				p.setLastModifiedTime(DateUtils.getNowDate());
				p.setCode(PinyinUtil.getTopSpellingUpCase(p.getName()));
				zoneService.update(p);
				context+="修改了基础数据校区设置，修改校区"+p.getId();
			} else {
				String id = sequenceManager.generateId("zone");
				p.setId(id);
				p.setCode(PinyinUtil.getTopSpellingUpCase(p.getName()));
				initCreate2(p, request);
				zoneService.insert(p);
				context+="新增了基础数据校区设置，新增校区"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/zone/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Zone> list = new ArrayList<Zone>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Zone p = new Zone();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Zone p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			zoneService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据校区："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/zone/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Zone> list = new ArrayList<Zone>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Zone p = new Zone();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Zone p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			zoneService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据校区："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/zone/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = zoneService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(zoneService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/jichushuju/zone/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Zone k =  zoneService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		
	@RequestMapping(value = "/ngres/jichushuju/zone/getZoneInfo")
	public String getZoneInfo(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			List<View_zone> zoneList = zoneService.getZoneInfo(null);
			model.put("zoneList", zoneList);
			String s = JSONObject.fromObject(model,jsonConfig).toString();
			AjaxUtils.ajaxJson(response, s);
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/zone/findAllZoneid")
	public String findAllZoneid(ModelMap model, @RequestBody String content,
								HttpServletRequest request, HttpServletResponse response) {
		try {
			WhereCondition wc = new WhereCondition();
			List list = zoneService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			Map map = new HashMap();
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
}
