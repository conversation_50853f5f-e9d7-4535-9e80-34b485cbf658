/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.lanmosoft.dao.model.Requirement;
import com.lanmosoft.dao.model.Student_course;
import com.lanmosoft.dao.model.Student;
import com.lanmosoft.dao.model.Zidingyi;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.RequirementService;
import com.lanmosoft.service.biz.Student_courseService;
import com.lanmosoft.service.biz.StudentService;
import com.lanmosoft.service.biz.TongjiService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.LanDateUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class RequirementController extends BaseController {

	@Autowired
	RequirementService requirementService;
	@Autowired
	Student_courseService student_courseService;
	@Autowired
	StudentService studentService;
	@Autowired
	TongjiService tongjiService;

	@RequestMapping(value = "/ngres/xueshengguanli/requirement/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			Requirement p = new Requirement();
			JSONObject.toBean(job,p,jsonConfig);
			
			List<Requirement> querycheck = requirementService.querycheck(p);
			if(querycheck.size()!=0){
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0016");
			}else{
				String context=getLogin(request).getUser().getUserName();
				if (StringUtils.isNotEmpty(p.getId())) {
					p=getRequirement(p);
					requirementService.update(p);
					context+="修改了学生诉求设置，修改学生诉求"+p.getId();
				} else {
					p=getRequirement(p);
					String id = sequenceManager.generateId("requirement");
					p.setId(id);
					initCreate2(p, request);
					requirementService.insert(p);
					context+="新增了学生诉求设置，新增学生诉求"+p.getId();
				}
				operationLog.setId(sequenceManager.generateId("operationLog"));
				operationLog.setUser_id(getLogin(request).getUser().getId());
				operationLog.setCreateTime(new Date());
				operationLog.setContent(context);
				operationLogService.insert(operationLog);
				AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
			}
			
			/*String string = JSONObject.fromObject(p).toString();
			System.out.println(string);*/
			
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}
	
	private Requirement getRequirement(Requirement p){
		if(p.getDayLowerLimit()==null){
			p.setDayLowerLimit(0d);
		}
		if(p.getDayUpperLimit()==null){
			p.setDayUpperLimit(0d);
		}
		if(p.getMonthLowerLimit()==null){
			p.setMonthLowerLimit(0d);
		}
		if(p.getMonthUpperLimit()==null){
			p.setMonthUpperLimit(0d);
		}
		if(p.getWeekLowerLimit()==null){
			p.setWeekLowerLimit(0d);
		}
		if(p.getWeekUpperLimit()==null){
			p.setWeekUpperLimit(0d);
		}
		if(p.getTotalLowerLimit()==null){
			p.setTotalLowerLimit(0d);
		}
		if(p.getTotalUpperLimit()==null){
			p.setTotalUpperLimit(0d);
		}
		if(p.getIntervalDay()==null){
			p.setIntervalDay(0);
		}
		return p;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/requirement/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Requirement> list = new ArrayList<Requirement>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Requirement p = new Requirement();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Requirement p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			requirementService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生诉求："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/xueshengguanli/requirement/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Requirement> list = new ArrayList<Requirement>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Requirement p = new Requirement();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Requirement p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			requirementService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生诉求："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/requirement/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = requirementService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(requirementService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/xueshengguanli/requirement/list1")
	public String executeByTeacher(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			String tid=JSONUtils.getStr(jsonObj, "teacher_id");
			List<Requirement> list=new ArrayList<Requirement>();
			Zidingyi z=new Zidingyi();
			String sql="select * from requirement a,course b ,student d where a.student_id=d.id and a.course_id=b.id and a.student_id in(select c.student_id from student_course c where c.teacher_id='"+tid+"')";
			String sql1="select count(1) from requirement a,course b ,student d where a.student_id=d.id and a.course_id=b.id and a.student_id in(select c.student_id from student_course c where c.teacher_id='"+tid+"')";
			if(!sql.contains("limit")){
				sql=sql+" limit "+(page.getCurrentPage()-1)*page.getItemsperpage()+ ","+page.getItemsperpage();
			}
			z.setSql(sql);
			List<Map> li=tongjiService.query(z);
            for(Map m:li){
            	Requirement r=new Requirement();
            	r.setCourse_id(m.get("course_id").toString());
            	r.setId(m.get("id").toString());
            	r.setDayLowerLimit(Double.valueOf(m.get("dayLowerLimit")!=null?m.get("dayLowerLimit").toString():"0"));
            	r.setDayUpperLimit(Double.valueOf(m.get("dayUpperLimit")!=null?m.get("dayUpperLimit").toString():"0"));
            	r.setIntervalDay(Integer.valueOf(m.get("intervalDay")!=null?m.get("intervalDay").toString():"0"));
            	r.setMonthLowerLimit(Double.valueOf(m.get("monthLowerLimit")!=null?m.get("monthLowerLimit").toString():"0"));
            	r.setMonthUpperLimit(Double.valueOf(m.get("monthUpperLimit")!=null?m.get("monthUpperLimit").toString():"0"));
            	r.setEndDate(LanDateUtils.stringToDate(m.get("endDate").toString()));
            	r.setStartDate(LanDateUtils.stringToDate(m.get("startDate").toString()));
            	r.setStudent_id(m.get("student_id").toString());
            	r.setStudentName(m.get("chineseName")!=null?m.get("chineseName").toString():"");
            	r.setTotalLowerLimit(Double.valueOf(m.get("totalLowerLimit")!=null?m.get("totalLowerLimit").toString():"0"));
            	r.setTotalUpperLimit(Double.valueOf(m.get("totalUpperLimit")!=null?m.get("totalUpperLimit").toString():"0"));
            	r.setWeekLowerLimit(Double.valueOf(m.get("weekLowerLimit")!=null?m.get("weekLowerLimit").toString():"0"));
            	r.setWeekUpperLimit(Double.valueOf(m.get("weekUpperLimit")!=null?m.get("weekUpperLimit").toString():"0"));
            	r.setCourseName(m.get("name")!=null?m.get("name").toString():"");
            	list.add(r);
            }
            z.setSql(sql1);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(tongjiService.count(z));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
		@RequestMapping(value = "/ngres/xueshengguanli/requirement/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Requirement k =  requirementService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
}
