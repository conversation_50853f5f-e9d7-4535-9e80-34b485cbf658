package com.lanmosoft.webapp.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.lanmosoft.dao.model.Student;
import com.lanmosoft.dao.model.View_coures_teacher;
import com.lanmosoft.dao.model.View_student_paike;
import com.lanmosoft.dao.model.Teacher_course;
import com.lanmosoft.dao.model.Zone;
import com.lanmosoft.dao.model.Teacher_zone;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.StudentService;
import com.lanmosoft.service.biz.View_coures_teacherService;
import com.lanmosoft.service.biz.View_student_paikeService;
import com.lanmosoft.service.biz.Teacher_courseService;
import com.lanmosoft.service.biz.ZoneService;
import com.lanmosoft.service.biz.Teacher_zoneService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.LanDateUtils;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class View_teacher_paikeController extends BaseController {
	
	@Autowired
	View_student_paikeService view_student_paikeService;
	@Autowired
	StudentService  studentService;
	@Autowired
	View_coures_teacherService  view_coures_teacherService;//科目和级别
	@Autowired
	Teacher_courseService teacher_courseService;
	@Autowired
	ZoneService zoneService;
	@Autowired
	Teacher_zoneService teacher_zoneService;

	@RequestMapping(value = "/ngres/jiaoshiguanli/view_teacher_paike/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			View_student_paike p = new View_student_paike();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				view_student_paikeService.update(p);
				context+="修改了学生课表设置，修改学生课表"+p.getId();
			} else {
				String id = sequenceManager.generateId("view_student_paike");
				p.setId(id);
				initCreate2(p, request);
				view_student_paikeService.insert(p);
				context+="新增了学生课表设置，新增学生课表"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/view_teacher_paike/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<View_student_paike> list = new ArrayList<View_student_paike>();
			for (int i = 0; i < jsonArray.size(); i++) {
				View_student_paike p = new View_student_paike();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (View_student_paike p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			view_student_paikeService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/view_teacher_paike/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<View_student_paike> list = new ArrayList<View_student_paike>();
			for (int i = 0; i < jsonArray.size(); i++) {
				View_student_paike p = new View_student_paike();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (View_student_paike p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			view_student_paikeService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/view_teacher_paike/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			String string = jsonObj.getString("id");
			WhereCondition wc=new WhereCondition();
			wc.andEquals("teacher_id", string);
			wc.andEquals("status","1");
			wc.setOrderBy("student_id asc");
			List<View_coures_teacher> query = view_coures_teacherService.query(wc);
			List<View_student_paike> list=new  ArrayList<View_student_paike>();	
			List<View_student_paike> list2=new  ArrayList<View_student_paike>();	
			//判断 选择当前老师的学生
			for (View_coures_teacher view_coures_teacher : query){
				View_student_paike vct=new View_student_paike();
				Date date=new Date();
				if(view_coures_teacher.getEndDate().getTime()<=LanDateUtils.getNext_Day(date, -1).getTime()){
					vct.setChecked(false);
				}else{
					vct.setChecked(true);
				}
				//vct.setAbbr("true");
				Student stu = studentService.loadById(view_coures_teacher.getStudent_id());
				vct.setStudent_id(view_coures_teacher.getStudent_id());
				vct.setChineseName(stu.getChineseName());
				vct.setEnglishName(stu.getEnglishName());
				vct.setCourse_name(view_coures_teacher.getCourse_name());
				vct.setCourse_id(view_coures_teacher.getCourse_id());
				vct.setGrade_id(view_coures_teacher.getGrade_id());
				vct.setGradeCategory_id(view_coures_teacher.getGradeCategory_id());
				vct.setTeacher_id(view_coures_teacher.getTeacher_id());
				vct.setZone_id(stu.getZone_id());  
				Zone z=zoneService.loadById(stu.getZone_id());
				vct.setZone_name(z.getName());
				list.add(vct);
			}
		    wc=new WhereCondition();
			wc.andEquals("teacher_id", string);
			List<Teacher_course> query1=teacher_courseService.query(wc);
			wc=new WhereCondition();
			wc.andEquals("teacher_id", string);
			List<Teacher_zone> listT=teacher_zoneService.query(wc);
			String zoneS="";
			for(Teacher_zone t:listT){
				zoneS+=t.getZone_id()+",";
			}
			for(Teacher_course tc:query1){
				wc=new WhereCondition();
				wc.andEquals("course_id", tc.getCourse_id());
				wc.andNotEquals("teacher_id", string);
				wc.setOrderBy("student_id asc");
				List<View_coures_teacher> list1 = view_coures_teacherService.query(wc);
				for(View_coures_teacher view_coures_teacher:list1){
					Student stu = studentService.loadById(view_coures_teacher.getStudent_id());
					if(zoneS.contains(stu.getZone_id())){
						View_student_paike vct=new View_student_paike();
						//vct.setAbbr("true");
						vct.setChecked(false);
						vct.setStudent_id(view_coures_teacher.getStudent_id());
						vct.setChineseName(stu.getChineseName());
						vct.setEnglishName(stu.getEnglishName());
						vct.setCourse_name(view_coures_teacher.getCourse_name());
						vct.setCourse_id(view_coures_teacher.getCourse_id());
						vct.setGrade_id(view_coures_teacher.getGrade_id());
						vct.setGradeCategory_id(view_coures_teacher.getGradeCategory_id());
						vct.setTeacher_id(view_coures_teacher.getTeacher_id());
						vct.setZone_id(stu.getZone_id());  
						Zone z=zoneService.loadById(stu.getZone_id());
						vct.setZone_name(z.getName());
						list2.add(vct);
					}
				}
			}
			System.out.println("0-------"+list.size());
			Map<String,String> map1 = new HashMap<String,String>();
			for(int i= list.size()-1;i>=0;i--){
				if(map1.get(list.get(i).getStudent_id()+"-"+list.get(i).getCourse_id())!=null){
					list.remove(i);
					continue;
				}
				map1.put(list.get(i).getStudent_id()+"-"+list.get(i).getCourse_id(), "1");
		    }
			map1.clear();
			System.out.println("1-------"+list.size());
			System.out.println("2-------"+list2.size());
			for(int i= list2.size()-1;i>=0;i--){
				if(map1.get(list2.get(i).getStudent_id()+"-"+list2.get(i).getCourse_id())!=null){
					list2.remove(i);
					continue;
				}
				map1.put(list2.get(i).getStudent_id()+"-"+list2.get(i).getCourse_id(), "1");
		    }
			System.out.println("3-------"+list2.size());
			JSONArray ja1 = JSONArray.fromObject(list,jsonConfig);
			JSONArray ja2 = JSONArray.fromObject(list2,jsonConfig);
			Map map = new HashMap();
			map.put("list", ja1);
			map.put("list2", ja2);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/view_teacher_paike/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			View_student_paike k =  view_student_paikeService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/view_teacher_paike/getWeekCount")
	public String executeWeekCount(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			String toDay = jsonObj.getString("toDay");
			String count=LanDateUtils.getWeekCount(toDay);
			System.out.println("toDay==="+toDay);
			System.out.println("count==="+count);
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("count", count);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s);
		} catch (Exception e) {//TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

}
