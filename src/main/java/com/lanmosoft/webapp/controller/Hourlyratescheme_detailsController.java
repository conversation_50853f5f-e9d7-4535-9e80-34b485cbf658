/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Hourlyratescheme_details;
import com.lanmosoft.dao.model.Teacher_course_teachingway;
import com.lanmosoft.dao.model.Teacher_hourlyratescheme;
import com.lanmosoft.dao.model.Zidingyi;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.Hourlyratescheme_detailsService;
import com.lanmosoft.service.biz.Teacher_course_teachingwayService;
import com.lanmosoft.service.biz.Teacher_hourlyrateschemeService;
import com.lanmosoft.service.biz.TongjiService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.LanDateUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class Hourlyratescheme_detailsController extends BaseController {

	@Autowired
	Hourlyratescheme_detailsService hourlyratescheme_detailsService;
	@Autowired
	Teacher_course_teachingwayService teacher_course_teachingwayService;
	@Autowired
	Teacher_hourlyrateschemeService teacher_hourlyrateschemeService;
	@Autowired
	TongjiService tongjiService;

	@RequestMapping(value = "/ngres/jiaoshiguanli/hourlyratescheme_details/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");

			JSONArray teachingWay_ids=job.getJSONArray("teachingWay_ids"); //上课方式集合
			List<String> teachingWay_idsList=new ArrayList<String>();
			String teachingWayids = "";
			if(teachingWay_ids!=null){
				for(int a=0;a<teachingWay_ids.size();a++){
					String teachingWayid=(String) teachingWay_ids.get(a);
					teachingWay_idsList.add(teachingWayid);
					teachingWayids += "'"+teachingWayid+"',";
				}
				teachingWayids = teachingWayids.substring(0,teachingWayids.length()-1);//去除逗号
				
			}
			String periodLowerLimit = JSONUtils.getStr(job,"periodLowerLimit");  //下限
			String periodUpperLimit = JSONUtils.getStr(job,"periodUpperLimit");  //上限

			//封装Hourlyratescheme_details
			String context=getLogin(request).getUser().getUserName();
			Hourlyratescheme_details p = new Hourlyratescheme_details();
			JSONObject.toBean(job,p,jsonConfig);
			if(p.getPeriodUpperLimit()==0){
				p.setPeriodUpperLimit(999);
			}
			if(p.getPeriodLowerLimit()>p.getPeriodUpperLimit()){
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");//下限大于上限
				return null;
			}
			//封装Teacher_course_teachingway
			Teacher_course_teachingway tctp=new Teacher_course_teachingway();
			JSONObject.toBean(job,tctp,jsonConfig);
			//封装Teacher_hourlyratescheme
			Teacher_hourlyratescheme th=new Teacher_hourlyratescheme();
			JSONObject.toBean(job,th,jsonConfig);
			th.setId(JSONUtils.getStr(job,"scheme_id"));


			Boolean returnFlag=false;

			//如果SchemeNo没有就创建teacher_hourlyratescheme
			if(!(StringUtils.isNotEmpty(th.getSchemeNo()))){
				returnFlag=true;
				WhereCondition wc = new WhereCondition();
				String teacher_id=th.getTeacher_id();
				wc.andEquals("teacher_id", teacher_id);
				wc.andEquals("BUType", th.getBUType());
				String maxSchemeNo=teacher_hourlyrateschemeService.getMaxSchemeNo(wc);
				int maxSchemeNoInt=0;
				if(maxSchemeNo!=null){
					maxSchemeNoInt=Integer.valueOf(maxSchemeNo.split("-")[1]);
					maxSchemeNoInt+=1;
				}
				String schemeNo="OFF-"+maxSchemeNoInt;
				String schemeType=th.getBUType();
				if("2".equals(schemeType)){
					schemeNo="ON-"+maxSchemeNoInt;
				}
				th.setSchemeNo(schemeNo);
				String thId = sequenceManager.generateId("teacher_hourlyratescheme");
				th.setId(thId);
				teacher_hourlyrateschemeService.insert(th);
				p.setScheme_id(thId);
			}
			String scheme_id = JSONUtils.getStr(job,"scheme_id");
			scheme_id = scheme_id == null ? th.getId() : scheme_id;
			String startDateItem = JSONUtils.getStr(job,"startDate");
			String endDateItem = JSONUtils.getStr(job,"endDate");

			Zidingyi zidingyi = new Zidingyi();

			if (StringUtils.isNotEmpty(p.getId())) {//更新hourlyratescheme_details

				hourlyratescheme_detailsService.update(p);
				//teachingWayId有多个，需要先删除所有teachingwayID,然后再更新
				WhereCondition wc = new WhereCondition();
				String schemeDetails_id=p.getId();
				wc.andEquals("schemeDetails_id", schemeDetails_id);
				teacher_course_teachingwayService.deleteByCondition(wc);
				for(String teachingWay_id:teachingWay_idsList){
					tctp.setTeachingWay_id(teachingWay_id);
					tctp.setSchemeDetails_id(schemeDetails_id);
					String tctpId = sequenceManager.generateId("teacher_course_teachingway");
					tctp.setId(tctpId);
					teacher_course_teachingwayService.insert(tctp);
				}

				context+="修改了课时费详情设置，修改课时费详情"+p.getId();

			}else {//创建hourlyratescheme_details

				//、判断上限和下限的问题
				//获取更新时间的数据,判断是否有值
				for (String teachingWay_id:teachingWay_idsList) {

					String currentDateSql = "";
					currentDateSql += " SELECT  hd.id, hd.periodLowerLimit, hd.periodUpperLimit, hd.unitPrice, hd.startDate, hd.endDate, tct.id tct_id, tct.teachingWay_id ";
					currentDateSql += " FROM hourlyratescheme_details hd LEFT JOIN teacher_course_teachingway tct ON tct.schemeDetails_id = hd.id ";
					currentDateSql += " WHERE tct.teachingWay_id = '"+teachingWay_id+"'";
					currentDateSql += " and  hd.scheme_id = '"+scheme_id+"' and hd.startDate = '"+startDateItem+"' ";
					currentDateSql += " and hd.id != '"+p.getId()+"'  ";
					currentDateSql += " ORDER BY hd.periodUpperLimit desc ";
					zidingyi.setSql(currentDateSql);
					List<Map> currentDateList = tongjiService.query(zidingyi);//获取当前日期的数据
					//如果没有数据,下限的值必须是1开始的
					if(currentDateList.isEmpty() && !"1".equals(periodLowerLimit)){
						AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0066");//单日课程下限只能从1开始
						return null;
					}
					if(!currentDateList.isEmpty()){
						Integer periodUpperLimitCD  = currentDateList.get(0).get("periodUpperLimit") == null? null : Integer.valueOf(currentDateList.get(0).get("periodUpperLimit").toString());
						String periodUpperLimitstr = String.valueOf(periodUpperLimitCD+1);
						if(!periodUpperLimitstr.equals(periodLowerLimit)){
							AjaxUtils.ajaxJsonErrorMessage(response, "table_filed_00604");//区间有间隔
							return null;
						}
					}



					String startDateSql;
					if(StringUtils.isNotEmpty(p.getId())){
						Hourlyratescheme_details hd = hourlyratescheme_detailsService.loadById(p.getId());
						startDateSql = LanDateUtils.format(hd.getStartDate(),"yyyy-MM-dd");
					}else {
						startDateSql = startDateItem;
					}
					int periodLowerLimit1 = p.getPeriodLowerLimit();
					int periodUpperLimit1 = p.getPeriodUpperLimit();

					//自动添加上一次课时费失效的时间
					Zidingyi zShixiao = new Zidingyi();
					String sqlShixiao = " SELECT DISTINCT hd.* FROM hourlyratescheme_details hd LEFT JOIN teacher_course_teachingway tct ON tct.schemeDetails_id = hd.id ";
					sqlShixiao += " WHERE hd.scheme_id = '"+th.getId()+"' ";
					sqlShixiao += " AND tct.teachingWay_id = '"+teachingWay_id+"' ";
					sqlShixiao += "  AND (  ";
					sqlShixiao += "   		( "+periodLowerLimit1+" <= hd.periodLowerLimit AND hd.periodUpperLimit <= "+periodUpperLimit1+" ) ";
					sqlShixiao += "   		OR ( hd.periodLowerLimit < "+periodUpperLimit1+" AND hd.periodUpperLimit > "+periodUpperLimit1+" ) ";
					sqlShixiao += "  		OR ( hd.periodLowerLimit < "+periodLowerLimit1+" AND hd.periodUpperLimit > "+periodLowerLimit1+" )  ";
					sqlShixiao += "  ) ";
					sqlShixiao += " AND hd.startDate = ( ";
					sqlShixiao += "   SELECT hd1.startDate FROM hourlyratescheme_details hd1 LEFT JOIN teacher_course_teachingway tct1 ON tct1.schemeDetails_id = hd1.id ";
					sqlShixiao += "   WHERE hd1.scheme_id = '"+th.getId()+"' ";
					sqlShixiao += " AND tct1.teachingWay_id = '"+teachingWay_id+"' ";
					sqlShixiao += "   and hd1.startDate < '"+startDateSql+"' ";
					sqlShixiao += "  AND (  ";
					sqlShixiao += "   		( "+periodLowerLimit1+" <= hd1.periodLowerLimit AND hd1.periodUpperLimit <= "+periodUpperLimit1+" ) ";
					sqlShixiao += "   		OR ( hd1.periodLowerLimit < "+periodUpperLimit1+" AND hd1.periodUpperLimit > "+periodUpperLimit1+" ) ";
					sqlShixiao += "  		OR ( hd1.periodLowerLimit < "+periodLowerLimit1+" AND hd1.periodUpperLimit > "+periodLowerLimit1+" )  ";
					sqlShixiao += "  ) ";
					sqlShixiao += "   ORDER BY hd1.startDate DESC LIMIT 0,1 ";
					sqlShixiao += " ) ";
					zShixiao.setSql(sqlShixiao);
					List<Map> listShixiao = tongjiService.query(zShixiao);

					if(!listShixiao.isEmpty()){
						for ( Map<String,Object> map:listShixiao) {
							Hourlyratescheme_details hd = new Hourlyratescheme_details();
							String idM =map.get("id") == null ? null: map.get("id").toString();
							String scheme_idM =map.get("scheme_id") == null ? null:map.get("scheme_id").toString();
							String intervalTypeM =map.get("intervalType") == null ? null:map.get("intervalType").toString();
							int periodLowerLimitM = map.get("periodLowerLimit") == null ? null:Integer.parseInt(map.get("periodLowerLimit").toString());
							int periodUpperLimitM = map.get("periodUpperLimit") == null ? null:Integer.parseInt(map.get("periodUpperLimit").toString());
							Double unitPriceM= map.get("unitPrice") == null ? null :Double.valueOf(map.get("unitPrice").toString());
							String currency_idM =map.get("currency_id") == null ? null:map.get("currency_id").toString();
							Date startDateM =map.get("startDate") == null ?null: LanDateUtils.stringToDate(map.get("startDate").toString());
							Date endDateM = map.get("endDate") == null ? null :LanDateUtils.stringToDate(map.get("endDate").toString());//存在的时间
							Date endDate = LanDateUtils.stringToDate(startDateItem);

							if(endDateM != null ){
								if(endDate.getTime() == endDateM.getTime()){
									continue;
								}else{
									AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0024");//时间有间隔
									return null;
								}
							}
						}
					}
				}


				//、上课方式问题
				List<Hourlyratescheme_details> listh = hourlyratescheme_detailsService.querycheckhd(p);//获取所有以前的数据
				if(!listh.isEmpty()){
					for(Hourlyratescheme_details hd:listh){
						String id=hd.getId();
						WhereCondition wct = new WhereCondition();
						wct.andEquals("schemeDetails_id",id);
						wct.andIn("teachingWay_id",teachingWay_idsList);
						List<Teacher_course_teachingway> tctList = teacher_course_teachingwayService.query(wct);
						if(!tctList.isEmpty()){
							AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0086");//课时费上课方式重复
							return null;
						}

					}
				}

				String id = sequenceManager.generateId("hourlyratescheme_details");
				p.setId(id);
				hourlyratescheme_detailsService.insert(p);
				tctp.setSchemeDetails_id(id);
				for(String teachingWay_id:teachingWay_idsList){
					tctp.setTeachingWay_id(teachingWay_id);

					String tctpId = sequenceManager.generateId("teacher_course_teachingway");
					tctp.setId(tctpId);
					teacher_course_teachingwayService.insert(tctp);

				}
				context+="新增了课时费详情设置，新增课时费详情"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			JSONObject jo= JSONObject.fromObject(th,jsonConfig);

			countEndDate(scheme_id);
			if(returnFlag){
				AjaxUtils.ajaxJson(response, jo.toString());
			}
			else{
				AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");//添加成功
			}

		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");//添加失败
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/hourlyratescheme_details/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			JSONObject job = (JSONObject) jsonArray.get(0);
			String[]  teachingWay_ids = JSONUtils.getStr(job, "teachingWay_ids").split(",");
			String scheme_id = JSONUtils.getStr(job,"scheme_id");
			String startDateItem = JSONUtils.getStr(job,"startDate");
			String endDateItem = JSONUtils.getStr(job,"endDate");
			String id = JSONUtils.getStr(job,"id");
			//封装上课方式
			List<String> teachingWay_idsList=new ArrayList<String>();
			String teachingWayids = "";
			if(teachingWay_ids!=null){
				for(int a=0;a<teachingWay_ids.length;a++){
					String teachingWayid=(String) teachingWay_ids[a];
					teachingWayids += "'"+teachingWayid+"',";
					teachingWay_idsList.add(teachingWayid);
				}
				teachingWayids = teachingWayids.substring(0,teachingWayids.length()-1);//去除逗号
			}

			//一判断上限下限问题
			String periodLowerLimit = JSONUtils.getStr(job,"periodLowerLimit");  //下限
			String periodUpperLimit = JSONUtils.getStr(job,"periodUpperLimit");  //上限

			Zidingyi zidingyi = new Zidingyi();
			String sqlBound = "  ";
			sqlBound += " SELECT DISTINCT hd.*  ";
			sqlBound += " FROM hourlyratescheme_details hd LEFT JOIN teacher_course_teachingway tct ON tct.schemeDetails_id = hd.id ";
			sqlBound += " where hd.scheme_id = '"+scheme_id+"' ";
			sqlBound += " AND hd.startDate = '"+startDateItem+"' and tct.teachingWay_id in ("+teachingWayids+") ";
			zidingyi.setSql(sqlBound);
			List<Map> listBound = tongjiService.query(zidingyi);
			if(!listBound.isEmpty() && listBound.size() > 1){
				if("1".equals(periodLowerLimit)){
					AjaxUtils.ajaxJsonErrorMessage(response, "new_msg_0001");//请删除区间最大的数据
					return null;
				}
			}else if(!listBound.isEmpty() && listBound.size() == 1){
				String count = count(scheme_id, startDateItem,teachingWayids, periodLowerLimit, teachingWay_idsList, id, response);
				if (count == null){
					return null;
				}
			}




			List<Hourlyratescheme_details> list = new ArrayList<Hourlyratescheme_details>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Hourlyratescheme_details p = new Hourlyratescheme_details();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Hourlyratescheme_details p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			
			List<String> idsh = new ArrayList<String>();
			List<Hourlyratescheme_details> listh = hourlyratescheme_detailsService.query(wc);
			for (Hourlyratescheme_details p : listh) {
				idsh.add(p.getId());
			}
			WhereCondition wc2 = new WhereCondition();
			wc2.andIn("schemeDetails_id", idsh);
			teacher_course_teachingwayService.deleteByCondition(wc2);
			hourlyratescheme_detailsService.deleteByCondition(wc);
			countEndDate(scheme_id);//重新计算失效时间
			String context=getLogin(request).getUser().getUserName()+"删除了课时费详情："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 判断
	 * @param scheme_id
	 * @param startDateItem
	 * @param teachingWayids
	 * @param periodLowerLimit
	 * @param teachingWay_idsList
	 * @param id
	 * @param response
	 * @return
	 */
	private  String count(String scheme_id,String startDateItem,String teachingWayids ,String periodLowerLimit ,List<String > teachingWay_idsList , String id ,HttpServletResponse response){

		Zidingyi zidingyi = new Zidingyi();
		List<Date> listDate = new ArrayList<Date>();
		//判断上课方式和有效时间的冲突问题
		for (String teachingWayIdSrc:teachingWay_idsList) {
			//通过删掉的行,去找上一个失效日期为删除行的有效日期的上课方式,通过上课方式去找下一个有效日期(不包括删除行),判断如果所有的上课方式对应的有效日期一致,可以删除,否则不能
			String sqlLower = "  ";
			sqlLower += " SELECT hd1.id,tct1.teachingWay_id,hd1.startDate,hd1.endDate ";
			sqlLower += " FROM hourlyratescheme_details hd1 LEFT JOIN teacher_course_teachingway tct1 ON tct1.schemeDetails_id = hd1.id ";
			sqlLower += " WHERE hd1.id = ( ";
			sqlLower += " 	SELECT hd.id FROM ";
			sqlLower += " 	hourlyratescheme_details hd LEFT JOIN teacher_course_teachingway tct ON tct.schemeDetails_id = hd.id ";
			sqlLower += "  	WHERE hd.scheme_id = '"+scheme_id+"' AND hd.endDate = '"+startDateItem+"' ";
			sqlLower += "  	AND tct.teachingWay_id = '"+teachingWayIdSrc+"' AND hd.periodLowerLimit = '1' ";
			sqlLower += " ) ";
			zidingyi.setSql(sqlLower);
			List<Map> listLower = tongjiService.query(zidingyi);
			if(!listLower.isEmpty()){
				for (Map mapLower :listLower) {
					String teachingWay_idLower = mapLower.get("teachingWay_id") == null ? null : mapLower.get("teachingWay_id").toString();
					String startDateLower = mapLower.get("startDate") == null ? null : mapLower.get("startDate").toString();
					//获取上课方式的下一个有效时间,(不包括删除行)
					String sqlUpper = " ";
					sqlUpper += " SELECT hd.id, tct.teachingWay_id, hd.startDate, hd.endDate ";
					sqlUpper += " FROM hourlyratescheme_details hd LEFT JOIN teacher_course_teachingway tct ON tct.schemeDetails_id = hd.id ";
					sqlUpper += " WHERE hd.scheme_id = '"+scheme_id+"' AND hd.periodLowerLimit = '1' ";
					sqlUpper += " and tct.teachingWay_id = '"+teachingWay_idLower+"' and hd.startDate > '"+startDateLower+"' and hd.id != '"+id+"' ";
					sqlUpper += " ORDER BY startDate LIMIT 0,1 ";
					zidingyi.setSql(sqlUpper);
					List<Map> listUpper = tongjiService.query(zidingyi);
					if(!listUpper.isEmpty()){
						//获取到下一个生效时间
						String  startDate = listUpper.get(0).get("startDate") == null ? null :listUpper.get(0).get("startDate").toString();
						if(startDate != null){
							Date date = LanDateUtils.convertStringToDate("yyyy-MM-dd", startDate);
							listDate.add(date);
						}
					}
				}
			}
		}

		if(!listDate.isEmpty()&& listDate.size() > 1){
			Date tempDate = listDate.get(0);
			for(int i = 1;i< listDate.size(); i++){
				if(tempDate.getTime() != listDate.get(i).getTime()){//如果listDate时间有一个不想等,就不能删除
					AjaxUtils.ajaxJsonErrorMessage(response, "删除本行数据,影响到它行数据");//请删除区间最大的数据
					return null;
				}
			}

		}


		return "111";
	}
	/**
	 * 计算结束日期
 	 *  @param scheme_id
	 *
	 */
	private void  countEndDate(String scheme_id){
		//获取要修改的数据
		WhereCondition wc = new WhereCondition();
		wc.andEquals("scheme_id",scheme_id);
		List<Hourlyratescheme_details> listhd = hourlyratescheme_detailsService.query(wc);

		//获取对比数据
		Zidingyi zidingyi = new Zidingyi();
		String sqlcontrastData = "  ";
		sqlcontrastData += " SELECT hd.id, hd.startDate, tct.teachingWay_id ";
		sqlcontrastData += " FROM hourlyratescheme_details hd LEFT JOIN teacher_course_teachingway tct ON tct.schemeDetails_id = hd.id ";
		sqlcontrastData += " where hd.scheme_id = '"+scheme_id+"' ";
		zidingyi.setSql(sqlcontrastData);
		List<Map> listData = tongjiService.query(zidingyi);

		//循环判断日期,去最近的日期
		if(!listhd.isEmpty() && listhd.size() > 1){
			for (Hourlyratescheme_details hd:listhd) {
				String id_src =  hd.getId();
				Date startDate_src = hd.getStartDate();
				Date endDate_src = null;
				Date endDate_s_src = null;
				String id=hd.getId();
				WhereCondition wct = new WhereCondition();
				wct.andEquals("schemeDetails_id",id_src);
				List<Teacher_course_teachingway> teachingWay_id_list_scr = teacher_course_teachingwayService.query(wct);
				if(!teachingWay_id_list_scr.isEmpty()){
					for (Teacher_course_teachingway tct :teachingWay_id_list_scr) {
						String teachingWay_id_src = tct.getTeachingWay_id();
						//循环对比
						for (Map m:listData) {
							String id_m =m.get("id") == null ? null : m.get("id").toString();
							Date startDate_m = m.get("startDate") == null ? null:LanDateUtils.convertStringToDate("yyyy-MM-dd",m.get("startDate").toString());
							String teachingWay_id_m = m.get("teachingWay_id") == null ? null : m.get("teachingWay_id").toString();
							//比较的数据不相等
							if(id_src.equals(id_m)){
								continue;
							}
							if(startDate_src.getTime() < startDate_m.getTime()){
								if(teachingWay_id_src.equals(teachingWay_id_m)){
									if(endDate_src == null){
										endDate_src = startDate_m;
									}
									//去最近值
									if(endDate_src.getTime() > startDate_m.getTime()){
										endDate_src = startDate_m;
									}

								}
							}else if(startDate_src.getTime() > startDate_m.getTime()){
								if(teachingWay_id_src.equals(teachingWay_id_m)){
									if(endDate_s_src == null){
										endDate_s_src = startDate_m;
									}
									//去最近值
									if(endDate_s_src.getTime() < startDate_m.getTime()){
										endDate_s_src = startDate_m;
									}

								}
							}

						}
					}
				}

				//更新
				hd.setEndDate(endDate_src);

				hourlyratescheme_detailsService.update(hd);

			}
		}
	}



	@RequestMapping(value = "/ngres/jiaoshiguanli/hourlyratescheme_details/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Hourlyratescheme_details> list = new ArrayList<Hourlyratescheme_details>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Hourlyratescheme_details p = new Hourlyratescheme_details();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Hourlyratescheme_details p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			hourlyratescheme_detailsService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了课时费详情："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/hourlyratescheme_details/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			if(orderGuize == null){
				orderGuize ="startDate";
			}
			WhereCondition wc = new WhereCondition();
//			wc.setLength(page.getItemsperpage());
//			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);

			initWanNengChaXun(jsonObj, wc);// 万能查询
			List<Hourlyratescheme_details> list = hourlyratescheme_detailsService.query(wc);
			for(Hourlyratescheme_details hds:list){
				String hdsId=hds.getId();
				WhereCondition wc2 = new WhereCondition();
				wc2.andEquals("schemeDetails_id", hdsId);
				List<Teacher_course_teachingway> tctList=teacher_course_teachingwayService.query(wc2);
				String teachingWay_ids="";
				String teachingWayName="";
				for(int j=0;j<tctList.size();j++){
					Teacher_course_teachingway tct=tctList.get(j);
					String teachingWay_id=tct.getTeachingWay_id();
					teachingWay_ids+=teachingWay_id;
					String teachingWay_name=tct.getTeachingwayName();
					teachingWayName+=teachingWay_name;
					if(j<tctList.size()-1){
						teachingWay_ids+=",";
						teachingWayName+=",";
					}
					
				}
				hds.setTeachingWayName(teachingWayName);
				hds.setTeachingWay_ids(teachingWay_ids);
				
			}
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(hourlyratescheme_detailsService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/jiaoshiguanli/hourlyratescheme_details/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Hourlyratescheme_details k =  hourlyratescheme_detailsService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
}
