/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Filerecord;
import com.lanmosoft.dao.model.User;
import com.lanmosoft.dao.model.ZonePermission;
import com.lanmosoft.enums.Enums.UserType;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.OrgService;
import com.lanmosoft.service.biz.UserService;
import com.lanmosoft.service.biz.ZonePermissionService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.PasswdUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class UserController extends BaseController {

	@Autowired
	UserService userService;
	@Autowired
	OrgService orgService;
	@Autowired
	ZonePermissionService zonePermissionService;
	@RequestMapping(value = "/ngres/zuzhijiagou/user/findAll")
	public String executeq(ModelMap model, @RequestBody final String content,
			final HttpServletRequest request, final HttpServletResponse response) {
			try {
				WhereCondition wc=new WhereCondition();
				wc.andEquals("isTeaching", "1");
				List<User> query = userService.query(wc);
				Map map=new HashMap();
				map.put("list",query);
				String string = JSONObject.fromObject(map).toString();
				AjaxUtils.ajaxJson(response, string);
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			}
		
		return null;
	}
	
	
	@RequestMapping(value = "/ngres/zuzhijiagou/user/edit")
	public String execute(ModelMap model, @RequestBody final String content,
			final HttpServletRequest request, final HttpServletResponse response) {
				try {
					JSONObject jsonObj = JSONObject.fromObject(content);
					final JSONObject job = jsonObj.getJSONObject("item");
					final JSONArray jArr = jsonObj.getJSONArray("zoneList");
					boolean flag=jsonObj.getBoolean("flag");
					final User p = new User();
					JSONObject.toBean(job,p,jsonConfig);
					if(flag){
						p.setFlag("1");
					}else{
						p.setFlag("0");
					}
					
					WhereCondition wc = new WhereCondition();
					wc.andEquals("userName", p.getUserName());
					if(StringUtils.isNotEmpty(p.getId())){
						wc.andNotEquals("id", p.getId());
					}
					int count = userService.count(wc);
					if(count>0){
						AjaxUtils.ajaxJsonWarnMessage(response, "new_toastr_0026");
						return null;
					}
					
					if(StringUtils.isNotEmpty(p.getDepartment_id())){
						p.setDepartment_name(orgService.loadById(p.getDepartment_id()).getName()); // 部门名称
					}
					if(StringUtils.isNotEmpty(p.getPosition_id())){
						p.setPosition_name(orgService.loadById(p.getPosition_id()).getName()); // 岗位名称
					}
					
					transactionTemplate.execute(new TransactionCallbackWithoutResult() {
						
						@Override
						protected void doInTransactionWithoutResult(TransactionStatus arg0) {
							try{
								/* 保存用户信息 */
								User login = getLogin(request).getUser();
								//TE员工编码
								if(StringUtils.isEmpty(p.getTeCode())){
									p.setTeCode(sequenceManager.generateTeCode(null, null));
								}
								String context=getLogin(request).getUser().getUserName();
								if (StringUtils.isNotEmpty(p.getId())) {
									if(StringUtils.isNotEmpty(JSONUtils.getStr(job, "newPassword"))){
										p.setPassword(PasswdUtil.encode(JSONUtils.getStr(job, "newPassword")));	
									}
									if(StringUtils.isEmpty(p.getAbbr()) || StringUtils.isEmpty(p.getAbbr().trim())){
										p.setAbbr(null);
									}
									p.setLastModifier_id(login.getUserName());
									p.setLastModifiedTime(DateUtils.getNowDate());
									userService.update(p);
									context+="修改了用户信息及设置，修改用户"+p.getId();
								} else {
									String id = sequenceManager.generateId("user");
									p.setId(id);
									p.setPassword(PasswdUtil.encode(p.getPassword()));
									initCreate2(p, request);
									userService.insert(p);
									context+="新增了用户信息及设置，新增用户"+p.getId();
								}
								operationLog.setId(sequenceManager.generateId("operationLog"));
								operationLog.setUser_id(getLogin(request).getUser().getId());
								operationLog.setCreateTime(new Date());
								operationLog.setContent(context);
								operationLogService.insert(operationLog);
								/* 保存校区授权表信息 */
								WhereCondition wc = new WhereCondition();
								wc.andEquals("user_id", p.getId());
								zonePermissionService.deleteByCondition(wc);
								boolean isExists = false;
								for(int i=0; i<jArr.size(); i++){
									JSONObject jObj = jArr.getJSONObject(i);
									String zone_id = JSONUtils.getStr(jObj, "zone_id");
									if(StringUtils.equals(p.getZone_id(), zone_id)){
										isExists = true;
									}
									ZonePermission zonePermission= new ZonePermission();
									zonePermission.setId(sequenceManager.generateId("zonePermission"));
									zonePermission.setUser_id(p.getId()); // 用户
									zonePermission.setZone_id(zone_id); //校区
									zonePermissionService.insert(zonePermission);
								}
								/* 保存默认的校区 */
								if(!isExists){
									ZonePermission zonePermission= new ZonePermission();
									zonePermission.setId(sequenceManager.generateId("zonePermission"));
									zonePermission.setUser_id(p.getId()); // 用户
									zonePermission.setZone_id(p.getZone_id()); //校区
									zonePermissionService.insert(zonePermission);
								}
							} catch(Exception e){
								arg0.setRollbackOnly();
								e.printStackTrace();
							}
						}
					});
					
					AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
				} catch (Exception e) {
					AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
					e.printStackTrace();
				}
		return null;
	}

	@RequestMapping(value = "/ngres/zuzhijiagou/user/delete")
	public String execute3(ModelMap model, @RequestBody final String content,
			String age,final HttpServletRequest request, final HttpServletResponse response) {
		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus arg0) {
				try {
					JSONArray jsonArray = JSONArray.fromObject(content);
					List<User> list = new ArrayList<User>();
					for (int i = 0; i < jsonArray.size(); i++) {
						User p = new User();
						JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
						list.add(p);
					}
					List<String> ids = new ArrayList<String>();
					for (User p : list) {
						ids.add(p.getId());
					}
					WhereCondition wc = new WhereCondition();
					wc.andIn("id", ids);
					userService.deleteByCondition(wc);
					String context=getLogin(request).getUser().getUserName()+"删除了用户数据："+ids;
					operationLog.setId(sequenceManager.generateId("operationLog"));
					operationLog.setUser_id(getLogin(request).getUser().getId());
					operationLog.setCreateTime(new Date());
					operationLog.setContent(context);
					operationLogService.insert(operationLog);
					AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
				} catch (Exception e) {
					arg0.setRollbackOnly();
					AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
					e.printStackTrace();
				}
			}
		});
		return null;
	}
	@RequestMapping(value = "/ngres/zuzhijiagou/user_tutor/list")
	public String executeTutor(ModelMap model, @RequestBody String content,
						   HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			wc.andNotEquals("category", UserType.SUPER);
			wc.andNotEquals("status", "2");// 筛掉离职员工
			List list = userService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(userService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/zuzhijiagou/user/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			String zoneIdTutor = JSONUtils.getStr(jsonObj,"zoneIdTutor");
			String flagLizhi = JSONUtils.getStr(jsonObj,"flagLizhi");
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			wc.andNotEquals("category", UserType.SUPER);
			wc.andNotEquals("status", "2");// 筛掉离职员工

			List list = null;
			int count = 0;
			if(StringUtils.isNotBlank(zoneIdTutor)){
				wc.andEquals("flag","1");
				wc.or();
				wc.andEquals("flag","0");
				wc.andEquals("zoneIdTutor",zoneIdTutor);
				wc.andNotEquals("category", UserType.SUPER);
				wc.andNotEquals("status", "2");// 筛掉离职员工
				initWanNengChaXun(jsonObj, wc);// 万能查询
				list = userService.queryTutor(wc);
				count = userService.countTutor(wc);
			}else{
				//显示离职员工
				if("0".equals(flagLizhi)){
					wc.remove("status");
				}
				list = userService.query(wc);
				count = userService.count(wc);
			}
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(count);
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/zuzhijiagou/user/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			String id = JSONUtils.getStr(jsonObj, "id");
			/* 用户信息 */
			User k =  userService.loadById(id);

			//获取语言
			LoginModel login = (LoginModel)request.getSession().getAttribute("login");
			String language = login.getLanguage();
			model.put("language",language);
			/* 用户校区授权表信息 */
			WhereCondition wc = new WhereCondition();
			wc.andEquals("user_id", id);
			List<ZonePermission> zonePermissionList = zonePermissionService.query(wc);
			model.put("item", k);
			model.put("zonePermissionList", zonePermissionList);
			AjaxUtils.ajaxJson(response, JSONObject.fromObject(model,jsonConfig).toString());
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	private void uploadImage(HttpServletRequest request, JSONObject jsonObj, User user) {
		try {
			JSONArray jaddson = jsonObj.getJSONArray("addson");
			List<String> idlist = new ArrayList<String>();
			if (StringUtils.isNotEmpty(user.getId())){
				WhereCondition wcff = new WhereCondition();
				wcff.andEquals("refid", user.getId()).andNotIn("id", idlist);
				filerecordService.deleteByCondition(wcff);
			}	
			if (jaddson != null && jaddson.size() > 0) {
				for (int i = 0; i < jaddson.size(); i++) {	
					String fileid = JSONUtils.getStr(jaddson.getJSONObject(i), "id");
					idlist.add(fileid);
					Filerecord fr=filerecordService.loadById(fileid);
					filerecordService.update(fr);
				}
			}
		} catch (Exception e) {
			
		}
		try {
			JSONArray ja= jsonObj.getJSONArray("files");
			if(ja!=null&&ja.size()>0){
				String sessionId=request.getSession().getId();
				for(int i=0;i<ja.size();i++){
					String uniqueIdentifier=JSONUtils.getStr(ja.getJSONObject(i), "uniqueIdentifier");
					WhereCondition wc = new WhereCondition();
					wc.andEquals("yuanshiid", sessionId+"_"+uniqueIdentifier);
					Filerecord fr=filerecordService.query(wc).get(0);
					fr.setReference_id(user.getId());
					filerecordService.update(fr);
				}
			}
		} catch (Exception e) {
			
		}
		WhereCondition wcfile = new WhereCondition();
		wcfile.andEquals("refid", user.getId());
		wcfile.setOrderBy("createTime desc");
		List<Filerecord> fileRecordList = filerecordService.query(wcfile);
		if(CollectionUtils.isNotEmpty(fileRecordList)){
			userService.update(user);
		}
	}
}
