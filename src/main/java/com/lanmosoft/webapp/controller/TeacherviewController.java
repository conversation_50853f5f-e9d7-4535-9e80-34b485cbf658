/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Teacherview;
import com.lanmosoft.dao.model.User;
import com.lanmosoft.dao.model.Zone;
import com.lanmosoft.enums.Enums.CourseType;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.*;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import com.lanmosoft.webapp.webmodel.StudentKeshi;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class TeacherviewController extends BaseController {

    @Autowired
    TeacherviewService teacherviewService;
    @Autowired
    Teacher_zoneService teacher_zoneService;
    @Autowired
    TongjiService tongjiService;
    @Autowired
    ZoneService zoneService;
    @Autowired
    View_teacher_courseService view_teacher_courseService;

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/findKeshi")
    public String executezz(ModelMap model, @RequestBody String content,
                            String age, HttpServletRequest request, HttpServletResponse response) {
        try {
	/*          d.courseType=='1'&&'正常课程'||
				d.courseType=='2'&&'少发需'||   不上课，发老师工资，扣学员课时
				d.courseType=='3'&&'多发（上课，不发工资，不扣学时）'||
				d.courseType=='4'&&'多发(上课，发工资，不扣学时)'}}*/
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            Teacherview p = new Teacherview();
            JSONObject.toBean(job, p, jsonConfig);
//				Zidingyi z=new Zidingyi();
//				
//				tongjiService.query(z);


            WhereCondition wc = new WhereCondition();
            wc.andEquals("studentId", p.getId());
            //总课时   	//已消耗   contract
            Double sum1 = 0d;
            Double sum2 = 0d;
//				List<Contract> query = contractService.query(wc);
//				List<String> list=new ArrayList<String>();
//				for (Contract contract : query) {
//					sum1+=contract.getAmount()*2;
//					sum2+=contract.getConsumedClass()==null? 0:contract.getConsumedClass();
//				}

            //已排课
            Double sum3 = 0d;
            wc = new WhereCondition();
            wc.andEquals("student_id", p.getId());
            wc.andNotEquals("courseType", CourseType.COURSETYPE_2);
            wc.andNotEquals("status", 2);
//				List<ClassSchedule> query2 = classScheduleService.query(wc);
//				for (ClassSchedule classSchedule : query2) {
//					sum3+=classSchedule.getTime();
//				}
            StudentKeshi st = new StudentKeshi();
            st.setSumKeshi(sum1);
            st.setYixiaohao(sum2);
            st.setYipaike(sum3);
            Double s = sum1 - sum3;
            if (s < 0) s = 0d;
            st.setWeipaike(s);
            Map map = new HashMap();
            map.put("item", st);
            String string = JSONObject.fromObject(map).toString();
            AjaxUtils.ajaxJson(response, string);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/sync")
    public String executesync(ModelMap model, @RequestBody String content,
                              String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginModel login = (LoginModel) request.getSession().getAttribute("login");
            JSONObject jsonObj = JSONObject.fromObject(content);
            //JSONObject job = jsonObj.getJSONObject("item");
            Teacherview p = new Teacherview();
            JSONObject.toBean(jsonObj, p, jsonConfig);

            String context = getLogin(request).getUser().getUserName();
            //context += "同步老师信息到268" + p.getId();
            context += "创建tencentID" + p.getId();
            String displayname = p.getEnglishName();
            String phone = p.getMobile();
            String email = p.getEmail();
            String type = "";
            String deptid = "";
            if (!StringUtils.equals(p.getZoom_id(), "1")) {
                type = "t";
                deptid = "dp-34c883cf1b0649bdbda78c59f460ee7d";
            } else {
                type = "te";
                deptid = "dp-831e342bcd8945b5992bb67cf08b1891";
            }
            try {
                String res = TencentUtil.createTencentAccount(email, email, displayname, phone, deptid, type);
                if (StringUtils.contains(res, "error") || StringUtils.contains(res, "account duplicated" ) || StringUtils.equals(res, "")) {
                    context += "创建腾讯账户错误" + p.getId() + " " + res.toString();
                } else {
                    context += "创建腾讯账户成功" + p.getId();
                    WhereCondition wc = new WhereCondition();
                    wc.andEquals("id", p.getId());
                    User user = new User();
                    user.setTencentid(res);
                    userService.updateByCondition(wc, user);

                }
            } catch (Exception e) {
                e.printStackTrace();
            }


            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);

            //sync to 268
//            TreeMap<String, String> params = new TreeMap<String, String>();
//            //todo: replace the hardcoded security code below
//            params.put("privateKey", "vuPgVevkiM7AnzuyjrKYihIGLmFLfk8Fxc8IX3QJtZJc6UUddutOHCOIebZxCK8L");
//            params.put("timestamps", String.valueOf(System.currentTimeMillis()));
//            params.put("crmTeacherId", p.getId());
//            params.put("teacherName", p.getEnglishName());
//
//            params.put("photo", "/img.jpg");
//            params.put("depict", "Teacher introduction");
//            List<String> keyList = new ArrayList<>(params.keySet());
//            StringBuilder signContext = new StringBuilder();
//
//            for (Iterator it = keyList.iterator(); it.hasNext(); ) {
//                String key = (String) it.next();
//                String value = params.get(key);
//                if (StringUtils.isNotEmpty(value)) {
//                    signContext.append(key).append("|").append(value);
//                    if (it.hasNext()) {
//                        signContext.append("|");
//                    }
//                }
//            }
//            String sign = MD5.sign2(signContext.toString(), "utf-8");
//            params.put("sign", sign);
//            Map<String, Object> resultMap = null;
//            JSONObject jo = null;
//            try {
//                String result = HttpClientRestful.postForm("https://api.oxbridgetutors.com/api/crm/docking/synTeacher", params, String.class);
//                //String result = HttpClientRestful.postForm("http://47.101.53.15:5001/api/crm/docking/synTeacher", params, String.class);
//                System.out.println(result);
//                jo = JSONObject.fromObject(result);
//
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//            Map map = new HashMap();
//            map.put("status", jo.getString("success"));
//            map.put("message", jo.getString("message"));
//            map.put("item", p);
//            String s = JSONObject.fromObject(map, jsonConfig).toString();
            String s = context;
            AjaxUtils.ajaxJson(response, s);

        } catch (Exception e) {// TODO
            Map map = new HashMap();
            map.put("status", "success");
            map.put("message", "new_toastr_0001");
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/edit")
    public String execute(ModelMap model, @RequestBody String content,
                          String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginModel login = (LoginModel) request.getSession().getAttribute("login");
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            Teacherview p = new Teacherview();
            JSONObject.toBean(job, p, jsonConfig);
            //修改BUTypey用于工资详细OFF/ON计算
            if (StringUtils.isNotEmpty(p.getZone_id())) {
                Zone zone = zoneService.loadById(p.getZone_id());
                p.setBUType(zone.getBUtype());
            }

            //TE员工编码
            if (StringUtils.isEmpty(p.getTeCode())) {
                p.setTeCode(sequenceManager.generateTeCode(null, null));
            }
            String context = getLogin(request).getUser().getUserName();
            if (StringUtils.isNotEmpty(p.getId())) {
                p.setLastModifier_id(login.getUser().getId());
                p.setLastModifiedTime(DateUtils.getNowDate());
                if (StringUtils.isEmpty(p.getPosition_id())) {
                    p.setPosition_id(null);
                }
                teacherviewService.update(p);
                context += "修改了老师信息，修改老师信息" + p.getId();
            } else {
                String id = sequenceManager.generateId("user");
                p.setId(id);
                initCreate2(p, request);
                teacherviewService.insert(p);
                context += "新增了老师信息，新增老师信息" + p.getId();
            }
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);

            //sync to 268
//            TreeMap<String, String> params = new TreeMap<String, String>();
//            //todo: replace the hardcoded security code below
//            params.put("privateKey", "vuPgVevkiM7AnzuyjrKYihIGLmFLfk8Fxc8IX3QJtZJc6UUddutOHCOIebZxCK8L");
//            params.put("timestamps", String.valueOf(System.currentTimeMillis()));
//            params.put("crmTeacherId", p.getId());
//            params.put("teacherName", p.getEnglishName());
//
//            params.put("photo", "https://images/111.jpg");
//            params.put("depict", "讲师L的简介666");
//            List<String> keyList = new ArrayList<>(params.keySet());
//            StringBuilder signContext = new StringBuilder();
//
//            for (Iterator it = keyList.iterator(); it.hasNext(); ) {
//                String key = (String) it.next();
//                String value = params.get(key);
//                if (StringUtils.isNotEmpty(value)) {
//                    signContext.append(key).append("|").append(value);
//                    if (it.hasNext()) {
//                        signContext.append("|");
//                    }
//                }
//            }
//            String sign = MD5.sign2(signContext.toString(), "utf-8");
//            params.put("sign", sign);
//            Map<String, Object> resultMap = null;
//            try {
//                String result = HttpClientRestful.postForm("http://api.oxbridgetutors.com/api/crm/docking/synTeacher", params, String.class);
//                System.out.println(result);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }

            Map map = new HashMap();
            map.put("status", "success");
            map.put("message", "new_toastr_0001");
            map.put("item", p);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);

        } catch (Exception e) {// TODO
            Map map = new HashMap();
            map.put("status", "success");
            map.put("message", "new_toastr_0001");
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/delete")
    public String execute3(ModelMap model, @RequestBody String content,
                           String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<Teacherview> list = new ArrayList<Teacherview>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Teacherview p = new Teacherview();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (Teacherview p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            teacherviewService.deleteByCondition(wc);
            String context = getLogin(request).getUser().getUserName() + "删除了老师数据：" + ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            wc.setLength(page.getItemsperpage());
            wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
            wc.setOrderBy(orderGuize);
            initWanNengChaXun(jsonObj, wc);// 万能查询

            WhereCondition wc2 = new WhereCondition();
            if (jsonObj.getJSONObject("searchItems").get("course_lk") != null) {
                wc2.andFullLike("course_name", jsonObj.getJSONObject("searchItems").get("course_lk").toString());
            }
            if (jsonObj.getJSONObject("searchItems").get("gradeCategory_name_lk") != null) {
                wc2.andFullLike("gradeCategory_name", jsonObj.getJSONObject("searchItems").get("gradeCategory_name_lk").toString());
            }
            if (jsonObj.getJSONObject("searchItems").get("grade_name_lk") != null) {
                wc2.andFullLike("grade_name", jsonObj.getJSONObject("searchItems").get("grade_name_lk").toString());
            }
            if (wc2.getCondition() != null) {
                List<String> tc_list = view_teacher_courseService.queryDistinctTeacherID(wc2);
                wc.andIn("id", tc_list);
            }


            /*
             * 校区权限过滤
             */
            //String zone_id = getLogin(request).getZone_id();
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
            System.out.print(zone_id);
            if (StringUtils.isNotEmpty(zone_id) && !StringUtils.equals("all", zone_id)) {
                List<String> teacher_ids = teacher_zoneService.queryTeacher_id(zone_id);
                wc.andIn("id", teacher_ids);
            }
            wc.remove("zone_id");

            wc.remove("course");
            wc.remove("gradeCategory_name");
            wc.remove("grade_name");
            //工资明细online和offline条件添加
            String BUTypeOnOROffGroup = JSONUtils.getStr(jsonObj, "BUTypeOnOROffGroup");
            if (!StringUtils.isEmpty(BUTypeOnOROffGroup)) {
                wc.andIn("BUType", Arrays.asList(BUTypeOnOROffGroup.split(",")));
            }
            String BUTypeOnOROff = JSONUtils.getStr(jsonObj, "BUTypeOnOROff");
            if (StringUtils.isNotEmpty(BUTypeOnOROff)) {
                wc.andEquals("BUType", BUTypeOnOROff);
            }
            List<Teacherview> list = teacherviewService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(teacherviewService.count(wc));
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/listCheck")
    public String executeCheck(ModelMap model, @RequestBody String content,
                               HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONArray tids = jsonObj.getJSONArray("tids");
            List<String> ids = new ArrayList<String>();
            for (int j = 0; j < tids.size(); j++) {
                ids.add(tids.getString(j));
            }
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            List list = teacherviewService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            Map map = new HashMap();
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    //abbr查重
    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/list/abbrchecked")
    public String execute11234(ModelMap model, @RequestBody String content,
                               HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            initWanNengChaXun(jsonObj, wc);// 万能查询
            List list = teacherviewService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            if (list.size() <= 0) {
                AjaxUtils.ajaxJsonSuccessMessage(response, "msg_0301");
            } else {
                AjaxUtils.ajaxJsonErrorMessage(response, "msg_0302");
            }
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 护照查重
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/list/possportNocheckchecked")
    public String executePossportNocheckchecked(ModelMap model, @RequestBody String content,
                                                HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            initWanNengChaXun(jsonObj, wc);// 万能查询
            List list = teacherviewService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            if (list.size() <= 0) {
                AjaxUtils.ajaxJsonSuccessMessage(response, "msg_0301_1");
            } else {
                AjaxUtils.ajaxJsonErrorMessage(response, "msg_0302_1");
            }
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/list/id")
    public String executesd1(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            Teacherview k = teacherviewService.loadById(JSONUtils.getStr(jsonObj, "id"));
            JSONObject jo = JSONObject.fromObject(k, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/findAllZoneid")
    public String findAllZoneid(ModelMap model, @RequestBody String content,
                                HttpServletRequest request, HttpServletResponse response) {
        try {
            WhereCondition wc = new WhereCondition();
            List list = zoneService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            Map map = new HashMap();
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jiaoshiguanli/teacherview/list/id1")
    public String executesd11(ModelMap model, @RequestBody String content,
                              HttpServletRequest request, HttpServletResponse response) {
        try {
            Teacherview k = teacherviewService.loadById(getLogin(request).getUser().getId());
            JSONObject jo = JSONObject.fromObject(k, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }
}
