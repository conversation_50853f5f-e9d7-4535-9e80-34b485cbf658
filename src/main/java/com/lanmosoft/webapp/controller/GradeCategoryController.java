/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.lanmosoft.dao.model.Grade;
import com.lanmosoft.dao.model.GradeCategory;
import com.lanmosoft.dao.model.GradeCourse;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.GradeCategoryService;
import com.lanmosoft.service.biz.GradeCourseService;
import com.lanmosoft.service.biz.GradeService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class GradeCategoryController extends BaseController {

	@Autowired
	GradeCategoryService gradeCategoryService;
	@Autowired
	GradeCourseService gradeCourseService;
	@Autowired
	GradeService gradeService;
	
	@RequestMapping(value="/ngres/jichushuju/gradeCategory/findAll")
	public String executes(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
			try {
				WhereCondition wc=new WhereCondition();
				List<GradeCategory> query = gradeCategoryService.query(wc);
				Map map=new HashMap();
				map.put("list", query);
				String string = JSONObject.fromObject(map).toString();
				AjaxUtils.ajaxJson(response, string);
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
				e.printStackTrace();
			}
		return null;
	}
	
	
	@RequestMapping(value="/ngres/jichushuju/gradeCategory/findAllByCourseId")
	public String executeByCourseId(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
			try {
				JSONObject jsonObj = JSONObject.fromObject(content);
				String courseId=JSONUtils.getStr(jsonObj, "id");
				WhereCondition wc=new WhereCondition();
				wc.andEquals("course_id", courseId);
				List<GradeCourse> li=gradeCourseService.query(wc);
				List<GradeCategory> query =new ArrayList<GradeCategory>();
				wc=new WhereCondition();
				if(!li.isEmpty()){
					String ggid="";
					for(GradeCourse gg:li){
						Grade g=gradeService.loadById(gg.getGrade_id());
						if(ggid.contains(g.getGradeCategory_id())){
							continue;
						}
						ggid+=g.getGradeCategory_id()+",";
						GradeCategory gc=gradeCategoryService.loadById(g.getGradeCategory_id());
						query.add(gc);
					}
				}else{
					query = gradeCategoryService.query(wc);
				}
				Map map=new HashMap();
				map.put("list", query);
				String string = JSONObject.fromObject(map).toString();
				AjaxUtils.ajaxJson(response, string);
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
				e.printStackTrace();
			}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/gradeCategory/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			GradeCategory p = new GradeCategory();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				gradeCategoryService.update(p);
				context+="修改了基础数据类型设置，修改类型"+p.getId();
			} else {
				String id = sequenceManager.generateId("gradeCategory");
				p.setId(id);
				initCreate2(p, request);
				gradeCategoryService.insert(p);
				context+="新增了基础数据类型设置，新增类型"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/gradeCategory/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<GradeCategory> list = new ArrayList<GradeCategory>();
			for (int i = 0; i < jsonArray.size(); i++) {
				GradeCategory p = new GradeCategory();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (GradeCategory p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			gradeCategoryService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据类型："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/gradeCategory/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<GradeCategory> list = new ArrayList<GradeCategory>();
			for (int i = 0; i < jsonArray.size(); i++) {
				GradeCategory p = new GradeCategory();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (GradeCategory p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			gradeCategoryService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据类型："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/gradeCategory/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = gradeCategoryService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(gradeCategoryService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/jichushuju/gradeCategory/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			GradeCategory k =  gradeCategoryService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
}
