/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Hourlyratescheme_details;
import com.lanmosoft.dao.model.Teacher_course_teachingway;
import com.lanmosoft.dao.model.Teacher_hourlyratescheme;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.Hourlyratescheme_detailsService;
import com.lanmosoft.service.biz.Teacher_course_teachingwayService;
import com.lanmosoft.service.biz.Teacher_hourlyrateschemeService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class Teacher_hourlyrateschemeController extends BaseController {

	@Autowired
	Teacher_hourlyrateschemeService teacher_hourlyrateschemeService;
	@Autowired
	Hourlyratescheme_detailsService hourlyratescheme_detailsService;
	@Autowired
	Teacher_course_teachingwayService teacher_course_teachingwayService;
	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_hourlyratescheme/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			String context=getLogin(request).getUser().getUserName();
			Teacher_hourlyratescheme p = new Teacher_hourlyratescheme();
			JSONObject.toBean(job,p,jsonConfig);
			
			List<Teacher_hourlyratescheme> querycheck =null;
			if(!(StringUtils.isNotEmpty(p.getSchemeNo()))){
				WhereCondition wc = new WhereCondition();
				String teacher_id=p.getTeacher_id();
				wc.andEquals("teacher_id", teacher_id);
				String maxSchemeNo=teacher_hourlyrateschemeService.getMaxSchemeNo(wc);
				int maxSchemeNoInt=0;
				if(maxSchemeNo!=null){
					maxSchemeNoInt=Integer.valueOf(maxSchemeNo.split("-")[1]);
					maxSchemeNoInt+=1;
				}
				String schemeNo="OFF-"+maxSchemeNoInt;
				p.setSchemeNo(schemeNo);
				
			}
			if(false){
				
			}
			else{
			if (StringUtils.isNotEmpty(p.getId())) {
				teacher_hourlyrateschemeService.update(p);
				context+="修改了教师课时费设置，修改教师课时费"+p.getId();
			} else {
				String id = sequenceManager.generateId("teacher_hourlyratescheme");
				p.setId(id);
				teacher_hourlyrateschemeService.insert(p);
				context+="新增了教师课时费设置，新增教师课时费"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		}
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_hourlyratescheme/delete")
	public String execute3(ModelMap model, @RequestBody final String content,
						   String age, final HttpServletRequest request, final HttpServletResponse response) {
		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
				try {
					JSONArray jsonArray = JSONArray.fromObject(content);
					List<Teacher_hourlyratescheme> list = new ArrayList<Teacher_hourlyratescheme>();
					for (int i = 0; i < jsonArray.size(); i++) {
						Teacher_hourlyratescheme p = new Teacher_hourlyratescheme();
						JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
						list.add(p);
					}
					List<String> ids = new ArrayList<String>();
					for (Teacher_hourlyratescheme p : list) {
						ids.add(p.getId());
					}
					WhereCondition wc = new WhereCondition();
					wc.andIn("id", ids);

					WhereCondition wc1 = new WhereCondition();
					wc1.andIn("scheme_id", ids);

					List<String> idsh = new ArrayList<String>();
					List<Hourlyratescheme_details> listh = hourlyratescheme_detailsService.query(wc1);
					for (Hourlyratescheme_details p : listh) {
						idsh.add(p.getId());
					}
					WhereCondition wc2 = new WhereCondition();
					wc2.andIn("schemeDetails_id", idsh);


					teacher_course_teachingwayService.deleteByCondition(wc2);
					hourlyratescheme_detailsService.deleteByCondition(wc1);
					teacher_hourlyrateschemeService.deleteByCondition(wc);

					String context=getLogin(request).getUser().getUserName()+"删除了教师课时费："+ids;
					operationLog.setId(sequenceManager.generateId("operationLog"));
					operationLog.setUser_id(getLogin(request).getUser().getId());
					operationLog.setCreateTime(new Date());
					operationLog.setContent(context);
					operationLogService.insert(operationLog);
					AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
				} catch (Exception e) {// TODO
					transactionStatus.setRollbackOnly();
					AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
					e.printStackTrace();
				}
			}
		});

		return null;
	}
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_hourlyratescheme/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Teacher_hourlyratescheme> list = new ArrayList<Teacher_hourlyratescheme>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Teacher_hourlyratescheme p = new Teacher_hourlyratescheme();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Teacher_hourlyratescheme p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			//teacher_hourlyrateschemeService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了教师课时费："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_hourlyratescheme/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List<Teacher_hourlyratescheme> list = teacher_hourlyrateschemeService.query(wc);
			for(int i=0;i<list.size();i++){
				Teacher_hourlyratescheme listI=list.get(i);
				String scheme_id=listI.getId();
				WhereCondition wc1 = new WhereCondition();
				wc1.andEquals("scheme_id", scheme_id);
				wc1.setOrderBy("startDate");
        		//查询出上课方式，形成逗号字符串，a1,a2,a3作为ids返回
				List<Hourlyratescheme_details> listD= hourlyratescheme_detailsService.query(wc1);
				for(Hourlyratescheme_details hds:listD){
					String hdsId=hds.getId();
					WhereCondition wc2 = new WhereCondition();
					wc2.andEquals("schemeDetails_id", hdsId);
					List<Teacher_course_teachingway> tctList=teacher_course_teachingwayService.query(wc2);
					String teachingWayName="";
					String teachingWay_ids="";
					for(int j=0;j<tctList.size();j++){
						Teacher_course_teachingway tct=tctList.get(j);
						String teachingWay_id=tct.getTeachingWay_id();
						String teachingWay_name=tct.getTeachingwayName();
						teachingWay_ids+=teachingWay_id;
						teachingWayName+=teachingWay_name;
						if(j<tctList.size()-1){
							teachingWay_ids+=",";
							teachingWayName+=",";
						}
						
					}
					hds.setTeachingWayName(teachingWayName);
					hds.setTeachingWay_ids(teachingWay_ids);
					
				}
				listI.setList(listD);
			}
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(teacher_hourlyrateschemeService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_hourlyratescheme/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Teacher_hourlyratescheme k =  teacher_hourlyrateschemeService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		
		@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_hourlyratescheme/checkList")
		public String executeCheck(ModelMap model, @RequestBody String content,
				HttpServletRequest request, HttpServletResponse response) {
			try {
				JSONObject jsonObj = JSONObject.fromObject(content);
				
				// 服务端排序规则
				String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
				// 组装查询条件
				Boolean retFlag=false;
				WhereCondition wc = new WhereCondition();
				wc.andEquals("teacher_id",JSONUtils.getStr(jsonObj, "teacher_id"));
				wc.andEquals("BUType",JSONUtils.getStr(jsonObj, "BUType"));
				wc.andEquals("isDefault",Enums.TeacherHourlyratescheme.ISDEFAULT);
				String id=JSONUtils.getStr(jsonObj, "id");
				if (StringUtils.isNotEmpty(id)) {
					wc.andNotEquals("id",id);
				}
				List<Teacher_hourlyratescheme> list = teacher_hourlyrateschemeService.query(wc);
				if(list.size()>0){
					retFlag=false;
				}
				else{
					retFlag=true;
				}
				Map map=new HashMap();
				map.put("retFlag",retFlag);
				String s=JSONObject.fromObject(map,jsonConfig).toString();
				AjaxUtils.ajaxJson(response,s );
			} catch (Exception e) {// TODO
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
				e.printStackTrace();
			}
			return null;
		}
}
