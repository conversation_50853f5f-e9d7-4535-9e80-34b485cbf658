/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.sql.Time;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.lanmosoft.dao.model.Schedule;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.ScheduleService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.TimeUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class ScheduleController extends BaseController {

	@Autowired
	ScheduleService scheduleService;
	
	@RequestMapping(value="/ngres/jichushuju/schedule/list/findAll")
	public String executea(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			WhereCondition wc=new WhereCondition();
			wc.andEquals("delStatus","0");
			wc.setOrderBy("startTime");
			/*wc.setOrderBy("startTime");*/
			List<Schedule> query = scheduleService.query(wc);
			Map map=new HashMap();
			map.put("list",query);
			String string = JSONObject.fromObject(map).toString();
			AjaxUtils.ajaxJson(response, string);
			
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/schedule/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			LoginModel login = (LoginModel)request.getSession().getAttribute("login");
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			Schedule p = new Schedule();
			JSONObject.toBean(job,p,jsonConfig);
			
			String pStart = p.getStartTime();
			String pEnd = p.getEndTime();
			
			WhereCondition wc = new WhereCondition();
			wc.andIn("delStatus", "0");
			List<Schedule> sList = scheduleService.query(wc);
			for(Schedule one : sList){
				String start = one.getStartTime();
				String end = one.getEndTime();
				//除去本身
				if(!one.getId().equals(p.getId())){
					if(!TimeUtil.timeRight(pStart, pEnd, start, end)){
						//开始时间在现有的时间段里面
						AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0017");
						return null;
					}
				}
			}
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				
				p.setLastModifier_id(login.getUser().getId());
				p.setLastModifiedTime(DateUtils.getNowDate());
				scheduleService.update(p);
				context+="修改了基础数据上课时间设置，修改上课时间"+p.getId();
			} else {
				
				String id = sequenceManager.generateId("schedule");
				p.setId(id);
				initCreate2(p, request);
				scheduleService.insert(p);
				context+="新增了基础数据上课时间设置，新增上课时间"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/schedule/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Schedule> list = new ArrayList<Schedule>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Schedule p = new Schedule();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Schedule p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			scheduleService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据上课时间："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/schedule/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Schedule> list = new ArrayList<Schedule>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Schedule p = new Schedule();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Schedule p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			scheduleService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据上课时间："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/schedule/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = scheduleService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(scheduleService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s);
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0018");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/jichushuju/schedule/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Schedule k =  scheduleService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0018");
			e.printStackTrace();
		}
		return null;
	}
}
