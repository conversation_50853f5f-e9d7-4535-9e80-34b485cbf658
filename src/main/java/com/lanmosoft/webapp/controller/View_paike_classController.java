/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.enums.Enums.*;
import com.lanmosoft.enums.LanConstants;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.LanmoMailSender;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.service.model.PaiKeGuwenInfo;
import com.lanmosoft.util.*;
import com.lanmosoft.webapp.controller.searchForm.Page;
import jdk.nashorn.internal.objects.annotations.Where;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class View_paike_classController extends BaseController {

    @Autowired
    View_paike_classService view_paike_classService;
    @Autowired
    ClassScheduleService classScheduleService;
    @Autowired
    Consumption_teacherService consumption_teacherService;
    @Autowired
    Student_leaveService student_leaveService;
    @Autowired
    Teacher_leaveService teacher_leaveService;
    @Autowired
    ScheduleService scheduleService;
    @Autowired
    TongjiService tongjiService;
    @Autowired
    StudentService studentService;
    @Autowired
    ContractService contractService;
    @Autowired
    LanmoMailSender lanmoMailSender;
    @Autowired
    UserService userService;
    @Autowired
    Consumption_studentService consumption_studentService;
    @Autowired
    Teacher_freeTimeService teacher_freeTimeService;
    @Autowired
    Student_freeTimeService student_freeTimeService;
    @Autowired
    View_student_leaveService view_student_leaveService;
    @Autowired
    View_teacher_leaveService view_teacher_leaveService;
    @Autowired
    PaiKeGuwenInfo paiKeGuwenInfo;
    @Autowired
    Teacher_zoneService teacher_zoneService;
    @Autowired
    EmailqueueService emailqueueService;
    @Autowired
    TimezoneService timezoneService;
    @Autowired
    ZoneService zoneService;
    @Autowired
    Consumption_student_contractService consumption_student_contractService;
    @Autowired
    CourseService courseService;
    @Autowired
    TeacherviewService teacherviewService;

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/edit")
    public String execute(ModelMap model, @RequestBody String content,
                          String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            ClassSchedule p = new ClassSchedule();
            JSONObject.toBean(job, p, jsonConfig);
            String context = getLogin(request).getUser().getUserName();
            if (StringUtils.isNotEmpty(p.getId())) {
                classScheduleService.update(p);
                context += "修改了课表数据，修改课表数据" + p.getId();
            } else {
                String id = sequenceManager.generateId("view_paike_class");
                p.setId(id);
                initCreate2(p, request);
                classScheduleService.insert(p);
                context += "新增了课表数据，新增课表数据" + p.getId();
            }
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/delete")
    public String execute3(ModelMap model, @RequestBody String content,
                           String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<View_paike_class> list = new ArrayList<View_paike_class>();
            for (int i = 0; i < jsonArray.size(); i++) {
                View_paike_class p = new View_paike_class();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (View_paike_class p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            ClassSchedule cls = new ClassSchedule();
            cls.setStatus(ClassScheduleStatus.DELETED);
            classScheduleService.updateByCondition(wc, cls);
            String context = getLogin(request).getUser().getUserName() + "删除了课表数据：" + ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            //sql
            String selectCount = " SELECT count(1) ";
            String select = " SELECT scheduledDate >=  ( SELECT startDate FROM lockData WHERE delStatus = '0') islockDate, id, courseType, student_id, stu_chineseName, stu_englishName, course_id, course_name, scheduledDate, startTime, endTime, time, freeTimeCode, ";
            select += " teacher_id, tutorenglishName, us_chineseName, us_englishName, abbr, teachingWay, classroom_id, classroom_name, STATUS, description, notes, leaveState, isAuto, creator_id, ";
            select += " createTime, lastModifier_id, lastModifiedTime, delStatus, realDate, realStartTime, realEndTime, realTime, ";
            select += " teacher_leave_id, t_approvalStatus, student_leave_id, s_approvalStatus, ctrator_userName, ctrator_chineseName, ctrator_englishName, ";
            select += " attendanceStatus, tutorId, BUType, deleteFlag, endDate, scheduledDate AS scheduledDateString, endDate AS endDateStr, teachingWayName ";
            String from = " FROM view_paike_class ";
            String where = " WHERE 1=1 ";

            // 组装查询条件
            JSONObject jsonObjsearchItems = jsonObj.getJSONObject("searchItems");
            Set set = jsonObjsearchItems.keySet();
            String paikeTimeStart = "";
            String paikeTimeEnd = "";
            String payDate = "";
            for (Object o : set) {
                String key = o.toString();
                String value = jsonObjsearchItems.getString(key);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                if ("scheduledDate_le".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        paikeTimeEnd = value;
                    }
                }
                if ("scheduledDate_ge".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        paikeTimeStart = value;
                    }
                }
                if ("delstatus_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and delstatus = '" + value + "'";
                    }
                }
                if ("leaveState_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and leaveState = '" + value + "'";
                    }
                }
                if ("status_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and STATUS = '" + value + "'";
                    }
                }
                if ("student_id_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and student_id = '" + value + "'";
                    }
                }
                if ("teacher_id_eq".equals(key.trim())) {
                    if (value != null && !"".equals(value)) {
                        where += " and teacher_id = '" + value + "'";
                    }
                }

            }
            //校区
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
            if (StringUtils.isNotBlank(zone_id) && !"all".equalsIgnoreCase(zone_id)) {
                where += " and zone_id = '" + zone_id + "' ";
            }
            //助教列表
            if (!"0".equals(getLogin(request).getUser().getCategory().trim())) {
                List<Permission> li = getLogin(request).getPermissionList();
                for (Permission pm : li) {
                    if ("133".equals(pm.getFunction_id())) {
                        where += " and tutorId = '" + getLogin(request).getUser().getId() + "' ";
                    }
                }
            }
            //时区转化
            //获取当前时区的名称
            String timezone_id = JSONUtils.getStr(jsonObj, "timezone_id");
            Timezone timezone = timezoneService.loadById(timezone_id);
            String timezoneName = timezone.getTimezone();
            //时间条件时区转换
            if (StringUtils.isNotBlank(paikeTimeStart) || StringUtils.isNotBlank(paikeTimeEnd)) {
                String sqlDate = startAndEndDateTimezone(paikeTimeStart, paikeTimeEnd, timezoneName, "scheduledDate");
                where += sqlDate;
            }
            //排序
            //String order = "ORDER BY id DESC ";
            String order = "ORDER BY scheduledDate DESC ";
            String limit = " limit " + (page.getCurrentPage() - 1) * page.getItemsperpage() + " , " + page.getItemsperpage();
            List<View_paike_class> list = new ArrayList<View_paike_class>();
            String sql = select + from + where + order + limit;
            String sqlCount = selectCount + from + where;

            Zidingyi zi = new Zidingyi();
            zi.setSql(sql);
            List<Map> listMap = tongjiService.query(zi);
            zi.setSql(sqlCount);
            int count = tongjiService.count(zi);

            //map与实体封装
            if (CollectionUtils.isNotEmpty(listMap)) {
                for (Map m : listMap) {
                    View_paike_class v = new View_paike_class();
                    BeanTranUtil.transMap2Bean1(m, v);
                    list.add(v);
                }
            }

            for (View_paike_class vst : list) {

                //更改上课时间
                List startlist = TimezoneUtil.timezoneChange(vst.getScheduledDate(), vst.getStartTime(), timezone.getTimezone(), "S");
                Date riqiDate = TimezoneUtil.changRiqi(vst.getScheduledDate(), vst.getStartTime(), vst.getEndTime());
                List endList = TimezoneUtil.timezoneChange(riqiDate, vst.getEndTime(), timezone.getTimezone(), "S");

                String riqi = startlist.get(2).toString();
                Date date = LanDateUtils.stringToDate(riqi);
                vst.setScheduledDate(date);
                vst.setStartTime(startlist.get(1).toString());
                vst.setEndTime(endList.get(1).toString());
                vst.setRiqiEnd(endList.get(2).toString());
            }


            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(count);
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * where 时区的时间条件
     *
     * @param startDateLeave
     * @param endDateLeave
     * @param timezoneName
     * @param field
     * @return
     */
    private String startAndEndDateTimezone(String startDateLeave, String endDateLeave, String timezoneName, String field) {
        String sql = " and( ";
        String startDate = "";
        String startTime = "";
        String endDate = "";
        String endTime = "";
        //时区转换
        if (StringUtils.isNotBlank(startDateLeave)) {
            List listStart = TimezoneUtil.timezoneChange(startDateLeave, "00:00", timezoneName, "G");
            startDate = listStart.get(2).toString();
            startTime = listStart.get(1).toString();
        }
        if (StringUtils.isNotBlank(endDateLeave)) {

            List listEnd = TimezoneUtil.timezoneChange(endDateLeave, "23:59", timezoneName, "G");
            endDate = listEnd.get(2).toString();
            endTime = listEnd.get(1).toString();
        }
        //开始时间与结束都有
        if (StringUtils.isNotBlank(startDateLeave) && StringUtils.isNotBlank(endDateLeave)) {
            sql += field + " = '" + startDate + "' and startTime >= '" + startTime + "'";    // scheduledDate = '2017-11-01' AND startTime >= '00:00'

            List<String> days = LanDateUtils.getDays(startDate, endDate);
            if (LanDateUtils.getDays(startDate, endDate).size() > 1) {
                String startDateNext = LanDateUtils.getNext_Day(startDate, 1);
                String endDateNext = LanDateUtils.getNext_Day(endDate, -1);
                sql += " OR  " + field + " >= '" + startDateNext + "' and  " + field + " <= '" + endDateNext + "'"; //or scheduledDate >= '2017-11-02' and scheduledDate <= '2017-11-02'
            }
            sql += " OR " + field + " = '" + endDate + "' and startTime <= '" + endTime + "'"; //OR scheduledDate = '2017-11-03' AND startTime <= '00:00'

        } else if (StringUtils.isNotBlank(startDateLeave)) {
            sql += field + " = '" + startDate + "' and startTime >= '" + startTime + "'";    // scheduledDate = '2017-11-01' AND startTime >= '00:00'
            sql += " OR " + field + " >= '" + LanDateUtils.getNext_Day(startDate, 1) + "' "; //OR scheduledDate > '2017-11-02' '

        } else if (StringUtils.isNotBlank(endDateLeave)) {
            sql += field + " = '" + endDate + "' and startTime <= '" + endTime + "'"; //OR scheduledDate = '2017-11-03' AND startTime <= '00:00'
            sql += " OR " + field + " <= '" + LanDateUtils.getNext_Day(endDate, -1) + "' "; //OR scheduledDate < '2017-11-02' '
        }

        sql += " ) ";
        return sql;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/list/id")
    public String executesd1(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            View_paike_class k = view_paike_classService.loadById(JSONUtils.getStr(jsonObj, "id"));
            JSONObject jo = JSONObject.fromObject(k, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/softDelete")
    public String softDelete(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONArray jsonArray = jsonObj.getJSONArray("items");
            String description = JSONUtils.getStr(jsonObj, "description");
            String flag0 = JSONUtils.getStr(jsonObj, "flag0");
            String flag1 = JSONUtils.getStr(jsonObj, "flag1");
            System.out.println("flag0-->" + flag0);
            System.out.println("flag1-->" + flag1);
            List<String> ids = new ArrayList<String>();
            List<View_paike_class> list = new ArrayList<View_paike_class>();
            String contextTencent = "";
            for (int i = 0; i < jsonArray.size(); i++) {
                View_paike_class p = new View_paike_class();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                if ("".equals(p.getAttendanceStatus()) && "0".equals(p.getStatus())) {
                    ids.add(p.getId());
                    list.add(p);
                }
                //d.status=='1'&&d.s_approvalStatus!='2'&&d.t_approvalStatus!='2'&&d.attendanceStatus==''&&d.leaveState=='0'
                if (!"2".equals(p.getS_approvalStatus()) && !"2".equals(p.getT_approvalStatus()) && "1".equals(p.getStatus()) && "".equals(p.getAttendanceStatus()) && "0".equals(p.getLeaveState())) {
                    ids.add(p.getId());
                    list.add(p);
                }

                // cencel  tencent meeting
                User usr = userService.loadById(p.getTeacher_id());
                ClassSchedule cls = classScheduleService.loadById(p.getId());
                String meetingid = cls.getTencentmeetingid();
                if (StringUtils.isNotEmpty(meetingid)) {
                    try {
                        String res = TencentUtil.cancelTencentMeeting(meetingid, usr.getEmail());
                        if (StringUtils.equals(res, "error")) {
                            contextTencent += "删除腾讯会议错误" + p.getId();
                        } else {
                            contextTencent += "删除腾讯会议成功" + p.getId();
                            // write back tencent url to classschedule
                            WhereCondition wc = new WhereCondition();
                            wc.andEquals("id", p.getId());
                            ClassSchedule classSchedule = new ClassSchedule();
                            classSchedule.setTencenturl("");
                            classSchedule.setTencentmeetingid("");
                            classSchedule.setTencentmeetingcode("");
                            classScheduleService.updateByCondition(wc, classSchedule);
                            //contextTencent += p.getId() + " 创建Tencent Meeting成功 " + p.getId();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();;
                    }
                }

            }
            if (CollectionUtils.isNotEmpty(ids)) {
                WhereCondition wc = new WhereCondition();
                wc.andIn("id", ids);
                ClassSchedule cls = new ClassSchedule();
                cls.setStatus(ClassScheduleStatus.DELETED);
                cls.setDescription(description);
                if (flag0 != null && "1".equals(flag0.trim()) && flag1 != null && "1".equals(flag1.trim())) {
                    cls.setDeleteFlag("2");
                } else if (flag0 != null && "1".equals(flag0.trim())) {
                    cls.setDeleteFlag("0");
                } else if (flag1 != null && "1".equals(flag1.trim())) {
                    cls.setDeleteFlag("1");
                }
                classScheduleService.updateByCondition(wc, cls);
                String context = getLogin(request).getUser().getUserName() + "删除了课表数据：" + ids + contextTencent;
                operationLog.setId(sequenceManager.generateId("operationLog"));
                operationLog.setUser_id(getLogin(request).getUser().getId());
                operationLog.setCreateTime(new Date());
                operationLog.setContent(context);
                operationLogService.insert(operationLog);
            }
            System.out.println("send Email start..");

            String crmCourseIds = "";
            for (View_paike_class p : list) {
                if (StringUtils.equals(ClassScheduleStatus.CONFIRMED, p.getStatus())) {
                    User user = userService.getByStudent(p.getStudent_id(), p.getZone_id());
                    if (user != null && StringUtils.isNotEmpty(user.getEmail())) {
                        String bodyText = "Dear " + user.getChineseName() + "\r\n";
                        bodyText += "学生" + p.getStu_englishName() + " " + p.getStu_chineseName() +
                                LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd") + "日" +
                                p.getStartTime() + "-" + p.getEndTime() + " " + p.getUs_englishName() +
                                " 的课表已被删除,请及时告知学生及老师\r\n" +
                                "删除原因：" + description;
                        String subject = "【删除课程】" + p.getStu_englishName() + " " + p.getStu_chineseName();
                        boolean isSuccess = lanmoMailSender.sendMassageDefault(user.getEmail(), null, null, bodyText, subject);
                        if (!isSuccess) {
                            System.out.println("Failure Sending Email To " + user.getEmail());
                        } else {
                            System.out.println("send email to " + user.getEmail() + " is successful");
                        }
                    }
                }

                //sync to 268 - compile a string of crmCourseIds splitting by ','
                if (p.getBUType().equals(Enums.ONLINETE)) {
                    //String onlinete_courseId = courseService.loadById(p.getCourse_id()).getOnlinete_id();
                    String onlinete_courseId = p.getId();
                    if (StringUtils.isNotEmpty(onlinete_courseId)) {
                        crmCourseIds += onlinete_courseId + ",";
                    }
                }
            }

            //sync to 268 - call deletion api
//            if (StringUtils.isNotEmpty(crmCourseIds)) {
//                crmCourseIds = crmCourseIds.substring(0, crmCourseIds.length() - 1);
//                TreeMap<String, String> params = new TreeMap<String, String>();
//                //todo: replace the hardcoded security code below
//                params.put("privateKey", "vuPgVevkiM7AnzuyjrKYihIGLmFLfk8Fxc8IX3QJtZJc6UUddutOHCOIebZxCK8L");
//                //params.put("buyCourseId", "13030");
//                params.put("crmCourseIds", crmCourseIds);
//                params.put("timestamps", String.valueOf(System.currentTimeMillis()));
//
//
//                List<String> keyList = new ArrayList<>(params.keySet());
//                StringBuilder signContext = new StringBuilder();
//
//                for (Iterator it = keyList.iterator(); it.hasNext(); ) {
//                    String key = (String) it.next();
//                    String value = params.get(key);
//                    if (StringUtils.isNotEmpty(value)) {
//                        signContext.append(key).append("|").append(value);
//                        if (it.hasNext()) {
//                            signContext.append("|");
//                        }
//                    }
//                }
//                String sign = MD5.sign2(signContext.toString(), "utf-8");
//                params.put("sign", sign);
//                Map<String, Object> resultMap = null;
//                try {
//
//                    String result = HttpClientRestful.postForm("https://api.oxbridgetutors.com/api/crm/docking/deleteCrmCourseByIds", params, String.class);
//                    //String result = HttpClientRestful.postForm("http://47.101.53.15:5001/api/crm/docking/deleteCrmCourseByIds", params, String.class);
//                    System.out.println(result);
//                    JSONObject jo = JSONObject.fromObject(result);
//                    if (jo.getString("success").equals("true")) {
//                        String context = crmCourseIds + "删除课程 同步268成功";
//                        operationLog.setId(sequenceManager.generateId("operationLog"));
//                        operationLog.setUser_id(getLogin(request).getUser().getId());
//                        operationLog.setCreateTime(new Date());
//                        operationLog.setContent(context);
//                        operationLogService.insert(operationLog);
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }

            System.out.println("send Email end..");
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/getOverClass")
    public String getOverClass(ModelMap model, @RequestBody String content,
                               HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String student_id = JSONUtils.getStr(jsonObj, "student_id");
            Double units = contractService.countUnitOfContract(student_id);
            double classHour = view_paike_classService.queryClassHour(student_id);
            if (classHour > (units * 2 + LanConstants.GLOBAL_OVERCLASS)) {
                model.put("isOver", true);
                model.put("overClass", classHour - (units * 2 + 15));
            } else {
                model.put("isOver", false);
            }
            AjaxUtils.ajaxJson(response, JSONObject.fromObject(model, jsonConfig).toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "添加失败!");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/confirm")
    public String confirm(ModelMap model, @RequestBody final String content,
                          final HttpServletRequest request, final HttpServletResponse response) {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {

                try {
                    JSONObject jsonObj = JSONObject.fromObject(content);
                    JSONArray jsonArray = jsonObj.getJSONArray("items");
                    List<String> ids = new ArrayList<String>();
                    String contextTencent = "";
                    boolean isTencent = false;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject obj = jsonArray.getJSONObject(i);
                        View_paike_class p = new View_paike_class();
                        JSONObject.toBean(obj, p, jsonConfig);
                        ClassSchedule c = classScheduleService.loadById(p.getId());

                        if (StringUtils.equals(ClassScheduleStatus.INITIALIZED, p.getStatus())) {
                            ids.add(p.getId());

                            // create tencent url
                            User usr = userService.loadById(p.getTeacher_id());
                            Student stu = studentService.loadById(p.getStudent_id());
                            if (StringUtils.isNotEmpty(usr.getTencentid()) && StringUtils.isNotEmpty(stu.getTencentid())) {
                                isTencent = true;
                                try{
                                    Map resp = new HashMap();
                                    resp = TencentUtil.createTencentMeeting(c.getScheduledDateStr(), c.getStartTime(), Double.parseDouble(p.getTime()), usr.getEmail(), stu.getAccountId().substring(stu.getAccountId().length() - 8), p.getCourse_name());
                                    if (StringUtils.equals(resp.get("url").toString(), "error")) {
                                        contextTencent += "创建腾讯会议错误: " + p.getId();
                                    } else {
                                        // write back tencent url to classschedule
                                        WhereCondition wc = new WhereCondition();
                                        wc.andEquals("id", p.getId());
                                        ClassSchedule clsread = classScheduleService.loadById(p.getId());
                                        if (clsread.getTencenturl() == null || "".equals(clsread.getTencenturl())) {
                                            ClassSchedule cls = new ClassSchedule();
                                            cls.setTencenturl(resp.get("url").toString().replace("\"", ""));
                                            cls.setTencentmeetingid(resp.get("meetingid").toString().replace("\"", ""));
                                            cls.setTencentmeetingcode(resp.get("meeting_code").toString().replace("\"", ""));
                                            classScheduleService.updateByCondition(wc, cls);
                                            contextTencent += p.getId() + " 创建Tencent Meeting成功 " + p.getId();
                                        } else {
                                            contextTencent += "Already existed." ;
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                            }
                            //sync to 268
//                            if (p.getBUType().equals(Enums.ONLINETE)) {
//                                String onlinete_courseId = courseService.loadById(p.getCourse_id()).getOnlinete_id();
//                                String onlinete_studentId = studentService.loadById(p.getStudent_id()).getOnlinete_id();
//                                String onlinete_courseName = courseService.loadById(p.getCourse_id()).getName();
//                                // get zoom id from Additional Info of Teacher detail
//                                String onlinete_roomNo = "";
//                                Date date = LanDateUtils.getNowDate();
//                                String nowDateStr = LanDateUtils.format(date, TimezoneUtil.strTimeFormat_V1);
//                                String sqlonlone = " SELECT url FROM teacher_onlineaddress ";
//                                sqlonlone += " WHERE teacher_id = '" + p.getTeacher_id() + "'  AND startDate <= '" + nowDateStr + "' AND endDate >= '" + nowDateStr + "' ";
//                                Zidingyi zonlineaddress = new Zidingyi();
//                                zonlineaddress.setSql(sqlonlone);
//                                List<Map> listonlineaddress = tongjiService.query(zonlineaddress);
//                                //System.out.println("sql1-->" + zonlineaddress);
//                                if (listonlineaddress != null && listonlineaddress.size() != 0) {
//                                    onlinete_roomNo = listonlineaddress.get(0).get("url").toString();
//                                    //eg.: "https://www.a.com/j/12345", get the last part after splitting by '/'
//                                    onlinete_roomNo = onlinete_roomNo.split("/")[onlinete_roomNo.split("/").length - 1];
//                                }
//
//                                //String onlinete_roomNo = teacherviewService.loadById(p.getTeacher_id()).getZoom_id();
//                                TreeMap<String, String> params = new TreeMap<String, String>();
//                                //todo: replace the hardcoded security code below
//                                params.put("privateKey", "vuPgVevkiM7AnzuyjrKYihIGLmFLfk8Fxc8IX3QJtZJc6UUddutOHCOIebZxCK8L");
//                                //params.put("buyCourseId", "13030");
//                                params.put("buyCourseId", onlinete_courseId);
//                                params.put("crmClassId", p.getStudent_id());
//                                params.put("crmClassName", onlinete_courseName);
//                                //params.put("userIds", "6914");
//                                params.put("userIds", onlinete_studentId);
//                                params.put("timestamps", String.valueOf(System.currentTimeMillis()));
//                                List<Map<String, String>> mapList = new ArrayList<>();
//                                Map<String, String> map = new HashMap<>();
//                                map.put("liveName", onlinete_courseName);
//                                map.put("liveNotice", "ClassNotice");
//                                map.put("roomId", onlinete_roomNo);
//                                map.put("studentCode", "123456");
//                                map.put("liveBeginTime", LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd") + " " + p.getStartTime() + ":00");
//                                map.put("liveEndTime", LanDateUtils.format(p.getScheduledDate(), "yyyy-MM-dd") + " " + p.getEndTime() + ":00");
//                                map.put("name", onlinete_courseName);
//                                map.put("videoDuration", String.valueOf((int) (Double.valueOf(p.getTime()) * 3600)));
//                                map.put("teacherId", p.getTeacher_id());
//                                map.put("assistantId", "id_assist000001");
//                                map.put("crmCourseId", p.getId());
//                                mapList.add(map);
//                                String jsonString = JSONArray.fromObject(mapList).toString();
//                                params.put("liveList", jsonString);
//
//                                List<String> keyList = new ArrayList<>(params.keySet());
//                                StringBuilder signContext = new StringBuilder();
//
//                                for (Iterator it = keyList.iterator(); it.hasNext(); ) {
//                                    String key = (String) it.next();
//                                    String value = params.get(key);
//                                    if (StringUtils.isNotEmpty(value)) {
//                                        signContext.append(key).append("|").append(value);
//                                        if (it.hasNext()) {
//                                            signContext.append("|");
//                                        }
//                                    }
//                                }
//                                String sign = MD5.sign2(signContext.toString(), "utf-8");
//                                params.put("sign", sign);
//                                Map<String, Object> resultMap = null;
//                                try {
//
//                                    String result = HttpClientRestful.postForm("https://api.oxbridgetutors.com/api/crm/docking/arrangeCourseCallBack", params, String.class);
//                                    //String result = HttpClientRestful.postForm("http://47.101.53.15:5001/api/crm/docking/arrangeCourseCallBack", params, String.class);
//                                    System.out.println(result);
//                                    JSONObject jo = JSONObject.fromObject(result);
//                                    if (jo.getString("success").equals("true")) {
//                                        context268 += p.getId() + " 同步268成功; ";
//                                    }
//
//                                    // write back tencent url
//                                    WhereCondition wc = new WhereCondition();
//                                    wc.andEquals("id", p.getId());
//                                    ClassSchedule cls = new ClassSchedule();
//                                    cls.setTencenturl("");
//                                    classScheduleService.updateByCondition(wc, cls);
//
//                                } catch (Exception e) {
//                                    e.printStackTrace();
//                                }
//                            }

                        }
                    }

                    if (CollectionUtils.isNotEmpty(ids)) {
                        WhereCondition wc = new WhereCondition();
                        wc.andIn("id", ids);
                        wc.andEquals("status", "0");
                        ClassSchedule cls = new ClassSchedule();
                        cls.setStatus(ClassScheduleStatus.CONFIRMED);
                        cls.setConfirmedTime(DateUtils.getNowDate());
                        classScheduleService.updateByCondition(wc, cls);
                        for (String idcs : ids) {
                            //判断此堂课是否有邮箱队列
                            WhereCondition wc1 = new WhereCondition();
                            wc1.andEquals("schedule_id", idcs);
                            List<Emailqueue> emailqueues = emailqueueService.query(wc1);
                            if (CollectionUtils.isEmpty(emailqueues)) {
                                Emailqueue t = new Emailqueue();
                                String id = sequenceManager.generateId("emailqueue");
                                t.setId(id);
                                t.setSchedule_id(idcs);
                                t.setIssendmail(Enums.IsSendEmail.noSend);
                                t.setIssendmaiTeacher(Enums.IsSendEmail.noSend);
                                initCreate2(t, request);
                                emailqueueService.insert(t);
                            }
                        }

                        String context = getLogin(request).getUser().getUserName() + "确认了课表数据：" + ids + contextTencent;
                        operationLog.setId(sequenceManager.generateId("operationLog"));
                        operationLog.setUser_id(getLogin(request).getUser().getId());
                        operationLog.setCreateTime(new Date());
                        operationLog.setContent(context);
                        operationLogService.insert(operationLog);
                    }
                    if (isTencent) {
                        AjaxUtils.ajaxJsonSuccessMessage(response, "Succeeded. " + contextTencent);
                    } else {
                        AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0034");
                    }

                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();
                    AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0035");
                    e.printStackTrace();
                }
            }
        });
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/revert")
    public String revert(ModelMap model, @RequestBody String content,
                         HttpServletRequest request, HttpServletResponse response) {
        String action = null;
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject jObj = jsonObj.getJSONObject("item");
            View_paike_class p = new View_paike_class();
            JSONObject.toBean(jObj, p, jsonConfig);
            p.setScheduledDateStr(JSONUtils.getStr(jObj, "scheduledDate"));
            action = JSONUtils.getStr(jsonObj, "action");
            if (StringUtils.equals(ClassScheduleAction.REVERT, action)) {
                boolean isContradiction = view_paike_classService.checkContradiction(p);
                if (isContradiction) {
                    AjaxUtils.ajaxJsonWarnMessage(response, "new_toastr_0036");
                    return null;
                }
            }
            ClassSchedule cs = new ClassSchedule();
            cs.setId(p.getId());
            if (StringUtils.equals(ClassScheduleAction.REVERT, action)) {
                cs.setStatus(ClassScheduleStatus.INITIALIZED);
            }

            classScheduleService.update(cs);
            String context = getLogin(request).getUser().getUserName() + "恢复了课表数据：" + cs.getId();
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            if (StringUtils.equals(ClassScheduleAction.REVERT, action)) {
                AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0037");
            }
        } catch (Exception e) {
            if (StringUtils.equals(ClassScheduleAction.REVERT, action)) {
                AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0038");
            }
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取时间差（小时）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    private double getclassHours(String startTime, String endTime) {
        double startHour = Integer.valueOf(startTime.split(":")[0]);
        double startMin = Integer.valueOf(startTime.split(":")[1]);
        double endHour = Integer.valueOf(endTime.split(":")[0]);
        double endMin = Integer.valueOf(endTime.split(":")[1]);
        double hours = ((endHour - startHour) * 60 + (endMin - startMin)) / 60;
        return hours;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/teacherInfo")
    public String teacherInfo(ModelMap model, @RequestBody String content,
                              HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String teacher_id = JSONUtils.getStr(jsonObj, "teacher_id");

            String student_id = JSONUtils.getStr(jsonObj, "student_id");


            String scheduledDateStr = JSONUtils.getStr(jsonObj, "scheduledDate");//上课日期
            Date scheduledDate = LanDateUtils.convertStringToDate("yyyy-MM-dd", scheduledDateStr);
            User teacher = userService.loadById(teacher_id);
            model.put("teacherType", teacher.getType());//教师性质

            WhereCondition wc = new WhereCondition();
            wc.andEquals("consumption_teacher.teacher_id", teacher_id);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(scheduledDate);
            wc.andGreaterEquals("scheduledDate", LanDateUtils.getFirstDayOfMonth(scheduledDate, 0));
            wc.andLessEquals("scheduledDate", LanDateUtils.getLastDayOfMonth(scheduledDate, 0));
            Double consumedClass_teacher = consumption_teacherService.getConsumedClass(wc);
            if (consumedClass_teacher != null && consumedClass_teacher > 80) {//是否超过80小时
                model.put("over80", true);
            } else {
                model.put("over80", false);
            }

            WhereCondition wc2 = new WhereCondition();
            wc2.andEquals("student_id", student_id);
            wc2.andEquals("status", "1");
            wc2.andGreaterEquals("scheduledDate", LanDateUtils.getFirstDayOfMonth(scheduledDate, 0));
            wc2.andLessEquals("scheduledDate", LanDateUtils.getLastDayOfMonth(scheduledDate, 0));
            Double summ = 0d;
//			summ = classScheduleService.sumClass(wc2);
//			model.put("summ", summ);
//

            Zidingyi z = new Zidingyi();
            //计算本月的排课次数,排除没有请假或者缺勤而删掉的课
            String sql = " SELECT COUNT(1) paiKeCiShu FROM classSchedule a LEFT JOIN attendancebook b on a.id = b.classSchedule_id ";
            sql += " WHERE a.student_id = '" + student_id + "' ";
            sql += " AND !(b.attendanceStatus is null  and a.`status` = '2' and a.leaveState = '0') ";
            sql += " AND a.scheduledDate >= '" + LanDateUtils.getFirstDayOfMonth(scheduledDate, 0) + "' ";
            sql += " AND a.scheduledDate <= '" + LanDateUtils.getLastDayOfMonth(scheduledDate, 0) + "' ";
            //sql += " AND a.zone_id = 'Id_zone00000007' ";
            z.setSql(sql);
            List<Map> list = tongjiService.query(z);
            if (!list.isEmpty()) {
                summ = list.get(0).get("paiKeCiShu") == null ? 0 : Double.parseDouble(list.get(0).get("paiKeCiShu").toString());
            }
            model.put("summ", summ);

            List<String> ids = new ArrayList<String>();
            ids.add("0");
            ids.add("2");
            WhereCondition wc3 = new WhereCondition();
            wc3.andIn("approvalStatus", ids);
            wc3.andEquals("student_id", student_id);
            wc3.andGreaterEquals("leaveDate", LanDateUtils.getFirstDayOfMonth(scheduledDate, 0));
            wc3.andLessEquals("leaveDate", LanDateUtils.getLastDayOfMonth(scheduledDate, 0));
            List<View_student_leave> l_summ = view_student_leaveService.query(wc3);
            Double total_leave = 0d;
            if (!l_summ.isEmpty()) {
                total_leave = Double.valueOf(l_summ.size());
            }
            model.put("total_leave", total_leave);

            String s = JSONObject.fromObject(model, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 请假保存
     *
     * @param model
     * @param content
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/ ngres/paikeguanli/view_paike_class/leave")
    public String leave(ModelMap model, @RequestBody String content,
                        final HttpServletRequest request, final HttpServletResponse response) {
        try {
            final User user = getLogin(request).getUser();
            final JSONObject jsonObj = JSONObject.fromObject(content);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                @Override
                protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                    try {
                        String actor = JSONUtils.getStr(jsonObj, "actor");
                        JSONObject jObj = jsonObj.getJSONObject("item");
                        //时区转换
                        String timezone_id = JSONUtils.getStr(jsonObj, "timezone_id");
                        Timezone timezone = timezoneService.loadById(timezone_id);
                        //获取要转换的时间
                        String handleTime = JSONUtils.getStr(jObj, "handleTime");
                        String leaveDatetimeStart = JSONUtils.getStr(jObj, "leaveDatetimeStart");
                        String leaveDatetimeEnd = JSONUtils.getStr(jObj, "leaveDatetimeEnd");
                        String createTime = JSONUtils.getStr(jObj, "createTime");
                        //转换为上海时区的时间
                        handleTime = TimezoneUtil.timezoneChangeString(handleTime, timezone.getTimezone());
                        leaveDatetimeStart = TimezoneUtil.timezoneChangeString(leaveDatetimeStart, timezone.getTimezone());
                        String leaveDate = leaveDatetimeStart.substring(0, 10);
                        String startTiemJson = leaveDatetimeStart.substring(11, 16);
                        leaveDatetimeEnd = TimezoneUtil.timezoneChangeString(leaveDatetimeEnd, timezone.getTimezone());
                        String endTimeJson = leaveDatetimeEnd.substring(11, 16);
                        createTime = TimezoneUtil.timezoneChangeString(createTime, timezone.getTimezone());
                        //封装到原数据
                        jObj.elementOpt("handleTime", handleTime);
                        jObj.elementOpt("leaveDatetimeStart", leaveDatetimeStart);
                        jObj.elementOpt("leaveDatetimeEnd", leaveDatetimeEnd);
                        jObj.elementOpt("leaveDate", leaveDate);
                        jObj.elementOpt("startTime", startTiemJson);
                        jObj.elementOpt("endTime", endTimeJson);
                        jObj.elementOpt("createTime", createTime);
                        //时区转化结束


                        String teacherType = JSONUtils.getStr(jObj, "teacherType");
                        ClassSchedule classSchedule = new ClassSchedule();
                        if (StringUtils.equalsIgnoreCase(LeaveActor.STUDENT, actor)) {
                            Student_leave student_leave = new Student_leave();
                            JSONObject.toBean(jObj, student_leave, jsonConfig);
                            WhereCondition wc = new WhereCondition();
                            wc.andEquals("classSchedule_id", student_leave.getClassSchedule_id());
                            List<Student_leave> list = student_leaveService.query(wc);
                            if ("1".equals(student_leave.getIsInadvance().trim())) {//是否大于24小时
                                //判断是否有可扣课时
                                String givenClass = JSONUtils.getStr(jObj, "givenClass");//补教师（小时）
                                String deductionClass = JSONUtils.getStr(jObj, "deductionClass");//扣学员（小时）
                                if (StringUtils.equals("0", givenClass) && StringUtils.equals("0", deductionClass)) {
                                    student_leave.setApprovalStatus(ApprovalStatus.STATUS_END); //审批状态，通过
									/*student_leave.setDeductionClass(0d);
									student_leave.setGivenClass(0d);*/
                                    classSchedule.setStatus(ClassScheduleStatus.DELETED);//删除课表
                                    classSchedule.setDescription("学生提前24小时请假");//删除原因
                                    updateStudentFreeTime(student_leave.getClassSchedule_id());
                                } else {
                                    student_leave.setApprovalStatus(ApprovalStatus.STATUS_NEW); //审批状态，待审批
                                }


                            } else {
                                student_leave.setApprovalStatus(ApprovalStatus.STATUS_NEW); //审批状态，待审批
                            }

                            student_leave.setLockStatus(Enums.FALSE_STRING);  //锁定状态，未锁定
                            student_leave.setCreator_id(user.getId());
                            student_leave.setCreateTime(LanDateUtils.getNowDate());
                            String context = getLogin(request).getUser().getUserName();
                            if (list.isEmpty()) {
                                student_leave.setId(sequenceManager.generateId("student_leave")); //ID
                                student_leaveService.insert(student_leave);
                                context += "新增了学生请假，新增学生请假" + student_leave.getId();
                            } else {
                                student_leave.setId(list.get(0).getId()); //ID
                                student_leaveService.update(student_leave);
                                context += "修改了学生请假，修改学生请假" + list.get(0).getId();
                            }
                            operationLog.setId(sequenceManager.generateId("operationLog"));
                            operationLog.setUser_id(getLogin(request).getUser().getId());
                            operationLog.setCreateTime(new Date());
                            operationLog.setContent(context);
                            operationLogService.insert(operationLog);
                            /*更改课表请假状态*/
                            classSchedule.setId(student_leave.getClassSchedule_id());
                            classSchedule.setLeaveState(LeaveState.LEAVE_STUDENT);

                            //如果学生请假通过，则发送邮件提醒到排课顾问
                            View_student_leave sl = view_student_leaveService.loadById(student_leave.getId());
                            ClassSchedule classSchedule1 = classScheduleService.loadById(sl.getClassSchedule_id());
                            User teacher = userService.loadById(classSchedule1.getTeacher_id());
                            WhereCondition wc2 = new WhereCondition();
//							wc2.andEquals("teacher_id", teacher.getId());
//							wc2.andEquals("zone_id", classSchedule1.getZone_id());
//							List<Teacher_zone> zone_list = teacher_zoneService.query(wc2);
//							boolean is_SH = false;
//							if(!zone_list.isEmpty()) {
//								for (Teacher_zone c : zone_list) {
//									if (StringUtils.equals("Id_city00000001", c.getCity_id().toString()) //上海地区的发送邮件
//										|| StringUtils.equals("2", classSchedule1.getBUType())	) //online地区的发送邮件
//										is_SH = true;
//								}
//							}
                            //发送邮箱
                            boolean isEmail = false;
                            String campusMailbox = null;
                            if (StringUtils.isNotBlank(classSchedule1.getZone_id())) {
                                Zone zone = zoneService.loadById(classSchedule1.getZone_id());
                                if (StringUtils.isNotBlank(zone.getCampusMailbox())) {
                                    campusMailbox = zone.getCampusMailbox();
                                    isEmail = true;
                                }
                            }
                            //审核通过,切有校区邮箱给顾问发送学生请假邮件
                            if (StringUtils.equals(ApprovalStatus.STATUS_END, student_leave.getApprovalStatus()) && isEmail) {
                                //判断邮箱是否多个
                                String[] campusMailboxs = campusMailbox.split(",");
                                //内容
                                String bodyText = "";
                                String subject = "";
                                if ("Id_zone00000016".equals(classSchedule1.getZone_id())) {
                                    subject += "OBT-学生请假-" + sl.getEnglishName() + " " + sl.getChineseName();
                                } else {
                                    subject += "【学生请假】" + sl.getEnglishName() + " " + sl.getChineseName();
                                }
                                bodyText += "学生" + sl.getEnglishName() + " " + sl.getChineseName() + "请假，" +
                                        LanDateUtils.format(sl.getLeaveDate(), "yyyy-MM-dd") + "日" +
                                        sl.getStartTime() + "-" + sl.getEndTime() + " " + teacher.getEnglishName() +
                                        " 的课已取消,请及时告知老师。\r\n" +
                                        "请假原因：" + sl.getReason();

                                System.out.println("send Email start..");
                                for (int i = 0; i < campusMailboxs.length; i++) {
                                    String paiKeGuWeiEamil = campusMailboxs[i];

                                    String bodyText0 = "Dear " + paiKeGuWeiEamil.substring(0, paiKeGuWeiEamil.indexOf("@")) + " \r\n";
                                    bodyText = bodyText0 + bodyText;
                                    boolean isSuccess = lanmoMailSender.sendMassageDefault(paiKeGuWeiEamil, null, null, bodyText, subject);
                                    if (!isSuccess) {
                                        System.out.println("Failure Sending Email To " + paiKeGuWeiEamil);
                                    } else {
                                        System.out.println("send email to " + paiKeGuWeiEamil + " is successful");
                                    }
                                }
                                System.out.println("send Email end..");

                                // OBT - send email to student
                                if ("Id_zone00000016".equals(classSchedule1.getZone_id())) {
                                    String courseName = courseService.loadById(classSchedule1.getCourse_id()).getName();
                                    String obt_subject = "Lesson cancellation";
                                    String obt_bodyText = "";
                                    obt_bodyText += "We recently received the cancellation request and are processing it." + "\r\n\r\n" +
                                    "Your class " + courseName + " " + LanDateUtils.format(sl.getLeaveDate(), "yyyy-MM-dd") +
                                    " " +sl.getStartTime() + "-" + sl.getEndTime() + "\r\n\r\n" +
                                    "If you have any questions about your lesson booking, please reach out to your study advisor." + "\r\n\r\n" +
                                            "Best regards,\r\n  Oxbridge Tutor \r\n Whatsapp : +852 56613196 \r\n Telephone:+ 852-2129-7175 \r\n Email address: <EMAIL>";
                                    String obt_bodyText0 = "Dear " + sl.getEnglishName() + " \r\n";
                                    obt_bodyText = obt_bodyText0 + obt_bodyText;
                                    String studentEmail = studentService.loadById(sl.getStudent_id()).getEmail().toString();
                                    if (StringUtils.isNotEmpty(studentEmail)) {
                                        boolean obt_isSuccess = lanmoMailSender.sendMassageDefault(studentEmail, null, null, obt_bodyText, obt_subject);
                                        if (!obt_isSuccess) {
                                            System.out.println("Failure Sending Email To " + studentEmail);
                                        } else {
                                            System.out.println("send email to " + studentEmail + " is successful");
                                        }
                                    }

                                }

                                // cencel  tencent meeting
                                User usr = userService.loadById(classSchedule1.getTeacher_id());
                                //ClassSchedule cls = classScheduleService.loadById(p.getId());
                                String meetingid = classSchedule1.getTencentmeetingid();
                                if (StringUtils.isNotEmpty(meetingid)) {
                                    try {
                                        String contextTencent = "";
                                        String res = TencentUtil.cancelTencentMeeting(meetingid, usr.getEmail());
                                        if (StringUtils.equals(res, "error")) {
                                            contextTencent += "学生请假，删除腾讯会议错误" + classSchedule.getId();
                                        } else {
                                            contextTencent += "学生请假，删除腾讯会议成功" + classSchedule.getId();
                                            classSchedule.setTencenturl("");
                                            classSchedule.setTencentmeetingid("");
                                            classSchedule.setTencentmeetingcode("");
                                        }
                                        operationLog.setId(sequenceManager.generateId("operationLog"));
                                        operationLog.setUser_id(getLogin(request).getUser().getId());
                                        operationLog.setCreateTime(new Date());
                                        operationLog.setContent(contextTencent);
                                        operationLogService.insert(operationLog);
                                    } catch (Exception e) {
                                        e.printStackTrace();;
                                    }
                                }

                            }

                        } else {
                            Teacher_leave teacher_leave = new Teacher_leave();
                            JSONObject.toBean(jObj, teacher_leave, jsonConfig);
                            WhereCondition wc = new WhereCondition();
                            wc.andEquals("classSchedule_id", teacher_leave.getClassSchedule_id());
                            List<Teacher_leave> list = teacher_leaveService.query(wc);
                            if (StringUtils.equals(TeacherType.FULL_TIME, teacherType)) {
                                teacher_leave.setDeductionClass(null);
                            } else {
                                teacher_leave.setDeductionWage(null);
                            }
                            if ("1".equals(teacher_leave.getIsInadvance().trim())) {//是否大于24小时
                                teacher_leave.setApprovalStatus(ApprovalStatus.STATUS_END); //审批状态，通过
                                teacher_leave.setGivenClass(0d);
                                teacher_leave.setDeductionClass(0d);
                                classSchedule.setStatus(ClassScheduleStatus.DELETED);
                                classSchedule.setDescription("教师提前24小时请假");//删除原因
                                updateTeacherFreeTime(teacher_leave.getClassSchedule_id());
                            } else {
                                teacher_leave.setApprovalStatus(ApprovalStatus.STATUS_NEW); //审批状态，待审批
                            }
                            teacher_leave.setLockStatus(Enums.FALSE_STRING);  //锁定状态，未锁定
                            teacher_leave.setCreator_id(user.getId());
                            teacher_leave.setCreateTime(LanDateUtils.getNowDate());
                            String context = getLogin(request).getUser().getUserName();
                            if (list.isEmpty()) {
                                teacher_leave.setId(sequenceManager.generateId("teacher_leave")); //ID
                                teacher_leaveService.insert(teacher_leave);
                                context += "新增了老师请假，新增老师请假" + teacher_leave.getId();
                            } else {
                                teacher_leave.setId(list.get(0).getId()); //ID
                                teacher_leaveService.update(teacher_leave);
                                context += "修改了老师请假，修改老师请假" + list.get(0).getId();
                            }
                            operationLog.setId(sequenceManager.generateId("operationLog"));
                            operationLog.setUser_id(getLogin(request).getUser().getId());
                            operationLog.setCreateTime(new Date());
                            operationLog.setContent(context);
                            operationLogService.insert(operationLog);
                            /*更改课表请假状态*/
                            classSchedule.setId(teacher_leave.getClassSchedule_id());
                            classSchedule.setLeaveState(LeaveState.LEAVE_TEACHER);

                            //如果老师请假通过，则发送邮件提醒到学生的study advisor
                            if (StringUtils.equals(ApprovalStatus.STATUS_END, teacher_leave.getApprovalStatus())) {

                                System.out.println("send Email start..");
                                View_teacher_leave tl = view_teacher_leaveService.loadById(teacher_leave.getId());
                                ClassSchedule classSchedule1 = classScheduleService.loadById(tl.getClassSchedule_id());
                                User teacher = userService.getByStudent((studentService.loadById(classSchedule1.getStudent_id()).getId()), classSchedule1.getZone_id());
                                if (null != teacher) {
                                    Student student = studentService.loadById(classScheduleService.loadById(tl.getClassSchedule_id()).getStudent_id());
                                    String bodyText = "Dear " + teacher.getChineseName() + "\r\n";
                                    String subject = "";
                                    if ("Id_zone00000016".equals(classSchedule1.getZone_id())) {
                                        subject += "OBT-老师请假-" + tl.getEnglishName();
                                    } else {
                                        subject += "【老师请假】" + tl.getEnglishName() + " " + tl.getChineseName();
                                    }
                                    bodyText += "老师" + tl.getEnglishName() + "请假，" +
                                            LanDateUtils.format(tl.getLeaveDate(), "yyyy-MM-dd") + "日" +
                                            tl.getStartTime() + "-" + tl.getEndTime() + " " + student.getChineseName() +
                                            " 的课已取消,请及时告知学生。\r\n" +
                                            "请假原因：" + tl.getReason();
                                    System.out.println(teacher.getEmail());
                                    boolean isSuccess = lanmoMailSender.sendMassageDefault(teacher.getEmail(), null, null, bodyText, subject);

                                    if (!isSuccess) {
                                        System.out.println("Failure Sending Email To " + teacher.getEmail());
                                    } else {
                                        System.out.println("send email to " + teacher.getEmail() + " is successful");
                                    }
                                }
                                System.out.println("send Email end..");

                                // cencel  tencent meeting
                                User usr = userService.loadById(classSchedule1.getTeacher_id());
                                //ClassSchedule cls = classScheduleService.loadById(p.getId());
                                String meetingid = classSchedule1.getTencentmeetingid();
                                if (StringUtils.isNotEmpty(meetingid)) {
                                    try {
                                        String contextTencent = "";
                                        String res = TencentUtil.cancelTencentMeeting(meetingid, usr.getEmail());
                                        if (StringUtils.equals(res, "error")) {
                                            contextTencent += "老师请假，删除腾讯会议错误" + classSchedule.getId();
                                        } else {
                                            contextTencent += "老师请假，删除腾讯会议成功" + classSchedule.getId();
                                            classSchedule.setTencenturl("");
                                            classSchedule.setTencentmeetingid("");
                                            classSchedule.setTencentmeetingcode("");
                                        }
                                        operationLog.setId(sequenceManager.generateId("operationLog"));
                                        operationLog.setUser_id(getLogin(request).getUser().getId());
                                        operationLog.setCreateTime(new Date());
                                        operationLog.setContent(contextTencent);
                                        operationLogService.insert(operationLog);
                                    } catch (Exception e) {
                                        e.printStackTrace();;
                                    }
                                }

                            }
                        }
                        classScheduleService.update(classSchedule);
                    } catch (Exception e) {
                        AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0039");
                        arg0.setRollbackOnly();
                        e.printStackTrace();
                    }
                }
            });

            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0040");
        } catch (Exception e) {
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0039");
            e.printStackTrace();
        }
        return null;
    }

    private void updateStudentFreeTime(String id) {
        ClassSchedule classSchedule = classScheduleService.loadById(id);
        String sql = "SELECT a.id as id from student_freeTime a where a.freeDate='" + LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd") + "' "
                + "and a.startTime>='" + classSchedule.getStartTime() + "' and a.endTime<='" + classSchedule.getEndTime() + "' and a.student_id='" + classSchedule.getStudent_id() + "'";
        Zidingyi z = new Zidingyi();
        z.setSql(sql);
        List<Map> li = tongjiService.query(z);
        for (Map m : li) {
            student_freeTimeService.delete(Integer.valueOf(m.get("id").toString()));
        }
        sql = "SELECT a.id as id from student_freeTime a where a.freeDate='" + LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd") + "' "
                + "and a.startTime<'" + classSchedule.getStartTime() + "' and a.endTime>'" + classSchedule.getStartTime() + "' and a.student_id='" + classSchedule.getStudent_id() + "'";
        z.setSql(sql);
        List<Map> li1 = tongjiService.query(z);
        for (Map m : li1) {
            Student_freeTime student_freeTime = student_freeTimeService.loadById(m.get("id").toString());
            //student_freeTime.setEndTime(classSchedule.getStartTime());
            student_freeTime.setFreeTimeCode(generateFreeTimeCode(student_freeTime.getStartTime(), classSchedule.getStartTime()));
            student_freeTimeService.update(student_freeTime);
        }
        sql = "SELECT a.id as id from student_freeTime a where a.freeDate='" + LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd") + "' "
                + "and a.startTime<'" + classSchedule.getEndTime() + "' and a.endTime>'" + classSchedule.getEndTime() + "' and a.student_id='" + classSchedule.getStudent_id() + "'";
        z.setSql(sql);
        List<Map> li2 = tongjiService.query(z);
        for (Map m : li2) {
            Student_freeTime student_freeTime = student_freeTimeService.loadById(m.get("id").toString());
            //student_freeTime.setStartTime(classSchedule.getEndTime());
            student_freeTime.setFreeTimeCode(generateFreeTimeCode(classSchedule.getEndTime(), student_freeTime.getEndTime()));
            student_freeTimeService.update(student_freeTime);
        }
    }


    private void updateTeacherFreeTime(String id) {
        ClassSchedule classSchedule = classScheduleService.loadById(id);
        String sql = "SELECT a.id as id from teacher_freeTime a where a.freeDate='" + LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd") + "' "
                + "and a.startTime>='" + classSchedule.getStartTime() + "' and a.endTime<='" + classSchedule.getEndTime() + "' and a.teacher_id='" + classSchedule.getTeacher_id() + "'";
        Zidingyi z = new Zidingyi();
        z.setSql(sql);
        List<Map> li = tongjiService.query(z);
        for (Map m : li) {
            teacher_freeTimeService.delete(Integer.valueOf(m.get("id").toString()));
        }
        sql = "SELECT a.id as id from teacher_freeTime a where a.freeDate='" + LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd") + "' "
                + "and a.startTime<'" + classSchedule.getStartTime() + "' and a.endTime>'" + classSchedule.getStartTime() + "' and a.teacher_id='" + classSchedule.getTeacher_id() + "'";
        z.setSql(sql);
        List<Map> li1 = tongjiService.query(z);
        for (Map m : li1) {
            Teacher_freeTime teacher_freeTime = teacher_freeTimeService.loadById(m.get("id").toString());
            teacher_freeTime.setEndTime(classSchedule.getStartTime());
            teacher_freeTime.setFreeTimeCode(generateFreeTimeCode(teacher_freeTime.getStartTime(), teacher_freeTime.getEndTime()));
            teacher_freeTimeService.update(teacher_freeTime);
        }
        sql = "SELECT a.id as id from teacher_freeTime a where a.freeDate='" + LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd") + "' "
                + "and a.startTime<'" + classSchedule.getEndTime() + "' and a.endTime>'" + classSchedule.getEndTime() + "' and a.teacher_id='" + classSchedule.getTeacher_id() + "'";
        z.setSql(sql);
        List<Map> li2 = tongjiService.query(z);
        for (Map m : li2) {
            Teacher_freeTime teacher_freeTime = teacher_freeTimeService.loadById(m.get("id").toString());
            teacher_freeTime.setStartTime(classSchedule.getEndTime());
            teacher_freeTime.setFreeTimeCode(generateFreeTimeCode(teacher_freeTime.getStartTime(), teacher_freeTime.getEndTime()));
            teacher_freeTimeService.update(teacher_freeTime);
        }
    }

    /**
     * 封装占位符
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private String generateFreeTimeCode(String startTime, String endTime) {
        String freeTimeCode = "";
        int startMin = Integer.valueOf(startTime.split(":")[1]);
        int endMin = Integer.valueOf(endTime.split(":")[1]);
        if (startMin == 0 && endMin == 15) {
            freeTimeCode += "1";
        } else if (startMin == 0 && endMin == 30) {
            freeTimeCode += "12";
        } else if (startMin == 0 && endMin == 45) {
            freeTimeCode += "123";
        } else if (startMin == 15 && endMin == 0) {
            freeTimeCode += "234";
        } else if (startMin == 15 && endMin == 30) {
            freeTimeCode += "2";
        } else if (startMin == 15 && endMin == 45) {
            freeTimeCode += "23";
        } else if (startMin == 30 && endMin == 0) {
            freeTimeCode += "34";
        } else if (startMin == 30 && endMin == 45) {
            freeTimeCode += "3";
        } else if (startMin == 45 && endMin == 0) {
            freeTimeCode += "4";
        }
        return freeTimeCode;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/cancelLeave")
    public String cancelLeave(ModelMap model, @RequestBody String content,
                              final HttpServletRequest request, final HttpServletResponse response) {
        try {
            final JSONObject jsonObj = JSONObject.fromObject(content);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                @Override
                protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                    try {
                        String actor = JSONUtils.getStr(jsonObj, "actor");
                        JSONObject jObj = jsonObj.getJSONObject("item");
                        String classSchedule_id = JSONUtils.getStr(jObj, "id");
                        ClassSchedule classSchedule = new ClassSchedule();
                        classSchedule.setId(classSchedule_id);
                        classSchedule.setLeaveState(LeaveState.LEAVE_NONE);

                        WhereCondition wc = new WhereCondition();
                        wc.andEquals("classSchedule_id", classSchedule_id);
                        String context = getLogin(request).getUser().getUserName();
                        boolean flag = false;
                        if (StringUtils.equalsIgnoreCase(LeaveActor.STUDENT, actor)) {
                            List<Student_leave> li = student_leaveService.query(wc);
                            if (CollectionUtils.isEmpty(li) || li.size() != 1) {
                                AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041");
                                return;
                            }

                            Student_leave sl = li.get(0);
                            if ("0".equals(sl.getApprovalStatus())) {
                                AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041");
                                flag = true;
                            } else if ("2".equals(sl.getApprovalStatus())) {
                                System.out.println("取消已审核学员请假");
                                classSchedule.setStatus(ClassScheduleStatus.CONFIRMED);//设置课程状态为已确认
                                classSchedule.setNotes("");
                                //取消已审核学员请假
                                boolean studentLeaveFlag = deleteApprovalLeave(classSchedule_id, response);
                                if (studentLeaveFlag) {
                                    student_leaveService.deleteByCondition(wc);
                                    context += "取消了已审核学生请假，课程：" + classSchedule_id;
                                    AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0042");
                                } else {
                                    flag = true;
                                }
                            } else {
                                //取消学生请假
                                student_leaveService.deleteByCondition(wc);
                                context += "取消了学生请假，课程：" + classSchedule_id;
                                AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0042");
                            }
                        } else { //取消老师请假
                            List<Teacher_leave> li = teacher_leaveService.query(wc);
                            if (CollectionUtils.isEmpty(li) || li.size() != 1) {
                                AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041");
                                return;
                            }
                            Teacher_leave tl = li.get(0);
                            //因为教师请假,赠送学员免费课时,而判断课与课时的数据是2017-12-27后加上的,所以该时间前的数据没法判断是对于的免费合同所以不让他修改
                            String date = "2017-12-27";
                            if (tl.getGivenClass() > 0 && LanDateUtils.compare_date(tl.getHandleTime().substring(0, 10), date) != 1) {
                                AjaxUtils.ajaxJsonErrorMessage(response, date + "号之前的已审核请假不能修改,请联系管理员");
                                return;
                            }

                            if ("0".equals(tl.getApprovalStatus())) {
                                flag = true;
                                AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041");
                            } else if ("2".equals(tl.getApprovalStatus())) {
                                //已审核教师取消请假
                                classSchedule.setStatus(ClassScheduleStatus.CONFIRMED);//设置课程状态为已确认
                                classSchedule.setNotes("");
                                boolean teacherLeavFlag = deleteApprovalLeave(classSchedule_id, response);
                                if (teacherLeavFlag) {
                                    teacher_leaveService.deleteByCondition(wc);
                                    context += "取消了老师请假，课程：" + classSchedule_id;
                                    AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0042");
                                } else {
                                    flag = true;
                                }

                            } else {
                                //教师取消请假
                                teacher_leaveService.deleteByCondition(wc);
                                context += "取消了老师请假，课程：" + classSchedule_id;
                                AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0042");
                            }
                        }
                        if (!flag) {
                            classScheduleService.update(classSchedule);
                        }
                        operationLog.setId(sequenceManager.generateId("operationLog"));
                        operationLog.setUser_id(getLogin(request).getUser().getId());
                        operationLog.setCreateTime(new Date());
                        operationLog.setContent(context);
                        operationLogService.insert(operationLog);
                    } catch (Exception e) {
                        AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041");
                        arg0.setRollbackOnly();
                        e.printStackTrace();
                    }
                }
            });

        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 是否有补偿合同
     *
     * @param classSchedule
     * @return
     */
    private void isContractOfIndemnity(ClassSchedule classSchedule) {
        //是否给学生补偿合同
        WhereCondition wc = new WhereCondition();
        wc.andEquals("compensateStudent", classSchedule.getId());
        List<Contract> contractList = contractService.query(wc);
        if (CollectionUtils.isNotEmpty(contractList)) {
            for (Contract contract : contractList) {
                //判断如何是否消耗,已经被消耗，还需要将该免费合同对应的consumption_student_contract中的contract_id置为该学员执行中的相应类型的合同id，如果没有执行中的相应类型的合同，则置为null
                if (contract.getConsumedClass() == 0) {
                    contractService.delete(contract.getId());//删除合同
                } else {
                    wc = new WhereCondition();
                    wc.andEquals("contract_id", contract.getId());
                    //获取消耗免费合同对于的消耗学生合同信息
                    List<Consumption_student_contract> cscList = consumption_student_contractService.query(wc);

                    if (CollectionUtils.isNotEmpty(cscList)) {
                        for (Consumption_student_contract csc : cscList) {
                            //消耗的课时数
                            Double consumedClass = csc.getConsumedClass();

                            List<String> contractType2List = new ArrayList<String>();
                            if (Enums.OFFlINE.equals(classSchedule.getBUType())) {
                                contractType2List.add(Enums.OFFlINE);
                                contractType2List.add(Enums.ContractType2.HYBRID);
                            }
                            if (Enums.ONLINE.equals(classSchedule.getBUType())) {
                                contractType2List.add(Enums.ONLINE);
                                contractType2List.add(Enums.ContractType2.HYBRID);
                            }
                            List<Contract> contracts = new ArrayList<Contract>();//学生相同状态所有未消耗完的合同
                            if (contractType2List.size() > 0) {
                                contracts = contractService.getContractByClass(classSchedule.getStudent_id(), contractType2List);
                            } else {
                                contracts = contractService.getContractByClass(classSchedule.getStudent_id());
                            }
                            //
                            for (Contract item : contracts) {
                                //排除免费合同更好
                                if (StringUtils.equals(item.getId(), contract.getId())) {
                                    continue;
                                }
                                if (item.getAmount() * 2 - item.getConsumedClass() >= consumedClass) {
                                    Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                    consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                    consumption_student_contract.setConsumption_student_id(csc.getConsumption_student_id());// 学生耗课ID
                                    consumption_student_contract.setContract_id(item.getId());//合同ID
                                    consumption_student_contract.setConsumedClass(consumedClass);
                                    consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                    consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                    consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                    consumption_student_contractService.insert(consumption_student_contract);
                                    if (item.getAmount() * 2 - item.getConsumedClass() > consumedClass) {
                                        item.setStatus(ContractStatus.STAUS_2);
                                    } else {
                                        item.setStatus(ContractStatus.STAUS_3);
                                    }
                                    item.setConsumedClass(item.getConsumedClass() + consumedClass);
                                    contractService.update(item);

                                    consumedClass = 0d;
                                    break;
                                } else {
                                    double consumedClass1 = item.getAmount() * 2 - item.getConsumedClass();
                                    Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                    consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                    consumption_student_contract.setConsumption_student_id(csc.getConsumption_student_id());// 学生耗课ID
                                    consumption_student_contract.setContract_id(item.getId());//合同ID
                                    consumption_student_contract.setConsumedClass(consumedClass1);
                                    consumption_student_contract.setUnitPrice(item.getTuitionFee() / item.getAmount());// 单价
                                    consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                    consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                    consumption_student_contractService.insert(consumption_student_contract);
                                    item.setStatus(ContractStatus.STAUS_3);
                                    item.setConsumedClass(item.getAmount() * 2);
                                    contractService.update(item);
                                    consumedClass = consumedClass - (consumedClass1);
                                }


                            }
                            //还有多余的课时而没有合同了,,合同id为空
                            if (consumedClass > 0) {
                                Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                consumption_student_contract.setConsumption_student_id(csc.getConsumption_student_id());// 学生耗课ID
                                consumption_student_contract.setConsumedClass(consumedClass);
                                consumption_student_contract.setLockStatus(Enums.FALSE_STRING);// 锁定状态
                                consumption_student_contract.setIsUploaded(Enums.FALSE_STRING);// 上传状态
                                consumption_student_contractService.insert(consumption_student_contract);
                            }

                            //删除原来的学生耗课合同
                            consumption_student_contractService.delete(csc.getId());

                        }
                    }

                }

                //删除免费合同信息
                contractService.delete(contract.getId());
            }
        }
    }

    /**
     * 取消已审核学员请假
     *
     * @param classSchedule_id
     * @param response
     */
    private boolean deleteApprovalLeave(String classSchedule_id, HttpServletResponse response) {

        ClassSchedule classSchedule = classScheduleService.loadById(classSchedule_id);
        //因为是审核的请假,status是删除状体,所以我们需要判断该时间段的学生和老师是否被占用了
        boolean flag = isClassScheduleEmploy(classSchedule, response);
        if (!flag) {
            return false;
        }
        WhereCondition wc = new WhereCondition();
        List<Consumption_student_contract> cscList = consumption_student_contractService.getByclassSchedule(classSchedule_id);

        //先去将合同的内容更新
        if (CollectionUtils.isNotEmpty(cscList)) {
            List<Map> contract_list = new ArrayList();
            //更新合同,把取消请假的耗课加入合同
            for (Consumption_student_contract csc : cscList) {
                if (StringUtils.isNotEmpty(csc.getContract_id())) {
                    Contract contract = contractService.loadById(csc.getContract_id());
                    Map contract_set = new HashMap();
                    contract_set.put("contract", contract.getId());
                    contract_set.put("diff", String.valueOf(contract.getConsumedClass() - csc.getConsumedClass()));
                    contract_list.add(contract_set);

                    contract.setConsumedClass(contract.getConsumedClass() - csc.getConsumedClass());
                    if (contract.getConsumedClass() == 0) {
                        contract.setStatus(ContractStatus.STAUS_1);
                    } else {
                        if (contract.getAmount() > contract.getConsumedClass() / 2) {
                            contract.setStatus(ContractStatus.STAUS_2);
                        } else {
                            contract.setStatus(ContractStatus.STAUS_3);
                        }
                    }
                    contractService.update(contract);
                    updateStudentShangke(contract.getStudentId(), contract.getStatus());
                }
            }

            if (!contract_list.isEmpty()) {
                for (Map ctt_set : contract_list) {
                    String contract_id = ctt_set.get("contract").toString();
                    Contract contract2 = new Contract();
                    contract2 = contractService.loadById(contract_id);
                    Double diff = Double.parseDouble(ctt_set.get("diff").toString());
                    //cscList2合同号为空的记录 所消耗课程类型和合同类型一致
                    List<Consumption_student_contract> cscList2 = consumption_student_contractService.getByStudent(contract2.getStudentId(), classSchedule_id, contract2.getContractType2());
                    //学生排课合同的耗课记录
                    if (!cscList2.isEmpty()) {
                        for (Consumption_student_contract csc : cscList2) {
                            boolean updateflag = false;
                            if (contract2.getAmount() * 2 - contract2.getConsumedClass() >= csc.getConsumedClass()
                                    && (contract2.getAmount() * 2 - contract2.getConsumedClass() > 0)) {

                                csc.setContract_id(contract2.getId());
                                contract2.setConsumedClass(contract2.getConsumedClass() + csc.getConsumedClass());
                                updateflag = true;
                            } else if (contract2.getAmount() * 2 - contract2.getConsumedClass() > 0) {
                                //当合同的耗课已有一部分时,
                                csc.setContract_id(contract2.getId());
                                Double diff_hours = csc.getConsumedClass() - (contract2.getAmount() * 2 - contract2.getConsumedClass());
                                csc.setConsumedClass(contract2.getAmount() * 2 - contract2.getConsumedClass());
                                contract2.setConsumedClass(contract2.getAmount() * 2);
                                // 多余的学生耗课为null
                                Consumption_student_contract consumption_student_contract = new Consumption_student_contract();
                                consumption_student_contract.setId(sequenceManager.generateId("consumption_student_contract"));
                                consumption_student_contract.setConsumption_student_id(csc.getConsumption_student_id());
                                consumption_student_contract.setContract_id(null);
                                consumption_student_contract.setConsumedClass(diff_hours);
                                consumption_student_contract.setUnitPrice(null);
                                consumption_student_contract.setLockStatus(csc.getLockStatus());
                                consumption_student_contract.setIsUploaded(csc.getIsUploaded());
                                consumption_student_contractService.insert(consumption_student_contract);
                                updateflag = true;
                            }
                            //只有true才更新
                            if (updateflag) {
                                contractService.update(contract2);
                                consumption_student_contractService.update(csc);
                            }
                        }
                    }
                }
            }
        }

        //判断是否给学生补偿合同,如果补偿删除
        isContractOfIndemnity(classSchedule);

        //删除学生耗课_合同
        consumption_student_contractService.deleteByclassSchedule(classSchedule_id);

        wc = new WhereCondition();
        wc.andEquals("classSchedule_id", classSchedule_id);

        // 删除学生耗课信息
        consumption_studentService.deleteByCondition(wc);

        // 删除教师耗课信息
        consumption_teacherService.deleteByCondition(wc);

        return true;
    }

    /**
     * 判断此段时间老师和学生是否被占用
     *
     * @param classSchedule
     * @param response
     * @return
     */
    private boolean isClassScheduleEmploy(ClassSchedule classSchedule, HttpServletResponse response) {
        String select = " SELECT  count(*) FROM classschedulelong ";
        String where = " WHERE STATUS <> 2 AND courseType <> 2 ";
        where += classScheduleService.getQueryWhereClassroomBytime(classSchedule.getStartTime(), classSchedule.getEndTime(), LanDateUtils.format(classSchedule.getScheduledDate(), "yyyy-MM-dd"));
        where += " and (teacher_id='" + classSchedule.getTeacher_id() + "' or student_id ='" + classSchedule.getStudent_id() + "' ) ";
        where += " and id <> '" + classSchedule.getId() + "' ";
        Zidingyi zi = new Zidingyi();
        zi.setSql(select + where);
        int count = tongjiService.count(zi);
        if (count > 0) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0041_1");//取消请假失败,学生或者老师被占用！
            return false;
        }
        return true;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/leaveData")
    public String leaveData(ModelMap model, @RequestBody String content,
                            HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String actor = JSONUtils.getStr(jsonObj, "actor");
            if (StringUtils.equalsIgnoreCase(LeaveActor.STUDENT, actor)) {
                String student_leave_id = JSONUtils.getStr(jsonObj, "student_leave_id");
                Student_leave student_leave = student_leaveService.loadById(student_leave_id);
                model.put("item", student_leave);
            } else {
                String teacher_leave_id = JSONUtils.getStr(jsonObj, "teacher_leave_id");
                Teacher_leave teacher_leave = teacher_leaveService.loadById(teacher_leave_id);
                model.put("item", teacher_leave);
                model.put("teacherType", userService.loadById(teacher_leave.getTeacher_id()).getType());//教师性质
            }
            model.put("actor", actor);
            AjaxUtils.ajaxJson(response, JSONObject.fromObject(model, jsonConfig).toString());
        } catch (Exception e) {
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/view_paike_class/autoArrangement")
    public String autoArrangement(ModelMap model, @RequestBody String content,
                                  HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");  //校区
            String startDate = JSONUtils.getStr(jsonObj, "startDate");
            String endDate = JSONUtils.getStr(jsonObj, "endDate");
            JSONArray jsonArr = jsonObj.getJSONArray("scheduledDateArray");
            if (LanDateUtils.compare_date(LanDateUtils.format(new Date(), "yyyy-MM-dd"), startDate) >= 0) {
                AjaxUtils.ajaxJsonErrorMessage(response, "自动排课开始时间不能小于今天");
                return null;
            }

            WhereCondition wc = new WhereCondition();
            wc.andEquals("delStatus", Enums.FALSE_STRING);
            //获取一天内最多可排课量
            List<Schedule> scheduleforDay = scheduleService.query(wc);
            String delete = "delete from classSchedule WHERE 1=1 and scheduledDate >='" + startDate + "' and scheduledDate <='" + endDate + "' and isAuto ='1' and status in(0,2) "
                    + " and classroom_id in( select c.id from classroom c where c.zone_id='" + zone_id + "') and id not in (select classSchedule_id from student_leave) and id not in (select classSchedule_id from teacher_leave)";
            Zidingyi z = new Zidingyi();
            z.setSql(delete);
            tongjiService.delete(z);
            int countClass = 0;
            Map<String, String> logMap = new HashMap<String, String>();
            Map<String, Map<String, Double>> weekMap = new HashMap<String, Map<String, Double>>();
            Map<String, String> mapW = getWeeks(jsonArr);
            for (Map.Entry<String, String> entry : mapW.entrySet()) {
                String sql = " select a.teacher_id as teacher_id,SUM(a.time) as keshi from classSchedule a where a.status<>'2' and a.scheduledDate in(" + entry.getKey() + ") group by a.teacher_id ";
                z = new Zidingyi();
                z.setSql(sql);
                List<Map> list = tongjiService.query(z);
                Map<String, Double> teacherMap = new HashMap<String, Double>();
                for (Map m : list) {
                    teacherMap.put(m.get("teacher_id").toString().trim(), Double.valueOf(m.get("keshi").toString().trim()));
                }
                weekMap.put(entry.getKey(), teacherMap);
            }
            Map<String, Map<String, Double>> monthMap = new HashMap<String, Map<String, Double>>();
            Map<String, String> mapM = getMonths(jsonArr);
            for (Map.Entry<String, String> entry : mapM.entrySet()) {
                String ms = entry.getKey();
                String[] mm = ms.split(",");
                String sql = " select a.teacher_id as teacher_id,SUM(a.time) as keshi from classSchedule a where a.status<>'2' and  a.scheduledDate>='" + mm[0] + "' and a.scheduledDate<='" + mm[1] + "' group by a.teacher_id ";
                z = new Zidingyi();
                z.setSql(sql);
                List<Map> list = tongjiService.query(z);
                Map<String, Double> teacherMap = new HashMap<String, Double>();
                for (Map m : list) {
                    teacherMap.put(m.get("teacher_id").toString().trim(), Double.valueOf(m.get("keshi").toString().trim()));
                }
                monthMap.put(entry.getKey().substring(0, 8), teacherMap);
            }
            Map<String, Map<String, String>> requirementMap = getStudentRequirement(zone_id, jsonArr);
            for (int i = 0; i < jsonArr.size(); i++) {
                Map<String, String> zoneMap = new HashMap<String, String>();
                Map<String, Double> studentDayMap = new HashMap<String, Double>();
                String day = jsonArr.getString(i);
                //教学点
                String sql = " select a.teacher_id as teacher,b.zone_id as zone from classSchedule a,classroom b where a.classroom_id=b.id "
                        + "and a.status<>'2' and a.scheduledDate='" + day + "' group by a.teacher_id,b.zone_id ";
                z = new Zidingyi();
                z.setSql(sql);
                List<Map> list = tongjiService.query(z);
                for (Map m : list) {
                    if (!zone_id.equals(m.get("zone").toString().trim())) {
                        zoneMap.put(m.get("teacher").toString().trim(), "1");
                    }
                }
                sql = " select a.student_id as student_id,SUM(a.time) as keshi from classSchedule a where a.status<>'2' and a.scheduledDate='" + day + "' group by a.student_id ";
                z.setSql(sql);
                list = tongjiService.query(z);
                for (Map m : list) {
                    studentDayMap.put(m.get("student_id").toString().trim(), Double.valueOf(m.get("keshi").toString().trim()));
                }
                sql = " select a.student_id as student_id,a.course_id as course_id,SUM(a.time) as keshi from classSchedule a where a.status<>'2' and a.scheduledDate='" + day + "' group by a.student_id,a.course_id ";
                z.setSql(sql);
                list = tongjiService.query(z);
                for (Map m : list) {
                    studentDayMap.put((m.get("student_id").toString().trim() + m.get("course_id").toString().trim()), Double.valueOf(m.get("keshi").toString().trim()));
                }
                for (Schedule schedule : scheduleforDay) {
                    countClass += view_paike_classService.generateClassSchedule2(day, schedule, getLogin(request).getUser().getId(), zone_id, zoneMap, studentDayMap, weekMap, monthMap, logMap, requirementMap, startDate, endDate);
                }
            }
            for (Map.Entry<String, String> entry : logMap.entrySet()) {
                System.out.println("Key3==>" + entry.getKey() + "  value3==>" + entry.getValue());
            }
            updateStudentShangke();
            String context = getLogin(request).getUser().getUserName() + "为校区" + zone_id + ",在" + startDate + "--" + endDate + ",自动排课,共排课" + countClass + "节";
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "共排课" + countClass + "节");
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0043");
            e.printStackTrace();
        }
        return null;
    }

    public void updateStudentShangke(String id, String flag) {
        Student student = studentService.loadById(id);
        if (student != null && student.getId() != null) {
            if ("2".equals(flag)) {
                student.setStatus("3");
            } else if ("1".equals(flag.trim())) {
                student.setStatus("2");
            } else if ("".equals(flag.trim())) {
                student.setStatus("1");
            } else {
                student.setStatus("4");
            }
            studentService.update(student);
        }
    }

    public void updateStudentShangke() {
        String sql = "select b.id as id,count(1) from classSchedule a,student b where a.student_id=b.id and a.status<>'2' and b.`status`='' GROUP BY b.id";
        Zidingyi z = new Zidingyi();
        z.setSql(sql);
        List<Map> list = tongjiService.query(z);
        for (Map m : list) {
            Student student = studentService.loadById(m.get("id").toString());
            if (student != null && student.getId() != null) {
                WhereCondition wc = new WhereCondition();
                wc.andEquals("studentId", student.getId());
                wc.andIsNull("persentType");
                List<Contract> li = contractService.query(wc);
                if (!li.isEmpty()) {
                    String flag = "0";
                    for (Contract c : li) {
                        if ("1".equals(c.getStatus()) && !"2".equals(flag)) {
                            flag = "1";
                        }
                        if ("2".equals(c.getStatus())) {
                            flag = "2";
                        }
                    }
                    if ("0".equals(flag)) {
                        student.setStatus("4");
                    }
                    if ("1".equals(flag)) {
                        student.setStatus("2");
                    }
                    if ("2".equals(flag)) {
                        student.setStatus("3");
                    }
                } else {
                    student.setStatus("1");
                }
                studentService.update(student);
            }
        }
    }

    public Map<String, String> getWeeks(JSONArray jsonArr) {
        Map<String, String> map = new HashMap<String, String>();
        for (int i = 0; i < jsonArr.size(); i++) {
            String s = LanDateUtils.getAllDayOfWeek(jsonArr.getString(i));
            map.put(s, "1");
        }
        return map;
    }

    public Map<String, String> getMonths(JSONArray jsonArr) {
        Map<String, String> map = new HashMap<String, String>();
        for (int i = 0; i < jsonArr.size(); i++) {
            String m = "";
            String m1 = jsonArr.getString(i).substring(0, 8);
            m += m1 + "01,";
            String m2 = jsonArr.getString(i).substring(5, 7);
            if ("01".equals(m2) || "03".equals(m2) || "05".equals(m2) || "07".equals(m2)
                    || "08".equals(m2) || "10".equals(m2) || "12".equals(m2)) {
                m += m1 + "31";
            }
            if ("04".equals(m2) || "06".equals(m2) || "09".equals(m2) || "11".equals(m2)) {
                m += m1 + "30";
            }
            if ("02".equals(m2)) {
                Integer m3 = Integer.valueOf(jsonArr.getString(i).substring(0, 4));
                if (m3 % 4 == 0) {
                    m += m1 + "29";
                } else {
                    m += m1 + "28";
                }
            }
            map.put(m, "1");
        }
        return map;
    }

    public Map<String, Map<String, String>> getStudentRequirement(String zone_id, JSONArray jsonArr) {//学生诉求
        System.out.println("jsonArr" + jsonArr);
        System.out.println("jsonArr" + jsonArr.getString(0));
        Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();
        String sql = " select a.student_id as student_id,a.course_id as course_id,a.startDate as startDate,a.endDate as endDate,"
                + "a.totalUpperLimit as total,a.dayUpperLimit as day,a.weekUpperLimit as week,a.monthUpperLimit as month,a.intervalDay as intervalDay";
        sql += " from requirement a,student b where a.student_id=b.id and a.endDate>='" + jsonArr.getString(0) + "' and b.zone_id='" + zone_id + "'";
        Zidingyi z = new Zidingyi();
        z.setSql(sql);
        List<Map> list = tongjiService.query(z);
        for (Map m : list) {
            Map<String, String> map1 = new HashMap<String, String>();
            map1.put("endDate", m.get("endDate").toString());
            sql = " select SUM(c.time) as keshi from classSchedule c where c.student_id='" + m.get("student_id").toString() + "' and c.course_id='" + m.get("course_id").toString() + "'"
                    + " and c.status<>'2' and  c.scheduledDate>='" + m.get("startDate").toString() + "' and c.scheduledDate<='" + m.get("endDate").toString() + "'";
            z = new Zidingyi();
            z.setSql(sql);
            list = tongjiService.query(z);
            if (m.get("total") == null || "".equals(m.get("total").toString().trim()) || "0".equals(m.get("total").toString().trim())) {
                map1.put("total,1", "true");
            } else {
                map1.put("total,1", m.get("total").toString());
                if (!list.isEmpty() && list.get(0) != null && list.get(0).get("keshi") != null) {
                    if (Double.valueOf(m.get("total").toString()) > Double.valueOf(list.get(0).get("keshi").toString())) {
                        map1.put("total,2", String.valueOf(list.get(0).get("keshi").toString()));
                    } else {
                        continue;
                    }
                } else {
                    map1.put("total,2", "0");
                }

            }
            for (int i = 0; i < jsonArr.size(); i++) {
                if (LanDateUtils.compare_date(m.get("endDate").toString(), jsonArr.getString(i)) >= 0) {
                    if (m.get("day") == null || "".equals(m.get("day").toString().trim()) || "0".equals(m.get("day").toString().trim())) {
                        map1.put(jsonArr.getString(i) + ",d1", "true");
                    } else {
                        map1.put(jsonArr.getString(i) + ",d1", m.get("day").toString());
                        map1.put(jsonArr.getString(i) + ",d2", "0");
                    }
                }
            }
            Map<String, String> tempWeek = new HashMap<String, String>();
            for (int i = 0; i < jsonArr.size(); i++) {
                if (LanDateUtils.compare_date(m.get("endDate").toString(), jsonArr.getString(i)) >= 0) {
                    String s = LanDateUtils.getAllDayOfWeek(jsonArr.getString(i));
                    if (m.get("week") == null || "".equals(m.get("week").toString().trim()) || "0".equals(m.get("week").toString().trim())) {
                        map1.put(s + ",w1", "true");
                    } else {
                        map1.put(s + ",w1", m.get("week").toString());
                        tempWeek.put(s, "1");
                    }

                }
            }
            for (Map.Entry<String, String> entry : tempWeek.entrySet()) {
                sql = " select SUM(c.time) as keshi from classSchedule c where c.status<>'2' and c.student_id='" + m.get("student_id").toString()
                        + "' and c.course_id='" + m.get("course_id").toString() + "' and c.scheduledDate in(" + entry.getKey() + ") ";
                z = new Zidingyi();
                z.setSql(sql);
                list = tongjiService.query(z);
                if (!list.isEmpty() && list.get(0) != null && list.get(0).get("keshi") != null) {
                    map1.put(entry.getKey() + ",w2", list.get(0).get("keshi").toString());
                } else {
                    map1.put(entry.getKey() + ",w2", "0");
                }

            }
            Map<String, String> tempMonth = new HashMap<String, String>();
            for (int i = 0; i < jsonArr.size(); i++) {
                String mm = "";
                String m1 = jsonArr.getString(i).substring(0, 8);
                mm += m1 + "01,";
                String m2 = jsonArr.getString(i).substring(5, 7);
                if ("01".equals(m2) || "03".equals(m2) || "05".equals(m2) || "07".equals(m2)
                        || "08".equals(m2) || "10".equals(m2) || "12".equals(m2)) {
                    mm += m1 + "31";
                }
                if ("04".equals(m2) || "06".equals(m2) || "09".equals(m2) || "11".equals(m2)) {
                    mm += m1 + "30";
                }
                if ("02".equals(m2)) {
                    Integer m3 = Integer.valueOf(jsonArr.getString(i).substring(0, 4));
                    if (m3 % 4 == 0) {
                        mm += m1 + "29";
                    } else {
                        mm += m1 + "28";
                    }
                }
                if (m.get("month") == null || "".equals(m.get("month").toString().trim()) || "0".equals(m.get("month").toString().trim())) {
                    map1.put(m1 + ",m1", "true");
                } else {
                    if (LanDateUtils.compare_date(m.get("endDate").toString(), jsonArr.getString(i)) >= 0) {
                        map1.put(m1 + ",m1", m.get("month").toString());
                        tempMonth.put(mm, m1);
                    }
                }

            }
            for (Map.Entry<String, String> entry : tempMonth.entrySet()) {
                String[] mm = entry.getKey().split(",");
                sql = " select SUM(c.time) as keshi from classSchedule c where c.status<>'2' and c.student_id='" + m.get("student_id").toString() + "' and c.course_id='"
                        + m.get("course_id").toString() + "' and c.scheduledDate>='" + mm[0] + "' and c.scheduledDate<='" + mm[1] + "'";
                z = new Zidingyi();
                z.setSql(sql);
                list = tongjiService.query(z);
                if (!list.isEmpty() && list.get(0) != null && list.get(0).get("keshi") != null) {
                    map1.put(entry.getValue() + ",m2", list.get(0).get("keshi").toString());
                } else {
                    map1.put(entry.getValue() + ",m2", "0");
                }
            }
            if (m.get("intervalDay") == null || "".equals(m.get("intervalDay").toString().trim()) || "0".equals(m.get("intervalDay").toString().trim())) {
                map1.put("intervalDay", "true");
            } else {
                map1.put("intervalDay", m.get("intervalDay").toString());
                map1.put("lastDay", LanDateUtils.getNext_Day(jsonArr.getString(0), -1 * (Integer.valueOf(m.get("intervalDay").toString()) + 1)));
            }
            map.put(m.get("student_id").toString() + "," + m.get("course_id").toString(), map1);
        }
        return map;
    }
}

