/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Classroom;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.ClassroomService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class ClassroomController extends BaseController {

    @Autowired
    ClassroomService classroomService;

    @RequestMapping(value = "/ngres/jichushuju/classroom/edit")
    public String execute(ModelMap model, @RequestBody String content,
                          String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginModel login = (LoginModel) request.getSession().getAttribute("login");
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            Classroom p = new Classroom();
            JSONObject.toBean(job, p, jsonConfig);
            String context = getLogin(request).getUser().getUserName();
            if (StringUtils.isNotEmpty(p.getId())) {
                p.setLastModifier_id(login.getUser().getId());
                p.setLastModifiedTime(DateUtils.getNowDate());
                classroomService.update(p);
                context += "修改了基础数据教室设置，修改教室" + p.getId();
            } else {
                String id = sequenceManager.generateId("classroom");
                p.setId(id);
                initCreate2(p, request);
                classroomService.insert(p);
                context += "新增了基础数据教室设置，新增教室" + p.getId();
            }

            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/classroom/delete")
    public String execute3(ModelMap model, @RequestBody String content,
                           String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<Classroom> list = new ArrayList<Classroom>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Classroom p = new Classroom();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (Classroom p : list) {
                ids.add(p.getId());
                p.setDelStatus("1");
                classroomService.update(p);//改为标记删除
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);


            //classroomService.deleteByCondition(wc);
            String context = getLogin(request).getUser().getUserName() + "删除了基础数据教室：" + ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/classroom/deletestatus")
    public String execute3s(ModelMap model, @RequestBody String content,
                            String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<Classroom> list = new ArrayList<Classroom>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Classroom p = new Classroom();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (Classroom p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            classroomService.deleteBystatus(wc);
            String context = getLogin(request).getUser().getUserName() + "删除了基础数据教室：" + ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/classroom/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            wc.setLength(page.getItemsperpage());
            wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
            wc.setOrderBy(orderGuize);
            initWanNengChaXun(jsonObj, wc);// 万能查询
            wc.andEquals("delStatus", "0");
            List list = classroomService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(classroomService.count(wc));
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/classroom/findAll")
    public String execute13(ModelMap model, @RequestBody String content,
                            HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            //wc.setLength(page.getItemsperpage());
            //wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
            wc.andEquals("zone_id", JSONUtils.getStr(jsonObj, "id"));
            wc.setOrderBy(orderGuize);
            initWanNengChaXun(jsonObj, wc);// 万能查询
            wc.andEquals("delStatus", "0");
            List list = classroomService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(classroomService.count(wc));
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }


    @RequestMapping(value = "/ngres/jichushuju/classroom/list/id")
    public String executesd1(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            Classroom k = classroomService.loadById(JSONUtils.getStr(jsonObj, "id"));
            JSONObject jo = JSONObject.fromObject(k, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }
}
