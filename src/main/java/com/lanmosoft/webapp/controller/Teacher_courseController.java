/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.LanmoMailSender;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.model.PaiKeGuwenInfo;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.LanDateUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class Teacher_courseController extends BaseController {

	@Autowired
	Teacher_courseService teacher_courseService;
	@Autowired
	View_student_paikeService view_student_paikeService;
	@Autowired
	View_coures_teacherService  view_coures_teacherService;//科目和级别
	@Autowired
	StudentService  studentService;
	@Autowired
	GradeService gradeService;
	@Autowired
	PaiKeGuwenInfo paiKeGuwenInfo;
	@Autowired
	LanmoMailSender lanmoMailSender;
	@Autowired
	View_teacher_courseService view_teacher_courseService;
	
	@RequestMapping(value="/ngres/jiaoshiguanli/teacher_course/list/findAllBystudent_id")
	public String executea(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
			try {
				JSONObject jsonObj = JSONObject.fromObject(content);
				String string = jsonObj.getString("id");
				WhereCondition wc=new WhereCondition();
				wc.andEquals("student_id", string);
				List<View_coures_teacher> query = view_coures_teacherService.query(wc);
				//得到科目id  和所授科目的老师 信息
				List<String> li1=new ArrayList<String>();//科目——id
				List<String> li2=new ArrayList<String>();//老师——id
				Student stu = studentService.loadById(string);
				for (View_coures_teacher view_coures_teacher : query){
					li1.add(view_coures_teacher.getCourse_id());
					li2.add(view_coures_teacher.getTeacher_id());	   
					
				/*	//按照学生的科目   要求去  查询符合条件的老师信息
					//城市  校区   				老师的 上课率
				    //@1.学生的校区     科目  级别去查询老师   
						WhereCondition wcq=new WhereCondition();
						wcq.andEquals("city_id",stu.getCity_id());
						wcq.andEquals("zone_id",stu.getZone_id());
					    wcq.andEquals("course_id",view_coures_teacher.getCourse_id());
					     
					    wcq.andEquals("grade_id",view_coures_teacher.getGrade_id());
					   //级别是大的大还是大的小
					  //  wcq.andLessEquals("level",view_coures_teacher.getLevel());//现在  小于的大
			*/
				}
				
				//按照学生的科目   要求去  查询符合条件的老师信息
				//城市  校区   				老师的 上课率
			    //@1.学生的校区     科目  级别去查询老师   
					WhereCondition wcq=new WhereCondition();
					wcq.andEquals("city_id",stu.getCity_id());
					wcq.andEquals("zone_id",stu.getZone_id());
				    wcq.andIn("course_id",li1);
				     
				    
				   // wcq.andIn("grade_id",view_coures_teacher.getGrade_id());
				   //级别是大的大还是大的小
				  //  wcq.andLessEquals("level",view_coures_teacher.getLevel());//现在  小于的大
		
				
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
				e.printStackTrace();
			}
		return null;
	}
	
	
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_course/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			Teacher_course p = new Teacher_course();
			
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			WhereCondition wcCheck=new WhereCondition();
			wcCheck.andEquals("gradeCategory_id", p.getGradeCategory_id());
			wcCheck.andEquals("course_id", p.getCourse_id());
			wcCheck.andEquals("teacher_id", p.getTeacher_id());
			wcCheck.andEquals("BUType", p.getBUType());
			wcCheck.andNotEquals("id",p.getId());
			List<Teacher_course> li = teacher_courseService.query(wcCheck);
			if(!li.isEmpty()){
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0084");
				return null;
			}
			if(StringUtils.isNotEmpty(p.getId())){

				//获取原来的数据,判断分类,可教授级别,科目是否更改,如果更改,没有课时费的权限,Scheme_id置为空
				Teacher_course teacher_course = teacher_courseService.loadById(p.getId());
				if(!p.getGradeCategory_id().equals(teacher_course.getGradeCategory_id()) || !p.getGrade_id().equals(teacher_course.getGrade_id())|| !p.getCourse_id().equals(teacher_course.getCourse_id())){
					boolean flag = true;
					if(!"0".equals(getLogin(request).getUser().getCategory().trim())){
						List<Permission> listPer=getLogin(request).getPermissionList();
						for(Permission pm:listPer){
							if("212".equals(pm.getFunction_id()) || "213".equals(pm.getFunction_id())){
								flag = false;
								break;
							}
						}
						if(flag){
							p.setScheme_id(null);
						}
					}

				}

				teacher_courseService.updateForce(p);
				context+="修改了教师科目"+p.getId();
			}
			else{

				//判断Hourly Rate Scheme的权限,是否有,如果没有权限Scheme_id置为空
				boolean flag = true;
				if(!"0".equals(getLogin(request).getUser().getCategory().trim())){
					List<Permission> listPer=getLogin(request).getPermissionList();
					for(Permission pm:listPer){
						if("212".equals(pm.getFunction_id()) || "213".equals(pm.getFunction_id())){
							flag = false;
							break;
						}
					}
					if(flag){
						p.setScheme_id(null);
					}
				}


				String id = sequenceManager.generateId("teacher_course");
				p.setId(id);
				Date d=new Date();
				teacher_courseService.insert(p);
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
			
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_course/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Teacher_course> list = new ArrayList<Teacher_course>();
			boolean b=true;
			for (int i = 0; i < jsonArray.size(); i++) {
				Teacher_course p = new Teacher_course();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
				
//@  要从最大的开始删除				
				Teacher_course loadById = teacher_courseService.loadById(p.getId());
				WhereCondition wc=new WhereCondition();
				wc.andEquals("teacher_id",p.getTeacher_id());
				wc.andEquals("course_id",p.getCourse_id());
				wc.andNotEquals("id",p.getId());
				wc.setOrderBy("id  desc");
				List<Teacher_course> query2 = teacher_courseService.query(wc);
				if(query2.size()!=0){
					Teacher_course teacher_course = query2.get(0);
					//if(loadById.getPeriodLowerLimit()>teacher_course.getPeriodUpperLimit()){
						teacher_courseService.delete(loadById.getId());
						String context=getLogin(request).getUser().getUserName()+"删除了老师可教授科目："+loadById.getId();
						operationLog.setId(sequenceManager.generateId("operationLog"));
						operationLog.setUser_id(getLogin(request).getUser().getId());
						operationLog.setCreateTime(new Date());
						operationLog.setContent(context);
						operationLogService.insert(operationLog);
					//}
				}else{//等于就删除
					teacher_courseService.delete(loadById.getId());
					String context=getLogin(request).getUser().getUserName()+"删除了老师可教授科目："+loadById.getId();
					operationLog.setId(sequenceManager.generateId("operationLog"));
					operationLog.setUser_id(getLogin(request).getUser().getId());
					operationLog.setCreateTime(new Date());
					operationLog.setContent(context);
					operationLogService.insert(operationLog);
				}
			}
			
			if(b){
				AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
			}else{
				AjaxUtils.ajaxJsonErrorMessage(response, "new_msg_0001");
			}
			/*List<String> ids = new ArrayList<String>();
			for (Teacher_course p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			teacher_courseService.deleteByCondition(wc);*/
			
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}
	
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_course/update")
	public String executeUpdate(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Teacher_course> list = new ArrayList<Teacher_course>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Teacher_course p = new Teacher_course();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
//				Teacher_course loadById = teacher_courseService.loadById(p.getId());
				WhereCondition wc=new WhereCondition();
				wc.andEquals("teacher_id",p.getTeacher_id());
				wc.andEquals("course_id",p.getCourse_id());
				List<Teacher_course> query2 = teacher_courseService.query(wc);
                for(Teacher_course tc:query2){
                	tc.setIsActivated("0");
                	tc.setEndDate(LanDateUtils.getNowDate());
                	teacher_courseService.update(tc);
                }
			}			
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_course/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = teacher_courseService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(teacher_courseService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
	
		@RequestMapping(value = "/ngres/jiaoshiguanli/teacher_course/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Teacher_course k =  teacher_courseService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		//按老师的list  和科目的id去联动老师的数据
		@RequestMapping(value="/ngres/jiaoshiguanli/teacher_course/list/checkTeacher")
		public String execu(ModelMap model, @RequestBody String content,
				HttpServletRequest request, HttpServletResponse response) {
			try {
				JSONObject jsonObj = JSONObject.fromObject(content);
				String string = jsonObj.getString("course_id");
				JSONArray jsonArray = jsonObj.getJSONArray("list");
				JSONArray jsonArray1 = jsonObj.getJSONArray("list1");
				JSONArray gradeCategory_ids = jsonObj.getJSONArray("gradeCategory_ids");
				JSONArray levels = jsonObj.getJSONArray("levels");
				String BUType = JSONUtils.getStr(jsonObj,"BUType");
				//String level = JSONUtils.getStr(jsonObj,"level");
				//String gradeCategory_id = JSONUtils.getStr(jsonObj,"gradeCategory_id");
				List<User> list=new ArrayList<User>();
				Map<String,String> m=new HashMap<String,String>();

				//获取已经勾选的老师
				List<View_teacher_course> listCheck = getCheck(jsonArray1);

				if(!gradeCategory_ids.isEmpty() && !levels.isEmpty()){
					for(int i = 0 ;i<gradeCategory_ids.size();i++){
						//m.clear();
						String gradeCategory_id = gradeCategory_ids.getString(i);
						String level = levels.getString(i);
						for (Object object : jsonArray) {
							User u=new User();
							JSONObject.toBean(JSONObject.fromObject(object),u,jsonConfig);
							if(m.get(u.getId() + '-' + string + '-' + gradeCategory_id)!=null){
								continue;
							}


							//获取课教师的老师
							WhereCondition wc=new WhereCondition();
							wc.andEquals("teacher_id",u.getId());
							wc.andEquals("course_id", string);
							wc.andEquals("BUType",BUType);
							wc.andEquals("gradeCategory_id",gradeCategory_id);
							boolean flag = false;
							List<View_teacher_course> query = view_teacher_courseService.query(wc);
							if(query.size()!=0){
								for (View_teacher_course v:query) {
									String level1 = v.getLevel();
									if(StringUtils.isNotBlank(level)&&StringUtils.isNotBlank(level1)){
										if(Integer.parseInt(level)<=Integer.parseInt(level1)){
											flag = true;
											m.put(u.getId() + '-' + string + '-' + gradeCategory_id, "1");
											break;
										}
									}
								}

							}

							//判断勾选的老师的是否教也勾选了这门课
							if(flag){
								if(!listCheck.isEmpty()){
									for (View_teacher_course vt:listCheck) {
										String levelvt = vt.getLevel();
										String teacher_idvt = vt.getTeacher_id();
										String course_idvt = vt.getCourse_id();
										String gradeCategory_idvt = vt.getGradeCategory_id();
										if(u.getId().equals(teacher_idvt) && string.equals(course_idvt) && gradeCategory_id.equals(gradeCategory_idvt)){
											if(Integer.parseInt(level)<=Integer.parseInt(levelvt)) {
												list.add(u);
											}
										}
									}
								}
							}
						}
					}
				}



				Map map=new HashMap();
				map.put("list",list);
				String string2 = JSONObject.fromObject(map).toString();
				AjaxUtils.ajaxJson(response, string2);
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
				e.printStackTrace();
			}
			return null;
		}

	/**
	 * 获取勾选的老师
	 * @param jsonArray1
	 * @return
	 */
	private List<View_teacher_course> getCheck(JSONArray jsonArray1) {
		List<View_teacher_course> list = new ArrayList<View_teacher_course>();

		for(int i = 0;i<jsonArray1.size();i++){
			JSONObject jsonObject = jsonArray1.getJSONObject(i);
			Object o = jsonArray1.get(i);
			String checked = JSONUtils.getStr(jsonObject, "checked");
			if("true".equalsIgnoreCase(checked)){
				View_teacher_course v = new View_teacher_course();
				JSONObject.toBean(JSONObject.fromObject(o),v,jsonConfig);//checked
				list.add(v);
			}
		}

		return list;
	}
}
