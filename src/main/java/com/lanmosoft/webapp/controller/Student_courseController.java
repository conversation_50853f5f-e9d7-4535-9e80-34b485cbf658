/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Student;
import com.lanmosoft.dao.model.Student_course;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.Student_courseService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class Student_courseController extends BaseController {

	@Autowired
	Student_courseService student_courseService;
	@RequestMapping(value = "/ngres/xueshengguanli/student_course/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			 String teachingWay_id=job.getString("teachingWay_id");
			Student_course p = new Student_course();
			JSONObject.toBean(job,p,jsonConfig);
			p.setTeachingWay(teachingWay_id);
			if(p.getTeacher_id() == null){
				p.setTeacher_id("");
			}
			//2.同一科目同级别同一上课方式同一老师不能再次提交      同一时间不能提交    即有交叉的时间段
			List<Student_course> querycheck = student_courseService.querycheck(p);
			if(querycheck.size()!=0){
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0099");
			}else{
				String context=getLogin(request).getUser().getUserName();
				if (StringUtils.isNotEmpty(p.getId())) {
					student_courseService.update(p);
					context+="修改了学生课表设置，修改学生课表"+p.getId();
				} else {
					String id = sequenceManager.generateId("student_course");
					p.setId(id);
					initCreate2(p, request);
					student_courseService.insert(p);
					context+="新增了学生课表设置，新增学生课表"+p.getId();
				}
				operationLog.setId(sequenceManager.generateId("operationLog"));
				operationLog.setUser_id(getLogin(request).getUser().getId());
				operationLog.setCreateTime(new Date());
				operationLog.setContent(context);
				operationLogService.insert(operationLog);
				AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
			}
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/student_course/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Student_course> list = new ArrayList<Student_course>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Student_course p = new Student_course();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Student_course p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			student_courseService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/xueshengguanli/student_course/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Student_course> list = new ArrayList<Student_course>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Student_course p = new Student_course();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Student_course p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			student_courseService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/student_course/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = student_courseService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(student_courseService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/xueshengguanli/student_course/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Student_course k =  student_courseService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		
		//按学生的list  和科目的id去联动学生的数据
	@RequestMapping(value="/ngres/xueshengguanli/student_course/list/checkStudent")
	public String execu(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			String string = jsonObj.getString("course_id");
			JSONArray jsonArray = jsonObj.getJSONArray("list");
			List<Student> list=new ArrayList<Student>();
			Map<String,String> m=new HashMap<String,String>();
			for (Object object : jsonArray) {
				Student u=new Student();
				JSONObject.toBean(JSONObject.fromObject(object),u,jsonConfig);
				if(m.get(u.getId())!=null){
					continue;
				}
				System.out.println("u-----"+u.getChineseName());
				WhereCondition wc=new WhereCondition();
				wc.andEquals("student_id",u.getId());
				wc.andEquals("course_id", string);
				List<Student_course> query = student_courseService.query(wc);
				if(query.size()!=0){
					m.put(u.getId(), "1");
					list.add(u);
				}
			}			
			Map map=new HashMap();
			map.put("list",list);
			String string2 = JSONObject.fromObject(map).toString();
			AjaxUtils.ajaxJson(response, string2);
		} catch (Exception e) {
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
}
