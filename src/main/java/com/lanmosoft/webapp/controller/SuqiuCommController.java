/**
 * auto generated
 * Copyright (C) 2013
 */
package com.lanmosoft.webapp.controller;


import com.lanmosoft.dao.model.Student_zone;
import com.lanmosoft.dao.model.SuqiuComm;
import com.lanmosoft.dao.model.Zone;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.LanmoMailSender;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class SuqiuCommController extends BaseController {

    @Autowired
    SuqiuCommService suqiuCommService;
    @Autowired
    ZoneService zoneService;
    @Autowired
    LanmoMailSender lanmoMailSender;
    @Autowired
    Student_zoneService student_zoneService;
    @Autowired
    UserService userService;
    @Autowired
    StudentService studentService;
    @Autowired
    SuqiuService suqiuService;

    //得到所有的校区
    @RequestMapping(value = "/ngres/jichushuju/suqiucomm/findAll")
    public String execute222(ModelMap model, @RequestBody String content,
                             String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            List<SuqiuComm> query = new ArrayList<SuqiuComm>();
            WhereCondition wc = new WhereCondition();
            LoginModel login = getLogin(request);

            query = suqiuCommService.query(wc);


            Map map = new HashMap();
            map.put("list", query);
            String string = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, string);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/suqiucomm/edit")
    public String execute(ModelMap model, @RequestBody String content,
                          String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginModel login = (LoginModel) request.getSession().getAttribute("login");
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
            String student_id = JSONUtils.getStr(jsonObj, "student_id");
            SuqiuComm p = new SuqiuComm();
            JSONObject.toBean(job, p, jsonConfig);
            String context = getLogin(request).getUser().getUserName();
            if (StringUtils.isNotEmpty(p.getId())) {
                System.out.println(p.getDelStatus());
                p.setLastModifier_id(login.getUser().getId());
                p.setLastModifiedTime(DateUtils.getNowDate());
                suqiuCommService.update(p);
                context += "修改了诉求沟通";

                //todo: send email to zone manager
                List<String> toList = new ArrayList<String>();

                if (!"all".equals(zone_id)) {
                    Zone zone = zoneService.loadById(zone_id);
                    // get scheduler email from Zone table
                    toList.add(zone.getCampusMailbox());
                }
                // get tutor email from Student_Zone table
                WhereCondition wc = new WhereCondition();
                wc.andEquals("student_id", student_id);
                wc.andEquals("zone_id", zone_id);
                List<Student_zone> szlist = student_zoneService.query(wc);
                if (!szlist.isEmpty()) {
                    for (Student_zone sz : szlist) {
                        toList.add(userService.loadById(sz.getTutorId()).getEmail());
                    }
                }
                // remove login user's email from the toList
                List<String> loginEmail = new ArrayList<String>();
                loginEmail.add(login.getUser().getEmail());
                toList.removeAll(loginEmail);
                String studentname = studentService.loadById(suqiuService.loadById(p.getSuqiu_id()).getStudent_id()).getChineseName();
                String subjectText = getLogin(request).getUser().getUserName() + "修改了学生" + studentname + "的排课诉求沟通留言";
                String bodyText = "Dear " + Arrays.toString(toList.toArray(new String[0])) + ",\r\n\r\n";
                bodyText += getLogin(request).getUser().getUserName() + "修改了学生" + studentname + "的排课诉求沟通留言，" +
                        "请登录系统查看。\r\n\r\n留言内容：" + p.getName();
                boolean isSuccess = lanmoMailSender.sendMassageDefault("<EMAIL>", toList.toArray(new String[0]), null, bodyText, subjectText);
                if (!isSuccess) {
                    System.out.println("Mail sent.");
                }

            } else {
                String id = sequenceManager.generateId("suqiucomm");
                p.setId(id);
                initCreate2(p, request);
                suqiuCommService.insert(p);
                context += "新增了诉求沟通";

                //todo: send email to zone manager
                List<String> toList = new ArrayList<String>();

                if (!"all".equals(zone_id)) {
                    Zone zone = zoneService.loadById(zone_id);
                    // get scheduler email from Zone table
                    toList.add(zone.getCampusMailbox());
                }
                // get tutor email from Student_Zone table
                WhereCondition wc = new WhereCondition();
                wc.andEquals("student_id", student_id);
                wc.andEquals("zone_id", zone_id);
                List<Student_zone> szlist = student_zoneService.query(wc);
                if (!szlist.isEmpty()) {
                    for (Student_zone sz : szlist) {
                        toList.add(userService.loadById(sz.getTutorId()).getEmail());
                    }
                }
                // remove login user's email from the toList
                List<String> loginEmail = new ArrayList<String>();
                loginEmail.add(login.getUser().getEmail());
                toList.removeAll(loginEmail);
                String studentname = studentService.loadById(suqiuService.loadById(p.getSuqiu_id()).getStudent_id()).getChineseName();
                String subjectText = getLogin(request).getUser().getUserName() + "新增了学生" + studentname + "的排课诉求沟通留言";
                String bodyText = "Dear " + Arrays.toString(toList.toArray(new String[0])) + ",\r\n\r\n";
                bodyText += getLogin(request).getUser().getUserName() + " 新增了学生" + studentname + "的排课诉求沟通留言，" +
                        "请登录系统查看。\r\n\r\n留言内容：" + p.getName();
                boolean isSuccess = lanmoMailSender.sendMassageDefault("<EMAIL>", toList.toArray(new String[0]), null, bodyText, subjectText);
                if (!isSuccess) {
                    System.out.println("Mail sent.");
                }
            }
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/suqiucomm/delete")
    public String execute3(ModelMap model, @RequestBody String content,
                           String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<SuqiuComm> list = new ArrayList<SuqiuComm>();
            for (int i = 0; i < jsonArray.size(); i++) {
                SuqiuComm p = new SuqiuComm();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (SuqiuComm p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            suqiuCommService.deleteByCondition(wc);
            String context = getLogin(request).getUser().getUserName() + "删除了基础数据城市：" + ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/suqiucomm/deletestatus")
    public String execute3s(ModelMap model, @RequestBody String content,
                            String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<SuqiuComm> list = new ArrayList<SuqiuComm>();
            for (int i = 0; i < jsonArray.size(); i++) {
                SuqiuComm p = new SuqiuComm();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (SuqiuComm p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            suqiuCommService.deleteBystatus(wc);
            String context = getLogin(request).getUser().getUserName() + "删除了基础数据城市：" + ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/suqiucomm/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject searchItems = jsonObj.getJSONObject("searchItems");
            String suqiu_id = JSONUtils.getStr(searchItems, "suqiu_id_eq");
            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            if (!StringUtils.isBlank(suqiu_id)) {
                wc.andEquals("suqiu_id", suqiu_id);
            }
            wc.setLength(page.getItemsperpage());
            wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
            wc.setOrderBy(orderGuize);
            initWanNengChaXun(jsonObj, wc);// 万能查询
            List list = suqiuCommService.query(wc);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(suqiuCommService.count(wc));
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/jichushuju/suqiucomm/list/id")
    public String executesd1(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            SuqiuComm k = suqiuCommService.loadById(JSONUtils.getStr(jsonObj, "id"));
            JSONObject jo = JSONObject.fromObject(k, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {

        Page wc = new Page();


    }
}
