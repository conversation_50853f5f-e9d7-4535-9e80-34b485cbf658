/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Student_timezone;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.Student_timezoneService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.LanDateUtils;
import com.lanmosoft.util.TimezoneUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class Student_timezoneController extends BaseController {

	@Autowired
	Student_timezoneService student_timezoneService;
	@RequestMapping(value = "/ngres/xueshengguanli/student_timezone/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			String context=getLogin(request).getUser().getUserName();
			Student_timezone p = new Student_timezone();
			JSONObject.toBean(job,p,jsonConfig);
			List<Student_timezone> querycheck = student_timezoneService.querycheck(p);
			if(querycheck.size()!=0){
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0019");
			}else{
			if (StringUtils.isNotEmpty(p.getId())) {
				student_timezoneService.update(p);
				context+="修改了学生课表时区设置，修改学生课表时区"+p.getId();
			} else {
				String id = sequenceManager.generateId("student_timezone");
				p.setId(id);
				student_timezoneService.insert(p);
				context+="新增了学生课表时区设置，新增学生课表时区"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		}
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/student_timezone/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Student_timezone> list = new ArrayList<Student_timezone>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Student_timezone p = new Student_timezone();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Student_timezone p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			student_timezoneService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表时区："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/xueshengguanli/student_timezone/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Student_timezone> list = new ArrayList<Student_timezone>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Student_timezone p = new Student_timezone();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Student_timezone p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			student_timezoneService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表时区："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/student_timezone/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			orderGuize = " startDate ASC ";
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List<Student_timezone> list = student_timezoneService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(student_timezoneService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/xueshengguanli/student_timezone/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Student_timezone k =  student_timezoneService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		
		@RequestMapping(value="/ngres/xueshengguanli/student_timezone/findAll")
		public String execute222(ModelMap model, @RequestBody String content,
				String age, HttpServletRequest request, HttpServletResponse response) {
				try {
					JSONObject jsonObj = JSONObject.fromObject(content);
					String student_id=JSONUtils.getStr(jsonObj, "student_id");
					JSONArray jsonWeeks = jsonObj.getJSONArray("weeks");
					String startDate=JSONUtils.getStr(jsonWeeks.getJSONObject(0), "date");
					String endDate=JSONUtils.getStr(jsonWeeks.getJSONObject(jsonWeeks.size()-1), "date");
					String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
					Student_timezone p=new Student_timezone();
					p.setStudent_id(student_id);
					Date dateStDate=LanDateUtils.convertStringToDate(TimezoneUtil.strTimeFormat_V1,startDate);
					Date dateEndDate=LanDateUtils.convertStringToDate(TimezoneUtil.strTimeFormat_V1,endDate);
					p.setStartDate(dateStDate);
					p.setEndDate(dateEndDate);
					List<Student_timezone> query = student_timezoneService.getStuTimezone(p);
					
					Map map=new HashMap();
					map.put("list",query);
					String string = JSONObject.fromObject(map, jsonConfig).toString();
					AjaxUtils.ajaxJson(response, string);
				} catch (Exception e) {
					AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
					e.printStackTrace();
				}
			return null;
		}
}
