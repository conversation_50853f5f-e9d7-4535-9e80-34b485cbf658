/**
 * auto generated
 * Copyright (C) 2013
 */
package com.lanmosoft.webapp.controller;


import com.lanmosoft.dao.model.Partner;
import com.lanmosoft.dao.model.View_ZonePermission;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.PartnerService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.PinyinUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class PartnerController extends BaseController {

	@Autowired
	PartnerService partnerService;
	
	//得到所有的校区
	@RequestMapping(value="/ngres/jichushuju/partner/findAll")
	public String execute222(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
			try {
				List<Partner> query = new ArrayList<Partner>();
				Set<String> listPartner = new HashSet<String>();
				WhereCondition wc=new WhereCondition();
				LoginModel login = getLogin(request);

				query = partnerService.query(wc);

//				if(!"0".equals(getLogin(request).getUser().getCategory().trim())){
//					String flag = login.getUser().getFlag();//判断是否是所有校区权限 1是所有校区
//					if("0".equals(flag)){//权限校区对应的城市
//						List<View_ZonePermission> zonePermissionList = login.getZonePermissionList();
//						if(!zonePermissionList.isEmpty()){
//							//去重
//							for (View_ZonePermission z: zonePermissionList  ) {
//								listPartner.add(z.getCity_id());
//							}
//							for (String partnerid:listPartner) {
//								Partner partner = partnerService.loadById(partnerid);
//								query.add(partner);
//							}
//						}
//					}else { //所有校区
//						query = partnerService.query(wc);
//					}
//				}else {//管理员权限
//					query = partnerService.query(wc);
//				}

				
				Map map=new HashMap();
				map.put("list",query);
				String string = JSONObject.fromObject(map, jsonConfig).toString();
				AjaxUtils.ajaxJson(response, string);
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
				e.printStackTrace();
			}
		return null;
	}
	@RequestMapping(value = "/ngres/jichushuju/partner/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			LoginModel login = (LoginModel)request.getSession().getAttribute("login");
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			Partner p = new Partner();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				System.out.println(p.getDelStatus());
				p.setLastModifier_id(login.getUser().getId());
				p.setLastModifiedTime(DateUtils.getNowDate());
				partnerService.update(p);
				context+="修改了基础数据城市设置，修改城市"+p.getId();
			} else {
				String id = sequenceManager.generateId("partner");
				p.setId(id);
				initCreate2(p, request);
				partnerService.insert(p);
				context+="新增了基础数据城市设置，新增城市"+p.getName();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/partner/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Partner> list = new ArrayList<Partner>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Partner p = new Partner();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Partner p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			partnerService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据城市："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/partner/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Partner> list = new ArrayList<Partner>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Partner p = new Partner();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Partner p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			partnerService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据城市："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/partner/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = partnerService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(partnerService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/jichushuju/partner/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Partner k =  partnerService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		
		public static void main(String[] args) {
			Page wc=new Page();
		}
}
