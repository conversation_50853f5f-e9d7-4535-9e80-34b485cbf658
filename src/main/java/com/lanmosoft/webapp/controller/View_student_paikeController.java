/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.Student;
import com.lanmosoft.dao.model.View_coures_teacher;
import com.lanmosoft.dao.model.View_student_paike;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.StudentService;
import com.lanmosoft.service.biz.View_coures_teacherService;
import com.lanmosoft.service.biz.View_student_paikeService;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.LanDateUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class View_student_paikeController extends BaseController {

	@Autowired
	View_student_paikeService view_student_paikeService;
	@Autowired
	StudentService  studentService;
	@Autowired
	View_coures_teacherService  view_coures_teacherService;//科目和级别
	
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/view_student_paike/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			View_student_paike p = new View_student_paike();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				view_student_paikeService.update(p);
				context+="修改了学生课表设置，修改学生课表"+p.getId();
			} else {
				String id = sequenceManager.generateId("view_student_paike");
				p.setId(id);
				initCreate2(p, request);
				view_student_paikeService.insert(p);
				context+="新增了学生课表设置，新增学生课表"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/view_student_paike/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<View_student_paike> list = new ArrayList<View_student_paike>();
			for (int i = 0; i < jsonArray.size(); i++) {
				View_student_paike p = new View_student_paike();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (View_student_paike p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			view_student_paikeService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/view_student_paike/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<View_student_paike> list = new ArrayList<View_student_paike>();
			for (int i = 0; i < jsonArray.size(); i++) {
				View_student_paike p = new View_student_paike();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (View_student_paike p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			view_student_paikeService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jiaoshiguanli/view_student_paike/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			
			String string = jsonObj.getString("id");
			String BUType=jsonObj.getString("BUType");
			String zone_id=JSONUtils.getStr(jsonObj, "zone_id");
			WhereCondition wc=new WhereCondition();
			wc.andEquals("student_id", string);
			wc.andEquals("status","1");
			//wc.andEquals("teacher_status", "1");
			//wc.andEquals("zone_id", zone_id);
			//wc.andEquals("BUType",BUType);
			//学员诉求
			List<View_coures_teacher> query = view_coures_teacherService.query(wc);//学员诉求
			//得到科目id  和所授科目的老师 信息
			List<String> li1=new ArrayList<String>();//科目——id
			List<String> li2=new ArrayList<String>();//老师——id
			List<String> li3=new ArrayList<String>();
			Student stu = studentService.loadById(string);
			for (View_coures_teacher view_coures_teacher : query){
				li1.add(view_coures_teacher.getCourse_id());
				li3.add(view_coures_teacher.getGradeCategory_id());
				li2.add(view_coures_teacher.getTeacher_id());	   
			}
			
		   //查询学员指定的老师
			List<View_student_paike> query2=new ArrayList<View_student_paike>();
			List<View_student_paike> query3=new ArrayList<View_student_paike>();
			if(li2.size()!=0){
				wc=new WhereCondition();
				wc.andEquals("zone_id",zone_id);//校区
				wc.andEquals("BUType", BUType);
				wc.andIn("course_id",li1);
				wc.andIn("gradeCategory_id",li3);
				wc.andEquals("teacher_status", "1");//教师状态有效
				//wc.andGreaterEquals(field, value);
				//wc.andIn("teacher_id", li2);
				wc.andEquals("tea_cou_status","1");//老师状态有效
				//指定的老师
				query2 = view_student_paikeService.query(wc);				
				for(int i=0;i<query2.size();i++){
					View_student_paike View=query2.get(i);
					for (View_coures_teacher view_coures_teacher : query){
						String course_id=view_coures_teacher.getCourse_id();
						String course_idA=View.getCourse_id();
						String grade_Category_id=view_coures_teacher.getGradeCategory_id();
						String grade_Category_idA=View.getGradeCategory_id();
						if((course_id.equals(course_idA))&&(grade_Category_id.equals(grade_Category_idA))){
							String level=view_coures_teacher.getLevel();
							String levelA=View.getLevel();
							/*String grade_mameNum=grade_mame.split(" ")[1];
							String grade_mameANum=grade_nameA.split(" ")[1];*/
							int levelInt=Integer.parseInt(level);
							int levelAInt=Integer.parseInt(levelA);
							if(levelInt>levelAInt){
								//query2.remove(i);
							}
							else{
								query3.add(View);
							}
						}
						
					}
				}
			  /* for(View_student_paike View:query2){
			    	boolean flag=false;
			    	for (View_coures_teacher view_coures_teacher : query){
			    		if(view_coures_teacher.getCourse_id().equals(View.getCourse_id())&&
			    				view_coures_teacher.getTeacher_id().equals(View.getTeacher_id())){
			    			flag=true;
			    		}
			    	}
			    	if(flag){
			    		View.setChecked(true);
			    	}
			    }*/
			}
			
			
			//判断 科目
			List<View_student_paike> list=new  ArrayList<View_student_paike>();
			if(li1.size()!=0){
				// 组装查询条件
				wc = new WhereCondition();
				wc.setOrderBy(orderGuize);
				wc.andEquals("city_id",stu.getCity_id());
				wc.andEquals("zone_id",stu.getZone_id());
				wc.andEquals("BUType",BUType);
			    wc.andIn("course_id",li1);
			    wc.andNotIn("teacher_id", li2);
			    wc.andEquals("teacher_status", "1");
				initWanNengChaXun(jsonObj, wc);// 万能查询
				list = view_student_paikeService.query(wc);
			}
			Map map1 = new HashMap();
			for(int i= list.size()-1;i>=0;i--){
				if(map1.get(list.get(i).getTeacher_id()+"-"+list.get(i).getCourse_id())!=null){
					list.remove(i);
					continue;
				}
				map1.put(list.get(i).getTeacher_id()+"-"+list.get(i).getCourse_id(), 1);
		    }
			map1.clear();
			//System.out.println("22222222222222"+query2.size());
			for(int i= query2.size()-1;i>=0;i--){
				if(map1.get(query2.get(i).getTeacher_id()+"-"+query2.get(i).getCourse_id())!=null){
					query2.remove(i);
					continue;
				}
				map1.put(query2.get(i).getTeacher_id()+"-"+query2.get(i).getCourse_id(), 1);
		    }
			//System.out.println("11111111111"+query2.size());
			Map map3=new HashMap();
			for(int i= query3.size()-1;i>=0;i--){
				if(map3.get(query3.get(i).getTeacher_id()+"-"+query3.get(i).getCourse_id() + "-" + query3.get(i).getGradeCategory_id())!=null){
					query3.remove(i);
					continue;
				}
				map3.put(query3.get(i).getTeacher_id()+"-"+query3.get(i).getCourse_id() + "-" + query3.get(i).getGradeCategory_id(), 1);
		    }
	
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			Map map = new HashMap();
			map.put("list", ja);
			map.put("list2", query3);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
/*	@RequestMapping(value = "/ngres/jiaoshiguanli/view_student_paike/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			
			
			
			String string = jsonObj.getString("id");
			WhereCondition wc=new WhereCondition();
			wc.andEquals("student_id", string);
			List<View_coures_teacher> query = view_coures_teacherService.query(wc);
			//得到科目id  和所授科目的老师 信息
			List<String> li1=new ArrayList<String>();//科目——id
			List<String> li2=new ArrayList<String>();//老师——id
			Student stu = studentService.loadById(string);
			for (View_coures_teacher view_coures_teacher : query){
				li1.add(view_coures_teacher.getCourse_id());
				li2.add(view_coures_teacher.getTeacher_id());	   
			}
				WhereCondition wcq=new WhereCondition();
				wcq.andEquals("city_id",stu.getCity_id());
				wcq.andEquals("zone_id",stu.getZone_id());
			    wcq.andIn("course_id",li1);
			
		   //查询学员指定的老师
			List<View_student_paike> query2=new ArrayList<View_student_paike>();
			if(li2.size()!=0){
				wc=new WhereCondition();
				wc.andIn("teacher_id", li2);
				//指定的老师
					query2 = view_student_paikeService.query(wc);
			    for(View_student_paike View:query2){
			    	View.setChecked(true);
			    }
			}
			
			//判断 科目
			List list=new  ArrayList();
			if(li1.size()!=0){
				// 组装查询条件
				wc = new WhereCondition();
				wc.setLength(page.getItemsperpage());
				wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
				wc.setOrderBy(orderGuize);
				wc.andEquals("city_id",stu.getCity_id());
				wc.andEquals("zone_id",stu.getZone_id());
			    wc.andIn("course_id",li1);
			    wc.andNotIn("teacher_id", li2);
				initWanNengChaXun(jsonObj, wc);// 万能查询
				list = view_student_paikeService.query(wc);
				page.setTotalItems(view_student_paikeService.count(wc));
			}
			
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			map.put("list2", query2);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "添加失败!");
			e.printStackTrace();
		}
		return null;
	}
		
	
	*/
	
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/view_student_paike/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			View_student_paike k =  view_student_paikeService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
	
	
	@RequestMapping(value = "/ngres/jiaoshiguanli/view_student_paike/getWeekCount")
	public String executeWeekCount(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			String toDay = jsonObj.getString("toDay");
			String count=LanDateUtils.getWeekCount(toDay);
			System.out.println("toDay==="+toDay);
			System.out.println("count==="+count);
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("count", count);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s);
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}
}
