/**
 * auto generated
 * Copyright (C) 2013
 */
package com.lanmosoft.webapp.controller;



import com.lanmosoft.dao.model.SmallGroup;
import com.lanmosoft.dao.model.Smallgroup_student;
import com.lanmosoft.dao.model.View_ZonePermission;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.SmallGroupService;
import com.lanmosoft.service.biz.Smallgroup_studentService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.PinyinUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class Smallgroup_studentController extends BaseController {

    @Autowired
    Smallgroup_studentService smallgroup_studentService;

    //得到所有的校区
    @RequestMapping(value="/ngres/paikeguanli/smallgroup_student/findAll")
    public String execute222(ModelMap model, @RequestBody String content,
                             String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            List<Smallgroup_student> query = new ArrayList<Smallgroup_student>();
            Set<String> listSmallGroup = new HashSet<String>();
            WhereCondition wc=new WhereCondition();
            LoginModel login = getLogin(request);

            query = smallgroup_studentService.query(wc);

//				if(!"0".equals(getLogin(request).getUser().getCategory().trim())){
//					String flag = login.getUser().getFlag();//判断是否是所有校区权限 1是所有校区
//					if("0".equals(flag)){//权限校区对应的城市
//						List<View_ZonePermission> zonePermissionList = login.getZonePermissionList();
//						if(!zonePermissionList.isEmpty()){
//							//去重
//							for (View_ZonePermission z: zonePermissionList  ) {
//								listPartner.add(z.getCity_id());
//							}
//							for (String partnerid:listPartner) {
//								Partner partner = partnerService.loadById(partnerid);
//								query.add(partner);
//							}
//						}
//					}else { //所有校区
//						query = partnerService.query(wc);
//					}
//				}else {//管理员权限
//					query = partnerService.query(wc);
//				}


            Map map=new HashMap();
            map.put("list",query);
            String string = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, string);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }
    @RequestMapping(value = "/ngres/paikeguanli/smallgroup_student/edit")
    public String execute(ModelMap model, @RequestBody String content,
                          String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginModel login = (LoginModel)request.getSession().getAttribute("login");
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            Smallgroup_student p = new Smallgroup_student();
            JSONObject.toBean(job,p,jsonConfig);
            String context=getLogin(request).getUser().getUserName();
            if (StringUtils.isNotEmpty(p.getId())) {
                System.out.println(p.getDelStatus());
                p.setLastModifier_id(login.getUser().getId());
                p.setLastModifiedTime(DateUtils.getNowDate());
                smallgroup_studentService.update(p);
                context+="修改了基础数据城市设置，修改城市"+p.getId();
            } else {
                //check existence
                WhereCondition wc = new WhereCondition();
                wc.andEquals("smallgroup_id", p.getSmallgroup_id());
                wc.andEquals("student_id", p.getStudent_id());
                int count = smallgroup_studentService.count(wc);
                if(count>0){
                    AjaxUtils.ajaxJsonWarnMessage(response, "new_toastr_0092");
                    return null;
                }

                String id = sequenceManager.generateId("smallgroup_student");
                p.setId(id);
                initCreate2(p, request);
                smallgroup_studentService.insert(p);
                context+="新增了基础数据城市设置，新增城市";
            }
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/smallgroup_student/delete")
    public String execute3(ModelMap model, @RequestBody String content,
                           String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<Smallgroup_student> list = new ArrayList<Smallgroup_student>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Smallgroup_student p = new Smallgroup_student();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (Smallgroup_student p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            smallgroup_studentService.deleteByCondition(wc);
            String context=getLogin(request).getUser().getUserName()+"删除了基础数据城市："+ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/smallgroup_student/deletestatus")
    public String execute3s(ModelMap model, @RequestBody String content,
                            String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<Smallgroup_student> list = new ArrayList<Smallgroup_student>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Smallgroup_student p = new Smallgroup_student();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (Smallgroup_student p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            smallgroup_studentService.deleteBystatus(wc);
            String context=getLogin(request).getUser().getUserName()+"删除了基础数据城市："+ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/paikeguanli/smallgroup_student/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            wc.setLength(page.getItemsperpage());
            wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
            wc.setOrderBy(orderGuize);
            initWanNengChaXun(jsonObj, wc);// 万能查询
            List list = smallgroup_studentService.query(wc);
            JSONArray ja = JSONArray.fromObject(list,jsonConfig);
            page.setTotalItems(smallgroup_studentService.count(wc));
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s=JSONObject.fromObject(map,jsonConfig).toString();
            AjaxUtils.ajaxJson(response,s );
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }
    @RequestMapping(value = "/ngres/paikeguanli/smallgroup_student/list/id")
    public String executesd1(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            Smallgroup_student k =  smallgroup_studentService.loadById(JSONUtils.getStr(jsonObj, "id"));
            JSONObject jo= JSONObject.fromObject(k,jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        Page wc=new Page();
    }
}
