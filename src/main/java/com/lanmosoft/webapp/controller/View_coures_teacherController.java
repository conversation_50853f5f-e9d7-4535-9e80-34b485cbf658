/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.MathUtils;
import com.lanmosoft.webapp.controller.searchForm.Page;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class View_coures_teacherController extends BaseController {

	@Autowired
	View_coures_teacherService view_coures_teacherService;
	@Autowired
	Student_courseService  student_courseService;
	@Autowired
	ContractviewService contractviewService;
	@Autowired
	View_consumption_student_contractService view_consumption_student_contractService;
	@Autowired
	View_attendanceBookService view_attendanceBookService;
	@RequestMapping(value = "/ngres/xueshengguanli/view_coures_teacher/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			View_coures_teacher p = new View_coures_teacher();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				view_coures_teacherService.update(p);
				context+="修改了老师可教授科目设置，修改老师可教授科目"+p.getId();
			} else {
				String id = sequenceManager.generateId("view_coures_teacher");
				p.setId(id);
				initCreate2(p, request);
				view_coures_teacherService.insert(p);
				context+="新增了老师可教授科目设置，新增老师可教授科目"+p.getId();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response,"new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/view_coures_teacher/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<View_coures_teacher> list = new ArrayList<View_coures_teacher>();
			for (int i = 0; i < jsonArray.size(); i++) {
				View_coures_teacher p = new View_coures_teacher();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (View_coures_teacher p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			student_courseService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了学生课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/xueshengguanli/view_coures_teacher/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<View_coures_teacher> list = new ArrayList<View_coures_teacher>();
			for (int i = 0; i < jsonArray.size(); i++) {
				View_coures_teacher p = new View_coures_teacher();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (View_coures_teacher p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			view_coures_teacherService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了老师课表："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/xueshengguanli/view_coures_teacher/obtlist")
	public String execute1s(ModelMap model, @RequestBody String content,
						   HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			String butype = JSONUtils.getStr(jsonObj, "BUType");
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			//wc.remove("status");
			wc.andEquals("BUType", butype);
			List<View_coures_teacher> list = view_coures_teacherService.query(wc);
			wc.remove("BUType");
			for (View_coures_teacher vct : list) {
				// total obt + course
				Double sumOBT = 0d;
				wc = new WhereCondition();
				wc.andEquals("contractType2", Enums.ContractType2.OBT);
				wc.andEquals("studentId", vct.getStudent_id());
				wc.andEquals("course_id", vct.getCourse_id());
				List<Contractview> queryOBT = contractviewService.query(wc);
				for (Contractview contract : queryOBT) {
					sumOBT += contract.getAmount() * 2;
				}

				//OBT已耗 = classschedule表中BUtype为4的课对应的consumption_student.consumedclass
				Double sumOBTYh = 0d;
				wc = new WhereCondition();
				wc.andEquals("student_id", vct.getStudent_id());
				wc.andEquals("course_id", vct.getCourse_id());
				wc.andEquals("BUType", "4");
				List<View_consumption_student_contract> listOBTYh = view_consumption_student_contractService.queryCS(wc);
				if (!listOBTYh.isEmpty()) {
					for (View_consumption_student_contract v : listOBTYh) {
						sumOBTYh += v.getConsumedClass_ct() == null ? 0 : v.getConsumedClass_ct();
					}
				}

				//未签到排课 = classchedule中status=0   +   classchedule.status=1 and attendancebook中id为null
				Double sumWeiqd = 0d;
				wc = new WhereCondition();
				wc.andEquals("student_id", vct.getStudent_id());
				wc.andEquals("course_id", vct.getCourse_id());
				wc.andEquals("status", 0);
				List<View_attendanceBook> listWeiQd1 = view_attendanceBookService.query(wc);
				wc.remove("status");
				wc.andEquals("status", 1);
				wc.andIsNull("attendanceBook_id");
				List<View_attendanceBook> listWeiQd2 = view_attendanceBookService.query(wc);
				listWeiQd1.addAll(listWeiQd2);
				if (!listWeiQd1.isEmpty()) {
					for (View_attendanceBook va : listWeiQd1) {
						sumWeiqd += va.getTime() == null ? 0 : Double.parseDouble(va.getTime());
					}
				}
				Double sumOBTYipwh = 0d;
				if (!listWeiQd1.isEmpty()) {
					for (View_attendanceBook va : listWeiQd1) {
						if ("4".equals(va.getBUType())) {
							sumOBTYipwh += va.getTime() == null ? 0 : Double.parseDouble(va.getTime());
						}
					}
				}

				Double sumOBTYp = MathUtils.add(sumOBTYh, sumOBTYipwh);

				vct.setSumOBT(sumOBT);
				vct.setSumOBTYh(sumOBTYh);
				vct.setSumOBTYp(sumOBTYp);

			}


			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			page.setTotalItems(view_coures_teacherService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}


	@RequestMapping(value = "/ngres/xueshengguanli/view_coures_teacher/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			//wc.remove("status");
			List<View_coures_teacher> list = view_coures_teacherService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(view_coures_teacherService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/xueshengguanli/view_coures_teacher/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			View_coures_teacher k =  view_coures_teacherService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
}
