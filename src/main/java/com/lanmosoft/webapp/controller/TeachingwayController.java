/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.webapp.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.lanmosoft.dao.model.Teachingway;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.TeachingwayService;
import com.lanmosoft.service.lanmobase.DateUtils;
import com.lanmosoft.util.AjaxUtils;
import com.lanmosoft.util.JSONUtils;
import com.lanmosoft.util.PinyinUtil;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: Sophy
 * </p>
 */
@Controller
public class TeachingwayController extends BaseController {

	@Autowired
	TeachingwayService teachingwayService;
	
	//得到所有的上课方式
	@RequestMapping(value="/ngres/jichushuju/teachingway/findAll")
	public String execute222(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
			try {
				WhereCondition wc=new WhereCondition();
				List<Teachingway> query = teachingwayService.query(wc);
				
				Map map=new HashMap();
				map.put("list",query);
				String string = JSONObject.fromObject(map, jsonConfig).toString();
				AjaxUtils.ajaxJson(response, string);
			} catch (Exception e) {
				AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
				e.printStackTrace();
			}
		return null;
	}
	@RequestMapping(value = "/ngres/jichushuju/teachingway/edit")
	public String execute(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			LoginModel login = (LoginModel)request.getSession().getAttribute("login");
			JSONObject jsonObj = JSONObject.fromObject(content);
			JSONObject job = jsonObj.getJSONObject("item");
			Teachingway p = new Teachingway();
			JSONObject.toBean(job,p,jsonConfig);
			String context=getLogin(request).getUser().getUserName();
			if (StringUtils.isNotEmpty(p.getId())) {
				System.out.println(p.getDelStatus());
				p.setLastModifier_id(login.getUser().getId());
				p.setLastModifiedTime(DateUtils.getNowDate());
				
				teachingwayService.update(p);
				context+="修改了基础数据上课方式设置，修改上课方式"+p.getId();
			} else {
				String id = sequenceManager.generateId("teachingway");
				p.setId(id);
				//p.setCode(PinyinUtil.getTopSpellingUpCase(p.getName()));
				initCreate2(p, request);
				teachingwayService.insert(p);
				context+="新增了基础数据上课方式设置，新增上课方式"+p.getName();
			}
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/teachingway/delete")
	public String execute3(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Teachingway> list = new ArrayList<Teachingway>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Teachingway p = new Teachingway();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Teachingway p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			teachingwayService.deleteByCondition(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据上课方式："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}
	
	@RequestMapping(value = "/ngres/jichushuju/teachingway/deletestatus")
	public String execute3s(ModelMap model, @RequestBody String content,
			String age, HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONArray jsonArray = JSONArray.fromObject(content);
			List<Teachingway> list = new ArrayList<Teachingway>();
			for (int i = 0; i < jsonArray.size(); i++) {
				Teachingway p = new Teachingway();
				JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)),p,jsonConfig);
				list.add(p);
			}
			List<String> ids = new ArrayList<String>();
			for (Teachingway p : list) {
				ids.add(p.getId());
			}
			WhereCondition wc = new WhereCondition();
			wc.andIn("id", ids);
			teachingwayService.deleteBystatus(wc);
			String context=getLogin(request).getUser().getUserName()+"删除了基础数据上课方式："+ids;
			operationLog.setId(sequenceManager.generateId("operationLog"));
			operationLog.setUser_id(getLogin(request).getUser().getId());
			operationLog.setCreateTime(new Date());
			operationLog.setContent(context);
			operationLogService.insert(operationLog);
			AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
			e.printStackTrace();
		}
		return null;
	}

	@RequestMapping(value = "/ngres/jichushuju/teachingway/list")
	public String execute1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			// 分页对象
			Page page = getPage(jsonObj);
			// 服务端排序规则
			String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
			// 组装查询条件
			WhereCondition wc = new WhereCondition();
			wc.setLength(page.getItemsperpage());
			wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
			wc.setOrderBy(orderGuize);
			initWanNengChaXun(jsonObj, wc);// 万能查询
			List list = teachingwayService.query(wc);
			JSONArray ja = JSONArray.fromObject(list,jsonConfig);
			page.setTotalItems(teachingwayService.count(wc));
			Map map = new HashMap();
			map.put("page", page);
			map.put("list", ja);
			String s=JSONObject.fromObject(map,jsonConfig).toString();
			AjaxUtils.ajaxJson(response,s );
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		@RequestMapping(value = "/ngres/jichushuju/teachingway/list/id")
	public String executesd1(ModelMap model, @RequestBody String content,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			JSONObject jsonObj = JSONObject.fromObject(content);
			Teachingway k =  teachingwayService.loadById(JSONUtils.getStr(jsonObj, "id"));
			JSONObject jo= JSONObject.fromObject(k,jsonConfig);
			AjaxUtils.ajaxJson(response, jo.toString());
		} catch (Exception e) {// TODO
			AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
			e.printStackTrace();
		}
		return null;
	}
		
		public static void main(String[] args) {
			Page wc=new Page();
		}
}
