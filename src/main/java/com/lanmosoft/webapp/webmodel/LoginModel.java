package com.lanmosoft.webapp.webmodel;

import java.io.Serializable;
import java.util.List;

import com.lanmosoft.dao.model.Permission;
import com.lanmosoft.dao.model.Timezone;
import com.lanmosoft.dao.model.User;
import com.lanmosoft.dao.model.View_ZonePermission;

public class LoginModel implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 4337904567503550126L;
	private User user;
	private List<Permission> permissionList;
	private List<View_ZonePermission> zonePermissionList;
	private List<Timezone> timezoneList;
	private String zone_id;
	private String language;
	private String timezone_id;
	private String BUType;
	private String timezone;
	
	public User getUser() {
		return user;
	}
	public void setUser(User user) {
		this.user = user;
	}
	public List<Permission> getPermissionList() {
		return permissionList;
	}
	public void setPermissionList(List<Permission> permissionList) {
		this.permissionList = permissionList;
	}
	public List<View_ZonePermission> getZonePermissionList() {
		return zonePermissionList;
	}
	public void setZonePermissionList(List<View_ZonePermission> zonePermissionList) {
		this.zonePermissionList = zonePermissionList;
	}
	public List<Timezone> getTimezoneList() {
		return timezoneList;
	}
	public void setTimezoneList(List<Timezone> timezoneList) {
		this.timezoneList = timezoneList;
	}
	public String getZone_id() {
		return zone_id;
	}
	public void setZone_id(String zone_id) {
		this.zone_id = zone_id;
	}
	public String getLanguage() {
		return language;
	}
	public void setLanguage(String language) {
		this.language = language;
	}
	public String getTimezone_id() {
		return timezone_id;
	}
	public void setTimezone_id(String timezone_id) {
		this.timezone_id = timezone_id;
	}
	public String getBUType() {
		return BUType;
	}
	public void setBUType(String BUType) {
		this.BUType = BUType;
	}
	public String getTimezone() {
		return timezone;
	}
	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}
}
