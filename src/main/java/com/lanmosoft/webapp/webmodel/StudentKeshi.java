package com.lanmosoft.webapp.webmodel;

public class StudentKeshi {
    private Double sumKeshi;
    private Double yixiaohao;
    private Double yipaike;
    private Double weipaike;
    private Double offlineYihao;
    private Double onlineYihao;
    private Double offlineYipai;
    private Double onlineYipai;
    private Double speYihao;
    private Double speYipai;
    private Double speTotal;

    private Double obtYihao;
    private Double obtYipai;
    private Double obtTotal;

    public Double getOfflineYihao() {
        return offlineYihao;
    }

    public void setOfflineYihao(Double offlineYihao) {
        this.offlineYihao = offlineYihao;
    }

    public Double getOnlineYihao() {
        return onlineYihao;
    }

    public void setSpeYihao(Double speYihao) {
        this.speYihao = speYihao;
    }

    public Double getSpeYihao() {
        return speYihao;
    }

    public void setSpeYipai(Double speYipai) {
        this.speYipai = speYipai;
    }

    public Double getSpeYipai() {
        return speYipai;
    }

    public void setObtYihao(Double obtYihao) {
        this.obtYihao = obtYihao;
    }

    public Double getObtYihao() {
        return obtYihao;
    }

    public void setObtYipai(Double obtYipai) {
        this.obtYipai = obtYipai;
    }

    public Double getObtYipai() {
        return obtYipai;
    }

    public void setOnlineYihao(Double onlineYihao) {
        this.onlineYihao = onlineYihao;
    }

    public Double getOfflineYipai() {
        return offlineYipai;
    }

    public void setOfflineYipai(Double offlineYipai) {
        this.offlineYipai = offlineYipai;
    }

    public Double getOnlineYipai() {
        return onlineYipai;
    }

    public void setOnlineYipai(Double onlineYipai) {
        this.onlineYipai = onlineYipai;
    }

    public Double getSumKeshi() {
        return sumKeshi;
    }

    public void setSumKeshi(Double sumKeshi) {
        this.sumKeshi = sumKeshi;
    }

    public Double getYixiaohao() {
        return yixiaohao;
    }

    public void setYixiaohao(Double yixiaohao) {
        this.yixiaohao = yixiaohao;
    }

    public Double getYipaike() {
        return yipaike;
    }

    public void setYipaike(Double yipaike) {
        this.yipaike = yipaike;
    }

    public Double getWeipaike() {
        return weipaike;
    }

    public void setWeipaike(Double weipaike) {
        this.weipaike = weipaike;
    }

    //总课时
    //已消耗
    //已排课
    public void setSpeTotal(Double speTotal) {
        this.speTotal = speTotal;
    }

    public Double getSpeTotal() {
        return speTotal;
    }

    public void setObtTotal(Double obtTotal) {
        this.obtTotal = obtTotal;
    }

    public Double getObtTotal() {
        return obtTotal;
    }
}
