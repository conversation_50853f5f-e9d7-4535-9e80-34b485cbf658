package com.lanmosoft.webapp.webmodel;

import java.util.List;

import com.lanmosoft.dao.model.Student_freeTime;
import com.lanmosoft.dao.model.Teacher_freeTime;
import com.lanmosoft.dao.model.User;

public class PaikeItem {
	private String name;
	private String startTime;
	private String centerTime;
	private String endTime;
	private List<Teacher_freeTime> t_freeTimeList;
	private List<Student_freeTime> s_freeTimeList;
	
	private String flag;//标记时间段 1-1小时 2-2小时
	
	
/*	private List<Student_freeTime> s_freeTimeListh;//后面一段时间的数据
	private List<List<Student_freeTime>> li_s_freeTimeList;*/
	

	
	
	
	

	/*public List<List<Student_freeTime>> getLi_s_freeTimeList() {
		return li_s_freeTimeList;
	}
	public void setLi_s_freeTimeList(List<List<Student_freeTime>> li_s_freeTimeList) {
		this.li_s_freeTimeList = li_s_freeTimeList;
	}
	public List<Student_freeTime> getS_freeTimeListh() {
		return s_freeTimeListh;
	}
	public void setS_freeTimeListh(List<Student_freeTime> s_freeTimeListh) {
		this.s_freeTimeListh = s_freeTimeListh;
	}*/
	public String getCenterTime() {
		return centerTime;
	}
	public void setCenterTime(String centerTime) {
		this.centerTime = centerTime;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public List<Teacher_freeTime> getT_freeTimeList() {
		return t_freeTimeList;
	}
	public void setT_freeTimeList(List<Teacher_freeTime> t_freeTimeList) {
		this.t_freeTimeList = t_freeTimeList;
	}
	public List<Student_freeTime> getS_freeTimeList() {
		return s_freeTimeList;
	}
	public void setS_freeTimeList(List<Student_freeTime> s_freeTimeList) {
		this.s_freeTimeList = s_freeTimeList;
	}
	public String getFlag() {
		return flag;
	}
	public void setFlag(String flag) {
		this.flag = flag;
	}
	
	
	
}
