/**
 * Enums.java
 * Created at 2014年3月5日
 * Created by Jackie
 * Copyright (C) 2014 lanmosoft.com, All rights reserved.
 */
package com.lanmosoft.enums;

/**
 * <p>ClassName: Enums</p>
 * <p>Description: 各种枚举数值的类</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2014年3月5日</p>
 */
public class Enums {

    public static final String SEPARATOR = "/";
    public static final String COMMA = ",";
    public static final String SPACE = " ";
    public static final String PERCENT = "%";
    public static final String TRUE_STRING = "1";
    public static final String FALSE_STRING = "0";
    public static final String OFFlINE = "1";
    public static final String ONLINE = "2";
    public static final String SPE = "3";
    public static final String ONLINETE = "4";

    public class DelStatus {
        public static final String UNREMOVED = "0";
        public static final String REMOVED = "1";
    }

    public class OrgType {
        public static final String DEPARTMENT = "dept";
        public static final String POSITION = "post";
    }

    public class IconSkin {
        public static final String pIcon02 = "pIcon02";
        public static final String POSITION = "post";
    }

    public class IsSendEmail {
        public static final String send = "1";
        public static final String noSend = "0";
        public static final String SendError = "2";
    }

    public class ModuleName {
        public static final String F_HOME = "home";

        public static final String F_STUDENT = "student";
        public static final String S_STUDENT_LIST = "student_list";
        public static final String S_STUDENT_REPORT = "student_report";
        public static final String S_STUDENT_FOLLOWUP = "student_followup";
        public static final String S_STUDENT_RETURNS = "student_returns";
        public static final String S_STUDENT_FOLLOWUPTASK = "student_followupTask";
        public static final String S_STUDENT_LEAVE = "student_leave";

        public static final String S_STUDENT_SUQIU = "suqiu";

        public static final String F_TEACHER = "teacher";
        public static final String S_TEACHER_LIST = "teacher_list";
        public static final String S_TEACHER_LEAVE = "teacher_leave";

        public static final String F_CONTRACT = "contract";
        public static final String S_CONTRACT_LIST = "contract_list";

        public static final String F_PAIKE = "paike";
        public static final String S_PAIKE_LIST = "paike_list";
        public static final String S_PAIKE_MANUAL = "paike_manual";

        public static final String F_ATTENDANCEBOOK = "attendanceBook";
        public static final String S_ATTENDANCEBOOK_TODAY = "attendanceBook_today";
        public static final String S_ATTENDANCEBOOK_HISTORY = "attendanceBook_history";

        public static final String F_JICHUSHUJU = "base";
        public static final String S_JICHUSHUJU_DATA = "base_data";
        public static final String S_JICHUSHUJU_PRODUCT = "base_product";

        public static final String F_SETTING = "setting";
        public static final String S_SETTING_ORG = "setting_org";
        public static final String S_SETTING_PERMISSION = "setting_permission";

        public static final String F_BAOBIAO = "report";
        public static final String S_BAOBIAO_ZONGKEBIAO = "report_zongkebiao";
        public static final String S_BAOBIAO_SHENGYUKESHI = "report_shengyukeshi";
        public static final String S_BAOBIAO_LAOSHIGONGZI = "report_laoshigongzi";
        public static final String S_BAOBIAO_DAIXUFEIXUEYUAN = "report_daixufeixueyuan";
        public static final String S_BAOBIAO_XUBAO = "report_xubao";
        public static final String S_BAOBIAO_ZHUANJIESHAO = "report_zhuanjieshao";
        public static final String S_BAOBIAO_KESHI = "report_keshi";
        public static final String S_BAOBIAO_FUWULIUCHENG = "report_fuwuliucheng";
        public static final String S_BAOBIAO_DANGYUEPAIKE = "report_dangyuepaike";
        public static final String S_BAOBIAO_JIXIAO = "report_jixiao";
        public static final String S_BAOBIAO_XINZHAO = "report_xinzhao";
        public static final String S_BAOBIAO_KESHISHENGYU = "report_keshishengyu";
        public static final String S_BAOBIAO_HUIZONG = "report_huizong";
        public static final String S_BAOBIAO_JIEZHUN = "report_jiezhuan";
        public static final String S_BAOBIAO_MINGXI = "report_mingxi";
        public static final String S_BAOBIAO_ZIDINGYI = "report_zidingyi";

        public static final String F_JIAOSHIKEBIAO = "teacherSchedule";
        public static final String S_JIAOSHIKEBIAO_CHAKANKEBIAO = "teacherSchedule_chakankebiao";
    }

    public class UserType {
        public static final String SUPER = "0";//超级管理员
        public static final String ADMIN = "1";//管理员
        public static final String GENERAL = "2";//普通用户
    }

    public class DataRole {
        public static final String DISABLED = "0";
        public static final String ALL = "1";
        public static final String POST = "2";
        public static final String DEPT = "3";
        public static final String SELF = "4";
    }

    public class Gender {
        public static final String FEMALE = "female";
        public static final String MALE = "male";
    }

    /**
     * 邮件提醒类型
     */
    public class EmailRenderType {
        public static final String Student = "1";
        public static final String Tutor = "2";
    }

    /**
     * 出勤状态
     *
     * <AUTHOR>
     */
    public class AttendanceStatus {
        /**
         * 正常出勤
         */
        public static final String NORMAL = "00";
        /**
         * 学生迟到
         */
        public static final String STUDENT_LATE = "11";
        /**
         * 学生缺勤
         */
        public static final String STUDENT_ABSENTEEISM = "12";
        /**
         * 老师迟到
         */
        public static final String TEACHER_LATE = "21";
        /**
         * 老师缺勤
         */
        public static final String TEACHER_ABSENTEEISM = "22";
    }

    /**
     * 审批状态
     *
     * <AUTHOR>
     */
    public class ApprovalStatus {
        /**
         * 无需审批（废弃）
         */
        public static final String STATUS_NOPROCESS = "0";// 无需审批
        /**
         * 待审批
         */
        public static final String STATUS_NEW = "1";// 待审批
        /**
         * 审批通过
         */
        public static final String STATUS_END = "2";// 审批通过
        /**
         * 驳回
         */
        public static final String STATUS_TERMINATE = "3";// 驳回
    }

    /**
     * 老师性质
     *
     * <AUTHOR>
     */
    public class TeacherType {
        public static final String FULL_TIME = "1"; // 全职
        public static final String HALF_TIME = "2"; // 兼职
        public static final String HALF_FULL_TIME = "3"; // 半兼职
    }

    /**
     * 赠送类型
     *
     * <AUTHOR>
     */
    public class PersentType {
        /**
         * SA赠送
         */
        public static final String SA = "1";
        /**
         * SC赠送
         */
        public static final String SC = "2";
        /**
         * TE推荐赠送
         */
        public static final String TE_REFERRAL = "3";
        /**
         * TE老师迟到赠送
         */
        public static final String TE_TEACHER_LATE = "4";
        /**
         * TE老师缺勤赠送
         */
        public static final String TE_TEACHER_ABSENTEEISM = "5";
        /**
         * TE其他赠送
         */
        public static final String TE_OTHER = "6";
    }

    /**
     * 产品类型
     *
     * <AUTHOR>
     */
    public class ProductType {
        public static final String ONE_TO_ONE = "1";
    }

    /**
     * 操作课表动作
     *
     * <AUTHOR>
     */
    public class ClassScheduleAction {
        public static final String INIT = "init";
        public static final String CONFIRM = "confirm";
        public static final String DELETE = "delete";
        public static final String REVERT = "revert";
    }

    /**
     * 课表状态
     *
     * <AUTHOR>
     */
    public class ClassScheduleStatus {
        /**
         * 初始化
         */
        public static final String INITIALIZED = "0";
        /**
         * 已确认
         */
        public static final String CONFIRMED = "1";
        /**
         * 已删除
         */
        public static final String DELETED = "2";
    }

    /**
     * 请假角色
     *
     * <AUTHOR>
     */
    public class LeaveActor {
        public static final String TEACHER = "teacher";
        public static final String STUDENT = "student";
    }

    /**
     * 合同类型
     *
     * <AUTHOR>
     */
    public class ContractType2 {
        public static final String HYBRID = "3";
        public static final String SPE = "4";
        public static final String OBT = "5";
    }


    /**
     * 请假状态
     *
     * <AUTHOR>
     */
    public class LeaveState {
        /**
         * 未请假
         */
        public static final String LEAVE_NONE = "0";
        /**
         * 学生请假
         */
        public static final String LEAVE_STUDENT = "1";
        /**
         * 老师请假
         */
        public static final String LEAVE_TEACHER = "2";

    }

    public class CourseType {
        /**
         * 正常课程
         */
        public static final String COURSETYPE_1 = "1";// 正常课程
        /**
         * 少发(不上课，发工资，扣学时）
         */
        public static final String COURSETYPE_2 = "2";
        /**
         * 多发（上课，不发工资，不扣学时）
         */
        public static final String COURSETYPE_3 = "3";//
        /**
         * 多发（上课，发工资，不扣学时）
         */
        public static final String COURSETYPE_4 = "4";//
    }

    public class TeachingWay {
        /**
         * 教室授课
         */
        public static final String TEACHINGWAY_1 = "1";
        /**
         * 上门授课
         */
        public static final String TEACHINGWAY_2 = "2";
        /**
         * 远程授课
         */
        public static final String TEACHINGWAY_3 = "3";
    }

    public class FilerecordStatus {
        /**
         * 删除状态
         */
        public static final String STAUS_0 = "0";
        /**
         * 正常状态
         */
        public static final String STAUS_1 = "1";
        /**
         * 临时状态
         */
        public static final String STAUS_2 = "2";
    }

    public class ContractStatus {
        /**
         * 待确认
         */
        public static final String STAUS_1 = "1";
        /**
         * 执行中
         */
        public static final String STAUS_2 = "2";
        /**
         * 已结束
         */
        public static final String STAUS_3 = "3";
    }

    public class Location {

        public static final String SH = "SH TE";
        public static final String BJ = "BJ TE";
        public static final String TJ = "TJ TE";
        public static final String CD = "CD TE";
        public static final String SZ = "SZ TE";
        public static final String UK = "UK TE";
    }

    public class StudentStatus {
        /**
         * 欠费
         */
        public static final String STAUS_1 = "1";
        /**
         * 待确认
         */
        public static final String STAUS_2 = "2";
        /**
         * 执行中
         */
        public static final String STAUS_3 = "3";
        /**
         * 已结束
         */
        public static final String STAUS_4 = "4";
        /**
         * 冻结
         */
        public static final String STAUS_5 = "5";
    }

    public class ProductName {
        /**
         * SA赠送
         */
        public static final String SA = "SA-赠送";
        /**
         * SC赠送
         */
        public static final String SC = "SC-赠送";
        /**
         * TE推荐赠送
         */
        public static final String TE_REFERRAL = "TE-推荐赠送";
        /**
         * TE老师迟到赠送
         */
        public static final String TE_TEACHER_LATE = "TE-老师迟到赠送";
        /**
         * TE老师缺勤赠送
         */
        public static final String TE_TEACHER_ABSENTEEISM = "TE-老师缺勤赠送";
        /**
         * TE其他赠送
         */
        public static final String TE_OTHER = "TE-其他赠送";
    }

    /**
     * 教师默认课时费
     *
     * <AUTHOR>
     */
    public class TeacherHourlyratescheme {
        /**
         * 默认
         */
        public static final String ISDEFAULT = "1";
        /**
         * 非默认
         */
        public static final String NOTISDEFAULT = "0";

    }

    public class PaymentMethod {
        public static final String Bank = "1";

        public static final String Cash = "2";

        public static final String B2B = "3";

        public static final String Ranstard = "4";
    }
}
