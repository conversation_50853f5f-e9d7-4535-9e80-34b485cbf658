package com.lanmosoft.enums;

/**
 * 
 * <AUTHOR>
 *
 */
public final class LanConstants {

	private LanConstants(){
		
	}
	// ======================静态变量================
	
	public static String BASEDIR = "/lanmo/upload";
	
	/* ====================迟到缺勤默认值==================*/
	public static Double CONSUMEDCLASS_PART_21_HALF = 1D;// 兼职，老师迟到，二分之一课时，学生消耗课时
	public static Double GIVENCLASS_PART_21_HALF = 1D;// 兼职，老师迟到，二分之一课时，学生获赠课时
	public static Double WAGE_PART_21_HALF = 1D;// 兼职，老师迟到，二分之一课时，老师工资（小时）
	public static Double WAGEDEDUCTION_PART_21_HALF = 1D;// 兼职，老师迟到，二分之一课时，倒扣老师工资（小时）
	
	public static Double CONSUMEDCLASS_PART_21_ONE = 0D;// 兼职，老师迟到，一课时，学生消耗课时
	public static Double GIVENCLASS_PART_21_ONE = 1D;// 兼职，老师迟到，一课时，学生获赠课时
	public static Double WAGE_PART_21_ONE = 0D;// 兼职，老师迟到，一课时，老师工资（小时）
	public static Double WAGEDEDUCTION_PART_21_ONE = 1D;// 兼职，老师迟到，一课时，倒扣老师工资（小时）
	
	public static Double CONSUMEDCLASS_PART_22 = 0D;// 兼职，老师缺勤，学生消耗课时
	public static Double GIVENCLASS_PART_22 = 1D;// 兼职，老师缺勤，学生获赠课时
	public static Double WAGE_PART_22 = 0D;// 兼职，老师缺勤，老师工资（小时）
	public static Double WAGEDEDUCTION_PART_22 = 1D;// 兼职，老师缺勤，倒扣老师工资（小时）
	
	public static Double GIVENCLASS_PART_11 = 0D;// 兼职，学生迟到，学生获赠课时
	public static Double WAGEDEDUCTION_PART_11 = 0D;// 兼职，学生迟到，倒扣老师工资（小时）
	
	public static Double GIVENCLASS_PART_12 = 0D;// 兼职，学生缺勤，学生获赠课时
	public static Double WAGE_PART_12 = 1D;// 兼职，学生缺勤，老师工资（小时）
	public static Double WAGEDEDUCTION_PART_12 = 0D;// 兼职，学生缺勤，倒扣老师工资（小时）
	
	public static Double CONSUMEDCLASS_FULL_21 = 1D;// 全职，老师迟到，学生消耗课时
	public static Double GIVENCLASS_FULL_21 = 1D;// 全职，老师迟到，学生获赠课时
	public static Double WAGE_FULL_21 = 1D;// 全职，老师迟到，老师工资（小时）
	public static Double WAGEDEDUCTION_UNDER80_FULL_21 = 0D;// 全职，老师迟到，80小时以内，倒扣老师工资（元）
	public static Double WAGEDEDUCTION_OVER80_FULL_21 = 250D;// 全职，老师迟到，超80小时，倒扣老师工资（元）
	
	public static Double CONSUMEDCLASS_FULL_22 = 0D;// 全职，老师缺勤，学生消耗课时
	public static Double GIVENCLASS_FULL_22 = 1D;// 全职，老师缺勤，学生获赠课时
	public static Double WAGE_FULL_22 = 0D;// 全职，老师缺勤，老师工资（小时）
	public static Double WAGEDEDUCTION_FULL_22 = 0D;// 全职，老师缺勤，倒扣老师工资（元）
	
	public static Double GIVENCLASS_FULL_11 = 0D;// 全职，学生迟到，学生获赠课时
	public static Double WAGE_FULL_11 = 0D;//  全职，学生迟到，老师工资（小时）
	public static Double WAGEDEDUCTION_FULL_11 = 0D;// 全职，学生迟到，倒扣老师工资（元）
	
	public static Double GIVENCLASS_FULL_12 = 0D;// 全职，学生缺勤，学生获赠课时
	public static Double WAGE_FULL_12 = 1D;// 全职，学生缺勤，老师工资（小时）
	public static Double WAGEDEDUCTION_FULL_12 = 0D;// 全职，学生缺勤，倒扣老师工资（元）
	
	/* ====================排课上下限==================*/
	public static Double GLOBAL_DAY_UPPER_LIMIT = 12D; //全局每日课时上限
	public static Double GLOBAL_DAY_UPPER_LIMIT_COURSE = 4D; //全局每日课时上限课程
	
	public static Double GLOBAL_WEEK_UPPER_LIMIT_FULL = 20D; //全局每周课时上限全职
	public static Double GLOBAL_WEEK_UPPER_LIMIT_PART = 15D; //全局每周课时上限兼职
	
	public static Double GLOBAL_MONTH_UPPER_LIMIT_FULL = 80D; //全局每月课时上限全职
	public static Double GLOBAL_MONTH_UPPER_LIMIT_PART = 60D; //全局每月课时上限兼职
	
	/* ====================学员跟进提醒==================*/
	public static int  FOLLOWUP_DAY = 3; //学员跟进提醒天数
	
	/* ====================课表删除提醒邮件主题==================*/
	public static String  SUJECT = "课表删除提醒"; //课表删除提醒邮件主题
	
	public static Double GLOBAL_OVERCLASS = 15D; //超出课时提醒
	
}
