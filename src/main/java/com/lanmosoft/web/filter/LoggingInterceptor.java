package com.lanmosoft.web.filter;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

import com.lanmosoft.util.LanDateUtils;

public class LoggingInterceptor extends AbstractPhaseInterceptor<Message> {
	private final static Logger logger = LogManager.getLogger(AuthInterceptor.class);
	public LoggingInterceptor() {
		super(Phase.USER_LOGICAL);
	}
	
	public LoggingInterceptor(String phase) {
		super(phase);
	}
	@Override
	public void handleMessage(Message message) throws Fault {
		//获取报文内容
		List paramList = message.getContent(List.class);
		if(CollectionUtils.isNotEmpty(paramList)){
			logger.info("sendTime:"+LanDateUtils.currentTimeStr_web()+",returnMsg:"+paramList.get(0).toString());
		}
	}
	public void handleFault(Message message) {  
        Exception exeption=message.getContent(Exception.class);  
        logger.error("errorTime:"+LanDateUtils.currentTimeStr_web()+",errorMsg:"+exeption.getMessage(), exeption);  
    }
}
