package com.lanmosoft.web.filter;

import org.apache.cxf.Bus;
import org.apache.cxf.feature.AbstractFeature;
import org.apache.cxf.interceptor.InterceptorProvider;

public class CustomerFeature extends AbstractFeature {

	@Override
	protected void initializeProvider(InterceptorProvider provider, Bus bus) {
		//输入拦截
		provider.getInInterceptors().add(new AuthInterceptor());
		//拦截输出
		provider.getOutInterceptors().add(new LoggingInterceptor());
	}

	
}
