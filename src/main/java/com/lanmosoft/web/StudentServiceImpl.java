package com.lanmosoft.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.model.Paikeinfo;
import com.lanmosoft.service.LanmoMailSender;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.model.PaiKeGuwenInfo;
import com.lanmosoft.util.JSONUtils;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.enums.Enums.ContractStatus;
import com.lanmosoft.enums.Enums.Gender;
import com.lanmosoft.enums.Enums.Location;
import com.lanmosoft.enums.Enums.StudentStatus;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.lanmobase.SequenceManager;
import com.lanmosoft.util.LanDateUtils;
import com.lanmosoft.util.PasswdUtil;
import com.lanmosoft.web.model.Message;

@WebService(serviceName = "StudentService", targetNamespace = "http://lanmosoft.com/services", endpointInterface = "com.lanmosoft.web.IStudentService")
@SOAPBinding(style = Style.DOCUMENT)
@Service
public class StudentServiceImpl implements IStudentService {
	@Autowired
	StudentService studentService;
	@Autowired
	ContractService contractService;
	@Autowired
	TransactionTemplate transactionTemplate;
	@Autowired
	SequenceManager sequenceManager;
	@Autowired
	Consumption_student_contractService consumption_student_contractService;
	@Autowired
	Student_zoneService student_zoneService;
	@Autowired
	PaiKeGuwenInfo paiKeGuwenInfo;
	@Autowired
	LanmoMailSender lanmoMailSender;
	@Autowired
	ZoneService zoneService;
	
	@WebMethod(operationName = "saveStudent")
	@WebResult(name = "message")
	@Override
	public String saveStudent(@WebParam(name = "studentInfo") final String data) {
		JsonParser jsonParser = new JsonParser();
		Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
		List<Message> returnMessages = new ArrayList<Message>();
		JsonElement element;
		try {
			element = jsonParser.parse(data);
		} catch (JsonSyntaxException jsonSyntaxException) {
			Message message = new Message();
			message.setSuccess(false);
			message.setErrorMsg("数据格式有误，请使用JSON格式字符串！");
			message.setCrmId("");
			returnMessages.add(message);
			return new Gson().toJson(returnMessages);
		}
		String Token = null;
		JsonArray ja = null;
		if (element.isJsonObject()) {
			JsonObject jobj = element.getAsJsonObject();
			if(jobj.get("Token")!=null){
				Token = jobj.get("Token").getAsString();
			}
			ja = jobj.getAsJsonArray("StudentInfo");
			System.out.println("studentInfo:"+ja);
		}
		//验证Token
		if(!StringUtils.equals(tokenStudent, PasswdUtil.decode(Token))){
			Message message = new Message();
			message.setSuccess(false);
			message.setErrorMsg("非法请求");
			message.setCrmId("");
			returnMessages.add(message);
			return new Gson().toJson(returnMessages);
		}
		if(ja==null){
			Message message = new Message();
			message.setSuccess(false);
			message.setErrorMsg("数据格式有误，请传入学生信息！");
			message.setCrmId("");
			returnMessages.add(message);
			return new Gson().toJson(returnMessages);
		}
		for (JsonElement ele : ja) {
			JsonObject obj = ele.getAsJsonObject();
			final Student s;
			final Message message = new Message();
			try {

				s= gson.fromJson(ele, Student.class);
				System.out.println("学生的咨询顾问:"+s.getOwnerZone());
				//根据sf传递过来的咨询顾问校区,添加资源顾问城市
				if(StringUtils.isNotBlank(s.getOwnerZone())){
					getOwnerCity(s);
				}
				//单独处理非字符串字段
				if(obj.get("birthday")!=null){
					if(StringUtils.isNotEmpty(obj.get("birthday").getAsString())){
						s.setBirthday(LanDateUtils.convertStringToDate("yyyy-MM-dd", obj.get("birthday").getAsString()));
					}
				}
			}catch (JsonSyntaxException jsonSyntaxException) {
				message.setSuccess(false);
				message.setErrorMsg("jsonSyntaxException:"
						+ jsonSyntaxException.getMessage());
				message.setCrmId(obj.get("crmId").getAsString());
				returnMessages.add(message);
				continue;
			}

			if (StringUtils.isNotEmpty(obj.get("type").getAsString())) {
				s.setType(obj.get("type").getAsString());
			}

			if (StringUtils.isEmpty(s.getAccountId())) {
				message.setSuccess(false);
				message.setErrorMsg("accountId 为空！");
				message.setCrmId(s.getCrmId());
				returnMessages.add(message);
				continue;
			}
			transactionTemplate.execute(new TransactionCallbackWithoutResult() {

				@Override
				protected void doInTransactionWithoutResult(
						TransactionStatus arg0) {
					try {
						// 保存学生信息
						WhereCondition wc = new WhereCondition();
						wc.andEquals("accountId", s.getAccountId());
						List<Student> studentList = studentService.query(wc);
						// 性别
						if (StringUtils.equalsIgnoreCase(Gender.MALE,
								s.getGender())) {
							s.setGender("1");
						} else {
							s.setGender("2");
						}
						// teReferral
						if (StringUtils.equalsIgnoreCase("true",
								s.getTeReferral())) {
							s.setTeReferral("1");
						} else if(StringUtils.equalsIgnoreCase("false",
								s.getTeReferral())){
							s.setTeReferral("0");
						}
						// contractType
						if (StringUtils.equalsIgnoreCase("renew",
								s.getContractType())) {
							s.setContractType("2");
						} else {
							s.setContractType("1");
						}
						if (CollectionUtils.isNotEmpty(studentList)) {
							s.setId(studentList.get(0).getId());
							// Referral如果不为空,在SF传输过来不更新
							if(StringUtils.isNotEmpty(studentList.get(0).getTeReferral())){
								s.setTeReferral(null);
								s.setReferral(null);
							}
							System.out.println("咨询顾问校区(更新):"+s.getOwnerZone());
							studentService.update(s);
						} else {
							s.setId(sequenceManager.generateId("student"));
							s.setDelStatus(Enums.FALSE_STRING);
							s.setStatus(StudentStatus.STAUS_1);//上课状态，欠费
							s.setCreateTime(LanDateUtils.getNowDate());
							s.setCreator_id("Salesforce");
							System.out.println("咨询顾问校区(插入):"+s.getOwnerZone());
							studentService.insert(s);
						}
						message.setSuccess(true);
						message.setErrorMsg(StringUtils.EMPTY);
						message.setCrmId(s.getCrmId());
					} catch (Exception e) {
						arg0.setRollbackOnly();
						message.setSuccess(false);
						message.setErrorMsg(e.getClass().getName()+": "+e.getCause().getMessage());
						message.setCrmId(s.getCrmId());
					}
					
				}
			});
			returnMessages.add(message);
		}
		return new Gson().toJson(returnMessages);
	}

	/**
	 * 根据咨询顾问校区得到城市
	 * @param s
	 */
	private void getOwnerCity(Student s) {
		String ownerCity = null;
		String ownerzone = s.getOwnerZone();
		if(StringUtils.equalsIgnoreCase("SH",ownerzone)){
			ownerCity ="Id_city00000001";
		}else if(StringUtils.equalsIgnoreCase("BJ",ownerzone)){
			ownerCity ="Id_city00000002";
		}else if(StringUtils.equalsIgnoreCase("CD",ownerzone)){
			ownerCity ="Id_city00000003";
		}else if(StringUtils.equalsIgnoreCase("SZ",ownerzone)){
			ownerCity ="Id_city00000004";
		}else if(StringUtils.equalsIgnoreCase("TJ",ownerzone)){
			ownerCity ="Id_city00000005";
		}else if(StringUtils.equalsIgnoreCase("ZZ",ownerzone)){
			ownerCity ="Id_city00000007";
		}

		if(ownerCity!= null){
			s.setOwnerCity(ownerCity);
		}

	}

	@WebMethod(operationName = "saveContrctAndStudent")
	@WebResult(name = "message")
	@Override
	public String saveContract(
			@WebParam(name = "contrctAndStudentInfo") final String data) {
		Long startTime = System.currentTimeMillis();
		System.out.println("data==>"+data);
		List<Message> returnMessages = new ArrayList<Message>();
		Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
		JsonParser jsonParser = new JsonParser();
		JsonElement element;
		try {
			element = jsonParser.parse(data);
		} catch (JsonSyntaxException jsonSyntaxException) {
			Message message = new Message();
			message.setSuccess(false);
			message.setErrorMsg("jsonSyntaxException: 数据格式有误，请使用JSON格式字符串！");
			message.setCrmId("");
			returnMessages.add(message);
			return new Gson().toJson(returnMessages);
		}
		String Token =null;
		JsonArray ja = null;
		if (element.isJsonObject()) {
			JsonObject jobj = element.getAsJsonObject();
			if(jobj.get("Token")!=null){
				Token = jobj.get("Token").getAsString();
			}
			ja = jobj.getAsJsonArray("ContractInfo");
		}
		//验证Token
		if(!StringUtils.equals(PasswdUtil.decode(Token), tokenContract)){
			Message message = new Message();
			message.setSuccess(false);
			message.setErrorMsg("非法请求");
			message.setCrmId("");
			returnMessages.add(message);
			return new Gson().toJson(returnMessages);
		}
		if(ja==null){
			Message message = new Message();
			message.setSuccess(false);
			message.setErrorMsg("数据格式有误，请传入合同信息！");
			message.setCrmId("");
			returnMessages.add(message);
			return new Gson().toJson(returnMessages);
		}
		for (JsonElement ele : ja) {
			final JsonObject obj = ele.getAsJsonObject();
			final Message msg = new Message();

			JsonElement delStatus = obj.get("delStatus");
			JsonElement contractId = obj.get("contractId");
			boolean flagDelStatus = delStatus.isJsonNull();
			boolean flagContractId = contractId.isJsonNull();

			if(!flagDelStatus&&!flagContractId&&StringUtils.equalsIgnoreCase("3", delStatus.getAsString())){
				System.out.println("SF合同删除,但是TE没有删除,所以删除合同");
				String contractIdStr = contractId.getAsString();
				deleteSFAndDeleteTEContract(contractIdStr);

				continue;
			}

			//非空验证
			String resultMsg = vilidateContract(obj);
			if(StringUtils.isNotEmpty(resultMsg)){
				msg.setSuccess(false);
				msg.setErrorMsg(resultMsg);
				if(obj.get("contractId")==null){
					msg.setCrmId("");
				}else{
					msg.setCrmId(obj.get("contractId").getAsString());
				}
				returnMessages.add(msg);
				continue;
			}
			final Student student;
			final Contract contract;
			try {
				System.out.println("SF数据：========"+ele);
				student = gson.fromJson(ele, Student.class);
				contract = gson.fromJson(ele, Contract.class);
			} catch (JsonSyntaxException jsonSyntaxException) {
				Message message = new Message();
				message.setSuccess(false);
				message.setErrorMsg("jsonSyntaxException:"
						+ jsonSyntaxException.getMessage());
				message.setCrmId(obj.get("contractId").getAsString());
				returnMessages.add(message);
				continue;
			}

			//判断合同单元数是否少了
			Double units = getDoubleValue(obj.get("units").getAsString());
			contract.setUnits(units);
			if(sfContractByAmout(contract)){
				continue;
			}
			
			transactionTemplate.execute(new TransactionCallbackWithoutResult() {

				@Override
				protected void doInTransactionWithoutResult(
						TransactionStatus arg0) {
					try {
						// 保存学生信息
						WhereCondition wc = new WhereCondition();
						wc.andEquals("accountId", student.getAccountId());
						List<Student> studentList = studentService.query(wc);
						// 性别
						if (StringUtils.equalsIgnoreCase(Gender.MALE,
								student.getGender())) {
							student.setGender("1");
						} else {
							student.setGender("2");
						}
						// teReferral
						if (StringUtils.equalsIgnoreCase("true",
								student.getTeReferral())) {
							student.setTeReferral("1");
						} else {
							student.setTeReferral("0");
						}
						// contractType
						if (StringUtils.equalsIgnoreCase("renew",
								student.getContractType())) {
							student.setContractType("2");
							contract.setContractType("2");
						} else {
							student.setContractType("1");
							contract.setContractType("1");
						}
						// contractType2: online, offline, hybrid
						if (StringUtils.isNotEmpty(obj.get("contractType2").getAsString())) {
						   contract.setContractType2(obj.get("contractType2").getAsString());
						}
						if (StringUtils.isNotEmpty(obj.get("onlinete_course_id").getAsString())) {
							contract.setCourse_id(obj.get("onlinete_course_id").getAsString());
						}
						if(StringUtils.equals(Location.SH, contract.getLocation())){
							contract.setLocation("上海");
						}else if(StringUtils.equals(Location.BJ, contract.getLocation())){
							contract.setLocation("北京");
						}else if(StringUtils.equals(Location.TJ, contract.getLocation())){
							contract.setLocation("天津");
						}else if(StringUtils.equals(Location.CD, contract.getLocation())){
							contract.setLocation("成都");
						}else if(StringUtils.equals(Location.SZ, contract.getLocation())){
							contract.setLocation("深圳");
						}else if(StringUtils.equals(Location.UK, contract.getLocation())){
							contract.setLocation("英国");
						}
						//单独处理非字符串字段
						if(obj.get("birthday")!=null){
							if(StringUtils.isNotEmpty(obj.get("birthday")+"")&&!"null".equals(obj.get("birthday")+"")){
								student.setBirthday(LanDateUtils.convertStringToDate("yyyy-MM-dd", obj.get("birthday").getAsString()));
							}
						}
						
						if(obj.get("startDate")!=null){
							if(StringUtils.isNotEmpty(obj.get("startDate").getAsString())&&!"null".equals(obj.get("startDate")+"")){
								//合同开始时间
								contract.setStartDate(LanDateUtils.convertStringToDate("yyyy-MM-dd", obj.get("startDate").getAsString()));
							}
						}
						if(obj.get("endDate")!=null){
							if(StringUtils.isNotEmpty(obj.get("endDate").getAsString())&&!"null".equals(obj.get("endDate")+"")){
								//合同结束时间
								contract.setEndDate(LanDateUtils.convertStringToDate("yyyy-MM-dd", obj.get("endDate").getAsString()));
							}
						}
						if(obj.get("thefirstpaymentDate")!=null){
							if(StringUtils.isNotEmpty(obj.get("thefirstpaymentDate").getAsString())&&!"null".equals(obj.get("thefirstpaymentDate")+"")){
								//合同结束时间
								contract.setThefirstpaymentDate(LanDateUtils.convertStringToDate("yyyy-MM-dd", obj.get("thefirstpaymentDate").getAsString()));
							}
						}

						if (StringUtils.isNotEmpty(obj.get("type").getAsString())) {
							student.setType(obj.get("type").getAsString());
						}

						if (CollectionUtils.isNotEmpty(studentList)) {
							student.setId(studentList.get(0).getId());
							studentService.update(student);
						} else {
							student.setId(sequenceManager.generateId("student"));
							student.setDelStatus(Enums.FALSE_STRING);
							student.setCreateTime(LanDateUtils.getNowDate());
							student.setCreator_id("Salesforce");
							studentService.insert(student);

							//create zone and study advisor for the new student
							if (StringUtils.isNotEmpty(obj.get("tutorid").getAsString()) && StringUtils.isNotEmpty(obj.get("zone").getAsString())) {

								Student_zone p = new Student_zone();
								String id = sequenceManager.generateId("student_zone");
								p.setId(id);
								p.setClassStatus("3");//设置默认的上课状态-执行中
								p.setStartDate(contract.getStartDate());
								p.setTutorId(obj.get("tutorid").getAsString());
								p.setStudent_id(student.getId());

								WhereCondition wc2 = new WhereCondition();
								wc2.andEquals("name", obj.get("zone").getAsString());
								List<Zone> list = zoneService.query(wc2);
								String zoneid = "";
								String cityid = "";
								if (!list.isEmpty()) {
									zoneid = list.get(0).getId();
									cityid = list.get(0).getCity_id();
								}

								p.setZone_id(zoneid);
								p.setCity_id(cityid);
								student_zoneService.insert(p);
							}
						}
						contract.setStudentId(student.getId());//学生ID
						contract.setUnits(getDoubleValue(obj.get("units").getAsString()));//单元
						contract.setAmount(getAmount(contract.getProductName(), contract.getUnits()));//数量
						contract.setTuitionFee(getDoubleValue(obj.get("tuitionFee").getAsString()));//学费

						//税率
						if(obj.get("vatRate")!=null && !"null".equals(obj.get("vatRate").toString())){
							String vatRateStr = obj.get("vatRate").getAsString();
							if(StringUtils.isNotEmpty(vatRateStr)){
								if(vatRateStr.contains(Enums.PERCENT)){
									vatRateStr = vatRateStr.replaceAll(Enums.PERCENT, StringUtils.EMPTY);
								}
								contract.setVatRate(getDoubleValue(vatRateStr));
							}
						}
						
						//不含税学费
						if(obj.get("amountWithoutVAT")!=null && !"null".equals(obj.get("amountWithoutVAT").toString())){
							if(StringUtils.isNotEmpty(obj.get("amountWithoutVAT").getAsString())){
								contract.setAmountWithoutVAT(StringUtils.isEmpty(obj.get("amountWithoutVAT").getAsString())?
										0D:getDoubleValue(obj.get("amountWithoutVAT").getAsString()));
							}
						}
						//Vat
						if(obj.get("vat")!=null && !"null".equals(obj.get("vat").toString())){
							if(StringUtils.isNotEmpty(obj.get("vat").getAsString())){
								contract.setVat(StringUtils.isEmpty(obj.get("vat").getAsString())?
										0D:getDoubleValue(obj.get("vat").getAsString()));
							}
						}
						//去除partACode(公司中的'.')
						contract.setPartACode(contract.getPartACode().split("\\.")[0]);
						// 保存合同信息
						wc = new WhereCondition();
						wc.andEquals("contractId", contract.getContractId());
						List<Contract> contracts = contractService.query(wc);
						if (CollectionUtils.isNotEmpty(contracts)) {
							Contract oldContract = contracts.get(0);
							contractService.insertHis(oldContract);
							double oldUnitPrice = oldContract.getAmountWithoutVAT()/oldContract.getAmount();
							double newUnitPrice = contract.getAmountWithoutVAT()/contract.getAmount();
							if(oldUnitPrice-newUnitPrice!=0){
								contract.setIsUnitPriceChange(Enums.TRUE_STRING);
								contract.setDiffUnitPrice(oldUnitPrice-newUnitPrice);
							}
							contract.setId(oldContract.getId());
							contract.setLastModifier_id("Salesforce");
							contract.setLastModifiedTime(LanDateUtils.getNowDate());
							//contractService.update(contract);
							
							
							//Chris
							//更新学生耗课合同 when contract type not null
							if (contract.getContractType2() != null) {
								double cons = consumption_student_contractService.updateByContract(contract.getId(), contract.getStudentId(), contract.getAmount()*2, contract.getContractType2());
								contract.setConsumedClass(cons);
								if(contract.getAmount()*2>contract.getConsumedClass()){
									contract.setStatus(ContractStatus.STAUS_2);
								}else{
									contract.setStatus(ContractStatus.STAUS_3);
								}
							}

							contractService.update(contract);


							


						} else {
							contract.setId(sequenceManager.generateId("contract"));
							contract.setDelStatus(Enums.FALSE_STRING);
							//已消耗课时
							if(contract.getConsumedClass()==null){
								contract.setConsumedClass(0D);
							}
							contract.setStatus(ContractStatus.STAUS_1);
							contract.setCreator_id("Salesforce");
							contract.setCreateTime(new Date());
							//是否为首份合同
							wc = new WhereCondition();
							wc.andEquals("studentId", student.getId());
							wc.andIsNull("persentType");
							List<Contract> oldContracts = contractService.query(wc);
							if(CollectionUtils.isEmpty(oldContracts)){
								contract.setIsFirst(Enums.TRUE_STRING);
							}else{
								contract.setIsFirst(Enums.FALSE_STRING);
							}
							contractService.insert(contract);
							//更新学生耗课合同 when contract type not null
							if (contract.getContractType2() != null) {
								double cons = consumption_student_contractService.updateByContract(contract.getId(), contract.getStudentId(), contract.getAmount()*2, contract.getContractType2());
								contract.setConsumedClass(cons);
								if(contract.getAmount()*2>contract.getConsumedClass()){
									contract.setStatus(ContractStatus.STAUS_2);
								}else{
									contract.setStatus(ContractStatus.STAUS_3);
								}
								contractService.update(contract);
							}

						}
						msg.setSuccess(true);
						msg.setErrorMsg(StringUtils.EMPTY);
						msg.setCrmId(contract.getContractId());
					} catch (Exception e) {
						e.printStackTrace();
						arg0.setRollbackOnly();
						msg.setSuccess(false);
						msg.setErrorMsg(e.getClass().getName()+": "+e.getCause().getMessage());
						msg.setCrmId(contract.getContractId());
					}
				}
			});

			//更新上课状态
			student_zoneService.updateClassStatusById(student.getId());
			returnMessages.add(msg);
		}
		long endTime = System.currentTimeMillis();
		System.out.println("花费时间："+(endTime-startTime));
		return new Gson().toJson(returnMessages);
	}

	/**
	 * 判断SF传递的合同的单元数是否小于TE的合同数
	 * @param sfContract
	 */
	private boolean sfContractByAmout(Contract sfContract) {
		//获取TE的合同信息
		Contract contract = contractService.getContractByContractId(sfContract.getContractId());

		Double amount = getAmount(sfContract.getProductName(), sfContract.getUnits());
		if(contract == null){
			return false;
		}
		if(amount*2 < contract.getConsumedClass()){
			//合同使用了，发送邮件给相关人员
			String conttext = "";
			conttext += "\n" +
					"\t你好!\n" +
					"\n" +
					"\tSalesforce中合同"+sfContract.getContractId()+", ContractNo:"+sfContract.getContractNo()+"已变更，但该合同变更后单元数小于TE系统中已消耗单元数，无法同步更新。请在TE系统中处理相关耗课后再同步该合同。\n" +
					"\t\n" +
					"\tTEsystem" ;
			String teContractMail = paiKeGuwenInfo.getTeContractMail();
			String[] mails = teContractMail.split(";");
			String name = null;
			for (String mail:mails) {
				name ="";
				name  = mail.substring(0, mail.indexOf("@"));
				lanmoMailSender.sendMassageDefault(mail,null,null,"Dear "+name+conttext,"SF合同单元数小于TE合同已消耗单元数通知");
			}
			System.out.println("SF合同单元数小于TE合同单元数通知"+sfContract.getContractId());
			return true;

		}

		return false;

	}

	/**
	 * 根据从SF传递过来已删除合同ID ,删除TE合同
	 * @param ContractId
	 */
	private void deleteSFAndDeleteTEContract(String ContractId) {

		//获取合同信息
		Contract contract = contractService.getContractByContractId(ContractId);
		if(contract == null){
			return;
		}

		//判断合同是否使用
		if(contract.getAmount()*2 == contract.getConsumedClass()){
			//合同没有使用，直接删除
			contractService.delete(contract.getId());
			System.out.println("因为SF合同删除，TE合同同步删除，合同号id："+ContractId);
			return;
		}

		//合同使用了，发送邮件给相关人员
		String conttext = "";
		conttext += "你好，\n" +
				"\n" +
				"\tSalesforce中合同"+ContractId+", ContractNo: "+contract.getContractNo()+"被删除，但该合同在TE系统中已经被消耗，无法删除。请在TE系统中处理相关耗课后再删除合同。\n" +
				"\t\n" +
				"\n" +
				"\tTESystem";
		String teContractMail = paiKeGuwenInfo.getTeContractMail();
		String[] mails = teContractMail.split(";");
		String name = null;
		for (String mail:mails) {
			name ="";
			name  = mail.substring(0, mail.indexOf("@"));
			lanmoMailSender.sendMassageDefault(mail,null,null,"Dear "+name+conttext,"SF合同删除，TE合同未删除通知");
		}
	}


	public String vilidateContract(JsonObject jsonObject){
		//contractId
//		if(jsonObject.get("contractId")==null||StringUtils.isEmpty(jsonObject.get("contractId").getAsString())){
//			return "合同Id为空！";
//		}
		//accountId
		if(jsonObject.get("accountId")==null||jsonObject.get("accountId").isJsonNull()){
			return "crm学生的记录Id为空！";
		}
		//合同开始日期
		if(jsonObject.get("startDate")==null||jsonObject.get("startDate").isJsonNull()){
			return "合同开始时间为空！";
		}
		//合同结束日期
		if(jsonObject.get("endDate")==null||jsonObject.get("endDate").isJsonNull()){
			return "合同结束日期为空！";
		}
		//产品ID
		if(jsonObject.get("productId")==null||jsonObject.get("productId").isJsonNull()){
			return "产品Id为空！";
		}
		//产品名称
		if(jsonObject.get("productName")==null||jsonObject.get("productName").isJsonNull()){
			return "产品名称为空！";
		}
		//单元
		if(jsonObject.get("units")==null||jsonObject.get("units").isJsonNull()){
			return "单元为空！";
		}
		//学费
		if(jsonObject.get("tuitionFee")==null||jsonObject.get("tuitionFee").isJsonNull()){
			return "学费为空！";
		}
		//学费
		if(jsonObject.get("amountWithoutVAT")==null||jsonObject.get("amountWithoutVAT").isJsonNull()){
			return "不含税学费为空！";
		}
		//公司
		if(jsonObject.get("partACode")==null||jsonObject.get("partACode").isJsonNull()){
			return "公司为空！";
		}
		return null;
		
	}
	public static Double getDoubleValue(String value){
		Double doubleValue;
		if(StringUtils.isEmpty(value)){
			return null;
		}
		if(!isNumber(value)){
			return null;
		}else{
			doubleValue  = Double.valueOf(value);
		}
		return doubleValue;
	}
	public static Integer getIntegerValue(String value){
		Integer intValue;
		if(StringUtils.isEmpty(value)){
			return null;
		}
		if(!isNumber(value)){
			return null;
		}else{
			intValue  = Double.valueOf(value).intValue();
		}
		return intValue;
	}
	public static boolean isNumber(String s){
		if(StringUtils.isEmpty(s)) return false;
		String regex = "^[0-9][0-9]*\\.[0-9]+$|^[1-9][0-9]*$|^0+\\.[0-9]+$|\\d+$";
		Pattern pattern = Pattern.compile(regex);
		char c = s.charAt(0);  
        if(c=='+'||c=='-'){  
            s = s.substring(1); 
        }
        Matcher matcher = pattern.matcher(s);
        return matcher.matches();
	}
	public static Double getAmount(String productName, Double units){
		if(StringUtils.isEmpty(productName)){
			return null;
		}else{
			String amountStr =StringUtils.EMPTY;
			if(productName.split(Enums.SPACE)!=null && productName.split(" ").length>0){
				amountStr = productName.split(" ")[0];
			}
			if(!isNumber(amountStr)){
				return units;
			}else{
				return getDoubleValue(amountStr)*units;
			}
		}
	}
	public static void main(String[] args) {
		System.out.println(getDoubleValue(""));
	}
}
