package com.lanmosoft.web;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;

@WebService(targetNamespace ="http://lanmosoft.com/services")
@SOAPBinding(style = Style.DOCUMENT)
public interface IStudentService {
	public static final String tokenStudent = "ws-saveStudent";
	public static final String tokenContract = "ws-saveContrctAndStudent";
	@WebMethod(operationName="saveStudent")
	@WebResult(name="message")
    public String saveStudent(@WebParam(name="studentInfo") String data);
	
	@WebMethod(operationName="saveContrctAndStudent")
	@WebResult(name="message")
    public String saveContract(@WebParam(name="contrctAndStudentInfo") String data);
}