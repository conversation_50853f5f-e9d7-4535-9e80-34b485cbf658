package com.lanmosoft.res;

import com.lanmosoft.enums.Enums.ModuleName;
import com.lanmosoft.model.Function;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

public class ResManager {
	private static List<Function> resList = new ArrayList<Function>();
	
	static{
		//首页提醒
		resList.add(new Function("001", "学员请假未审核提醒", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("002", "教师请假未审核提醒", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("003", "待续费学员提醒", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("004", "学员跟进提醒", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("005", "异常签到提醒", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("006", "退费未审核提醒", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("007", "课时费（首页）新增", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("008", "online课时费", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("009", "offline课时费", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		resList.add(new Function("010", "当天未确认排课", "complex", ModuleName.F_HOME, ModuleName.F_HOME, "menu"));
		//1.学生管理
		//1.1学生列表
		resList.add(new Function("101", "列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LIST, "menu"));
		resList.add(new Function("102", "详情", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LIST , "button"));
//		resList.add(new Function("103", "新增", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LIST, "button"));
		resList.add(new Function("104", "编辑", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LIST, "button"));
//		resList.add(new Function("105", "删除", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LIST, "button"));
		resList.add(new Function("131", "诉求列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LIST, "button"));
		resList.add(new Function("132", "诉求编辑", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LIST, "button"));
		//1.2学员跟进
		resList.add(new Function("106", "全部列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP, "menu"));
		resList.add(new Function("107", "详情", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP , "button"));
		resList.add(new Function("108", "新增", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP, "button"));
		resList.add(new Function("109", "编辑", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP, "button"));
		resList.add(new Function("110", "删除", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP, "button"));
		resList.add(new Function("133", "助教列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP, "menu"));
		//1.3学员退费
		resList.add(new Function("111", "列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_RETURNS, "menu"));
//		resList.add(new Function("112", "详情", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP , "button"));
		resList.add(new Function("113", "审批1", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_RETURNS, "button"));
		resList.add(new Function("114", "审批2", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_RETURNS, "button"));
		
		//1.4跟进提醒
		resList.add(new Function("116", "列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUPTASK, "menu"));
//		resList.add(new Function("117", "详情", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_FOLLOWUP , "button"));

		//1.5学员请假
		resList.add(new Function("121", "列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LEAVE, "menu"));
		resList.add(new Function("122", "审核", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LEAVE, "button"));
//		resList.add(new Function("123", "审核不通过", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LEAVE, "button"));
		resList.add(new Function("124", "详情", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_LEAVE, "button"));

		//1.6学员诉求
		resList.add(new Function("141", "列表", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_SUQIU, "menu"));
		resList.add(new Function("142", "新增", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_SUQIU, "button"));
		resList.add(new Function("143", "编辑", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_SUQIU, "button"));
		resList.add(new Function("144", "详情", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_SUQIU, "button"));
		resList.add(new Function("145", "删除", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_SUQIU, "button"));

		//1.7是否允许超课时排课
		resList.add(new Function("151", "是否允许超课时排课", "simple", ModuleName.F_STUDENT, ModuleName.S_STUDENT_SUQIU, "button"));

		//1.8学员报告
		resList.add(new Function("161", "学员报告", "complex", ModuleName.F_STUDENT, ModuleName.S_STUDENT_REPORT, "menu"));
		//2.教师管理
		//2.1教师列表
		resList.add(new Function("201", "列表", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "menu"));
		resList.add(new Function("202", "详情", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("203", "新增", "simple", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("204", "编辑", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("205", "删除", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("210", "可教授科目列表", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("211", "可教授科目编辑", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("212", "课时费编辑", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("213", "课时费查看（列表）", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		//resList.add(new Function("214", "Hourly Rate Scheme", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("215", "可排课时间查看", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));
		resList.add(new Function("216", "可排课时间编辑", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LIST, "button"));


		//2.2教师请假
		resList.add(new Function("208", "列表", "complex", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LEAVE, "menu"));
		resList.add(new Function("206", "审核", "simple", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LEAVE, "button"));
		resList.add(new Function("207", "详情", "simple", ModuleName.F_TEACHER, ModuleName.S_TEACHER_LEAVE, "button"));
		
		//3.合同管理
		//3.1合同列表
		resList.add(new Function("301", "列表", "complex", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST, "menu"));
		resList.add(new Function("302", "详情", "complex", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST , "button"));
//		resList.add(new Function("303", "新增", "simple", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST, "button"));
//		resList.add(new Function("304", "编辑", "complex", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST, "button"));
//		resList.add(new Function("305", "删除", "complex", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST, "button"));
		resList.add(new Function("306", "退费", "simple", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST, "button"));
		resList.add(new Function("307", "类型", "simple", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST, "button"));
		resList.add(new Function("308", "修改单元", "simple", ModuleName.F_CONTRACT, ModuleName.S_CONTRACT_LIST, "button"));
		
		//4.排课管理
		//4.1已排课列表
		resList.add(new Function("401", "列表", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "menu"));
		resList.add(new Function("402", "详情", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST , "button"));
		resList.add(new Function("403", "自动排课", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
//		resList.add(new Function("404", "编辑", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("405", "删除", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401a", "确认", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401b", "恢复", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401c", "学员请假", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401f", "教师请假", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401d", "学员取消请假", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401g", "教师取消请假", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401h", "取消已审核教师请假", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		resList.add(new Function("401i", "取消已审核学员请假", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
//		resList.add(new Function("401e", "审核", "simple", ModuleName.F_PAIKE, ModuleName.S_PAIKE_LIST, "button"));
		//4.2学生排课
		resList.add(new Function("406", "学生排课", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_MANUAL, "menu"));
		//4.3老师排课
		resList.add(new Function("407", "老师排课", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_MANUAL, "menu"));
		resList.add(new Function("408", "Hybrid排课", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_MANUAL, "menu"));
		resList.add(new Function("409", "小班排课", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_MANUAL, "menu"));
		resList.add(new Function("410", "小班管理", "complex", ModuleName.F_PAIKE, ModuleName.S_PAIKE_MANUAL, "menu"));
		//5.签到管理
		//5.1今日签到
		resList.add(new Function("501", "列表", "complex", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_TODAY, "menu"));
		resList.add(new Function("502", "签到", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_TODAY , "button"));
		resList.add(new Function("503", "异常签到", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_TODAY, "button"));
		resList.add(new Function("503a", "特殊情况（异常签到-手工处理课时、工资）", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_TODAY, "button"));
		resList.add(new Function("504", "审批", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_TODAY, "button"));
		resList.add(new Function("505", "取消签到", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_TODAY, "button"));
		resList.add(new Function("505a", "取消已审核签到", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_TODAY, "button"));
		//5.2签到列表
		resList.add(new Function("506", "签到列表", "complex", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_HISTORY, "menu"));
		resList.add(new Function("507", "同步ERP", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_HISTORY, "button"));
		resList.add(new Function("508", "查看异常签到", "simple", ModuleName.F_ATTENDANCEBOOK, ModuleName.S_ATTENDANCEBOOK_HISTORY, "button"));
		//6.基础数据
		//6.1基础数据
		resList.add(new Function("601", "列表", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_DATA, "menu"));
//		resList.add(new Function("602", "详情", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_DATA , "button"));
		resList.add(new Function("603", "新增", "simple", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_DATA, "button"));
		resList.add(new Function("604", "编辑", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_DATA, "button"));
		resList.add(new Function("605", "删除", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_DATA, "button"));
		//6.2产品管理
		resList.add(new Function("606", "列表", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_PRODUCT, "menu"));
//		resList.add(new Function("607", "详情", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_PRODUCT , "button"));
		resList.add(new Function("608", "新增", "simple", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_PRODUCT, "button"));
		resList.add(new Function("609", "编辑", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_PRODUCT, "button"));
		resList.add(new Function("610", "删除", "complex", ModuleName.F_JICHUSHUJU, ModuleName.S_JICHUSHUJU_PRODUCT, "button"));
		
		//7.系统设置
		//7.1组织结构
		resList.add(new Function("711", "列表", "complex", ModuleName.F_SETTING, ModuleName.S_SETTING_ORG, "menu"));
		resList.add(new Function("712", "详情", "complex", ModuleName.F_SETTING, ModuleName.S_SETTING_ORG , "button"));
		resList.add(new Function("713", "新增", "simple", ModuleName.F_SETTING, ModuleName.S_SETTING_ORG, "button"));
		resList.add(new Function("714", "编辑", "complex", ModuleName.F_SETTING, ModuleName.S_SETTING_ORG, "button"));
		resList.add(new Function("715", "删除", "complex", ModuleName.F_SETTING, ModuleName.S_SETTING_ORG, "button"));
		//7.2权限管理
		resList.add(new Function("716", "列表", "complex", ModuleName.F_SETTING, ModuleName.S_SETTING_PERMISSION, "menu"));
		resList.add(new Function("717", "授权", "simple", ModuleName.F_SETTING, ModuleName.S_SETTING_PERMISSION, "button"));
		
		//8.统计报表
		resList.add(new Function("801", "总课表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_ZONGKEBIAO, "menu"));
//		resList.add(new Function("802", "学员剩余课时", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_SHENGYUKESHI, "menu"));
		resList.add(new Function("803", "TE老师工资", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_LAOSHIGONGZI, "menu"));
		resList.add(new Function("804", "待续费学员", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_DAIXUFEIXUEYUAN, "menu"));
		resList.add(new Function("805", "绩效-续报", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_XUBAO, "menu"));
		resList.add(new Function("806", "绩效-转介绍", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_ZHUANJIESHAO, "menu"));
		resList.add(new Function("807", "TE-课时统计", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_KESHI, "menu"));
		resList.add(new Function("808", "服务流程监控", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_FUWULIUCHENG, "menu"));
		resList.add(new Function("809", "自定义报表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_ZIDINGYI, "menu"));
		resList.add(new Function("810", "当月排课Tutor", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_DANGYUEPAIKE, "menu"));
		resList.add(new Function("811", "EM绩效", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_JIXIAO, "menu"));
		resList.add(new Function("812", "各办公室新招生学员情况", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_XINZHAO, "menu"));
		resList.add(new Function("813", "排耗课报表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_KESHISHENGYU, "menu"));
		resList.add(new Function("814", "工资汇总", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_HUIZONG, "menu"));
		resList.add(new Function("815", "结转课时报表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_JIEZHUN, "menu"));
		resList.add(new Function("816", "工资明细", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_MINGXI, "menu"));
		resList.add(new Function("817", "工资明细offline", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_MINGXI, "menu"));
		resList.add(new Function("818", "工资明细online", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_MINGXI, "menu"));
		resList.add(new Function("819", "导出老师课表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_MINGXI, "menu"));
		resList.add(new Function("808a", "学员上课报表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_MINGXI, "menu"));
		resList.add(new Function("808b", "教师上课报表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_MINGXI, "menu"));
		resList.add(new Function("808c", "学员排耗课报表报表", "simple", ModuleName.F_BAOBIAO, ModuleName.S_BAOBIAO_MINGXI, "menu"));

		//9.教师课表
		resList.add(new Function("901", "查看课表", "simple", ModuleName.F_JIAOSHIKEBIAO, ModuleName.S_JIAOSHIKEBIAO_CHAKANKEBIAO, "menu"));
		
	}
	public static List<Function> getAllRes(){
		 List<Function> dest=new ArrayList<Function>();
		for(Function z:resList){
			Function zt =new Function();
			try {
				BeanUtils.copyProperties(zt, z);
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
			dest.add(zt);
		}
		return  dest;
	}
	public static Function getFunction(String id){
		for(Function function:resList){
			if(StringUtils.equals(id, function.getId())){
				return function;
			}
		}
		return null;
	}
}
