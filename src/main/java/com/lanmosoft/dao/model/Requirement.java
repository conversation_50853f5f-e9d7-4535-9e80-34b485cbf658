package com.lanmosoft.dao.model;
import java.util.Date;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;
/**
 * requirement
 * <AUTHOR>
 *
 */
public class Requirement {
	 private String id;//id
	 private String student_id;//student_id
	 private Date startDate;//startDate
	 private Date endDate;//endDate
	 private String course_id;//course_id
	 private Double totalLowerLimit;//totalLowerLimit
	 private Double totalUpperLimit;//totalUpperLimit
	 private Double dayLowerLimit;//dayLowerLimit
	 private Double dayUpperLimit;//dayUpperLimit
	 private Double weekLowerLimit;//weekLowerLimit
	 private Double weekUpperLimit;//weekUpperLimit
	 private Double monthLowerLimit;//monthLowerLimit
	 private Double monthUpperLimit;//monthUpperLimit
	 private Integer intervalDay;//intervalDay
	 private String studentName;//
	 private String courseName;//
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
	 public void setStartDate(Date startDate) {
          this.startDate = startDate ;
    }
	 public Date getStartDate(){
        return startDate;
    }
	 public void setEndDate(Date endDate) {
          this.endDate = endDate ;
    }
	 public Date getEndDate(){
        return endDate;
    }
	public void setCourse_id(String course_id) {
        this.course_id = course_id == null ? null : course_id.trim();
    }
	 public String getCourse_id(){
        return course_id;
    }
	 public void setTotalLowerLimit(Double totalLowerLimit) {
          this.totalLowerLimit = totalLowerLimit ;
    }
	 public Double getTotalLowerLimit(){
        return totalLowerLimit;
    }
	 public void setTotalUpperLimit(Double totalUpperLimit) {
          this.totalUpperLimit = totalUpperLimit ;
    }
	 public Double getTotalUpperLimit(){
        return totalUpperLimit;
    }
	 public void setDayLowerLimit(Double dayLowerLimit) {
          this.dayLowerLimit = dayLowerLimit ;
    }
	 public Double getDayLowerLimit(){
        return dayLowerLimit;
    }
	 public void setDayUpperLimit(Double dayUpperLimit) {
          this.dayUpperLimit = dayUpperLimit ;
    }
	 public Double getDayUpperLimit(){
        return dayUpperLimit;
    }
	 public void setWeekLowerLimit(Double weekLowerLimit) {
          this.weekLowerLimit = weekLowerLimit ;
    }
	 public Double getWeekLowerLimit(){
        return weekLowerLimit;
    }
	 public void setWeekUpperLimit(Double weekUpperLimit) {
          this.weekUpperLimit = weekUpperLimit ;
    }
	 public Double getWeekUpperLimit(){
        return weekUpperLimit;
    }
	 public void setMonthLowerLimit(Double monthLowerLimit) {
          this.monthLowerLimit = monthLowerLimit ;
    }
	 public Double getMonthLowerLimit(){
        return monthLowerLimit;
    }
	 public void setMonthUpperLimit(Double monthUpperLimit) {
          this.monthUpperLimit = monthUpperLimit ;
    }
	 public Double getMonthUpperLimit(){
        return monthUpperLimit;
    }
	 public void setIntervalDay(Integer intervalDay) {
          this.intervalDay = intervalDay ;
    }
	 public Integer getIntervalDay(){
        return intervalDay;
    }
	public String getStudentName() {
		return studentName;
	}
	public void setStudentName(String studentName) {
		this.studentName = studentName;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
}