package com.lanmosoft.dao.model;

import java.util.List;

/**
 * 查询方案
 */
public class QueryScheme {
    private String id;
    private String querySchemeName;//方案名称
    private String userId ;//用户
    private String querySchemeHtml;//给一个识标符(证明属于那一个HTML)
    private String teacherNames;  //老师姓名
    private String studentName;  //学生姓名
    private String teacherIds; //老师id集合
    private String studentIds; //学生id集合
    private String querySchemeDay; //日期
    private String cityId; //城市
    private String zoneId; //校区
    private String classroom; //教室
    private String course; //课程
    private String starDate;//开始时间
    private String endDate;//结束时间
    private String payDate;//付款时间


    public String getStarDate() {
        return starDate;
    }

    public void setStarDate(String starDate) {
        this.starDate = starDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getQuerySchemeName() {
        return querySchemeName;
    }

    public void setQuerySchemeName(String querySchemeName) {
        this.querySchemeName = querySchemeName;
    }

    public String getQuerySchemeHtml() {
        return querySchemeHtml;
    }

    public void setQuerySchemeHtml(String querySchemeHtml) {
        this.querySchemeHtml = querySchemeHtml;
    }

    public String getTeacherNames() {
        return teacherNames;
    }

    public void setTeacherNames(String teacherNames) {
        this.teacherNames = teacherNames;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getTeacherIds() {
        return teacherIds;
    }

    public void setTeacherIds(String teacherIds) {
        this.teacherIds = teacherIds;
    }

    public String getStudentIds() {
        return studentIds;
    }

    public void setStudentIds(String studentIds) {
        this.studentIds = studentIds;
    }

    public String getQuerySchemeDay() {
        return querySchemeDay;
    }

    public void setQuerySchemeDay(String querySchemeDay) {
        this.querySchemeDay = querySchemeDay;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getZoneId() {
        return zoneId;
    }

    public void setZoneId(String zoneId) {
        this.zoneId = zoneId;
    }

    public String getClassroom() {
        return classroom;
    }

    public void setClassroom(String classroom) {
        this.classroom = classroom;
    }

    public String getCourse() {
        return course;
    }

    public void setCourse(String course) {
        this.course = course;
    }
}
