package com.lanmosoft.dao.model;

import java.util.Date;

public class Timezone {
	private String id;
	private String timezone;
	private String region;
	private String displayName;
	private String creator_id;//creator_id
	 private Date createTime;//createTime
	 private String lastModifier_id;//lastModifier_id
	 private Date lastModifiedTime;//lastModifiedTime
	 private String delStatus;//delStatus
	 private String fullName;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id== null ? null : id.trim();
	}
	
	public String getTimezone() {
		return timezone;
	}
	public void setTimezone(String timezone) {
		this.timezone = timezone== null ? null :timezone.trim();
	}
	public String getRegion() {
		return region;
	}
	public void setRegion(String region) {
		this.region = region== null ? null : region.trim();
	}
	public String getDisplayName() {
		return displayName;
	}
	public void setDisplayName(String displayName) {
		this.displayName = displayName== null ? null : displayName.trim();
	}
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	public String getFullName() {
		return fullName;
	}
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	
	
}
