package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 教师可教授科目View
 * <AUTHOR>
 *
 */
public class View_teacher_course implements Comparable<View_teacher_course> {
	 private String id;//id
	 private String teacher_id;//教师ID
	 private String chineseName;//chineseName
	 private String englishName;//englishName
	 private String course_id;//课程ID
	 private String course_name;//课程name
	 private Integer periodLowerLimit;//课时数下限
	 private Integer periodUpperLimit;//课时数上限
	 private String intervalType;//课时累计间隔
	 private String gradeCategory_id;//gradeCategory_id
	 private String gradeCategory_name;//gradeCategory_name
	 private String grade_id;//可教授级别
	 private String level;//level
	 private String grade_name;//可教授级别name
	 private String teachingType;//授课类型
	 private Date startDate;//生效开始时间
	 private Date endDate;//生效结束时间
	 private Double unitPrice;//课时单价
	 private String BUType;
	 private String schemeNo;
	 private String zone_id;//校区
	 
	 private Double activeRate;//active率
	 private Double matchingRate;//匹配率
	 private Integer activeStudent;//老师所带active学生数
	 private String isActivated;//是否生效
	 private String status;//状态 1-在职，2-离职 3-冻结
	 private String scheme_id;

	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }
	 public String getChineseName(){
        return chineseName;
    }
	public void setEnglishName(String englishName) {
        this.englishName = englishName == null ? null : englishName.trim();
    }
	 public String getEnglishName(){
        return englishName;
    }
	public void setCourse_id(String course_id) {
        this.course_id = course_id == null ? null : course_id.trim();
    }
	 public String getCourse_id(){
        return course_id;
    }
	public void setCourse_name(String course_name) {
        this.course_name = course_name == null ? null : course_name.trim();
    }
	 public String getCourse_name(){
        return course_name;
    }
	 public void setPeriodLowerLimit(Integer periodLowerLimit) {
          this.periodLowerLimit = periodLowerLimit ;
    }
	 public Integer getPeriodLowerLimit(){
        return periodLowerLimit;
    }
	 public void setPeriodUpperLimit(Integer periodUpperLimit) {
          this.periodUpperLimit = periodUpperLimit ;
    }
	 public Integer getPeriodUpperLimit(){
        return periodUpperLimit;
    }
	public void setIntervalType(String intervalType) {
        this.intervalType = intervalType == null ? null : intervalType.trim();
    }
	 public String getIntervalType(){
        return intervalType;
    }
	public void setGradeCategory_id(String gradeCategory_id) {
        this.gradeCategory_id = gradeCategory_id == null ? null : gradeCategory_id.trim();
    }
	 public String getGradeCategory_id(){
        return gradeCategory_id;
    }
	public void setGradeCategory_name(String gradeCategory_name) {
        this.gradeCategory_name = gradeCategory_name == null ? null : gradeCategory_name.trim();
    }
	 public String getGradeCategory_name(){
        return gradeCategory_name;
    }
	public void setGrade_id(String grade_id) {
        this.grade_id = grade_id == null ? null : grade_id.trim();
    }
	 public String getGrade_id(){
        return grade_id;
    }
	public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }
	 public String getLevel(){
        return level;
    }
	public void setGrade_name(String grade_name) {
        this.grade_name = grade_name == null ? null : grade_name.trim();
    }
	 public String getGrade_name(){
        return grade_name;
    }
	public void setTeachingType(String teachingType) {
        this.teachingType = teachingType == null ? null : teachingType.trim();
    }
	 public String getTeachingType(){
        return teachingType;
    }
	 public void setStartDate(Date startDate) {
          this.startDate = startDate ;
    }
	 public Date getStartDate(){
        return startDate;
    }
	 public void setEndDate(Date endDate) {
          this.endDate = endDate ;
    }
	 public Date getEndDate(){
        return endDate;
    }
	 public void setUnitPrice(Double unitPrice) {
          this.unitPrice = unitPrice ;
    }
	 public Double getUnitPrice(){
        return unitPrice;
    }
	public Double getActiveRate() {
		return activeRate;
	}
	public void setActiveRate(Double activeRate) {
		this.activeRate = activeRate;
	}
	public Double getMatchingRate() {
		return matchingRate;
	}
	public void setMatchingRate(Double matchingRate) {
		this.matchingRate = matchingRate;
	}
	public Integer getActiveStudent() {
		return activeStudent;
	}
	public void setActiveStudent(Integer activeStudent) {
		this.activeStudent = activeStudent;
	}
	public String getZone_id() {
		return zone_id;
	}
	public void setZone_id(String zone_id) {
		this.zone_id = zone_id;
	}
	public String getIsActivated() {
		return isActivated;
	}
	public void setIsActivated(String isActivated) {
		this.isActivated = isActivated;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getBUType() {
		return BUType;
	}
	public void setBUType(String BUType) {
		this.BUType = BUType;
	}
	public String getSchemeNo() {
		return schemeNo;
	}
	public void setSchemeNo(String schemeNo) {
		this.schemeNo = schemeNo;
	}
	public String getScheme_id() {
		return scheme_id;
	}
	public void setScheme_id(String scheme_id) {
		this.scheme_id = scheme_id;
	}
	@Override
	public int compareTo(View_teacher_course o1) {
		if(o1.getActiveRate()!=this.getActiveRate()){
			return this.getActiveRate().compareTo(o1.getActiveRate());
		}
		if(o1.getActiveRate()==this.getActiveRate()){
			if(o1.getMatchingRate()!=this.getMatchingRate()){
				return this.getMatchingRate().compareTo(o1.getMatchingRate())*(-1);
			}
			if(o1.getMatchingRate()==this.getMatchingRate()){
				return this.getActiveStudent().compareTo(o1.getActiveStudent());
			}
		}
		return 0;
	}
}