package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 组织
 * <AUTHOR>
 *
 */
public class Org {
	 private String id;//ID
	 private String name;//名称
	 private String department_id;//所属部门id
	 private String department_name;//所属部门名称
	 private String position_id;//所属岗位id
	 private String position_name;//所属岗位名称
	 private String description;//描述
	 private String org_type;//组织类型
	 private String superOrg_id;//顶级组织id
	 private String superOrg_name;//顶级组织名称
	 private String fulldept_id;//全部门路径id
	 private String fulldept_name;//全部门路径名
	 private String fullpost_id;//全岗位路径id
	 private String fullpost_name;//全岗位路径名
	 private Integer sortNum;//排序
	 private String creator_id;//创建者
	 private Date createTime;//创建时间
	 private String lastModifier_id;//最后修改者
	 private Date lastModifiedTime;//最后修改时间
	 private String delStatus;//是否禁用
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }
	 public String getName(){
        return name;
    }
	public void setDepartment_id(String department_id) {
        this.department_id = department_id == null ? null : department_id.trim();
    }
	 public String getDepartment_id(){
        return department_id;
    }
	public void setDepartment_name(String department_name) {
        this.department_name = department_name == null ? null : department_name.trim();
    }
	 public String getDepartment_name(){
        return department_name;
    }
	public void setPosition_id(String position_id) {
        this.position_id = position_id == null ? null : position_id.trim();
    }
	 public String getPosition_id(){
        return position_id;
    }
	public void setPosition_name(String position_name) {
        this.position_name = position_name == null ? null : position_name.trim();
    }
	 public String getPosition_name(){
        return position_name;
    }
	public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }
	 public String getDescription(){
        return description;
    }
	public void setOrg_type(String org_type) {
        this.org_type = org_type == null ? null : org_type.trim();
    }
	 public String getOrg_type(){
        return org_type;
    }
	public void setSuperOrg_id(String superOrg_id) {
        this.superOrg_id = superOrg_id == null ? null : superOrg_id.trim();
    }
	 public String getSuperOrg_id(){
        return superOrg_id;
    }
	public void setSuperOrg_name(String superOrg_name) {
        this.superOrg_name = superOrg_name == null ? null : superOrg_name.trim();
    }
	 public String getSuperOrg_name(){
        return superOrg_name;
    }
	public void setFulldept_id(String fulldept_id) {
        this.fulldept_id = fulldept_id == null ? null : fulldept_id.trim();
    }
	 public String getFulldept_id(){
        return fulldept_id;
    }
	public void setFulldept_name(String fulldept_name) {
        this.fulldept_name = fulldept_name == null ? null : fulldept_name.trim();
    }
	 public String getFulldept_name(){
        return fulldept_name;
    }
	public void setFullpost_id(String fullpost_id) {
        this.fullpost_id = fullpost_id == null ? null : fullpost_id.trim();
    }
	 public String getFullpost_id(){
        return fullpost_id;
    }
	public void setFullpost_name(String fullpost_name) {
        this.fullpost_name = fullpost_name == null ? null : fullpost_name.trim();
    }
	 public String getFullpost_name(){
        return fullpost_name;
    }
	 public void setSortNum(Integer sortNum) {
          this.sortNum = sortNum ;
    }
	 public Integer getSortNum(){
        return sortNum;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
}