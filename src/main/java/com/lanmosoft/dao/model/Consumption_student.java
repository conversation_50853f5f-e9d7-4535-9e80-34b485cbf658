package com.lanmosoft.dao.model;
import java.util.Date;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;
/**
 * 学生耗课信息表
 * <AUTHOR>
 *
 */
public class Consumption_student {
	 private String id;//ID
	 private String student_id;//学生
	 private String classSchedule_id;//课表
	 private Double consumedClass;//消耗课时
	 private Double givenClass;//赠送课时
	 private String lockStatus;//锁定状态
	 
	// private String flag;// 初始化标记字段，null为未初始化，否则是已初始化
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
	public void setClassSchedule_id(String classSchedule_id) {
        this.classSchedule_id = classSchedule_id == null ? null : classSchedule_id.trim();
    }
	 public String getClassSchedule_id(){
        return classSchedule_id;
    }
	 public void setConsumedClass(Double consumedClass) {
          this.consumedClass = consumedClass ;
    }
	 public Double getConsumedClass(){
        return consumedClass;
    }
	 public void setGivenClass(Double givenClass) {
          this.givenClass = givenClass ;
    }
	 public Double getGivenClass(){
        return givenClass;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
//	public String getFlag() {
//		return flag;
//	}
//	public void setFlag(String flag) {
//		this.flag = flag;
//	}
}