package com.lanmosoft.dao.model;

import java.util.Date;
/**
 * 操作日志
 * <AUTHOR>
 *
 */
public class OperationLog {
	
	private String id;//主键ID
	private String user_id;//操作人Id
	private String content;//操作内容
	private Date createTime;//创建时间
	//==不关联数据库
	private String caozuoren;//操作人名称
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getUser_id() {
		return user_id;
	}
	public void setUser_id(String user_id) {
		this.user_id = user_id;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getCaozuoren() {
		return caozuoren;
	}
	public void setCaozuoren(String caozuoren) {
		this.caozuoren = caozuoren;
	}

}
