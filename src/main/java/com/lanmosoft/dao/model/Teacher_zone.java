package com.lanmosoft.dao.model;
import java.util.Date;
import java.util.List;
/**
 * 教师可授课校区
 * <AUTHOR>
 *
 */
public class Teacher_zone {
	 private String id;//ID
	 private String teacher_id;//教师ID
	 private String city_id;//城市ID
	 private String zone_id;//校区ID
	 private Date startDate;//开始日期
	 private Date endDate;//结束日期
	 
	 private String sms;

	 private String email;

	 private String emailTemplate_id;

	 private String mainzone;

	 private Date mainstart;

	 private Date mainend;

	 private String notes;
	 private String startDateStr;
	 private String endDateStr;

	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setCity_id(String city_id) {
        this.city_id = city_id == null ? null : city_id.trim();
    }
	 public String getCity_id(){
        return city_id;
    }
	public void setZone_id(String zone_id) {
        this.zone_id = zone_id == null ? null : zone_id.trim();
    }
	 public String getZone_id(){
        return zone_id;
    }
	 public void setStartDate(Date startDate) {
          this.startDate = startDate ;
    }
	 public Date getStartDate(){
        return startDate;
    }
	 public void setEndDate(Date endDate) {
          this.endDate = endDate ;
    }
	 public Date getEndDate(){
        return endDate;
    }
	 public String getSms() {
	        return sms;
	    }

	    public void setSms(String sms) {
	        this.sms = sms == null ? null : sms.trim();
	    }

	    public String getEmail() {
	        return email;
	    }

	    public void setEmail(String email) {
	        this.email = email == null ? null : email.trim();
	    }

	    public String getEmailTemplate_id() {
	        return emailTemplate_id;
	    }

	    public void setEmailTemplate_id(String emailTemplate_id) {
	        this.emailTemplate_id = emailTemplate_id == null ? null : emailTemplate_id.trim();
	    }

	    public String getMainzone() {
	        return mainzone;
	    }

	    public void setMainzone(String mainzone) {
	        this.mainzone = mainzone == null ? null : mainzone.trim();
	    }

	    public Date getMainstart() {
	        return mainstart;
	    }

	    public void setMainstart(Date mainstart) {
	        this.mainstart = mainstart;
	    }

	    public Date getMainend() {
	        return mainend;
	    }

	    public void setMainend(Date mainend) {
	        this.mainend = mainend;
	    }

	    public String getNotes() {
	        return notes;
	    }

	    public void setNotes(String notes) {
	        this.notes = notes == null ? null : notes.trim();
	    }
		public String getStartDateStr() {
			return startDateStr;
		}
		public void setStartDateStr(String startDateStr) {
			this.startDateStr = startDateStr;
		}
		public String getEndDateStr() {
			return endDateStr;
		}
		public void setEndDateStr(String endDateStr) {
			this.endDateStr = endDateStr;
		}
}