package com.lanmosoft.dao.model;

import java.util.Date;

/**
 * view_student_paike
 *
 * <AUTHOR>
 */
public class View_student_paike {
    private boolean checked;
    private String id;//id
    private String teacher_id;//教师ID
    private String chineseName;//chineseName
    private String englishName;//englishName
    private String abbr;//abbr
    private String course_id;//课程ID
    private String course_name;//课程name
    private Integer periodLowerLimit;//课时数下限
    private Integer periodUpperLimit;//课时数上限
    private String intervalType;//课时累计间隔
    private String gradeCategory_id;//gradeCategory_id
    private String gradeCategory_name;//gradeCategory_name
    private String grade_id;//可教授级别
    private String grade_name;//可教授级别name
    private String level;//level
    private String teachingType;//授课类型
    private Date startDate;//生效开始时间
    private Date endDate;//生效结束时间
    private Double unitPrice;//课时单价
    private String city_id;//city_id
    private String zone_id;//zone_id

    private String zone_name;//中间字段，视图没有,校区
    private String student_id;//中间字段，视图没有,学生Id

    private String classroom;

    public String getClassroom() {
        return classroom;
    }

    public void setClassroom(String classroom) {
        this.classroom = classroom;
    }

    public String getStudent_id() {
        return student_id;
    }

    public void setStudent_id(String student_id) {
        this.student_id = student_id;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getId() {
        return id;
    }

    public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }

    public String getTeacher_id() {
        return teacher_id;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName == null ? null : englishName.trim();
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setAbbr(String abbr) {
        this.abbr = abbr == null ? null : abbr.trim();
    }

    public String getAbbr() {
        return abbr;
    }

    public void setCourse_id(String course_id) {
        this.course_id = course_id == null ? null : course_id.trim();
    }

    public String getCourse_id() {
        return course_id;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name == null ? null : course_name.trim();
    }

    public String getCourse_name() {
        return course_name;
    }

    public void setPeriodLowerLimit(Integer periodLowerLimit) {
        this.periodLowerLimit = periodLowerLimit;
    }

    public Integer getPeriodLowerLimit() {
        return periodLowerLimit;
    }

    public void setPeriodUpperLimit(Integer periodUpperLimit) {
        this.periodUpperLimit = periodUpperLimit;
    }

    public Integer getPeriodUpperLimit() {
        return periodUpperLimit;
    }

    public void setIntervalType(String intervalType) {
        this.intervalType = intervalType == null ? null : intervalType.trim();
    }

    public String getIntervalType() {
        return intervalType;
    }

    public void setGradeCategory_id(String gradeCategory_id) {
        this.gradeCategory_id = gradeCategory_id == null ? null : gradeCategory_id.trim();
    }

    public String getGradeCategory_id() {
        return gradeCategory_id;
    }

    public void setGradeCategory_name(String gradeCategory_name) {
        this.gradeCategory_name = gradeCategory_name == null ? null : gradeCategory_name.trim();
    }

    public String getGradeCategory_name() {
        return gradeCategory_name;
    }

    public void setGrade_id(String grade_id) {
        this.grade_id = grade_id == null ? null : grade_id.trim();
    }

    public String getGrade_id() {
        return grade_id;
    }

    public void setGrade_name(String grade_name) {
        this.grade_name = grade_name == null ? null : grade_name.trim();
    }

    public String getGrade_name() {
        return grade_name;
    }

    public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }

    public String getLevel() {
        return level;
    }

    public void setTeachingType(String teachingType) {
        this.teachingType = teachingType == null ? null : teachingType.trim();
    }

    public String getTeachingType() {
        return teachingType;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setCity_id(String city_id) {
        this.city_id = city_id == null ? null : city_id.trim();
    }

    public String getCity_id() {
        return city_id;
    }

    public void setZone_id(String zone_id) {
        this.zone_id = zone_id == null ? null : zone_id.trim();
    }

    public String getZone_id() {
        return zone_id;
    }

    public String getZone_name() {
        return zone_name;
    }

    public void setZone_name(String zone_name) {
        this.zone_name = zone_name;
    }
}