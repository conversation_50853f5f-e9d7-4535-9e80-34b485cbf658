package com.lanmosoft.dao.model;

import java.util.Date;

public class Teacher_course_teachingway {
    private String id;

    private String teachingWay_id;

    private String schemeDetails_id;

    private String creator_id;

    private Date createTime;

    private String lastModifier_id;

    private Date lastModifiedTime;

    private String delStatus;
    private String teachingwayName;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getTeachingWay_id() {
        return teachingWay_id;
    }

    public void setTeachingWay_id(String teachingWay_id) {
        this.teachingWay_id = teachingWay_id == null ? null : teachingWay_id.trim();
    }

    public String getSchemeDetails_id() {
        return schemeDetails_id;
    }

    public void setSchemeDetails_id(String schemeDetails_id) {
        this.schemeDetails_id = schemeDetails_id == null ? null : schemeDetails_id.trim();
    }

    public String getCreator_id() {
        return creator_id;
    }

    public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastModifier_id() {
        return lastModifier_id;
    }

    public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }

    public Date getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(Date lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }

	public String getTeachingwayName() {
		return teachingwayName;
	}

	public void setTeachingwayName(String teachingwayName) {
		this.teachingwayName = teachingwayName;
	}
}