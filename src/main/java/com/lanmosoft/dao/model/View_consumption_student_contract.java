package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 学生耗课_合同视图
 * <AUTHOR>
 *
 */
public class View_consumption_student_contract {
	 private String id;//学生耗课_合同ID
	 private String consumption_student_id;//学生耗课ID
	 private String contract_id;//合同ID
	 private Double consumedClass;//合同消耗课时
	 private Double consumedClass_ct; //学生消耗课时
	 private Double unitPrice;//单价
	 private String lockStatus;//锁定状态
	 private String isUploaded;//是否已上传
	 private String student_id;//学生
	 private Date scheduledDate;//上课日期
	 private String accountId;//SFaccountId
	 private String crmId;//SFcrmId
	 private String chineseName;//中文名称
	 private String englishName;//英文名称
	 private String contractNo;//SF合同号
	 private String contractNoR;//关联合同号
	 private String partACode;//公司
	 private Double tuitionFee;//学费
	 private Double amount;//数量
	 private Double amountWithoutVAT;//不含税学费
	 private Double vatRate;//税率
	 private Double vat;//VAT
	 private String isUnitPriceChange;
	 private String BUType;
	 private String classSchedule_id;

	public Double getConsumedClass_ct() { return consumedClass_ct; }
	public void setConsumedClass_ct(Double consumedClass_ct) { this.consumedClass_ct = consumedClass_ct; }

	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setConsumption_student_id(String consumption_student_id) {
        this.consumption_student_id = consumption_student_id == null ? null : consumption_student_id.trim();
    }
	 public String getConsumption_student_id(){
        return consumption_student_id;
    }
	public void setContract_id(String contract_id) {
        this.contract_id = contract_id == null ? null : contract_id.trim();
    }
	 public String getContract_id(){
        return contract_id;
    }
	 public void setConsumedClass(Double consumedClass) {
          this.consumedClass = consumedClass ;
    }
	 public Double getConsumedClass(){
        return consumedClass;
    }
	 public void setUnitPrice(Double unitPrice) {
          this.unitPrice = unitPrice ;
    }
	 public Double getUnitPrice(){
        return unitPrice;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
	public void setIsUploaded(String isUploaded) {
        this.isUploaded = isUploaded == null ? null : isUploaded.trim();
    }
	 public String getIsUploaded(){
        return isUploaded;
    }
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
	 public void setScheduledDate(Date scheduledDate) {
          this.scheduledDate = scheduledDate ;
    }
	 public Date getScheduledDate(){
        return scheduledDate;
    }
	public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }
	 public String getAccountId(){
        return accountId;
    }
	public void setCrmId(String crmId) {
        this.crmId = crmId == null ? null : crmId.trim();
    }
	 public String getCrmId(){
        return crmId;
    }
	public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }
	 public String getChineseName(){
        return chineseName;
    }
	public void setEnglishName(String englishName) {
        this.englishName = englishName == null ? null : englishName.trim();
    }
	 public String getEnglishName(){
        return englishName;
    }
	public void setContractNo(String contractNo) {
        this.contractNo = contractNo == null ? null : contractNo.trim();
    }
	 public String getContractNo(){
        return contractNo;
    }
	public void setContractNoR(String contractNoR) {
        this.contractNoR = contractNoR == null ? null : contractNoR.trim();
    }
	 public String getContractNoR(){
        return contractNoR;
    }
	 public String getPartACode() {
		return partACode;
	}
	public void setPartACode(String partACode) {
		this.partACode = partACode;
	}
	public void setTuitionFee(Double tuitionFee) {
          this.tuitionFee = tuitionFee ;
    }
	 public Double getTuitionFee(){
        return tuitionFee;
    }
	 public void setAmount(Double amount) {
          this.amount = amount ;
    }
	 public Double getAmount(){
        return amount;
    }
	 public void setAmountWithoutVAT(Double amountWithoutVAT) {
          this.amountWithoutVAT = amountWithoutVAT ;
    }
	 public Double getAmountWithoutVAT(){
        return amountWithoutVAT;
    }
	 public void setVatRate(Double vatRate) {
          this.vatRate = vatRate ;
    }
	 public Double getVatRate(){
        return vatRate;
    }
	 public void setVat(Double vat) {
          this.vat = vat ;
    }
	 public Double getVat(){
        return vat;
    }
	public String getIsUnitPriceChange() {
		return isUnitPriceChange;
	}
	public void setIsUnitPriceChange(String isUnitPriceChange) {
		this.isUnitPriceChange = isUnitPriceChange == null ? null : isUnitPriceChange;
	}
	public String getBUType() {
		return BUType;
	}
	public void setBUType(String BUType) {
		this.BUType =BUType;
	}
	public String getClassSchedule_id() {
		return classSchedule_id;
	}
	public void setClassSchedule_id(String classSchedule_id) {
		this.classSchedule_id = classSchedule_id;
	}
}