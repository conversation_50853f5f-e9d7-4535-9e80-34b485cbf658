package com.lanmosoft.dao.model;

import java.util.Date;
/**
 * 学员请假表视图
 *
 * <AUTHOR>
 */
public class View_student_leave {
    private String id;//ID
    private String classSchedule_id;//课表
    private String student_id;//学生
    private String handleTime;//办理时间
    private String isInadvance;//是否提前12小时
    private String reschedule;//重排
    private Date leaveDate;//请假日期
    private String startTime;//请假开始时间
    private String endTime;//请假结束时间
    private String reason;//请假原因
    private Double deductionClass;//扣学生课时
    private Double givenClass;//补老师课时
    private Double givenWage;//补老师工资
    private String contract_id;//合同
    private String approvalStatus;//审批状态
    private String lockStatus;//锁定状态
    private String creator_id;//创建者
    private Date createTime;//创建时间
    private String lastModifier_id;//最后修改者
    private Date lastModifiedTime;//最后修改时间
    private String code;//学号
    private String chineseName;//中文名称
    private String englishName;//英文名称
    private String zone_name;//校区
    private String zone_id;//校区id
    private String tutorId;//助教Id
    private String tutor_englishName;//助教
    private String TeacherId;
    private String TeacherEnglishName;
    private String Consultant;

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getId() {
        return id;
    }

    public void setClassSchedule_id(String classSchedule_id) {
        this.classSchedule_id = classSchedule_id == null ? null : classSchedule_id.trim();
    }

    public String getClassSchedule_id() {
        return classSchedule_id;
    }

    public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }

    public String getStudent_id() {
        return student_id;
    }

    public void setHandleTime(String handleTime) {
        this.handleTime = handleTime == null ? null : handleTime.trim();
    }

    public String getHandleTime() {
        return handleTime;
    }

    public void setIsInadvance(String isInadvance) {
        this.isInadvance = isInadvance == null ? null : isInadvance.trim();
    }

    public String getIsInadvance() {
        return isInadvance;
    }

    public void setReschedule(String reschedule) {
        this.reschedule = reschedule;
    }

    public String getReschedule() {
        return reschedule;
    }

    public void setLeaveDate(Date leaveDate) {
        this.leaveDate = leaveDate;
    }

    public Date getLeaveDate() {
        return leaveDate;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }
	 public String getStartTime(){
        return startTime;
    }
	public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }
	 public String getEndTime(){
        return endTime;
    }
	 public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason == null ? null : reason.trim();
	}
	public void setDeductionClass(Double deductionClass) {
          this.deductionClass = deductionClass ;
    }
	 public Double getDeductionClass(){
        return deductionClass;
    }
	 public void setGivenClass(Double givenClass) {
          this.givenClass = givenClass ;
    }
	 public Double getGivenClass(){
        return givenClass;
    }
	 public void setGivenWage(Double givenWage) {
          this.givenWage = givenWage ;
    }
	 public Double getGivenWage(){
        return givenWage;
    }
	public void setContract_id(String contract_id) {
        this.contract_id = contract_id == null ? null : contract_id.trim();
    }
	 public String getContract_id(){
        return contract_id;
    }
	public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus == null ? null : approvalStatus.trim();
    }
	 public String getApprovalStatus(){
        return approvalStatus;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }
	 public String getCode(){
        return code;
    }
	public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }
	 public String getChineseName(){
        return chineseName;
    }
	public void setEnglishName(String englishName) {
        this.englishName = englishName == null ? null : englishName.trim();
    }
	 public String getEnglishName(){
        return englishName;
    }
	public void setZone_name(String zone_name) {
        this.zone_name = zone_name == null ? null : zone_name.trim();
    }
	 public String getZone_name(){
        return zone_name;
    }
	public void setZone_id(String zone_id) {
        this.zone_id = zone_id == null ? null : zone_id.trim();
    }
	 public String getZone_id(){
        return zone_id;
    }
	public String getTutorId() {
		return tutorId;
    }

    public void setTutorId(String tutorId) {
        this.tutorId = tutorId;
    }

    public String getTutor_englishName() {
        return tutor_englishName;
    }

    public void setTutor_englishName(String tutor_englishName) {
        this.tutor_englishName = tutor_englishName;
    }

    public String getTeacherId() {
        return TeacherId;
    }

    public void setTeacherId(String TeacherId) {
        this.TeacherId = TeacherId;
    }

    public String getTeacherEnglishName() {
        return TeacherEnglishName;
    }

    public void setTeacherEnglishName(String TeacherEnglishName) {
        this.TeacherEnglishName = TeacherEnglishName;
    }

    public String getConsultant() {
        return Consultant;
    }

    public void setConsultant(String Consultant) {
        this.Consultant = Consultant;
    }
}