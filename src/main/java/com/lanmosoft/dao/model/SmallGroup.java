package com.lanmosoft.dao.model;

import lombok.Data;

import java.util.Date;

@Data
public class SmallGroup {
    private String id;//id
    private String name;//name
    private String code;//code
    private String creator_id;//creator_id
    private Date createTime;//createTime
    private String lastModifier_id;//lastModifier_id
    private Date lastModifiedTime;//lastModifiedTime
    private String delStatus;//delStatus
    private String tutor_id;//tutor_id
    private String studyadvisor_id;
    private String zone_id;
    private String zonename;
    private String tutorname;
}
