package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * view_paike_class
 * <AUTHOR>
 *
 */
public class View_paike_class {
	private String zhuangtai;
	 private String id;//id
	 private String courseType;//courseType
	 private String student_id;//student_id
	 private String stu_chineseName;//stu_chineseName
	 private String stu_englishName;//stu_englishName
	 private String zone_id;//zone_id
	 private String course_id;//course_id
	 private String course_name;//course_name
	 private Date scheduledDate;//scheduledDate
	 private String startTime;//startTime
	 private String endTime;//endTime
	 private String time;//time
	 private String freeTimeCode;//freeTimeCode
	 private String teacher_id;//teacher_id
	 private String us_chineseName;//us_chineseName
	private String us_englishName;//us_englishName
	private String abbr;//abbr
	private String teachingWay;//teachingWay
	private String classroom_id;//classroom_id
	private String classroom_name;//classroom_name
	private String status;//status
	private String description;//description
	private String notes;//notes
	private String leaveState;//leaveState
	private String isAuto;//isAuto
	private String creator_id;//creator_id
	private Date createTime;//createTime
	private String lastModifier_id;//lastModifier_id
	private Date lastModifiedTime;//lastModifiedTime
	private String delStatus;//delStatus
	private Date realDate;//realDate
	private String realStartTime;//realStartTime
	private String realEndTime;//realEndTime
	private String realTime;//realTime
	private String teacher_leave_id;//老师请假ID
	private String t_approvalStatus;//老师请假审批状态
	private String s_approvalStatus;//学生请假审批状态
	private String student_leave_id;//老师请假ID
	private String ctrator_userName;//创建者用户名
	private String ctrator_chineseName;//创建者中文名
	private String ctrator_englishName;//创建者英文名
	private String attendanceStatus;//出勤状态

	private String scheduledDateStr;//上课日期
	private String tutorId;//助教Id
	private String tutorenglishName;

	private String BUType;
	private String deleteFlag;
	private Date endDate;

	private String scheduledDateString;
	private String endDateStr;

	private String teachingWayName;
	private String crossDst;
	private String riqiEnd;
	private String islockDate;//是否锁定数据 1 锁定 0 不锁定

	public String getIslockDate() {
		return islockDate;
	}

	public void setIslockDate(String islockDate) {
		this.islockDate = islockDate;
	}

	public String getRiqiEnd() {
		return riqiEnd;
	}

	public void setRiqiEnd(String riqiEnd) {
		this.riqiEnd = riqiEnd;
	}

	public String getTutorId() {
		return tutorId;
	}

	public void setTutorId(String tutorId) {
		this.tutorId = tutorId;
	}

	public String getTutorenglishName() {
		return tutorenglishName;
	}

	public void setTutorenglishName(String tutorenglishName) {
		this.tutorenglishName = tutorenglishName;
	}

	public String getZone_id() {
		return zone_id;
	}

	public void setZone_id(String zone_id) {
		this.zone_id = zone_id;
	}

	public String getZhuangtai() {
		return zhuangtai;
	}
	public void setZhuangtai(String zhuangtai) {
		this.zhuangtai = zhuangtai;
	}
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setCourseType(String courseType) {
        this.courseType = courseType == null ? null : courseType.trim();
    }
	 public String getCourseType(){
        return courseType;
    }
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
	public void setStu_chineseName(String stu_chineseName) {
        this.stu_chineseName = stu_chineseName == null ? null : stu_chineseName.trim();
    }
	 public String getStu_chineseName(){
        return stu_chineseName;
    }
	public void setStu_englishName(String stu_englishName) {
        this.stu_englishName = stu_englishName == null ? null : stu_englishName.trim();
    }
	 public String getStu_englishName(){
        return stu_englishName;
    }
	public void setCourse_id(String course_id) {
        this.course_id = course_id == null ? null : course_id.trim();
    }
	 public String getCourse_id(){
        return course_id;
    }
	public void setCourse_name(String course_name) {
        this.course_name = course_name == null ? null : course_name.trim();
    }
	 public String getCourse_name(){
        return course_name;
    }
	 public void setScheduledDate(Date scheduledDate) {
          this.scheduledDate = scheduledDate ;
    }
	 public Date getScheduledDate(){
        return scheduledDate;
    }
	public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }
	 public String getStartTime(){
        return startTime;
    }
	public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }
	 public String getEndTime(){
        return endTime;
    }
	public void setTime(String time) {
        this.time = time == null ? null : time.trim();
    }
	 public String getTime(){
        return time;
    }
	public void setFreeTimeCode(String freeTimeCode) {
        this.freeTimeCode = freeTimeCode == null ? null : freeTimeCode.trim();
    }
	 public String getFreeTimeCode(){
        return freeTimeCode;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setUs_chineseName(String us_chineseName) {
        this.us_chineseName = us_chineseName == null ? null : us_chineseName.trim();
    }
	 public String getUs_chineseName(){
        return us_chineseName;
    }
	public void setUs_englishName(String us_englishName) {
        this.us_englishName = us_englishName == null ? null : us_englishName.trim();
    }
	 public String getUs_englishName(){
        return us_englishName;
    }
	public void setAbbr(String abbr) {
        this.abbr = abbr == null ? null : abbr.trim();
    }
	 public String getAbbr(){
        return abbr;
    }
	public void setTeachingWay(String teachingWay) {
        this.teachingWay = teachingWay == null ? null : teachingWay.trim();
    }
	 public String getTeachingWay(){
        return teachingWay;
    }
	public void setClassroom_id(String classroom_id) {
        this.classroom_id = classroom_id == null ? null : classroom_id.trim();
    }
	 public String getClassroom_id(){
        return classroom_id;
    }
	public void setClassroom_name(String classroom_name) {
        this.classroom_name = classroom_name == null ? null : classroom_name.trim();
    }
	 public String getClassroom_name(){
        return classroom_name;
    }
	public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }
	 public String getStatus(){
        return status;
    }
	public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }
	 public String getDescription(){
        return description;
    }
	public void setNotes(String notes) {
        this.notes = notes == null ? null : notes.trim();
    }
	 public String getNotes(){
        return notes;
    }
	public void setLeaveState(String leaveState) {
        this.leaveState = leaveState == null ? null : leaveState.trim();
    }
	 public String getLeaveState(){
        return leaveState;
    }
	public void setIsAuto(String isAuto) {
        this.isAuto = isAuto == null ? null : isAuto.trim();
    }
	 public String getIsAuto(){
        return isAuto;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	 public void setRealDate(Date realDate) {
          this.realDate = realDate ;
    }
	 public Date getRealDate(){
        return realDate;
    }
	public void setRealStartTime(String realStartTime) {
        this.realStartTime = realStartTime == null ? null : realStartTime.trim();
    }
	 public String getRealStartTime(){
        return realStartTime;
    }
	public void setRealEndTime(String realEndTime) {
        this.realEndTime = realEndTime == null ? null : realEndTime.trim();
    }
	 public String getRealEndTime(){
        return realEndTime;
    }
	public void setRealTime(String realTime) {
        this.realTime = realTime == null ? null : realTime.trim();
    }
	 public String getRealTime(){
        return realTime;
    }
	public String getTeacher_leave_id() {
		return teacher_leave_id;
	}
	public void setTeacher_leave_id(String teacher_leave_id) {
		this.teacher_leave_id = teacher_leave_id;
	}
	public String getT_approvalStatus() {
		return t_approvalStatus;
	}
	public void setT_approvalStatus(String t_approvalStatus) {
		this.t_approvalStatus = t_approvalStatus;
	}
	public String getS_approvalStatus() {
		return s_approvalStatus;
	}
	public void setS_approvalStatus(String s_approvalStatus) {
		this.s_approvalStatus = s_approvalStatus;
	}
	public String getStudent_leave_id() {
		return student_leave_id;
	}
	public void setStudent_leave_id(String student_leave_id) {
		this.student_leave_id = student_leave_id;
	}
	public String getCtrator_userName() {
		return ctrator_userName;
	}
	public void setCtrator_userName(String ctrator_userName) {
		this.ctrator_userName = ctrator_userName;
	}
	public String getCtrator_chineseName() {
		return ctrator_chineseName;
	}
	public void setCtrator_chineseName(String ctrator_chineseName) {
		this.ctrator_chineseName = ctrator_chineseName;
	}
	public String getCtrator_englishName() {
		return ctrator_englishName;
	}
	public void setCtrator_englishName(String ctrator_englishName) {
		this.ctrator_englishName = ctrator_englishName;
	}
	public String getAttendanceStatus() {
		return attendanceStatus;
	}
	public void setAttendanceStatus(String attendanceStatus) {
		this.attendanceStatus = attendanceStatus;
	}
	public String getScheduledDateStr() {
		return scheduledDateStr;
	}
	public void setScheduledDateStr(String scheduledDateStr) {
		this.scheduledDateStr = scheduledDateStr;
	}
	 public String getBUType() {
			return BUType;
	}
		public void setBUType(String BUType) {
			this.BUType = BUType== null ? null : BUType.trim();
	}
		public String getDeleteFlag() {
			return deleteFlag;
		}
		public void setDeleteFlag(String deleteFlag) {
			this.deleteFlag = deleteFlag;
		}
		public Date getEndDate() {
			return endDate;
		}
		public void setEndDate(Date endDate) {
			this.endDate = endDate;
		}
		public String getScheduledDateString() {
			return scheduledDateString;
		}
		public void setScheduledDateString(String scheduledDateString) {
			this.scheduledDateString = scheduledDateString;
		}
		public String getEndDateStr() {
			return endDateStr;
		}
		public void setEndDateStr(String endDateStr) {
			this.endDateStr = endDateStr;
		}
		public String getTeachingWayName() {
			return teachingWayName;
		}
		public void setTeachingWayName(String teachingWayName) {
			this.teachingWayName = teachingWayName;
		}

	public String getCrossDst() {
		return crossDst;
	}
	public void setCrossDst(String crossDst) {
		this.crossDst = crossDst;
	}
		
}