package com.lanmosoft.dao.model;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

/**
 * 人员
 *
 * <AUTHOR>
 */
@XmlRootElement
public class User {
    private String id;//ID
    private String userName;//登录名
    private String password;//密码
    private String isTeaching;//是否授课
    private String category;//用户类别
    private String code;//ERP员工编码
    private String teCode;//TE员工编码
    private String chineseName;//中文姓名
    private String englishName;//英文姓名
    private String abbr;//简称
    private Date birthday;//出生年月
    private String gender;//性别
    private String nationality;//国籍
    private String IDNo;//身份证号
    private String possportNo;//护照号
    private String mobile;//手机
    private String email;//电子邮件
    private String wechat;//微信
    private String address;//地址
    private String type;//老师性质
    private String zone_id;//校区
    private String timezone_id; //时区 bohuang
    private String status;//状态
    private String department_id;//部门ID
    private String department_name;//部门名称
    private String position_id;//岗位ID
    private String position_name;//岗位名称
    private String creator_id;//创建者
    private Date createTime;//创建时间
    private String lastModifier_id;//最后修改者
    private Date lastModifiedTime;//最后修改时间
    private String delStatus;//是否禁用
    private String sms;//短信
    private String flag;//是否看所有校区
    private String city_id;
    private String payment_method;
    private String university;
    private String partner_id;
    private String settlement_id;

    private String abbr1;//简称1 非数据库字段，只作封装

    private String tencentid;

    public void setTencentid(String tencentid) {
        this.tencentid = tencentid == null ? null : tencentid.trim();
    }

    public String getTencentid() {
        return tencentid;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getId() {
        return id;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getUserName() {
        return userName;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setIsTeaching(String isTeaching) {
        this.isTeaching = isTeaching == null ? null : isTeaching.trim();
    }

    public String getIsTeaching() {
        return isTeaching;
    }

    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    public String getCategory() {
        return category;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getCode() {
        return code;
    }

    public void setTeCode(String teCode) {
        this.teCode = teCode == null ? null : teCode.trim();
    }

    public String getTeCode() {
        return teCode;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName == null ? null : englishName.trim();
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setAbbr(String abbr) {
        this.abbr = abbr == null ? null : abbr.trim();
    }

    public String getAbbr() {
        return abbr;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setGender(String gender) {
        this.gender = gender == null ? null : gender.trim();
    }

    public String getGender() {
        return gender;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality == null ? null : nationality.trim();
    }

    public String getNationality() {
        return nationality;
    }

    public void setIDNo(String IDNo) {
        this.IDNo = IDNo == null ? null : IDNo.trim();
    }

    public String getIDNo() {
        return IDNo;
    }

    public void setPossportNo(String possportNo) {
        this.possportNo = possportNo == null ? null : possportNo.trim();
    }

    public String getPossportNo() {
        return possportNo;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat == null ? null : wechat.trim();
    }

    public String getWechat() {
        return wechat;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getType() {
        return type;
    }

    public void setZone_id(String zone_id) {
        this.zone_id = zone_id == null ? null : zone_id.trim();
    }

    public String getZone_id() {
        return zone_id;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setDepartment_id(String department_id) {
        this.department_id = department_id == null ? null : department_id.trim();
    }

    public String getDepartment_id() {
        return department_id;
    }

    public void setDepartment_name(String department_name) {
        this.department_name = department_name == null ? null : department_name.trim();
    }

    public String getDepartment_name() {
        return department_name;
    }

    public void setPosition_id(String position_id) {
        this.position_id = position_id == null ? null : position_id.trim();
    }

    public String getPosition_id() {
        return position_id;
    }

    public void setPosition_name(String position_name) {
        this.position_name = position_name == null ? null : position_name.trim();
    }

    public String getPosition_name() {
        return position_name;
    }

    public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }

    public String getCreator_id() {
        return creator_id;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }

    public String getLastModifier_id() {
        return lastModifier_id;
    }

    public void setLastModifiedTime(Date lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public Date getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }

    public String getDelStatus() {
        return delStatus;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getAbbr1() {
        return abbr1;
    }

    public void setAbbr1(String abbr1) {
        this.abbr1 = abbr1;
    }

    public String getTimezone_id() {
        return timezone_id;
    }

    public void setTimezone_id(String timezone_id) {
        this.timezone_id = timezone_id == null ? null : timezone_id.trim();
    }

    public String getCity_id() {
        return city_id;
    }

    public void setCity_id(String city_id) {
        this.city_id = city_id == null ? null : city_id.trim();
    }

    public String getPayment_method() {
        return payment_method;
    }

    public void setPayment_method(String payment_method) {
        this.payment_method = payment_method == null ? null : payment_method.trim();
    }

    public String getUniversity() {
        return university;
    }

    public void setUniversity(String university) {
        this.university = university == null ? null : university.trim();
    }

    public String getPartner_id() {
        return partner_id;
    }

    public void setPartner_id(String partner_id) {
        this.partner_id = partner_id == null ? null : partner_id.trim();
    }

    public String getSettlement_id() {
        return settlement_id;
    }

    public void setSettlement_id(String settlement_id) {
        this.settlement_id = settlement_id == null ? null : settlement_id.trim();
    }
}