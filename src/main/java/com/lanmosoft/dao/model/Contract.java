package com.lanmosoft.dao.model;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.google.gson.annotations.Expose;
/**
 * contract
 * <AUTHOR>
 *
 */
public class Contract implements Serializable{
	private static final long serialVersionUID = 4297819300143485237L;
	private String id;//id
	 @Expose
	 private String contractId;//contractId
	 @Expose
	 private String crmId;//crmId
	 private String studentId;//学员ID
	 @Expose
	 private String accountId;//accountId
	 @Expose
	 private String contractNo;//SF合同号
	 private Date startDate;//合同开始日期
	 private Date endDate;//合同结束日期
	 @Expose
	 private String productId;//产品ID
	 @Expose
	 private String contractNoR;//关联合同号
	 @Expose
	 private String contractOwnerId;//咨询顾问
	 @Expose
	 private String ownerName;//咨询顾问名称
	 @Expose
	 private String contractType;//学员类型
	 private String grade_id;//报读级别
	 private Date studyDate_start;//学习开始日期
	 private Date studyDate_end;//学习结束日期
	 private String company;//公司名称
	 private String status;//当前状态
	 private String isAbandoned;//是否弃学
	 private Date abandonedDate;//弃学日期
	 private String persentType;//赠送类型
	 private String creator_id;//creator_id
	 private Date createTime;//createTime
	 private String lastModifier_id;//lastModifier_id
	 private Date lastModifiedTime;//lastModifiedTime
	 private String delStatus;//delStatus
	 
	 private String product_id;//TE产品ID
	 @Expose
	 private String productName;//产品名称
	 private Double amount;//数量
	 private Double units;//单元
	 private Double tuitionFee;//学费
	 @Expose
	 private String pricebookId;//SFPricebookId
	 private String productType;//productType
	 private String isUnitPriceChange;//单价是否变化
	 private Double diffUnitPrice;//单价差价
	 private Double amountWithoutVAT;//不含税学费
	 private Double vatRate;//税率
	 private Double vat;//VAT
	 @Expose
	 private String location;//Location
	 @Expose
	 private String partACode;//公司
	 private Double consumedClass;//已消耗课时
	 private String isFirst;//是否为首份合同
	 
	 private Double currentConsumedClass;//本次消耗课时
	 private Date thefirstpaymentDate;
	 
	 private String contractType2;//合同类型
	private String allowchangetype;
	 
	 private List<String> contractType2List;
	 private String compensateStudent;//补偿合同对应的课程id
	@Expose
	private String course_id;//contractId

	public void setCourse_id(String course_id) {
		this.course_id = course_id == null ? null : course_id.trim();
	}
	public String getCourse_id(){
		return course_id;
	}
	public String getCompensateStudent() {
		return compensateStudent;
	}
	public void setCompensateStudent(String compensateStudent) {
		this.compensateStudent = compensateStudent;
	}
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }
	 public String getContractId(){
        return contractId;
    }
	public void setCrmId(String crmId) {
        this.crmId = crmId == null ? null : crmId.trim();
    }
	 public String getCrmId(){
        return crmId;
    }
	public void setStudentId(String studentId) {
        this.studentId = studentId == null ? null : studentId.trim();
    }
	 public String getStudentId(){
        return studentId;
    }
	public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }
	 public String getAccountId(){
        return accountId;
    }
	public void setContractNo(String contractNo) {
        this.contractNo = contractNo == null ? null : contractNo.trim();
    }
	 public String getContractNo(){
        return contractNo;
    }
	 public void setStartDate(Date startDate) {
          this.startDate = startDate ;
    }
	 public Date getStartDate(){
        return startDate;
    }
	 public void setEndDate(Date endDate) {
          this.endDate = endDate ;
    }
	 public Date getEndDate(){
        return endDate;
    }
	public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }
	 public String getProductId(){
        return productId;
    }
	public void setContractNoR(String contractNoR) {
        this.contractNoR = contractNoR == null ? null : contractNoR.trim();
    }
	 public String getContractNoR(){
        return contractNoR;
    }
	public void setContractOwnerId(String contractOwnerId) {
        this.contractOwnerId = contractOwnerId == null ? null : contractOwnerId.trim();
    }
	 public String getContractOwnerId(){
        return contractOwnerId;
    }
	public String getOwnerName() {
		return ownerName;
	}
	public void setOwnerName(String ownerName) {
		this.ownerName = ownerName;
	}
	public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }
	 public String getContractType(){
        return contractType;
    }
	public void setGrade_id(String grade_id) {
        this.grade_id = grade_id == null ? null : grade_id.trim();
    }
	 public String getGrade_id(){
        return grade_id;
    }
	 public void setStudyDate_start(Date studyDate_start) {
          this.studyDate_start = studyDate_start ;
    }
	 public Date getStudyDate_start(){
        return studyDate_start;
    }
	 public void setStudyDate_end(Date studyDate_end) {
          this.studyDate_end = studyDate_end ;
    }
	 public Date getStudyDate_end(){
        return studyDate_end;
    }
	public void setCompany(String company) {
        this.company = company == null ? null : company.trim();
    }
	 public String getCompany(){
        return company;
    }
	public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }
	 public String getStatus(){
        return status;
    }
	public void setIsAbandoned(String isAbandoned) {
        this.isAbandoned = isAbandoned == null ? null : isAbandoned.trim();
    }
	 public String getIsAbandoned(){
        return isAbandoned;
    }
	 public void setAbandonedDate(Date abandonedDate) {
          this.abandonedDate = abandonedDate ;
    }
	 public Date getAbandonedDate(){
        return abandonedDate;
    }
	public void setPersentType(String persentType) {
        this.persentType = persentType == null ? null : persentType.trim();
    }
	 public String getPersentType(){
        return persentType;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }
	 public String getProductName(){
        return productName;
    }
	 public void setAmount(Double amount) {
          this.amount = amount ;
    }
	 public Double getAmount(){
        return amount;
    }
	 public void setUnits(Double units) {
          this.units = units ;
    }
	 public Double getUnits(){
        return units;
    }
	 public void setTuitionFee(Double tuitionFee) {
          this.tuitionFee = tuitionFee ;
    }
	 public Double getTuitionFee(){
        return tuitionFee;
    }
	public void setPricebookId(String pricebookId) {
        this.pricebookId = pricebookId == null ? null : pricebookId.trim();
    }
	 public String getPricebookId(){
        return pricebookId;
    }
	public void setProductType(String productType) {
        this.productType = productType == null ? null : productType.trim();
    }
	 public String getProductType(){
        return productType;
    }
	public void setIsUnitPriceChange(String isUnitPriceChange) {
        this.isUnitPriceChange = isUnitPriceChange == null ? null : isUnitPriceChange.trim();
    }
	 public String getIsUnitPriceChange(){
        return isUnitPriceChange;
    }
	 public Double getDiffUnitPrice() {
		return diffUnitPrice;
	}
	public void setDiffUnitPrice(Double diffUnitPrice) {
		this.diffUnitPrice = diffUnitPrice;
	}
	public Double getAmountWithoutVAT() {
		return amountWithoutVAT;
	}
	public void setAmountWithoutVAT(Double amountWithoutVAT) {
		this.amountWithoutVAT = amountWithoutVAT;
	}
	public Double getVatRate() {
		return vatRate;
	}
	public void setVatRate(Double vatRate) {
		this.vatRate = vatRate;
	}
	public Double getVat() {
		return vat;
	}
	public void setVat(Double vat) {
		this.vat = vat;
	}
	public void setConsumedClass(Double consumedClass) {
          this.consumedClass = consumedClass ;
    }
	 public Double getConsumedClass(){
        return consumedClass;
    }
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public String getPartACode() {
		return partACode;
	}
	public void setPartACode(String partACode) {
		this.partACode = partACode;
	}
	public String getIsFirst() {
		return isFirst;
	}
	public void setIsFirst(String isFirst) {
		this.isFirst = isFirst;
	}
	public Double getCurrentConsumedClass() {
		return currentConsumedClass;
	}
	public void setCurrentConsumedClass(Double currentConsumedClass) {
		this.currentConsumedClass = currentConsumedClass;
	}
	public Date getThefirstpaymentDate() {
		return thefirstpaymentDate;
	}
	public void setThefirstpaymentDate(Date thefirstpaymentDate) {
		this.thefirstpaymentDate = thefirstpaymentDate;
	}
	public String getContractType2() {
		return contractType2;
	}
	public void setContractType2(String contractType2) {
		this.contractType2 = contractType2;
	}
	public List<String> getContractType2List() {
		return contractType2List;
	}
	public void setContractType2List(List<String> contractType2List) {
		this.contractType2List = contractType2List;
	}
	public String getallowchangetype() {
		return allowchangetype;
	}
	public void setallowchangetype(String allowchangetype) {
		this.allowchangetype = allowchangetype;
	}
}