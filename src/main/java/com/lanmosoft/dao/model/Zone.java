package com.lanmosoft.dao.model;
import java.util.Date;

import com.lanmosoft.model.BaseModel;
/**
 * zone
 * <AUTHOR>
 *
 */
public class Zone {
	 private String id;//id
	 private String name;//name
	 private String code;//code
	 private String city_id;//city_id
	 private String creator_id;//creator_id
	 private Date createTime;//createTime
	 private String lastModifier_id;//lastModifier_id
	 private Date lastModifiedTime;//lastModifiedTime
	 private String delStatus;//delStatus
	 private String BUtype;
	 private String campusMailbox;//校区邮箱

    public String getCampusMailbox() {
        return campusMailbox;
    }
    public void setCampusMailbox(String campusMailbox) {
        this.campusMailbox = campusMailbox;
    }
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }
	 public String getName(){
        return name;
    }
	public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }
	 public String getCode(){
        return code;
    }
	public void setCity_id(String city_id) {
        this.city_id = city_id == null ? null : city_id.trim();
    }
	 public String getCity_id(){
        return city_id;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	public String getBUtype() {
		return BUtype;
	}
	public void setBUtype(String BUtype) {
		this.BUtype = BUtype== null ? null : BUtype.trim();
	}
	
	
	
	
}