package com.lanmosoft.dao.model;

import java.util.ArrayList;
import java.util.List;

public class Teacher_hourlyratescheme {
    private String id;

    private String teacher_id;

    private String schemeNo;

    private String BUType;

    private String notes;

    private String isDefault;
    
    private List<Hourlyratescheme_details> list=new ArrayList<Hourlyratescheme_details>();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getTeacher_id() {
        return teacher_id;
    }

    public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }

    public String getSchemeNo() {
        return schemeNo;
    }

    public void setSchemeNo(String schemeNo) {
        this.schemeNo = schemeNo == null ? null : schemeNo.trim();
    }

    public String getBUType() {
        return BUType;
    }

    public void setBUType(String BUType) {
        this.BUType = BUType == null ? null : BUType.trim();
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes == null ? null : notes.trim();
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault == null ? null : isDefault.trim();
    }

	public List<Hourlyratescheme_details> getList() {
		return list;
	}

	public void setList(List<Hourlyratescheme_details> list) {
		this.list = list;
	}
}