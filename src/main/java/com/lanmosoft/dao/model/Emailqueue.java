package com.lanmosoft.dao.model;
import java.util.Date;

import com.lanmosoft.model.BaseModel;
/**
 * city
 * <AUTHOR>
 *
 */
public class Emailqueue{
	 private String id;//id
	 private String schedule_id;//name
	 private String issendmail;//code
	 private String creator_id;//creator_id
	 private Date createTime;//createTime
	 private String lastModifier_id;//lastModifier_id
	 private Date lastModifiedTime;//lastModifiedTime
	 private String delStatus;//delStatus
	 private String  issendmaiTeacher;//老师邮件是否发送

	public String getIssendmaiTeacher() {
		return issendmaiTeacher;
	}

	public void setIssendmaiTeacher(String issendmaiTeacher) {
		this.issendmaiTeacher = issendmaiTeacher;
	}

	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	
	public String getSchedule_id() {
		return schedule_id;
	}
	public void setSchedule_id(String schedule_id) {
		this.schedule_id = schedule_id;
	}
	public String getIssendmail() {
		return issendmail;
	}
	public void setIssendmail(String issendmail) {
		this.issendmail = issendmail;
	}
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
}