package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 学生耗课_合同
 * <AUTHOR>
 *
 */
public class Consumption_student_contract {
	 private String id;//ID
	 private String consumption_student_id;//学生耗课ID
	 private String contract_id;//合同
	 private Double consumedClass;//消耗课时
	 private Double unitPrice;//单价
	 private String lockStatus;//锁定状态
	 private String isUploaded;//是否已上传
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setConsumption_student_id(String consumption_student_id) {
        this.consumption_student_id = consumption_student_id == null ? null : consumption_student_id.trim();
    }
	 public String getConsumption_student_id(){
        return consumption_student_id;
    }
	public void setContract_id(String contract_id) {
        this.contract_id = contract_id == null ? null : contract_id.trim();
    }
	 public String getContract_id(){
        return contract_id;
    }
	 public void setConsumedClass(Double consumedClass) {
          this.consumedClass = consumedClass ;
    }
	 public Double getConsumedClass(){
        return consumedClass;
    }
	 public void setUnitPrice(Double unitPrice) {
          this.unitPrice = unitPrice ;
    }
	 public Double getUnitPrice(){
        return unitPrice;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
	public void setIsUploaded(String isUploaded) {
        this.isUploaded = isUploaded == null ? null : isUploaded.trim();
    }
	 public String getIsUploaded(){
        return isUploaded;
    }
}