package com.lanmosoft.dao.model;

import lombok.Data;

import java.util.Date;

@Data
public class Suqiu
{
    private String id;//id
    private String student_id;
    private String zone_id;
    private String month_class_datetime;
    private String freq_ratio;
    private String grade_id;
    private String course_id;
    private String tutor_id;
    private String description;
    private String comm;

    private String creator_id;//creator_id
    private Date createTime;//createTime
    private String lastModifier_id;//lastModifier_id
    private Date lastModifiedTime;//lastModifiedTime
    private String delStatus;//delStatus
    private String coursetutor;

    private String zonename;
    private String tutorname;
    private String studentname;
    private String student_englishName;
    private String student_chineseName;
    private String gradename;
    private String type;
    private String classscheduleStatus;
    private String status;
    private String paiketype;
}
