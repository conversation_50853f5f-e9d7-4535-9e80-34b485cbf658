package com.lanmosoft.dao.model;

/**
 * 上课报表
 */
public class ClassReport {

    private String campus; //所属校区
    private String sEnglishName;//学员英文名称
    private String sChineseName;//学员中文名称
    private String day;//时间
    private String time;//时刻
    private String courseName;//课程
    private String classroomName;//教室
    private String teacherName;//老师名称
    private String cTeacherName;//老师中文名称
    private String approvalStatus;//签到状态
    private String attendanceStatus;//出勤状态
    private String consumedClass;//消耗课时
    private String givenClass;//赠送课时
    private String reason;//备注
    private String BUType;//上课类型
    private String classHour;//课时数
    private String teachingWayName;//上课方式
    private String wage; //工资（小时计）
    private String wage2; //工资（元）
    private String wageDeduction; //扣除工资（小时计）
    private String wageDeduction2; //扣除工资（金额计）
    private String currencyName; //货币
    private String ownerName;
    private String tutor_englishName;
    private String student_id;

    public String getStudent_id() {
        return student_id;
    }

    public void setStudent_id(String student_id) {
        this.student_id = student_id;
    }

    public String getTutor_englishName() {
        return tutor_englishName;
    }

    public void setTutor_englishName(String tutor_englishName) {
        this.tutor_englishName = tutor_englishName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getWageDeduction2() {
        return wageDeduction2;
    }

    public void setWageDeduction2(String wageDeduction2) {
        this.wageDeduction2 = wageDeduction2;
    }

    public String getWage() {
        return wage;
    }

    public void setWage(String wage) {
        this.wage = wage;
    }

    public String getWage2() {
        return wage2;
    }

    public void setWage2(String wage2) {
        this.wage2 = wage2;
    }

    public String getWageDeduction() {
        return wageDeduction;
    }

    public void setWageDeduction(String wageDeduction) {
        this.wageDeduction = wageDeduction;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public String getTeachingWayName() {
        return teachingWayName;
    }

    public void setTeachingWayName(String teachingWayName) {
        this.teachingWayName = teachingWayName;
    }

    public String getClassHour() {
        return classHour;
    }

    public void setClassHour(String classHour) {
        this.classHour = classHour;
    }

    public String getcTeacherName() {
        return cTeacherName;
    }

    public void setcTeacherName(String cTeacherName) {
        this.cTeacherName = cTeacherName;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public String getCampus() {
        return campus;
    }

    public void setCampus(String campus) {
        this.campus = campus;
    }

    public String getsEnglishName() {
        return sEnglishName;
    }

    public void setsEnglishName(String sEnglishName) {
        this.sEnglishName = sEnglishName;
    }

    public String getsChineseName() {
        return sChineseName;
    }

    public void setsChineseName(String sChineseName) {
        this.sChineseName = sChineseName;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getClassroomName() {
        return classroomName;
    }

    public void setClassroomName(String classroomName) {
        this.classroomName = classroomName;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getConsumedClass() {
        return consumedClass;
    }

    public void setConsumedClass(String consumedClass) {
        this.consumedClass = consumedClass;
    }

    public String getGivenClass() {
        return givenClass;
    }

    public void setGivenClass(String givenClass) {
        this.givenClass = givenClass;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getBUType() {
        return BUType;
    }

    public void setBUType(String BUType) {
        this.BUType = BUType;
    }
}
