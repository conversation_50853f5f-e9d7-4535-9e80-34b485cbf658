package com.lanmosoft.dao.model;

import lombok.Data;

import java.util.Date;

@Data
public class Smallgroup_student {
    private String id;//id
    private String creator_id;//creator_id
    private Date createTime;//createTime
    private String lastModifier_id;//lastModifier_id
    private Date lastModifiedTime;//lastModifiedTime
    private String delStatus;//delStatus
    private String tutor_id;//tutor_id
    private String student_id;
    private String smallgroup_id;
    private String zone_id;
    private String zonename;
    private String studentname;
    private String chineseName;
    private String englishName;
}