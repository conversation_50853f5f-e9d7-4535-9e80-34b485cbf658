package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 教师请假表
 * <AUTHOR>
 *
 */
public class Teacher_leave {
	 private String id;//ID
	 private String classSchedule_id;//课表
	 private String teacher_id;//教师
	 private String handleTime;//办理时间
	 private String isInadvance;//是否提前12小时
	 private Date leaveDate;//请假日期
	 private String startTime;//请假开始时间
	 private String endTime;//请假结束时间
	 private String reason;//请假原因
	 private Double givenClass;//补学生课时
	 private Double deductionClass;//扣老师课时
	 private Double deductionWage;//扣老师工资
	 private String contract_id;//合同
	 private String approvalStatus;//审批状态
	 private String lockStatus;//锁定状态
	 private String creator_id;//创建者
	 private Date createTime;//创建时间
	 private String lastModifier_id;//最后修改者
	 private Date lastModifiedTime;//最后修改时间
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setClassSchedule_id(String classSchedule_id) {
        this.classSchedule_id = classSchedule_id == null ? null : classSchedule_id.trim();
    }
	 public String getClassSchedule_id(){
        return classSchedule_id;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setHandleTime(String handleTime) {
        this.handleTime = handleTime == null ? null : handleTime.trim();
    }
	 public String getHandleTime(){
        return handleTime;
    }
	public void setIsInadvance(String isInadvance) {
        this.isInadvance = isInadvance == null ? null : isInadvance.trim();
    }
	 public String getIsInadvance(){
        return isInadvance;
    }
	 public void setLeaveDate(Date leaveDate) {
          this.leaveDate = leaveDate ;
    }
	 public Date getLeaveDate(){
        return leaveDate;
    }
	public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }
	 public String getStartTime(){
        return startTime;
    }
	public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }
	 public String getEndTime(){
        return endTime;
    }
	public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }
	 public String getReason(){
        return reason;
    }
	 public void setGivenClass(Double givenClass) {
          this.givenClass = givenClass ;
    }
	 public Double getGivenClass(){
        return givenClass;
    }
	 public void setDeductionClass(Double deductionClass) {
          this.deductionClass = deductionClass ;
    }
	 public Double getDeductionClass(){
        return deductionClass;
    }
	 public void setDeductionWage(Double deductionWage) {
          this.deductionWage = deductionWage ;
    }
	 public Double getDeductionWage(){
        return deductionWage;
    }
	public void setContract_id(String contract_id) {
        this.contract_id = contract_id == null ? null : contract_id.trim();
    }
	 public String getContract_id(){
        return contract_id;
    }
	public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus == null ? null : approvalStatus.trim();
    }
	 public String getApprovalStatus(){
        return approvalStatus;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
}