package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 学员退费
 * <AUTHOR>
 *
 */
public class Student_refundFee {
	 private String id;//ID
	 private String contract_id;//TE合同ID
	 private Date handleTime;//退费时间
	 private String refundType;//退费类型
	 private Double unitPrice;//课程单价
	 private Double refundFuitionFee;//退费金额
	 private String refundReason;//退费原因
	 private String creator_id;//创建者ID
	 private Date createTime;//创建时间
	 private String lastModifier_id;//最后修改者
	 private Date lastModifiedTime;//最后修改时间
	 private String delStatus;//是否禁用
	 private Double periods;//退费课时
	 private String approvalStatus;//审批状态
	 private String approver;//审批人
	 private Date approverTime;//审批时间
	 
	 private String old_prorduct_id;//原产品Id
	 private String new_prorduct_id;//新产品Id
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setContract_id(String contract_id) {
        this.contract_id = contract_id == null ? null : contract_id.trim();
    }
	 public String getContract_id(){
        return contract_id;
    }
	 public void setHandleTime(Date handleTime) {
          this.handleTime = handleTime ;
    }
	 public Date getHandleTime(){
        return handleTime;
    }
	public void setRefundType(String refundType) {
        this.refundType = refundType == null ? null : refundType.trim();
    }
	 public String getRefundType(){
        return refundType;
    }
	 public void setUnitPrice(Double unitPrice) {
          this.unitPrice = unitPrice ;
    }
	 public Double getUnitPrice(){
        return unitPrice;
    }
	 public void setRefundFuitionFee(Double refundFuitionFee) {
          this.refundFuitionFee = refundFuitionFee ;
    }
	 public Double getRefundFuitionFee(){
        return refundFuitionFee;
    }
	public void setRefundReason(String refundReason) {
        this.refundReason = refundReason == null ? null : refundReason.trim();
    }
	 public String getRefundReason(){
        return refundReason;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	 public void setPeriods(Double periods) {
          this.periods = periods ;
    }
	 public Double getPeriods(){
        return periods;
    }
	public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus == null ? null : approvalStatus.trim();
    }
	 public String getApprovalStatus(){
        return approvalStatus;
    }
	public void setApprover(String approver) {
        this.approver = approver == null ? null : approver.trim();
    }
	 public String getApprover(){
        return approver;
    }
	 public void setApproverTime(Date approverTime) {
          this.approverTime = approverTime ;
    }
	 public Date getApproverTime(){
        return approverTime;
    }
	public String getOld_prorduct_id() {
		return old_prorduct_id;
	}
	public void setOld_prorduct_id(String old_prorduct_id) {
		this.old_prorduct_id = old_prorduct_id;
	}
	public String getNew_prorduct_id() {
		return new_prorduct_id;
	}
	public void setNew_prorduct_id(String new_prorduct_id) {
		this.new_prorduct_id = new_prorduct_id;
	}
}