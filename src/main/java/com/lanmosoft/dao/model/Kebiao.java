package com.lanmosoft.dao.model;

import java.util.List;
import java.util.ArrayList;
/**
 * 统计课表
 * <AUTHOR>
 *
 */
public class Kebiao {
	
	private String chengshi;//城市
	private String jiaoxuedian;//教学点
	private String riqi;//日期
	private String startTime;//开始时间
	private String endTime;//结束时间
	private String xueshengC;//学生中文名
	private String xueshengE;//学生英文名
	private String laoshiC;//老师中文名
	private String laoshiE;//老师英文名
	private String kecheng;//课程
	private String shiduan;//时段
	private String xingqi;//星期
	
	private String jiaoshi;//教室
	private String keshi;//课时
	private String shangkefangshi;//上课方式
	private String chuqin;//出勤
	private String zhuangtai;//状态
	private String xuehao;//学号
	private String id;
	private String courseType;//排课类型
	private String qiandao;//签到状态
	private String status;//课程状态
	private String time;//课时
	private String timezone;
	private String displayTz;
	private String riqiEnd;
	private String flag;
	private String zhujiao;
	private List<Filerecord> list=new ArrayList<Filerecord>();
	private List<Teacher_timezone> listTz;
	private List<Student_timezone> listTzStu;
	
	//private List<Teacher_timezone> listTz=new ArrayList<Teacher_timezone>();
	
	public String getChengshi() {
		return chengshi;
	}
	public void setChengshi(String chengshi) {
		this.chengshi = chengshi;
	}
	public String getJiaoxuedian() {
		return jiaoxuedian;
	}
	public void setJiaoxuedian(String jiaoxuedian) {
		this.jiaoxuedian = jiaoxuedian;
	}
	public String getRiqi() {
		return riqi;
	}
	public void setRiqi(String riqi) {
		this.riqi = riqi;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getXueshengC() {
		return xueshengC;
	}
	public void setXueshengC(String xueshengC) {
		this.xueshengC = xueshengC;
	}
	public String getXueshengE() {
		return xueshengE;
	}
	public void setXueshengE(String xueshengE) {
		this.xueshengE = xueshengE;
	}
	public String getLaoshiC() {
		return laoshiC;
	}
	public void setLaoshiC(String laoshiC) {
		this.laoshiC = laoshiC;
	}
	public String getLaoshiE() {
		return laoshiE;
	}
	public void setLaoshiE(String laoshiE) {
		this.laoshiE = laoshiE;
	}
	public String getKecheng() {
		return kecheng;
	}
	public void setKecheng(String kecheng) {
		this.kecheng = kecheng;
	}
	public String getShiduan() {
		return shiduan;
	}
	public void setShiduan(String shiduan) {
		this.shiduan = shiduan;
	}
	public String getXingqi() {
		return xingqi;
	}
	public void setXingqi(String xingqi) {
		this.xingqi = xingqi;
	}
	public String getJiaoshi() {
		return jiaoshi;
	}
	public void setJiaoshi(String jiaoshi) {
		this.jiaoshi = jiaoshi;
	}
	public String getKeshi() {
		return keshi;
	}
	public void setKeshi(String keshi) {
		this.keshi = keshi;
	}
	public String getShangkefangshi() {
		return shangkefangshi;
	}
	public void setShangkefangshi(String shangkefangshi) {
		this.shangkefangshi = shangkefangshi;
	}
	public String getChuqin() {
		return chuqin;
	}
	public void setChuqin(String chuqin) {
		this.chuqin = chuqin;
	}
	public String getZhuangtai() {
		return zhuangtai;
	}
	public void setZhuangtai(String zhuangtai) {
		this.zhuangtai = zhuangtai;
	}
	public String getXuehao() {
		return xuehao;
	}
	public void setXuehao(String xuehao) {
		this.xuehao = xuehao;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public List<Filerecord> getList() {
		return list;
	}
	public void setList(List<Filerecord> list) {
		this.list = list;
	}
	public String getCourseType() {
		return courseType;
	}
	public void setCourseType(String courseType) {
		this.courseType = courseType;
	}
	public String getQiandao() {
		return qiandao;
	}
	public void setQiandao(String qiandao) {
		this.qiandao = qiandao;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	public String getTimezone() {
		return timezone;
	}
	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}
	public String getDisplayTz() {
		return displayTz;
	}
	public void setDisplayTz(String displayTz) {
		this.displayTz = displayTz;
	}
	public String getRiqiEnd() {
		return riqiEnd;
	}
	public void setRiqiEnd(String riqiEnd) {
		this.riqiEnd = riqiEnd;
	}
	public String getFlag() {
		return flag;
	}
	public void setFlag(String flag) {
		this.flag = flag;
	}
	public String getZhujiao() {
		return zhujiao;
	}
	public void setZhujiao(String zhujiao) {
		this.zhujiao = zhujiao;
	}
	public List<Teacher_timezone> getListTz() {
		return listTz;
	}
	public void setListTz(List<Teacher_timezone> listTz) {
		this.listTz = listTz;
	}
	public List<Student_timezone> getListTzStu() {
		return listTzStu;
	}
	public void setListTzStu(List<Student_timezone> listTzStu) {
		this.listTzStu = listTzStu;
	}
	

}
