package com.lanmosoft.dao.model;
/**
 * 授权表
 * <AUTHOR>
 *
 */
public class Permission {
	 private String id;//ID
	 private String position_id;//岗位id
	 private String position_name;//岗位名称
	 private String function_id;//功能点id
	 private String function_name;//功能点名称
	 private String function_type;//功能点类型
	 private String firstModule;//所属一级模块
	 private String secondModule;//所二级属模块
	 private String data_role;//数据角色权限
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setPosition_id(String position_id) {
        this.position_id = position_id == null ? null : position_id.trim();
    }
	 public String getPosition_id(){
        return position_id;
    }
	public void setPosition_name(String position_name) {
        this.position_name = position_name == null ? null : position_name.trim();
    }
	 public String getPosition_name(){
        return position_name;
    }
	public void setFunction_id(String function_id) {
        this.function_id = function_id == null ? null : function_id.trim();
    }
	 public String getFunction_id(){
        return function_id;
    }
	public void setFunction_name(String function_name) {
        this.function_name = function_name == null ? null : function_name.trim();
    }
	 public String getFunction_name(){
        return function_name;
    }
	public void setFunction_type(String function_type) {
        this.function_type = function_type == null ? null : function_type.trim();
    }
	 public String getFunction_type(){
        return function_type;
    }
	public void setFirstModule(String firstModule) {
        this.firstModule = firstModule == null ? null : firstModule.trim();
    }
	 public String getFirstModule(){
        return firstModule;
    }
	public void setSecondModule(String secondModule) {
        this.secondModule = secondModule == null ? null : secondModule.trim();
    }
	 public String getSecondModule(){
        return secondModule;
    }
	public void setData_role(String data_role) {
        this.data_role = data_role == null ? null : data_role.trim();
    }
	 public String getData_role(){
        return data_role;
    }
}