package com.lanmosoft.dao.model;
import java.util.Date;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;

import javax.persistence.criteria.CriteriaBuilder;

/**
 * view_coures_teacher
 * <AUTHOR>
 *
 */
public class View_coures_teacher {
	 private String id;//id
	 private String student_id;//student_id
    private String accountId;
	 private String course_id;//course_id
	 private String course_name;//course_name
	 private String gradeCategory_id;//gradeCategory_id
	 private String gradeCategory_name;//gradeCategory_name
	 private String grade_id;//grade_id
	 private String grade_name;//grade_name
	 private String level;//level
	 private String teacher_id;//teacher_id
	 private String teacher_chineseName;//teacher_chineseName
	 private String teacher_englishName;//teacher_englishName
	 private String teachingWay;//teachingWay
	 private Date startDate;//startDate
	 private Date endDate;//endDate
	 private Integer units;//units
	 private String status;//status
	 private String BUType;
	 private String teachingWayName;

     private Double sumOBT;
     private Double sumOBTYh;
     private Double sumOBTYp;

    public void setSumOBT(Double sumOBT) {
        this.sumOBT = sumOBT ;
    }
    public Double getSumOBT(){
        return sumOBT;
    }

    public void setSumOBTYh(Double sumOBTYh) {
        this.sumOBTYh = sumOBTYh ;
    }
    public Double getSumOBTYh(){
        return sumOBTYh;
    }

    public void setSumOBTYp(Double sumOBTYp) {
        this.sumOBTYp = sumOBTYp ;
    }
    public Double getSumOBTYp(){
        return sumOBTYp;
    }
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
    public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }
    public String getAccountId(){
        return accountId;
    }
	public void setCourse_id(String course_id) {
        this.course_id = course_id == null ? null : course_id.trim();
    }
	 public String getCourse_id(){
        return course_id;
    }
	public void setCourse_name(String course_name) {
        this.course_name = course_name == null ? null : course_name.trim();
    }
	 public String getCourse_name(){
        return course_name;
    }
	public void setGradeCategory_id(String gradeCategory_id) {
        this.gradeCategory_id = gradeCategory_id == null ? null : gradeCategory_id.trim();
    }
	 public String getGradeCategory_id(){
        return gradeCategory_id;
    }
	public void setGradeCategory_name(String gradeCategory_name) {
        this.gradeCategory_name = gradeCategory_name == null ? null : gradeCategory_name.trim();
    }
	 public String getGradeCategory_name(){
        return gradeCategory_name;
    }
	public void setGrade_id(String grade_id) {
        this.grade_id = grade_id == null ? null : grade_id.trim();
    }
	 public String getGrade_id(){
        return grade_id;
    }
	public void setGrade_name(String grade_name) {
        this.grade_name = grade_name == null ? null : grade_name.trim();
    }
	 public String getGrade_name(){
        return grade_name;
    }
	public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }
	 public String getLevel(){
        return level;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setTeacher_chineseName(String teacher_chineseName) {
        this.teacher_chineseName = teacher_chineseName == null ? null : teacher_chineseName.trim();
    }
	 public String getTeacher_chineseName(){
        return teacher_chineseName;
    }
	public void setTeacher_englishName(String teacher_englishName) {
        this.teacher_englishName = teacher_englishName == null ? null : teacher_englishName.trim();
    }
	 public String getTeacher_englishName(){
        return teacher_englishName;
    }
	public void setTeachingWay(String teachingWay) {
        this.teachingWay = teachingWay == null ? null : teachingWay.trim();
    }
	 public String getTeachingWay(){
        return teachingWay;
    }
	 public void setStartDate(Date startDate) {
          this.startDate = startDate ;
    }
	 public Date getStartDate(){
        return startDate;
    }
	 public void setEndDate(Date endDate) {
          this.endDate = endDate ;
    }
	 public Date getEndDate(){
        return endDate;
    }
	 public void setUnits(Integer units) {
          this.units = units ;
    }
	 public Integer getUnits(){
        return units;
    }
	public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }
	 public String getStatus(){
        return status;
    }
	public String getBUType() {
		return BUType;
	}
	public void setBUType(String BUType) {
		this.BUType =BUType == null ? null : BUType.trim();
	}
	public String getTeachingWayName() {
		return teachingWayName;
	}
	public void setTeachingWayName(String teachingWayName) {
		this.teachingWayName = teachingWayName== null ? null : teachingWayName.trim();
	}
}