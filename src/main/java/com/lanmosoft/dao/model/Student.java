package com.lanmosoft.dao.model;

import com.google.gson.annotations.Expose;

import java.io.Serializable;
import java.util.Date;

/**
 * student
 *
 * <AUTHOR>
 */
public class Student implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1835620552417119638L;

    private String id;//id
    private String code;//code
    @Expose
    private String crmId;//crmId
    @Expose
    private String accountId;//accountId
    @Expose
    private String chineseName;//chineseName
    @Expose
    private String englishName;//englishName
    @Expose
    private String gender;//gender
    private Integer age;//age
    private Date birthday;//birthday
    @Expose
    private String nationality;//nationality
    @Expose
    private String mobile;//mobile
    @Expose
    private String email;//email
    @Expose
    private String wechat;//wechat

    private String city_id;//city_id
    private String city_name;// 城市名称
    private String city_code;// 城市编码
    private String zone_id;//zone_id
    private String zone_name;// 校区名称
    private String zone_code;// 校区编码
    private String school;//school
    private String grade;//grade
    private String sms;//短信
    @Expose
    private String address;//address
    @Expose
    private String teReferral;//teReferral
    @Expose
    private String relationshipTypeP;//relationshipTypeP
    @Expose
    private String chineseNameP;//chineseNameP
    @Expose
    private String contactNumberP;//contractNumberP
    @Expose
    private String companyP;//companyP
    @Expose
    private String occupationP;//occupationP
    @Expose
    private String wechatP;//wechatP

    private String IDNoP;//IDNoP

    private String emailP;//emailP
    @Expose
    private String relationshipTypeS;//relationshipTypeS
    @Expose
    private String chineseNameS;//chineseNameS
    @Expose
    private String contactNumberS;//contractNumberS
    @Expose
    private String companyS;//companyS
    @Expose
    private String occupationS;//occupationS
    @Expose
    private String wechatS;//wechatS

    private String IDNoS;//IDNoS

    private String emailS;//emailS

    private String source;//source
    @Expose
    private String referral;//referral
    @Expose
    private String contractOwnerId;//contractOwnerId
    @Expose
    private String ownerName;//咨询顾问名称
    @Expose
    private String tppId;//tppId

    private String tutorId;//tutorId
    private String tutor_chineseName;//助教中文名
    private String tutor_englishName;//助教英文名
    @Expose
    private String contractType;//contractType

    private String isOC;//isOC
    private Date startTime;//startTime
    private Date endTime;//endTime
    private String notes;//notes
    private String status;//status
    private Date freezeDate;//freezeDate
    private Date thawedDate;//解冻日期
    private Date thawedDateBegin;//冻结开始日期
    private String creator_id;//创建者ID
    private Date createTime;//创建时间
    private String lastModifier_id;//最后修改者
    private Date lastModifiedTime;//最后修改时间
    private String delStatus;//delStatus
    private String isGiven;//是否已赠送推荐人
    @Expose
    private String location;//招生办公室

    private String BUType;

    private String secondarEmail;//备用邮箱
    private String secondarMobile;//备用手机
    @Expose
    private String ownerZone;//咨询顾问校区
    private String ownerCity;//咨询顾问城市
    private String allowOC;

    private String classscheduleStatus;//学生排课状态 0:Active 1:Inactive 2:Half-closed 3:Closed

    private String type;

    private String consumedClass;
    private String totalHours;

    @Expose
    private String onlinete_id;
    @Expose
    private String tencentid;

    public String getTencentid() {
        return tencentid;
    }

    public void setTencentid(String tencentid) {
        this.tencentid = tencentid;
    }

    public String getOnlinete_id() {
        return onlinete_id;
    }

    public void setOnlinete_id(String onlinete_id) {
        this.onlinete_id = onlinete_id;
    }

    public String getConsumedClass() {
        return consumedClass;
    }

    public void setConsumedClass(String consumedClass) {
        this.consumedClass = consumedClass;
    }

    public String getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(String totalHours) {
        this.totalHours = totalHours;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getClassscheduleStatus() {
        return classscheduleStatus;
    }

    public void setClassscheduleStatus(String classscheduleStatus) {
        this.classscheduleStatus = classscheduleStatus;
    }

    public String getOwnerCity() {
        return ownerCity;
    }

    public void setOwnerCity(String ownerCity) {
        this.ownerCity = ownerCity;
    }

    public String getOwnerZone() {
        return ownerZone;
    }

    public void setOwnerZone(String ownerZone) {
        this.ownerZone = ownerZone;
    }

    public String getSecondarMobile() {
        return secondarMobile;
    }

    public void setSecondarMobile(String secondarMobile) {
        this.secondarMobile = secondarMobile;
    }

    public String getSecondarEmail() {
        return secondarEmail;
    }

    public void setSecondarEmail(String secondarEmail) {
        this.secondarEmail = secondarEmail;
    }

    /*视图字段*/

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getId() {
        return id;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCrmId(String crmId) {
        this.crmId = crmId == null ? null : crmId.trim();
    }

    public String getCrmId() {
        return crmId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }

    public String getAccountId() {
        return accountId;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName == null ? null : englishName.trim();
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setGender(String gender) {
        this.gender = gender == null ? null : gender.trim();
    }

    public String getGender() {
        return gender;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getAge() {
        return age;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality == null ? null : nationality.trim();
    }

    public String getNationality() {
        return nationality;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat == null ? null : wechat.trim();
    }

    public String getWechat() {
        return wechat;
    }

    public void setCity_id(String city_id) {
        this.city_id = city_id == null ? null : city_id.trim();
    }

    public String getCity_id() {
        return city_id;
    }

    public String getCity_name() {
        return city_name;
    }

    public void setCity_name(String city_name) {
        this.city_name = city_name;
    }

    public String getCity_code() {
        return city_code;
    }

    public void setCity_code(String city_code) {
        this.city_code = city_code;
    }

    public void setZone_id(String zone_id) {
        this.zone_id = zone_id == null ? null : zone_id.trim();
    }

    public String getZone_id() {
        return zone_id;
    }

    public String getZone_name() {
        return zone_name;
    }

    public void setZone_name(String zone_name) {
        this.zone_name = zone_name;
    }

    public String getZone_code() {
        return zone_code;
    }

    public void setZone_code(String zone_code) {
        this.zone_code = zone_code;
    }

    public void setSchool(String school) {
        this.school = school == null ? null : school.trim();
    }

    public String getSchool() {
        return school;
    }

    public void setGrade(String grade) {
        this.grade = grade == null ? null : grade.trim();
    }

    public String getGrade() {
        return grade;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setTeReferral(String teReferral) {
        this.teReferral = teReferral == null ? null : teReferral.trim();
    }

    public String getTeReferral() {
        return teReferral;
    }

    public void setRelationshipTypeP(String relationshipTypeP) {
        this.relationshipTypeP = relationshipTypeP == null ? null : relationshipTypeP.trim();
    }

    public String getRelationshipTypeP() {
        return relationshipTypeP;
    }

    public void setChineseNameP(String chineseNameP) {
        this.chineseNameP = chineseNameP == null ? null : chineseNameP.trim();
    }

    public String getChineseNameP() {
        return chineseNameP;
    }

    public void setContactNumberP(String contactNumberP) {
        this.contactNumberP = contactNumberP == null ? null : contactNumberP.trim();
    }

    public String getContactNumberP() {
        return contactNumberP;
    }

    public void setCompanyP(String companyP) {
        this.companyP = companyP == null ? null : companyP.trim();
    }

    public String getCompanyP() {
        return companyP;
    }

    public void setOccupationP(String occupationP) {
        this.occupationP = occupationP == null ? null : occupationP.trim();
    }

    public String getOccupationP() {
        return occupationP;
    }

    public void setWechatP(String wechatP) {
        this.wechatP = wechatP == null ? null : wechatP.trim();
    }

    public String getWechatP() {
        return wechatP;
    }

    public void setIDNoP(String IDNoP) {
        this.IDNoP = IDNoP == null ? null : IDNoP.trim();
    }

    public String getIDNoP() {
        return IDNoP;
    }

    public void setEmailP(String emailP) {
        this.emailP = emailP == null ? null : emailP.trim();
    }

    public String getEmailP() {
        return emailP;
    }

    public void setRelationshipTypeS(String relationshipTypeS) {
        this.relationshipTypeS = relationshipTypeS == null ? null : relationshipTypeS.trim();
    }

    public String getRelationshipTypeS() {
        return relationshipTypeS;
    }

    public void setChineseNameS(String chineseNameS) {
        this.chineseNameS = chineseNameS == null ? null : chineseNameS.trim();
    }

    public String getChineseNameS() {
        return chineseNameS;
    }

    public void setContactNumberS(String contactNumberS) {
        this.contactNumberS = contactNumberS == null ? null : contactNumberS.trim();
    }

    public String getContactNumberS() {
        return contactNumberS;
    }

    public void setCompanyS(String companyS) {
        this.companyS = companyS == null ? null : companyS.trim();
    }

    public String getCompanyS() {
        return companyS;
    }

    public void setOccupationS(String occupationS) {
        this.occupationS = occupationS == null ? null : occupationS.trim();
    }

    public String getOccupationS() {
        return occupationS;
    }

    public void setWechatS(String wechatS) {
        this.wechatS = wechatS == null ? null : wechatS.trim();
    }

    public String getWechatS() {
        return wechatS;
    }

    public void setIDNoS(String IDNoS) {
        this.IDNoS = IDNoS == null ? null : IDNoS.trim();
    }

    public String getIDNoS() {
        return IDNoS;
    }

    public void setEmailS(String emailS) {
        this.emailS = emailS == null ? null : emailS.trim();
    }

    public String getEmailS() {
        return emailS;
    }

    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    public String getSource() {
        return source;
    }

    public void setReferral(String referral) {
        this.referral = referral == null ? null : referral.trim();
    }

    public String getReferral() {
        return referral;
    }

    public void setContractOwnerId(String contractOwnerId) {
        this.contractOwnerId = contractOwnerId == null ? null : contractOwnerId.trim();
    }

    public String getContractOwnerId() {
        return contractOwnerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public void setTppId(String tppId) {
        this.tppId = tppId == null ? null : tppId.trim();
    }

    public String getTppId() {
        return tppId;
    }

    public void setTutorId(String tutorId) {
        this.tutorId = tutorId == null ? null : tutorId.trim();
    }

    public String getTutorId() {
        return tutorId;
    }

    public String getTutor_chineseName() {
        return tutor_chineseName;
    }

    public void setTutor_chineseName(String tutor_chineseName) {
        this.tutor_chineseName = tutor_chineseName;
    }

    public String getTutor_englishName() {
        return tutor_englishName;
    }

    public void setTutor_englishName(String tutor_englishName) {
        this.tutor_englishName = tutor_englishName;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    public String getContractType() {
        return contractType;
    }

    public void setIsOC(String isOC) {
        this.isOC = isOC == null ? null : isOC.trim();
    }

    public String getIsOC() {
        return isOC;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setNotes(String notes) {
        this.notes = notes == null ? null : notes.trim();
    }

    public String getNotes() {
        return notes;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setFreezeDate(Date freezeDate) {
        this.freezeDate = freezeDate;
    }

    public Date getFreezeDate() {
        return freezeDate;
    }

    public Date getThawedDate() {
        return thawedDate;
    }

    public void setThawedDate(Date thawedDate) {
        this.thawedDate = thawedDate;
    }

    public Date getThawedDateBegin() {
        return thawedDateBegin;
    }

    public void setThawedDateBegin(Date thawedDateBegin) {
        this.thawedDateBegin = thawedDateBegin;
    }

    public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }

    public String getCreator_id() {
        return creator_id;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }

    public String getLastModifier_id() {
        return lastModifier_id;
    }

    public void setLastModifiedTime(Date lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public Date getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }

    public String getDelStatus() {
        return delStatus;
    }

    public void setIsGiven(String isGiven) {
        this.isGiven = isGiven == null ? null : isGiven.trim();
    }

    public String getIsGiven() {
        return isGiven;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getBUType() {
        return BUType;
    }

    public void setBUType(String BUType) {
        this.BUType = BUType;
    }

    public String getAllowOC() {
        return allowOC;
    }

    public void setAllowOC(String allowOC) {
        this.allowOC = allowOC;
    }
}