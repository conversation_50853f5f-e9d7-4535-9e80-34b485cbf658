package com.lanmosoft.dao.model;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;
/**
 * 教师可排课时间
 * <AUTHOR>
 *
 */
public class Teacher_freeTime {
	 private Integer id;//ID
	 private String teacher_id;//教师ID
	 private Date freeDate;//日期
	 private String startTime;//开始时间
	 private String endTime;//结束时间
	 private String teachingWay;//上课方式
	 private String isFree;//是否可排课
	 private String freeTimeCode;//可排课时间编码
	
	 private String studentNameE;//学生英文名 非数据库字段，只做封装
	 private String classScheduleId;//课表Id 非数据库字段，只做封装
	 
	 //老师的排课时间段信息
	 private Teacher_freeTime lis;
	 private String start;
	 private String end;
	 private String length;
	 private boolean  isPai;
     //可排课的学生信息
	 private List<Student> student_list;
	 //排课老师和学生信息
	 private List<View_paike_class> listClass;//
		 
		 
	 private String kaishiTime;
	 private String jieshuTime;
	 
	 private String BUType;
	 
	 private List<Map<String,String>> textList;
	 
	 private String freeDateStr;
	 
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	 public void setFreeDate(Date freeDate) {
          this.freeDate = freeDate ;
    }
	 public Date getFreeDate(){
        return freeDate;
    }
	public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }
	 public String getStartTime(){
        return startTime;
    }
	public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }
	 public String getEndTime(){
        return endTime;
    }
	public void setTeachingWay(String teachingWay) {
        this.teachingWay = teachingWay == null ? null : teachingWay.trim();
    }
	 public String getTeachingWay(){
        return teachingWay;
    }
	public void setIsFree(String isFree) {
        this.isFree = isFree == null ? null : isFree.trim();
    }
	 public String getIsFree(){
        return isFree;
    }
	public void setFreeTimeCode(String freeTimeCode) {
        this.freeTimeCode = freeTimeCode == null ? null : freeTimeCode.trim();
    }
	 public String getFreeTimeCode(){
        return freeTimeCode;
    }
	public String getStudentNameE() {
		return studentNameE;
	}
	public void setStudentNameE(String studentNameE) {
		this.studentNameE = studentNameE;
	}
	public String getClassScheduleId() {
		return classScheduleId;
	}
	public void setClassScheduleId(String classScheduleId) {
		this.classScheduleId = classScheduleId;
	}
	public Teacher_freeTime getLis() {
		return lis;
	}
	public void setLis(Teacher_freeTime lis) {
		this.lis = lis;
	}
	public String getStart() {
		return start;
	}
	public void setStart(String start) {
		this.start = start;
	}
	public String getEnd() {
		return end;
	}
	public void setEnd(String end) {
		this.end = end;
	}
	public String getLength() {
		return length;
	}
	public void setLength(String length) {
		this.length = length;
	}
	public boolean isPai() {
		return isPai;
	}
	public void setPai(boolean isPai) {
		this.isPai = isPai;
	}
	public List<Student> getStudent_list() {
		return student_list;
	}
	public void setStudent_list(List<Student> student_list) {
		this.student_list = student_list;
	}
	public List<View_paike_class> getListClass() {
		return listClass;
	}
	public void setListClass(List<View_paike_class> listClass) {
		this.listClass = listClass;
	}
	public String getKaishiTime() {
		return kaishiTime;
	}
	public void setKaishiTime(String kaishiTime) {
		this.kaishiTime = kaishiTime;
	}
	public String getJieshuTime() {
		return jieshuTime;
	}
	public void setJieshuTime(String jieshuTime) {
		this.jieshuTime = jieshuTime;
	}
	public List<Map<String, String>> getTextList() {
		return textList;
	}
	public void setTextList(List<Map<String, String>> textList) {
		this.textList = textList;
	}
	public String getBUType() {
		return BUType;
	}
	public void setBUType(String BUType) {
		this.BUType = BUType;
	}
	public String getFreeDateStr() {
		return freeDateStr;
	}
	public void setFreeDateStr(String freeDateStr) {
		this.freeDateStr = freeDateStr;
	}
	
}