package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 签到表视图
 * <AUTHOR>
 *
 */
public class View_attendanceBook {
	 private String classSchedule_id;//课表ID
	 private String courseType;//课程类型
	 private String student_id;//学员
	 private String course_id;//课程
	 private Date scheduledDate;//上课日期
	 private String startTime;//上课开始时间
	 private String endTime;//上课结束时间
	 private String time;//课时数
	 private String freeTimeCode;//排课时间编码
	 private String teacher_id;//教师
	 private String teachingWay;//上课方式
	 private String classroom_id;//教室
	 private String description;//删除原因
	 private String notes;//备注
	 private String leaveState;//请假状态
	 private String isAuto;//是否为自动排课
	 private Date realDate;//实际上课日期
	 private String realStartTime;//实际开始时间
	 private String realEndTime;//实际结束时间
	 private String realTime;//实际课时数
	 private String attendanceBook_id;//签到ID
	 private String attendanceStatus;//出勤状态
	 private String reason;//修改原因
	 private String approvalStatus;//审批状态
	 private String approver;//审批人
	 private String creator_id;//创建者ID
	 private Date createTime;//创建时间
	 private String lastModifier_id;//最后修改者
	 private Date lastModifiedTime;//最后修改时间
	 private String delStatus;//是否禁用
	 private String contract_id;//合同
	 private String lockStatus;//锁定状态
	 private String s_chineseName;//学生中文姓名
	 private String s_englishName;//学生英文姓名
	 private String zone_id;//校区ID
	 private String t_chineseName;//教师中文姓名
	 private String t_englishName;//教师英文姓名
	 private String c_name;//课程名称
	 private String z_name;//校区名称
	 private String cr_name;//教室名称
	 private String tutorId;//助教Id
	private String tutorenglishName;
	private String status;//课程状态
	 
	 private String consumedClass;//
	 private String givenClass;//
	 private String wage;//
	 private String wageDeduction2;//
	 private String wageDeduction;//
	 private String BUType;

	 private String riqiEnd;//
	 private String teachingWayName;//上课方式
	 private String teacherType;//老师状态 全职 兼职 半全职
	private String islockDate;//是否锁定

	public String getIslockDate() {
		return islockDate;
	}

	public void setIslockDate(String islockDate) {
		this.islockDate = islockDate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTeacherType() {
		return teacherType;
	}

	public void setTeacherType(String teacherType) {
		this.teacherType = teacherType;
	}

	public String getTeachingWayName() {
		return teachingWayName;
	}

	public void setTeachingWayName(String teachingWayName) {
		this.teachingWayName = teachingWayName;
	}

	public String getRiqiEnd() {
		return riqiEnd;
	}

	public void setRiqiEnd(String riqiEnd) {
		this.riqiEnd = riqiEnd;
	}

	public void setClassSchedule_id(String classSchedule_id) {
        this.classSchedule_id = classSchedule_id == null ? null : classSchedule_id.trim();
    }
	 public String getClassSchedule_id(){
        return classSchedule_id;
    }
	public void setCourseType(String courseType) {
        this.courseType = courseType == null ? null : courseType.trim();
    }
	 public String getCourseType(){
        return courseType;
    }
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
	public void setCourse_id(String course_id) {
        this.course_id = course_id == null ? null : course_id.trim();
    }
	 public String getCourse_id(){
        return course_id;
    }
	 public void setScheduledDate(Date scheduledDate) {
          this.scheduledDate = scheduledDate ;
    }
	 public Date getScheduledDate(){
        return scheduledDate;
    }
	public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }
	 public String getStartTime(){
        return startTime;
    }
	public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }
	 public String getEndTime(){
        return endTime;
    }
	public void setTime(String time) {
        this.time = time == null ? null : time.trim();
    }
	 public String getTime(){
        return time;
    }
	public void setFreeTimeCode(String freeTimeCode) {
        this.freeTimeCode = freeTimeCode == null ? null : freeTimeCode.trim();
    }
	 public String getFreeTimeCode(){
        return freeTimeCode;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setTeachingWay(String teachingWay) {
        this.teachingWay = teachingWay == null ? null : teachingWay.trim();
    }
	 public String getTeachingWay(){
        return teachingWay;
    }
	public void setClassroom_id(String classroom_id) {
        this.classroom_id = classroom_id == null ? null : classroom_id.trim();
    }
	 public String getClassroom_id(){
        return classroom_id;
    }
	public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }
	 public String getDescription(){
        return description;
    }
	public void setNotes(String notes) {
        this.notes = notes == null ? null : notes.trim();
    }
	 public String getNotes(){
        return notes;
    }
	public void setLeaveState(String leaveState) {
        this.leaveState = leaveState == null ? null : leaveState.trim();
    }
	 public String getLeaveState(){
        return leaveState;
    }
	public void setIsAuto(String isAuto) {
        this.isAuto = isAuto == null ? null : isAuto.trim();
    }
	 public String getIsAuto(){
        return isAuto;
    }
	 public void setRealDate(Date realDate) {
          this.realDate = realDate ;
    }
	 public Date getRealDate(){
        return realDate;
    }
	public void setRealStartTime(String realStartTime) {
        this.realStartTime = realStartTime == null ? null : realStartTime.trim();
    }
	 public String getRealStartTime(){
        return realStartTime;
    }
	public void setRealEndTime(String realEndTime) {
        this.realEndTime = realEndTime == null ? null : realEndTime.trim();
    }
	 public String getRealEndTime(){
        return realEndTime;
    }
	public void setRealTime(String realTime) {
        this.realTime = realTime == null ? null : realTime.trim();
    }
	 public String getRealTime(){
        return realTime;
    }
	public void setAttendanceBook_id(String attendanceBook_id) {
        this.attendanceBook_id = attendanceBook_id == null ? null : attendanceBook_id.trim();
    }
	 public String getAttendanceBook_id(){
        return attendanceBook_id;
    }
	public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus == null ? null : attendanceStatus.trim();
    }
	 public String getAttendanceStatus(){
        return attendanceStatus;
    }
	public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }
	 public String getReason(){
        return reason;
    }
	public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus == null ? null : approvalStatus.trim();
    }
	 public String getApprovalStatus(){
        return approvalStatus;
    }
	public void setApprover(String approver) {
        this.approver = approver == null ? null : approver.trim();
    }
	 public String getApprover(){
        return approver;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	public void setContract_id(String contract_id) {
        this.contract_id = contract_id == null ? null : contract_id.trim();
    }
	 public String getContract_id(){
        return contract_id;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
	public void setS_chineseName(String s_chineseName) {
        this.s_chineseName = s_chineseName == null ? null : s_chineseName.trim();
    }
	 public String getS_chineseName(){
        return s_chineseName;
    }
	public void setS_englishName(String s_englishName) {
        this.s_englishName = s_englishName == null ? null : s_englishName.trim();
    }
	 public String getS_englishName(){
        return s_englishName;
    }
	public void setZone_id(String zone_id) {
        this.zone_id = zone_id == null ? null : zone_id.trim();
    }
	 public String getZone_id(){
        return zone_id;
    }
	public void setT_chineseName(String t_chineseName) {
        this.t_chineseName = t_chineseName == null ? null : t_chineseName.trim();
    }
	 public String getT_chineseName(){
        return t_chineseName;
    }
	public void setT_englishName(String t_englishName) {
        this.t_englishName = t_englishName == null ? null : t_englishName.trim();
    }
	 public String getT_englishName(){
        return t_englishName;
    }
	public void setC_name(String c_name) {
        this.c_name = c_name == null ? null : c_name.trim();
    }
	 public String getC_name(){
        return c_name;
    }
	public void setZ_name(String z_name) {
        this.z_name = z_name == null ? null : z_name.trim();
    }
	 public String getZ_name(){
        return z_name;
    }
	public void setCr_name(String cr_name) {
        this.cr_name = cr_name == null ? null : cr_name.trim();
    }
	 public String getCr_name(){
        return cr_name;
    }
	public String getTutorId() {
		return tutorId;
	}
	public void setTutorId(String tutorId) {
		this.tutorId = tutorId;
	}
	public String getConsumedClass() {
		return consumedClass;
	}
	public void setConsumedClass(String consumedClass) {
		this.consumedClass = consumedClass;
	}
	public String getGivenClass() {
		return givenClass;
	}
	public void setGivenClass(String givenClass) {
		this.givenClass = givenClass;
	}
	public String getWage() {
		return wage;
	}
	public void setWage(String wage) {
		this.wage = wage;
	}
	public String getWageDeduction2() {
		return wageDeduction2;
	}
	public void setWageDeduction2(String wageDeduction2) {
		this.wageDeduction2 = wageDeduction2;
	}
	public String getWageDeduction() {
		return wageDeduction;
	}

	public void setWageDeduction(String wageDeduction) {
		this.wageDeduction = wageDeduction;
	}

	public String getBUType() {
		return BUType;
	}

	public void setBUType(String BUType) {
		this.BUType = BUType;
	}

	public String getTutorenglishName() {
		return tutorenglishName;
	}

	public void setTutorenglishName(String tutorenglishName) {
		this.tutorenglishName = tutorenglishName;
	}
}