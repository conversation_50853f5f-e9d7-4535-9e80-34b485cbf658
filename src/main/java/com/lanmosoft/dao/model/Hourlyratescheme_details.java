package com.lanmosoft.dao.model;

import java.util.Date;

public class Hourlyratescheme_details {
    private String id;

    private String scheme_id;

    private String intervalType;

    private int periodLowerLimit;

    private int periodUpperLimit;

    private Double unitPrice;

    private String currency_id;

    private Date startDate;

    private Date endDate;
    private String teachingWay_id;
    private String teachingWayName;
    private String currencyName;
    
    private String schemeNo;
    private Teacher_hourlyratescheme teacher_hourlyratescheme=new Teacher_hourlyratescheme();
    private String teacher_id;
    private String BUType;
    private String teacher_course_teachingway_id;
    private String teachingWay_ids;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getScheme_id() {
        return scheme_id;
    }

    public void setScheme_id(String scheme_id) {
        this.scheme_id = scheme_id == null ? null : scheme_id.trim();
    }

    public String getIntervalType() {
        return intervalType;
    }

    public void setIntervalType(String intervalType) {
        this.intervalType = intervalType == null ? null : intervalType.trim();
    }

    public int getPeriodLowerLimit() {
        return periodLowerLimit;
    }

    public void setPeriodLowerLimit(int periodLowerLimit) {
        this.periodLowerLimit = periodLowerLimit;
    }

    public int getPeriodUpperLimit() {
        return periodUpperLimit;
    }

    public void setPeriodUpperLimit(int periodUpperLimit) {
        this.periodUpperLimit = periodUpperLimit;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getCurrency_id() {
        return currency_id;
    }

    public void setCurrency_id(String currency_id) {
        this.currency_id = currency_id == null ? null : currency_id.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

	public String getTeachingWay_id() {
		return teachingWay_id;
	}

	public void setTeachingWay_id(String teachingWay_id) {
		this.teachingWay_id = teachingWay_id;
	}

	public String getTeachingWayName() {
		return teachingWayName;
	}

	public void setTeachingWayName(String teachingWayName) {
		this.teachingWayName = teachingWayName;
	}

	public String getCurrencyName() {
		return currencyName;
	}

	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}

	public String getSchemeNo() {
		return schemeNo;
	}

	public void setSchemeNo(String schemeNo) {
		 this.schemeNo = schemeNo == null ? null : schemeNo.trim();
	}

	public Teacher_hourlyratescheme getTeacher_hourlyratescheme() {
		return teacher_hourlyratescheme;
	}

	public void setTeacher_hourlyratescheme(Teacher_hourlyratescheme teacher_hourlyratescheme) {
		this.teacher_hourlyratescheme = teacher_hourlyratescheme;
	}

	public String getTeacher_id() {
		return teacher_id;
	}

	public void setTeacher_id(String teacher_id) {
		this.teacher_id = teacher_id;
	}

	public String getBUType() {
		return BUType;
	}

	public void setBUType(String bUType) {
		BUType = bUType;
	}

	public String getTeacher_course_teachingway_id() {
		return teacher_course_teachingway_id;
	}

	public void setTeacher_course_teachingway_id(String teacher_course_teachingway_id) {
		this.teacher_course_teachingway_id = teacher_course_teachingway_id;
	}

	public String getTeachingWay_ids() {
		return teachingWay_ids;
	}

	public void setTeachingWay_ids(String teachingWay_ids) {
		this.teachingWay_ids = teachingWay_ids;
	}
}