package com.lanmosoft.dao.model;
import java.util.Date;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;
/**
 * 签到表
 * <AUTHOR>
 *
 */
public class AttendanceBook {
	 private String id;//ID
	 private String classSchedule_id;//课表
	 private String attendanceStatus;//出勤状态
	 private String reason;//修改原因
	 private String approvalStatus;//审批状态
	 private String approver;//审批人
	 private String creator_id;//创建者ID
	 private Date createTime;//创建时间
	 private String lastModifier_id;//最后修改者
	 private Date lastModifiedTime;//最后修改时间
	 private String delStatus;//是否禁用
	 private String contract_id;//合同
	 private String lockStatus;//锁定状态
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setClassSchedule_id(String classSchedule_id) {
        this.classSchedule_id = classSchedule_id == null ? null : classSchedule_id.trim();
    }
	 public String getClassSchedule_id(){
        return classSchedule_id;
    }
	public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus == null ? null : attendanceStatus.trim();
    }
	 public String getAttendanceStatus(){
        return attendanceStatus;
    }
	public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }
	 public String getReason(){
        return reason;
    }
	public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus == null ? null : approvalStatus.trim();
    }
	 public String getApprovalStatus(){
        return approvalStatus;
    }
	public void setApprover(String approver) {
        this.approver = approver == null ? null : approver.trim();
    }
	 public String getApprover(){
        return approver;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	public void setContract_id(String contract_id) {
        this.contract_id = contract_id == null ? null : contract_id.trim();
    }
	 public String getContract_id(){
        return contract_id;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
}