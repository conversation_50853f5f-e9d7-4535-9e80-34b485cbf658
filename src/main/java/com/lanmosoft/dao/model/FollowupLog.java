package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 学员跟进
 * <AUTHOR>
 *
 */
public class FollowupLog {
	 private String id;//ID
	 private String student_id;//学员
	 private String followupWay;//跟进方式
	 private Date followupTime;//跟进时间
	 private Date nextFollowupTime;//下次跟进时间
	 private String followupContent;//跟进内容
	 private String creator_id;//创建者ID
	 private Date createTime;//创建时间
	 private String lastModifier_id;//最后修改者
	 private Date lastModifiedTime;//最后修改时间
	 private String delStatus;//是否禁用
	 
	 private String student_code;// 学号
	 private String student_chineseName;//中文姓名
	 private String student_englishName;//英文姓名
	 private String status;//上课状态
	 private String zone_id;//校区ID
	 private String zone_name;//校区
	 private String tutorId;//助教
	 private String tutor_englishName;//助教
	 private String BUType;
	private String classscheduleStatus;//状态
	 
	 private Student student;


	public String getClassscheduleStatus() {
		return classscheduleStatus;
	}

	public void setClassscheduleStatus(String classscheduleStatus) {
		this.classscheduleStatus = classscheduleStatus;
	}

	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
	public void setFollowupWay(String followupWay) {
        this.followupWay = followupWay == null ? null : followupWay.trim();
    }
	 public String getFollowupWay(){
        return followupWay;
    }
	 public void setFollowupTime(Date followupTime) {
          this.followupTime = followupTime ;
    }
	 public Date getFollowupTime(){
        return followupTime;
    }
	 public void setNextFollowupTime(Date nextFollowupTime) {
          this.nextFollowupTime = nextFollowupTime ;
    }
	 public Date getNextFollowupTime(){
        return nextFollowupTime;
    }
	public void setFollowupContent(String followupContent) {
        this.followupContent = followupContent == null ? null : followupContent.trim();
    }
	 public String getFollowupContent(){
        return followupContent;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
	public String getStudent_code() {
		return student_code;
	}
	public void setStudent_code(String student_code) {
		this.student_code = student_code;
	}
	public String getStudent_chineseName() {
		return student_chineseName;
	}
	public void setStudent_chineseName(String student_chineseName) {
		this.student_chineseName = student_chineseName;
	}
	public String getStudent_englishName() {
		return student_englishName;
	}
	public void setStudent_englishName(String student_englishName) {
		this.student_englishName = student_englishName;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getZone_id() {
		return zone_id;
	}
	public void setZone_id(String zone_id) {
		this.zone_id = zone_id;
	}
	public String getZone_name() {
		return zone_name;
	}
	public void setZone_name(String zone_name) {
		this.zone_name = zone_name;
	}
	public Student getStudent() {
		return student;
	}
	public void setStudent(Student student) {
		this.student = student;
	}
	public String getTutorId() {
		return tutorId;
	}
	public void setTutorId(String tutorId) {
		this.tutorId = tutorId;
	}
	public String getTutor_englishName() {
		return tutor_englishName;
	}
	public void setTutor_englishName(String tutor_englishName) {
		this.tutor_englishName = tutor_englishName;
	}
	public String getBUType() {
		return BUType;
    }
	public void setBUType(String BUType) {
		this.BUType = BUType== null ? null : BUType.trim();
    }
}