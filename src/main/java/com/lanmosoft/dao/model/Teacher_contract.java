package com.lanmosoft.dao.model;
import java.util.Date;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;
/**
 * 教师_合同
 * <AUTHOR>
 *
 */
public class Teacher_contract {
	 private String id;//ID
	 private String teacher_id;//教师ID
	 private Date startDate;//合同开始时间
	 private Date endDate;//合同结束时间
	 private String postType;//岗位类别
	 private String creator_id;//creator_id
	 private Date createTime;//createTime
	 private String lastModifier_id;//lastModifier_id
	 private Date lastModifiedTime;//lastModifiedTime
	 private String delStatus;//delStatus
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	 public void setStartDate(Date startDate) {
          this.startDate = startDate ;
    }
	 public Date getStartDate(){
        return startDate;
    }
	 public void setEndDate(Date endDate) {
          this.endDate = endDate ;
    }
	 public Date getEndDate(){
        return endDate;
    }
	public void setPostType(String postType) {
        this.postType = postType == null ? null : postType.trim();
    }
	 public String getPostType(){
        return postType;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
	public void setLastModifier_id(String lastModifier_id) {
        this.lastModifier_id = lastModifier_id == null ? null : lastModifier_id.trim();
    }
	 public String getLastModifier_id(){
        return lastModifier_id;
    }
	 public void setLastModifiedTime(Date lastModifiedTime) {
          this.lastModifiedTime = lastModifiedTime ;
    }
	 public Date getLastModifiedTime(){
        return lastModifiedTime;
    }
	public void setDelStatus(String delStatus) {
        this.delStatus = delStatus == null ? null : delStatus.trim();
    }
	 public String getDelStatus(){
        return delStatus;
    }
}