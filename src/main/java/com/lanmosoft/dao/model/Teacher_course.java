package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 教师可教授科目
 * <AUTHOR>
 *
 */
public class Teacher_course {
	 private String id;//id
	 private String teacher_id;//教师ID
	 private String course_id;//课程ID
	 private Double periodLowerLimit;//课时数下限
	 private Double periodUpperLimit;//课时数上限
	 private String intervalType;//课时累计间隔
	 private String gradeCategory_id;//级别分类
	 private String grade_id;//可教授级别
	 private String teachingType;//授课类型
	 private Date startDate;//生效开始时间
	 private Date endDate;//生效结束时间
	 private Double unitPrice;//课时单价
	 private String isActivated;//是否生效
	 private String BUType;
	 private String scheme_id;
	 private String status;
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setCourse_id(String course_id) {
        this.course_id = course_id == null ? null : course_id.trim();
    }
	 public String getCourse_id(){
        return course_id;
    }
	 public void setPeriodLowerLimit(Double periodLowerLimit) {
          this.periodLowerLimit = periodLowerLimit ;
    }
	 public Double getPeriodLowerLimit(){
        return periodLowerLimit;
    }
	 public void setPeriodUpperLimit(Double periodUpperLimit) {
          this.periodUpperLimit = periodUpperLimit ;
    }
	 public Double getPeriodUpperLimit(){
        return periodUpperLimit;
    }
	public void setIntervalType(String intervalType) {
        this.intervalType = intervalType == null ? null : intervalType.trim();
    }
	 public String getIntervalType(){
        return intervalType;
    }
	public void setGradeCategory_id(String gradeCategory_id) {
        this.gradeCategory_id = gradeCategory_id == null ? null : gradeCategory_id.trim();
    }
	 public String getGradeCategory_id(){
        return gradeCategory_id;
    }
	public void setGrade_id(String grade_id) {
        this.grade_id = grade_id == null ? null : grade_id.trim();
    }
	 public String getGrade_id(){
        return grade_id;
    }
	public void setTeachingType(String teachingType) {
        this.teachingType = teachingType == null ? null : teachingType.trim();
    }
	 public String getTeachingType(){
        return teachingType;
    }
	 public void setStartDate(Date startDate) {
          this.startDate = startDate ;
    }
	 public Date getStartDate(){
        return startDate;
    }
	 public void setEndDate(Date endDate) {
          this.endDate = endDate ;
    }
	 public Date getEndDate(){
        return endDate;
    }
	 public void setUnitPrice(Double unitPrice) {
          this.unitPrice = unitPrice ;
    }
	 public Double getUnitPrice(){
        return unitPrice;
    }
	public void setIsActivated(String isActivated) {
        this.isActivated = isActivated == null ? null : isActivated.trim();
    }
	 public String getIsActivated(){
        return isActivated;
    }
	public String getBUType() {
		return BUType;
	}
	public void setBUType(String bUType) {
		BUType = bUType;
	}
	public String getScheme_id() {
		return scheme_id;
	}
	public void setScheme_id(String scheme_id) {
		this.scheme_id = scheme_id;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
}