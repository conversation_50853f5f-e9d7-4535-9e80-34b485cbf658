package com.lanmosoft.dao.model;
import java.util.Date;
import java.util.List;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;
/**
 * 学生可用时间
 * <AUTHOR>
 *
 */
public class Student_freeTime {
	 private Integer id;//ID
	 private String student_id;//学生
	 private Date freeDate;//日期
	 private String startTime;//开始时间
	 private String endTime;//结束时间
	 private String teachingWay;//上课方式
	 private String isFree;//是否可排课
	 private String freeTimeCode;//可排课时间编码
	 private String BUType;
	 
	//学员的排课时间段信息
	 private Student_freeTime lis;
	 private String start;
	 private String end;
	 private String length;
	 private boolean  isPai;
	 //可排课的老师信息
	 private List<User> user_list;
	 
	 //排课老师和学生信息
	 private List<View_paike_class> listClass;//
	 
	 
	 private String kaishiTime;
	 private String jieshuTime;
	 
	 private String freeDateStr;
	
	 
	 
	 
	public String getKaishiTime() {
		return kaishiTime;
	}
	public void setKaishiTime(String kaishiTime) {
		this.kaishiTime = kaishiTime;
	}
	public String getJieshuTime() {
		return jieshuTime;
	}
	public void setJieshuTime(String jieshuTime) {
		this.jieshuTime = jieshuTime;
	}
	public List<View_paike_class> getListClass() {
		return listClass;
	}
	public void setListClass(List<View_paike_class> listClass) {
		this.listClass = listClass;
	}
	public List<User> getUser_list() {
		return user_list;
	}
	public void setUser_list(List<User> user_list) {
		this.user_list = user_list;
	}
	public boolean isPai() {
		return isPai;
	}
	public void setPai(boolean isPai) {
		this.isPai = isPai;
	}
	public String getStart() {
		return start;
	}
	public void setStart(String start) {
		this.start = start;
	}
	public String getEnd() {
		return end;
	}
	public void setEnd(String end) {
		this.end = end;
	}
	public String getLength() {
		return length;
	}
	public void setLength(String length) {
		this.length = length;
	}
	public Student_freeTime getLis() {
		return lis;
	}
	public void setLis(Student_freeTime lis) {
		this.lis = lis;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public void setStudent_id(String student_id) {
        this.student_id = student_id == null ? null : student_id.trim();
    }
	 public String getStudent_id(){
        return student_id;
    }
	 public void setFreeDate(Date freeDate) {
          this.freeDate = freeDate ;
    }
	public Date getFreeDate(){
        return freeDate;
    }
	public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }
	public String getStartTime(){
        return startTime;
    }
	public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }
	public String getEndTime(){
        return endTime;
    }
	public void setTeachingWay(String teachingWay) {
        this.teachingWay = teachingWay == null ? null : teachingWay.trim();
    }
	 public String getTeachingWay(){
        return teachingWay;
    }
	public void setIsFree(String isFree) {
        this.isFree = isFree == null ? null : isFree.trim();
    }
	public String getIsFree(){
        return isFree;
    }
	public void setFreeTimeCode(String freeTimeCode) {
        this.freeTimeCode = freeTimeCode == null ? null : freeTimeCode.trim();
    }
	/**
	 * 学生可用时间
	 * <AUTHOR>
	 *
	 */
	 public String getFreeTimeCode(){
        return freeTimeCode;
    }
	 public String getBUType() {
			return BUType;
	}
		public void setBUType(String BUType) {
			this.BUType = BUType== null ? null : BUType.trim();
	}
		public String getFreeDateStr() {
			return freeDateStr;
		}
		public void setFreeDateStr(String freeDateStr) {
			this.freeDateStr = freeDateStr;
		}
	
}