package com.lanmosoft.dao.model;
import java.util.Date;
/**
 * 文件记录
 * <AUTHOR>
 *
 */
public class Filerecord {
	 private String id;//ID
	 private String originalName;//原始名称
	 private String displayName;//显示名称
	 private String suffixName;//后缀名
	 private String logicName;//逻辑名称
	 private String logicPath;//逻辑存储路径
	 private Integer size;//文件大小
	 private String beizhu;//备注
	 private String status;//状态
	 private String reference_id;//引用
	 private String reference_type;//引用类别
	 private String creator_id;//创建者
	 private Date createTime;//创建时间
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setOriginalName(String originalName) {
        this.originalName = originalName == null ? null : originalName.trim();
    }
	 public String getOriginalName(){
        return originalName;
    }
	public void setDisplayName(String displayName) {
        this.displayName = displayName == null ? null : displayName.trim();
    }
	 public String getDisplayName(){
        return displayName;
    }
	public void setSuffixName(String suffixName) {
        this.suffixName = suffixName == null ? null : suffixName.trim();
    }
	 public String getSuffixName(){
        return suffixName;
    }
	public void setLogicName(String logicName) {
        this.logicName = logicName == null ? null : logicName.trim();
    }
	 public String getLogicName(){
        return logicName;
    }
	public void setLogicPath(String logicPath) {
        this.logicPath = logicPath == null ? null : logicPath.trim();
    }
	 public String getLogicPath(){
        return logicPath;
    }
	 public void setSize(Integer size) {
          this.size = size ;
    }
	 public Integer getSize(){
        return size;
    }
	public void setBeizhu(String beizhu) {
        this.beizhu = beizhu == null ? null : beizhu.trim();
    }
	 public String getBeizhu(){
        return beizhu;
    }
	public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }
	 public String getStatus(){
        return status;
    }
	public void setReference_id(String reference_id) {
        this.reference_id = reference_id == null ? null : reference_id.trim();
    }
	 public String getReference_id(){
        return reference_id;
    }
	public void setReference_type(String reference_type) {
        this.reference_type = reference_type == null ? null : reference_type.trim();
    }
	 public String getReference_type(){
        return reference_type;
    }
	public void setCreator_id(String creator_id) {
        this.creator_id = creator_id == null ? null : creator_id.trim();
    }
	 public String getCreator_id(){
        return creator_id;
    }
	 public void setCreateTime(Date createTime) {
          this.createTime = createTime ;
    }
	 public Date getCreateTime(){
        return createTime;
    }
}