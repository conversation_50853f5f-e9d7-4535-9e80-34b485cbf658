package com.lanmosoft.dao.model;
import java.util.Date;
import java.sql.Time;

import com.lanmosoft.model.BaseModel;
/**
 * 老师耗课信息表
 * <AUTHOR>
 *
 */
public class Consumption_teacher {
	 private String id;//ID
	 private String teacher_id;//教师
	 private String classSchedule_id;//课表
	 private Double wage;//工资（小时）
	 private Double wage2;//工资（元）
	 private Double wageDeduction;//扣除工资（小时）
	 private Double wageDeduction2;//扣除工资（元）
	 private Double incidental;//杂费
	 private String description;//杂费摘要
	 private String lockStatus;//锁定状态
	 private Double next;//超出本档小时数
	 private Double nowprice;//当前单价
	 private String currency_id;
	
	public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }
	 public String getId(){
        return id;
    }
	public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id == null ? null : teacher_id.trim();
    }
	 public String getTeacher_id(){
        return teacher_id;
    }
	public void setClassSchedule_id(String classSchedule_id) {
        this.classSchedule_id = classSchedule_id == null ? null : classSchedule_id.trim();
    }
	 public String getClassSchedule_id(){
        return classSchedule_id;
    }
	 public void setWage(Double wage) {
          this.wage = wage ;
    }
	 public Double getWage(){
        return wage;
    }
	public void setWage2(Double wage2) {
          this.wage2 = wage2 ;
    }
	 public Double getWage2(){
        return wage2;
    }
	 public void setWageDeduction(Double wageDeduction) {
          this.wageDeduction = wageDeduction ;
    }
	 public Double getWageDeduction(){
        return wageDeduction;
    }
	 public void setWageDeduction2(Double wageDeduction2) {
          this.wageDeduction2 = wageDeduction2 ;
    }
	 public Double getWageDeduction2(){
        return wageDeduction2;
    }
	 public void setIncidental(Double incidental) {
          this.incidental = incidental ;
    }
	 public Double getIncidental(){
        return incidental;
    }
	public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }
	 public String getDescription(){
        return description;
    }
	public void setLockStatus(String lockStatus) {
        this.lockStatus = lockStatus == null ? null : lockStatus.trim();
    }
	 public String getLockStatus(){
        return lockStatus;
    }
	public Double getNext() {
		return next;
	}
	public void setNext(Double next) {
		this.next = next;
	}
	public Double getNowprice() {
		return nowprice;
	}
	public void setNowprice(Double nowprice) {
		this.nowprice = nowprice;
	}
	public String getCurrency_id() {
		return currency_id;
	}
	public void setCurrency_id(String currency_id) {
		this.currency_id = currency_id;
	}
}