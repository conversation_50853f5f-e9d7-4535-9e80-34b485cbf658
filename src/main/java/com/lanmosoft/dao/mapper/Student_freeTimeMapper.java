package com.lanmosoft.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.lanmosoft.dao.model.Student_freeTime;
import com.lanmosoft.model.WhereCondition;
/**
 * 学生可用时间
 * <AUTHOR>
 *
 */
public interface Student_freeTimeMapper extends BaseMapper<Student_freeTime>{
	  void	deleteBystatus(WhereCondition wc);

	void batchInsert(List<Student_freeTime> s_freeTimeNew);
	
	double queryFreeHour(WhereCondition wc);

	double matchingHour(@Param(value="student_id")String student_id, 
			@Param(value="teacher_id")String teacher_id, @Param(value="startDate")String startDate,
			@Param(value="endDate")String endDate);

    List<Student_freeTime> queryLong(WhereCondition wc);
}
