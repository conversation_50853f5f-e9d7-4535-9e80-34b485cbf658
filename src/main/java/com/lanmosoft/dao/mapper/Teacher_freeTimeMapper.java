package com.lanmosoft.dao.mapper;

import java.util.List;

import com.lanmosoft.dao.model.Teacher_freeTime;
import com.lanmosoft.model.WhereCondition;
/**
 * 教师可排课时间
 * <AUTHOR>
 *
 */
public interface Teacher_freeTimeMapper extends BaseMapper<Teacher_freeTime>{
	  void	deleteBystatus(WhereCondition wc);

	void batchInsert(List<Teacher_freeTime> t_FreeTimeNew);

	double queryFreeHour(WhereCondition wc);

    List<Teacher_freeTime> queryLong(WhereCondition wc);
}
