package com.lanmosoft.dao.mapper;

import java.util.List;

import com.lanmosoft.dao.model.Teacher_course;
import com.lanmosoft.model.WhereCondition;
/**
 * 教师可教授科目
 * <AUTHOR>
 *
 */
public interface Teacher_courseMapper extends BaseMapper<Teacher_course>{
	 List<Teacher_course> querycheck(Teacher_course t);
	List<Teacher_course> getByPeriods(WhereCondition wc);
	void updateunitprice(Teacher_course t);
}
