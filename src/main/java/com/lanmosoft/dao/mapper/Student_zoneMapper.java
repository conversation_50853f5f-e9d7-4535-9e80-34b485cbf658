package com.lanmosoft.dao.mapper;

import java.util.List;

import com.lanmosoft.dao.model.Student_timezone;
import com.lanmosoft.dao.model.Student_zone;
import com.lanmosoft.model.WhereCondition;

public interface Student_zoneMapper extends BaseMapper<Student_zone>{
	  void	deleteBystatus(WhereCondition wc);
	  List<Student_zone>  querycheck(Student_zone s);
	  List<Student_zone>  querycheckZone(Student_zone s);
	  
		
}