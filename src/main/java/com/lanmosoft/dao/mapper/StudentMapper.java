package com.lanmosoft.dao.mapper;

import com.lanmosoft.dao.model.Student;
import com.lanmosoft.model.WhereCondition;

import java.util.List;

/**
 * student
 *
 * <AUTHOR>
 */
public interface StudentMapper extends BaseMapper<Student> {
    List<Student> queryStudent(WhereCondition var1);

    List<Student> queryall(WhereCondition var1);

    int countStudent(WhereCondition wc);

    List<Student> findAllByActive();

    List<Student> findAllByInactive();

    List<Student> findAllByHalfClosed();

    List<Student> findAllByClosed();

    void deleteBystatus(WhereCondition wc);
}
