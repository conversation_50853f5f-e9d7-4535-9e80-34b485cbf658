package com.lanmosoft.dao.mapper;

import java.util.List;

import com.lanmosoft.dao.model.Filerecord;
import com.lanmosoft.model.WhereCondition;
/**
 * 文件记录
 * <AUTHOR>
 *
 */
public interface FilerecordMapper extends BaseMapper<Filerecord>{

	List<Filerecord> query_followup(WhereCondition wc);

	List<Filerecord> query_classschedule(WhereCondition wc);

	int count_followup(WhereCondition wc);

	int count_classschedule(WhereCondition wc);
}
