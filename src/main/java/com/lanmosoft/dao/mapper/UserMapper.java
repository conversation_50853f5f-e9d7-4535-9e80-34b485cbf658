package com.lanmosoft.dao.mapper;

import com.lanmosoft.dao.model.User;
import com.lanmosoft.model.WhereCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * 人员
 * <AUTHOR>
 *
 */
public interface UserMapper extends BaseMapper<User>{
	List<String> queryByPost(WhereCondition wc);

	User getByStudent(String student_id);

	User getByStudentZone(@Param("student_id") String student_id, @Param("zone_id") String zone_id);

    List<User> queryTutor(WhereCondition wc);

	int countTutor(WhereCondition wc);
}
