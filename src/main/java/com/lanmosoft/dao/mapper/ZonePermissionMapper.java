package com.lanmosoft.dao.mapper;

import java.util.List;

import com.lanmosoft.dao.model.View_ZonePermission;
import com.lanmosoft.dao.model.ZonePermission;
import com.lanmosoft.model.WhereCondition;
/**
 * 校区授权表
 * <AUTHOR>
 *
 */
public interface ZonePermissionMapper extends BaseMapper<ZonePermission>{
	  void	deleteBystatus(WhereCondition wc);

	List<View_ZonePermission> queryZonePermissionInfo(
			WhereCondition wc);

	List<View_ZonePermission> queryZoneInfo();
		
}
