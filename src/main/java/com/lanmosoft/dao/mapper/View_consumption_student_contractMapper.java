package com.lanmosoft.dao.mapper;

import com.lanmosoft.dao.model.View_consumption_student_contract;
import com.lanmosoft.model.WhereCondition;

import java.util.List;
/**
 * 学生耗课_合同视图
 * <AUTHOR>
 *
 */
public interface View_consumption_student_contractMapper extends BaseMapper<View_consumption_student_contract>{
	public double sum(WhereCondition wc);

	public List<View_consumption_student_contract> getConsumption(
			WhereCondition wc);
	
	public List<View_consumption_student_contract> getConsumption_null(
			WhereCondition wc);

	public List<String> getconsumption_student_contract_id(WhereCondition wc);

	public List<View_consumption_student_contract> getBystudent(String student_id, String classschedule_id);

	List<View_consumption_student_contract> queryCSC(WhereCondition var1);

	List<View_consumption_student_contract> queryCS(WhereCondition var1);
}
