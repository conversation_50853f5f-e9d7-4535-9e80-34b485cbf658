package com.lanmosoft.dao.mapper;

import java.util.List;

import com.lanmosoft.dao.model.View_teacher_course;
import com.lanmosoft.model.WhereCondition;
/**
 * 教师可教授科目View
 * <AUTHOR>
 *
 */
public interface View_teacher_courseMapper extends BaseMapper<View_teacher_course>{
	  void	deleteBystatus(WhereCondition wc);

	List<View_teacher_course> queryDistinct(WhereCondition wc);

	List<String> queryDistinctTeacherID(WhereCondition wc);

	Integer countDistinct(WhereCondition wc);
		
}
