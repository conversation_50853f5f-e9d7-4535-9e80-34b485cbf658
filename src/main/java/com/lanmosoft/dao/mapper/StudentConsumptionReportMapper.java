package com.lanmosoft.dao.mapper;

import com.lanmosoft.dao.model.Constant;
import com.lanmosoft.dao.model.StudentConsumptionReport;
import com.lanmosoft.model.WhereCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生排耗课报表dao
 * <AUTHOR>
 *
 */
public interface StudentConsumptionReportMapper extends BaseMapper<StudentConsumptionReport>{

	List<StudentConsumptionReport> getActiveBySCR(@Param("zoneIds")  List<String> zoneIds,@Param("studentId") String studentId);

	List<StudentConsumptionReport> getInactiveBySCR(@Param("zoneIds")  List<String> zoneIds,@Param("studentId") String studentId);

	List<StudentConsumptionReport> getHalfClosedBySCR(@Param("zoneIds")  List<String> zoneIds,@Param("studentId") String studentId);

	List<StudentConsumptionReport> getClosedBySCR(@Param("zoneIds")  List<String> zoneIds,@Param("studentId") String studentId);
}
