package com.lanmosoft.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.lanmosoft.dao.model.Contract;
/**
 * 合同
 * <AUTHOR>
 *
 */
public interface ContractMapper extends BaseMapper<Contract>{
	List<Contract> getUseableContract(@Param("student_id")String student_id,
			@Param("isInculuedGiven")boolean isInculuedGiven);
	
	Double countUnitOfContract(String student_id);
	void insertHis(Contract t);
	List<Contract> getUseableContractType(@Param("student_id")String student_id, 
			@Param("isInculuedGiven")boolean isInculuedGiven, 
			@Param("contractType2List")List<String> contractType2List);

	List<Contract> getUseableContractTypeCourse(@Param("student_id")String student_id,
			@Param("isInculuedGiven")boolean isInculuedGiven,
			@Param("contractType2List")List<String> contractType2List,
			@Param("course_id")String course_id);
}
