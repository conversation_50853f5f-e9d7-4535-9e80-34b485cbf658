package com.lanmosoft.dao.mapper;

import java.util.List;

import com.lanmosoft.dao.model.ClassSchedule;
import com.lanmosoft.dao.model.View_paike_class;
import com.lanmosoft.model.WhereCondition;
/**
 * view_paike_class
 * <AUTHOR>
 *
 */
public interface View_paike_classMapper extends BaseMapper<View_paike_class>{

	int countContradiction(View_paike_class p);

	List<ClassSchedule> queryDayMaxClassSchedule(String zone_id);

	double queryClassHour(WhereCondition wc);
		
}
