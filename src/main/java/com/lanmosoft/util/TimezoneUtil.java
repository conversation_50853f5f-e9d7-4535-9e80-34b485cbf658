
package com.lanmosoft.util;

import com.lanmosoft.dao.model.ClassSchedule;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;


public class TimezoneUtil {

	public static String strTimeFormat_V1 = "yyyy-MM-dd";
	public static String strTimeFormat_V2 = "yyyy-MM-dd HH:mm";
	public static String defaultTimezone = "Asia/Shanghai";

	public static boolean checkCrossDst (String startDate, String endDate, String timezone) {

		Date dateStart = new Date();
		Date dateEnd = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat(strTimeFormat_V2);
		//sdf.setTimeZone(TimeZone.getTimeZone(timezone));
		try {
			dateStart = sdf.parse(startDate);
			dateEnd = sdf.parse(endDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		System.out.println(TimeZone.getTimeZone(timezone).inDaylightTime(dateStart));
		System.out.println(TimeZone.getTimeZone(timezone).inDaylightTime(dateEnd));
		if (TimeZone.getTimeZone(timezone).inDaylightTime(dateStart) != TimeZone.getTimeZone(timezone).inDaylightTime(dateEnd)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 时区转换,转为上海时间，yyyy-MM-dd hh:mm
	 * @param time
	 * @param timezone
	 * @return
	 */
	public static String timezoneChangeString(String time,String timezone){
		String riqi = time.substring(0,10);
		String shijian = time.substring(11,16);

		List list = TimezoneUtil.timezoneChange(riqi, shijian, timezone, "G");
		String result = list.get(2).toString()+" "+list.get(1).toString();
		return result;
	}
	/**
	 * 时区转换,yyyy-MM-dd hh:mm
	 * @param time
	 * @param timezone
	 * @return
	 */
	public static String timezoneChangeString(String time,String timezone,String flag){
		String riqi = time.substring(0,10);
		String shijian = time.substring(11,16);

		List list = TimezoneUtil.timezoneChange(riqi, shijian, timezone, flag);
		String result = list.get(2).toString()+" "+list.get(1).toString();
		return result;
	}
	/**
	 * 时区转换，上海时间转为指定时区时间，yyyy-MM-dd hh:mm
	 * @param time
	 * @param timezone
	 * @return
	 */
	public static String timezoneChangeStringmiao(String time,String timezone,String flag){
		String riqi = time.substring(0,10);
		String shijian = time.substring(11,16);
		String miao = time.substring(time.length()-2);
		List list = TimezoneUtil.timezoneChange(riqi, shijian, timezone, flag);
		String result = list.get(2).toString()+" "+list.get(1).toString()+":"+miao;
		return result;
	}

	public static List  timezoneChange(Date freeDate,String time,String timezone,String flag) {
		SimpleDateFormat sdf_temp=new SimpleDateFormat("yyyy-MM-dd");
		String freeDateStr=sdf_temp.format(freeDate);

		String source_date = freeDateStr + " " + time; // original datetime yyyy-mm-dd hh:mm

		SimpleDateFormat o_sdf = new SimpleDateFormat(strTimeFormat_V2);
		SimpleDateFormat d_sdf = new SimpleDateFormat(strTimeFormat_V1);
		SimpleDateFormat sdf_time = new SimpleDateFormat("HH:mm");
		if ("S".equals(flag)) {
			o_sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
			d_sdf.setTimeZone(TimeZone.getTimeZone(timezone));
			sdf_time.setTimeZone(TimeZone.getTimeZone(timezone));
		} else if ("G".equals(flag)) {
			o_sdf.setTimeZone(TimeZone.getTimeZone(timezone));
			d_sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
			sdf_time.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
		}
		List retList=new ArrayList();
		Date date = null;
		try {
			date = o_sdf.parse(source_date);
			retList.add(d_sdf.parse(d_sdf.format(date)));
			retList.add(sdf_time.format(date));
			retList.add(d_sdf.format(date));
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return retList;
	}
	/*public static List  timezoneChange(Date freeDate,String time,String timezone,String flag) {
		Boolean timeFlag=false;
		List retList=new ArrayList();
		String freeDateStr=LanDateUtils.format(freeDate, strTimeFormat_V1);
		String combineStr=freeDateStr;
		if(!("".equals(time))){
			freeDateStr=freeDateStr+" ";
			combineStr=freeDateStr+time;
			timeFlag=true;
		}
		else{
			combineStr=freeDateStr;
			timeFlag=false;
		}
		
		
		String retStr="";
		if("S".equals(flag)){
			retStr=utc2other(combineStr,timezone,timeFlag);
		}
		if("G".equals(flag)){
			retStr=other2utc(combineStr,timezone,timeFlag);
		}
		if(!("".equals(retStr))){
			String dateStr="";
			String timeStr="";
			if(timeFlag){
				String[] timeArr=retStr.split(" ");
				dateStr=timeArr[0];
				timeStr=timeArr[1];
			}
			else{
				dateStr=retStr;
				timeStr="";
				
			}
			Date freeDateRet=LanDateUtils.convertStringToDate(strTimeFormat_V1, dateStr);
			retList.add(freeDateRet);
			retList.add(timeStr);
			retList.add(dateStr);
		}
		return retList;
	}*/
	
	public static List  timezoneChange(String freeDateStr,String time,String timezone,String flag) {

		String source_date = freeDateStr + " " + time; // original datetime yyyy-mm-dd hh:mm

		SimpleDateFormat o_sdf = new SimpleDateFormat(strTimeFormat_V2);
		SimpleDateFormat d_sdf = new SimpleDateFormat(strTimeFormat_V1);
		SimpleDateFormat d_sdf2 = new SimpleDateFormat(strTimeFormat_V1);
		SimpleDateFormat sdf_time = new SimpleDateFormat("HH:mm");
		if ("S".equals(flag)) {
			o_sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
			d_sdf.setTimeZone(TimeZone.getTimeZone(timezone));
			//d_sdf2.setTimeZone(TimeZone.getTimeZone(timezone));
			sdf_time.setTimeZone(TimeZone.getTimeZone(timezone));
		} else if ("G".equals(flag)) {
			o_sdf.setTimeZone(TimeZone.getTimeZone(timezone));
			d_sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
			//d_sdf2.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
			sdf_time.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
		}
		List retList=new ArrayList();
		Date date = null;
		Date earlyDate = null;
		try {
			date = o_sdf.parse(source_date);
/*			if("Australia/Melbourne".equals(timezone)) {
				earlyDate = LanDateUtils.getNext_Day(date, 1);
				retList.add(d_sdf.parse(d_sdf.format(earlyDate)));
			} else {
				retList.add(d_sdf.parse(d_sdf.format(date)));
			}*/
			retList.add(d_sdf2.parse(d_sdf.format(date)));
			//retList.add(d_sdf.parse(d_sdf.format(date)));
			retList.add(sdf_time.format(date));
			retList.add(d_sdf.format(date));
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return retList;
	}

/*	public static List  timezoneChange(String freeDateStr,String time,String timezone,String flag) {
		Boolean timeFlag=false;
		List retList=new ArrayList();
		String combineStr=freeDateStr;
		if(!("".equals(time))){
			freeDateStr=freeDateStr+" ";
			combineStr=freeDateStr+time;
			timeFlag=true;
		}
		else{
			combineStr=freeDateStr;
			timeFlag=false;
		}


		String retStr="";
		if("S".equals(flag)){
			retStr=utc2other(combineStr,timezone,timeFlag);
		}
		if("G".equals(flag)){
			retStr=other2utc(combineStr,timezone,timeFlag);
		}
		if(!("".equals(retStr))){
			String dateStr="";
			String timeStr="";
			if(timeFlag){
				String[] timeArr=retStr.split(" ");
				dateStr=timeArr[0];
				timeStr=timeArr[1];
			}
			else{
				dateStr=retStr;
				timeStr="";

			}
			Date freeDateRet=LanDateUtils.convertStringToDate(strTimeFormat_V1, dateStr);
			retList.add(freeDateRet);
			retList.add(timeStr);
			retList.add(dateStr);
		}
		return retList;
	}*/
	
	public static String  utc2other (String time,String timezone,Boolean timeFlag) {
		
		//TimeZone.setDefault(TimeZone.getTimeZone(defaultTimezone));
		SimpleDateFormat df1 = new SimpleDateFormat(strTimeFormat_V1);
		Date date=new Date();
		if(timeFlag){
			df1 = new SimpleDateFormat(strTimeFormat_V2);
			date=LanDateUtils.convertStringToDate(strTimeFormat_V2, time);
		}
		else{
			df1 = new SimpleDateFormat(strTimeFormat_V1);
			date=LanDateUtils.convertStringToDate(strTimeFormat_V1, time);
		}
		
		
		//System.out.println(df1.format(date));
       
        df1.setTimeZone(TimeZone.getTimeZone(timezone));
        String dateStr=df1.format(date);
        //System.out.println(df1.format(date));
       
		return dateStr;
	}
	
	public static String  other2utc (String time,String timezone,Boolean timeFlag) {
		//TimeZone.setDefault(TimeZone.getTimeZone(timezone));
		SimpleDateFormat df1 = new SimpleDateFormat(strTimeFormat_V2);
		Date date=new Date();
		if(timeFlag){
			df1 = new SimpleDateFormat(strTimeFormat_V2);
			date=LanDateUtils.convertStringToDate(strTimeFormat_V2, time);
		}
		else{
			df1 = new SimpleDateFormat(strTimeFormat_V1);
			date=LanDateUtils.convertStringToDate(strTimeFormat_V1, time);
		}
		
		//System.out.println(df1.format(date));
       
        df1.setTimeZone(TimeZone.getTimeZone(defaultTimezone));
        String dateStr=df1.format(date);
       // System.out.println(df1.format(date));
       
		return dateStr;
	}
	 
	
	public static void main(String[] args) {
		String handleTime = "Europe/London";
		String g = TimezoneUtil.timezoneChangeStringmiao("2017-08-14 24:00:00", handleTime, "G");
		System.out.println(g);


	}

	/**
	 * 判断日期
	 * @param startDate
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static Date changRiqi(Date startDate, String startTime, String endTime) {
		String startTime1 = startTime.replace(":", "");
		String endTime1 = endTime.replace(":", "");

		if(Integer.parseInt(startTime1) > Integer.parseInt(endTime1)){
			return  LanDateUtils.getNext_Day(startDate,1);
		}
		return startDate;
	}

	/**
	 * 把课程转为指定时区的课程
	 * @param timezone
	 * @param cla
	 * @param flag
	 */
	public static void changTimezoneClassSchedele(String timezone, ClassSchedule cla ,String flag){

		List retDateListStart=TimezoneUtil.timezoneChange(cla.getScheduledDate(),cla.getStartTime(),timezone,flag);
		String startTime_SHStr=(String)retDateListStart.get(1);
		String schedule_SHStr=(String)retDateListStart.get(2);
		Date scheduleDate_SH=(Date)retDateListStart.get(0);
		Date riqiDate = TimezoneUtil.changRiqi(cla.getScheduledDate(),cla.getStartTime(),cla.getEndTime());
		List retDateListEnd=TimezoneUtil.timezoneChange(riqiDate,cla.getEndTime(),timezone,flag);
		String endTime_SHStr=(String)retDateListEnd.get(1);
		String endDate_SHStr=(String)retDateListEnd.get(2);
		Date endDate_SH=(Date) retDateListEnd.get(0);
		cla.setScheduledDate(scheduleDate_SH);
		cla.setStartTime(startTime_SHStr);
		cla.setScheduledDateStr(schedule_SHStr);
		cla.setEndDate(endDate_SH);
		cla.setEndDateStr(endDate_SHStr);
		cla.setEndTime(endTime_SHStr);
	}


}
	
	


