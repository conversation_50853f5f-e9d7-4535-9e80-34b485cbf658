package com.lanmosoft.util;

import com.lanmosoft.dao.model.ZidingyiSon;
import com.lanmosoft.webapp.webmodel.LabelValue;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.hssf.util.Region;
import org.apache.poi.ss.usermodel.*;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ExcelUtil {

	// 静态颜色映射表，存储颜色HEX值和对应的索引
	private static final Map<String, Short> COLOR_INDEX_MAP = new HashMap<>();

	// 静态初始化块，预先分配颜色索引
	static {
		// 为常用颜色预分配索引，从16开始（POI允许的自定义颜色范围是21-64）
		short index = 21;

		// TongjiController中使用的颜色
		COLOR_INDEX_MAP.put("#00B0F0", index++); // English, Reading, English Literature, Reading&Vocabulary, IELTS, ISEB Pre Test English, TOEFL, CAT4, ISEB Pretest English
		COLOR_INDEX_MAP.put("#FFFF00", index++); // Mathematics, Math and NVR, ISEB Pretest Math, Further Mathematics
		COLOR_INDEX_MAP.put("#F4B084", index++); // Science, Physics, Psychology
		COLOR_INDEX_MAP.put("#C6E0B4", index++); // Writing, Reading & Writing, Writing&Grammar, IELTS Writing
		COLOR_INDEX_MAP.put("#FFC000", index++); // Chemistry
		COLOR_INDEX_MAP.put("#A9D08E", index++); // Biology, Grammar, Computer Science, Design Technology
		COLOR_INDEX_MAP.put("#94DCF8", index++); // Verbal Reasoning, ISEB Pretest English&VR, English and VR, ISEB Pre Test VR, CAT-VR
		COLOR_INDEX_MAP.put("#E9ABE5", index++); // Economics, Speaking & Listening, Interview Preparation, Speaking, IELTS Writing, IELTS Speaking, Personal Statement, IELTS Listening
		COLOR_INDEX_MAP.put("#9999FF", index++); // History, Business Studies, Religious Studies
		COLOR_INDEX_MAP.put("#FFFF99", index++); // Non-verbal Reasoning, ISEB Pre Test NVR, Further Mathematics
		COLOR_INDEX_MAP.put("#CC99FF", index++); // French, Spanish, German
		COLOR_INDEX_MAP.put("#CCFF66", index++); // Geography, Vocabulary
		COLOR_INDEX_MAP.put("#D9D9D9", index++); // group class, Cultural Context, UKiset

		// 可以根据需要添加更多颜色
	}

	// 获取颜色的预分配索引，如果没有预分配则返回null
	private static Short getPreallocatedColorIndex(String colorHex) {
		return COLOR_INDEX_MAP.get(colorHex.toUpperCase());
	}

	public static String getRowCellStr(Row row,int columnIndex){
		Cell cell  =row.getCell(columnIndex);
		if(cell==null){
			return null;
		}
			String strval=null;
			   switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    strval=cell.getRichStringCellValue().getString();
                    break;
                case Cell.CELL_TYPE_NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");
                        strval=sdf.format(cell.getDateCellValue());
                    } else {
                        strval=""+cell.getNumericCellValue();
                    }
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    strval=""+cell.getBooleanCellValue();
                    break;
                case Cell.CELL_TYPE_FORMULA:
//                    System.out.println(cell.getCellFormula());
                    break;
                default:
//                    System.out.println();
            }
		return strval;
	}
	
	public static List<String> getRowCellStrList(Row row){
		List<String> rowList = new ArrayList<String>();
		for (Cell cell : row) {
			String strval=null;
			   switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    strval=cell.getRichStringCellValue().getString();
                    break;
                case Cell.CELL_TYPE_NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        SimpleDateFormat sdf =new SimpleDateFormat("yyyyMMdd");
                        strval=sdf.format(cell.getDateCellValue());
                    } else {
                        strval=""+cell.getNumericCellValue();
                    }
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    strval=""+cell.getBooleanCellValue();
                    break;
                case Cell.CELL_TYPE_FORMULA:
//                    System.out.println(cell.getCellFormula());
                    break;
                default:
//                    System.out.println();
            }
				rowList.add(strval);
		}
		return rowList;
	}
	
	public static Object getRowCellVal2(Row row,int columnIndex){
		Cell cell  =row.getCell(columnIndex);
		if(cell==null){
			return null;
		}
			Object strval=null;
			   switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    strval=cell.getRichStringCellValue().getString();
                    break;
                case Cell.CELL_TYPE_NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        strval=cell.getDateCellValue();
                    } else {
                    	cell.setCellType(Cell.CELL_TYPE_STRING);                                              
                        String temp = cell.getStringCellValue();
                        if (temp.indexOf(".") > -1) {
                        	strval = String.valueOf(new Double(temp)).trim();
                        } else {
                        	strval = temp.trim();
                        }                        
                    }
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    strval=""+cell.getBooleanCellValue();
                    break;
                case Cell.CELL_TYPE_FORMULA:
                	strval = cell.getNumericCellValue();
                    break;
                default:
//                    System.out.println();
            }
		return strval;
	}
	
	@SuppressWarnings({"unchecked"})
	public static HSSFWorkbook exportExcelRiLi(List<String> listKey, List<Map> listValue,String sl,
			List<String> listKeyR,List<Map> listValueR,List<String> listKeyD,List<Map> listValueD) {
	      // 声明一个工作薄
	      HSSFWorkbook workbook = new HSSFWorkbook();

	   // 生成一个表格
	      HSSFSheet sheet = workbook.createSheet(sl);


		sheet.setMargin(HSSFSheet.TopMargin,0.191);// 页边距（上）
		sheet.setMargin(HSSFSheet.BottomMargin,0.191);// 页边距（下）
		sheet.setMargin(HSSFSheet.LeftMargin,0.064 );// 页边距（左）
		sheet.setMargin(HSSFSheet.RightMargin,0.064);// 页边距（右)
		sheet.setMargin(HSSFSheet.HeaderMargin,0.064);// 页眉
		sheet.setMargin(HSSFSheet.FooterMargin,0.064);// 页脚
	      //设置打印样式
		HSSFPrintSetup print = (HSSFPrintSetup) sheet.getPrintSetup();
		print.setLandscape(true);//设置横向打印
		print.setScale((short) 50);//设置打印缩放70%
		print.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);//设置为A4纸张
		//print.setLeftToRight(true);//設置打印顺序先行后列,默认为先列行
		print.setFitHeight((short) 1);//设置缩放调整为10页高
		print.setFitWidth((short) 1);//设置缩放调整为宽高
		System.out.println("================="+print.getHeaderMargin());
		System.out.println("================="+print.getFooterMargin());
		// 设置表格默认列宽度为30个字节
	      sheet.setDefaultColumnWidth((int) 30);
	      sheet.setColumnWidth(0, 8 * 256);
	      // 生成一个样式
	      HSSFCellStyle style = workbook.createCellStyle();
	      // 设置这些样式
	      style.setBorderBottom(HSSFCellStyle.BORDER_MEDIUM); //底部边框2
	      style.setBorderLeft(HSSFCellStyle.BORDER_MEDIUM); //左边边框2
	      style.setBorderRight(HSSFCellStyle.BORDER_MEDIUM);//右边边框2
	      style.setBorderTop(HSSFCellStyle.BORDER_MEDIUM);//上边边框2
	      style.setAlignment(HSSFCellStyle.ALIGN_CENTER);//排成直线
	      style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); //竖向定线
	      // 生成一个字体
	      HSSFFont font = workbook.createFont();
	      font.setColor(HSSFColor.VIOLET.index);//紫罗兰
	      font.setFontHeightInPoints((short) 12);//字体大小
	      font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
	      // 把字体应用到当前的样式
	      style.setFont(font);
	      // 生成并设置另一个样式
	      HSSFCellStyle style2 = workbook.createCellStyle();
	      style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      style2.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
	      // 生成另一个字体
	      HSSFFont font2 = workbook.createFont();
	      font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
	      // 把字体应用到当前的样式
	      style2.setFont(font2);

		// 生成并设置另一个样式
		HSSFCellStyle style2_1 = workbook.createCellStyle();
		style2_1.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		style2_1.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		style2_1.setBorderRight(HSSFCellStyle.BORDER_THIN);
		style2_1.setBorderTop(HSSFCellStyle.BORDER_THIN);
		style2_1.setAlignment(HSSFCellStyle.ALIGN_LEFT);
		style2_1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
		style2_1.setWrapText(true);
		// 生成另一个字体
		font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
		// 把字体应用到当前的样式
		style2_1.setFont(font2);
	      
	      // 生成并设置另一个样式
	      HSSFCellStyle style3 = workbook.createCellStyle();
	      style3.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style3.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style3.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style3.setBorderTop(HSSFCellStyle.BORDER_MEDIUM);
	      style3.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      style3.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
		// 生成并设置另一个样式
		HSSFCellStyle style3_1 = workbook.createCellStyle();
		style3_1.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		style3_1.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		style3_1.setBorderRight(HSSFCellStyle.BORDER_THIN);
		style3_1.setBorderTop(HSSFCellStyle.BORDER_MEDIUM);
		style3_1.setAlignment(HSSFCellStyle.ALIGN_LEFT);
		style3_1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
		style3_1.setWrapText(true);

	      // 生成另一个字体
	      HSSFFont font3 = workbook.createFont();
	      font3.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
	      // 把字体应用到当前的样式
	      style3.setFont(font3);
	      
	      // 生成并设置另一个样式
	      HSSFCellStyle style4 = workbook.createCellStyle();
	      style4.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style4.setBorderLeft(HSSFCellStyle.BORDER_MEDIUM);
	      style4.setBorderRight(HSSFCellStyle.BORDER_MEDIUM);
	      style4.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      style4.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      style4.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
	      // 生成另一个字体
	      HSSFFont font4 = workbook.createFont();
	      font4.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
	      // 把字体应用到当前的样式
	      style4.setFont(font4);
	      
	   // 生成并设置另一个样式
	      HSSFCellStyle style5 = workbook.createCellStyle();
	      style5.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style5.setBorderLeft(HSSFCellStyle.BORDER_MEDIUM);
	      style5.setBorderRight(HSSFCellStyle.BORDER_MEDIUM);
	      style5.setBorderTop(HSSFCellStyle.BORDER_MEDIUM);
	      style5.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      style5.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

	      // 把字体应用到当前的样式
	      style5.setFont(font4);
	      
	      
	   // 生成一个样式
	      HSSFCellStyle style6 = workbook.createCellStyle();
	      // 设置这些样式
	      style6.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style6.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style6.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style6.setBorderTop(HSSFCellStyle.BORDER_MEDIUM);
	      style6.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
	      style6.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); 
	      // 生成一个字体
	      HSSFFont font6 = workbook.createFont();
	      font6.setFontHeightInPoints((short) 10);
	      font6.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
	      // 把字体应用到当前的样式
	      style6.setFont(font6);
	      
	      
	      HSSFCellStyle cellStyle = workbook.createCellStyle();    //创建一个样式
	      //cellStyle.setFillForegroundColor(HSSFColor.LIGHT_BLUE.index);    //设置颜色为蓝色

	      HSSFPalette palette = workbook.getCustomPalette();  //wb HSSFWorkbook对象
	      palette.setColorAtIndex((short)9, (byte) (0xff & 155), (byte) (0xff & 194), (byte) (0xff & 230));//设置颜色为蓝色
	      cellStyle.setFillForegroundColor((short) 9);

	      cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
	      cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      cellStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
	      // 把字体应用到当前的样式
	      cellStyle.setFont(font2);

		HSSFCellStyle cellStyle_1 = workbook.createCellStyle();    //创建一个样式
		//cellStyle.setFillForegroundColor(HSSFColor.LIGHT_BLUE.index);    //设置颜色为蓝色

		HSSFPalette palette_1 = workbook.getCustomPalette();  //wb HSSFWorkbook对象
		palette_1.setColorAtIndex((short)9, (byte) (0xff & 155), (byte) (0xff & 194), (byte) (0xff & 230));//设置颜色为蓝色
		cellStyle_1.setFillForegroundColor((short) 9);

		cellStyle_1.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		cellStyle_1.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		cellStyle_1.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		cellStyle_1.setBorderRight(HSSFCellStyle.BORDER_THIN);
		cellStyle_1.setBorderTop(HSSFCellStyle.BORDER_THIN);
		cellStyle_1.setAlignment(HSSFCellStyle.ALIGN_LEFT);
		cellStyle_1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
		cellStyle_1.setWrapText(true);
		// 把字体应用到当前的样式
		cellStyle_1.setFont(font2);



	      int index = 0;
	      //产生表格标题行
	      HSSFRow row = sheet.createRow(index);
	      for (int i = 0; i < listKey.size(); i++) {
	         HSSFCell cell = row.createCell(i);
	         cell.setCellStyle(style);
	         HSSFRichTextString text = new HSSFRichTextString(listKey.get(i));
	         cell.setCellValue(text);
	      }

		boolean flag = false;
		int count = 0;

		//求出本月上课最早的时间和最晚的时间
		List<Integer> timeMAXMin =  getMaxandMin(listValue);
		if(timeMAXMin == null){
			return null;
		}
		int delStrat = timeMAXMin.get(0);
		int delEnd = timeMAXMin.get(1);
		int delmin = timeMAXMin.get(2);

		//遍历集合数据，产生数据行
		 int countstart = 0;

	      for(Map m:listValue){//循环课程数据
			  //判断是否是星期行
			  flag = false;
			  if((!"".equals(m.get("H").toString()))){
				  flag = true;
				  countstart = 0;
			  }else{
			  	  countstart ++;
			  }
			  if(delStrat != 0 || delEnd !=0){
			  	 //删除头
				  if(countstart > 0 && countstart <= delStrat){
				  		continue;
				  }
				  if(countstart > 0 && delmin < 7){//少删两行
						if(countstart > delStrat + delmin +2){
							continue;
						}
				  }
				  if(countstart > 0 && delmin >=7){
					  if(countstart > delStrat + delmin){
						  continue;
					  }
				  }
			  }
			  //添加空行
//			  if(flag){
//				  if(count != 0 && count < max){
//					  for(int j=0;j<max-count;j++){
//						  index++;
//						  row =  sheet.createRow(index);
//						  for(int i = 0; i < listKey.size(); i++){
//							  //创建一个单元格
//							  HSSFCell cell = row.createCell(i);
//							  if(i==0){ //H列
//								  cell.setCellStyle(style4);
//
//							  }else{//日期列
//								  cell.setCellStyle(style2);
//							  }
//							  cell.setCellValue("");
//						  }
//					  }
//				  }
//				  count =0;
//			  }else{
//				  count++;
//			  }
			  index++;
			  row = sheet.createRow(index);//创建行
			  //循环每行单元格数据
		      for(int i = 0; i < listKey.size(); i++){
		      	//创建一个单元格
				  HSSFCell cell = row.createCell(i);
				  if(i==0){ //H列
					  if(flag){
						  cell.setCellStyle(style5);
					  }else{
						  cell.setCellStyle(style4);
					  }
				  }else{//日期列
					  if(flag){
						  cell.setCellStyle(style3);
					  }else{
						  cell.setCellStyle(style2);
					  }
				  }

				  String value =m.get(listKey.get(i))!=null?m.get(listKey.get(i)).toString():"";

				  HSSFRichTextString text_temp = new HSSFRichTextString();
				  Boolean flagDraft = false;

				  TreeMap<Integer, String> mapBc = new TreeMap<Integer, String>();
				  if(value!=null && value.contains("bcolorb")){

					  String[] bcb = value.split("bcolorb");
					  Map<Integer,Integer> map = new HashMap<Integer, Integer>();
//					  if(bcb.length >2 || value.indexOf("\r\n") > 1){
//						  if(flagDraft){
//							  cell.setCellStyle(cellStyle_1);
//						  }else{
//							  if(flag){
//								  cell.setCellStyle(style3_1);
//							  }else{
//								  cell.setCellStyle(style2_1);
//							  }
//						  }
//
//					  }
					  for (int ik =1 ;ik < bcb.length;ik++){
						  int bkcb = MathUtils.getFromIndex(value,"bcolorb",ik)-14*(ik-1);
						  int bkce = MathUtils.getFromIndex(value,"bcolore",ik)-7-14*(ik-1);
						  map.put(bkcb,bkce);

					  }
					  String newValue = value;
					  value = value.replace("bcolorb", "");
					  value = value.replace("bcolore", "");


					  // 使用正则表达式匹配 bcolorb 到 bcolore 之间的内容
					  String regex = "bcolorb.*?bcolore";
					  Pattern pattern = Pattern.compile(regex);
					  Matcher matcher = pattern.matcher(newValue);

					  // 替换所有匹配的内容为空字符串
					  String result = matcher.replaceAll("");

					  // 输出结果，去掉多余的空白字符
					  //System.out.println(result.trim());


					  for (Map.Entry<Integer, Integer> entry:map.entrySet() ) {
						  mapBc.put(2,value.substring(entry.getKey(), entry.getValue()));
					  }
					  value = result.trim();
				  }
				  
				  if(value!=null&&value.contains("Draft")){//初始化的课
					  flagDraft = true;
					  cell.setCellStyle(cellStyle);
					  value = value.replace("Draft", "");
				  }else{
					  if(value!=null&&value.contains("!@day!@")){
						  cell.setCellStyle(style6);
						  value = value.substring(0, value.length()-7);
						  String v1 = value.substring(0,10);
						  String v2= value.substring(10);
						  String v3 ="      ";
						  value = v1+v3+v2;
						  text_temp = new HSSFRichTextString(value);
						  HSSFFont font5 = workbook.createFont();
						  font5.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
						  font5.setColor(HSSFColor.VIOLET.index);
						  String s = value.substring(16,value.length()-1);

						  text_temp.applyFont(16, value.length(), font5);
						  cell.setCellValue(text_temp);
						  continue;
					  }
				  }
				  //general formatting
				  String[] OLb2 = value.split("\r\n");
				  if(OLb2.length >1 ){
					  if(flagDraft){
						  cell.setCellStyle(cellStyle_1);
					  }else{
						  if(flag){
							  cell.setCellStyle(style3_1);
						  }else{
							  cell.setCellStyle(style2_1);
						  }
					  }

				  }
				  text_temp = new HSSFRichTextString(value);

				  TreeMap<Integer, String> mapCR = new TreeMap<Integer, String>();
				  TreeMap<Integer, String> mapOL = new TreeMap<Integer, String>();

				  if (value.indexOf("!@crb!@") >= 0) {
					  String[] OLb = value.split("!@crb!@");
					  Map<Integer,Integer> map = new HashMap<Integer, Integer>();
						if(OLb.length >2 || value.indexOf("\r\n") > 1){
							if(flagDraft){
								cell.setCellStyle(cellStyle_1);
							}else{
								if(flag){
									cell.setCellStyle(style3_1);
								}else{
									cell.setCellStyle(style2_1);
								}
							}

						}
					  for (int ik =1 ;ik < OLb.length;ik++){
						  int crb = MathUtils.getFromIndex(value,"!@crb!@",ik)-14*(ik-1);
						  int cre = MathUtils.getFromIndex(value,"!@cre!@",ik)-7-14*(ik-1);
						  map.put(crb,cre);

					  }
					  value = value.replace("!@crb!@", "");
					  value = value.replace("!@cre!@", "");

					  for (Map.Entry<Integer, Integer> entry:map.entrySet() ) {
						  mapCR.put(0,value.substring(entry.getKey(), entry.getValue()));
					  }

//					  text_temp = new HSSFRichTextString(value);
//					  HSSFFont font5 = workbook.createFont();
//				      font5.setColor(HSSFColor.RED.index);
//				      font5.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
//					  Set<Map.Entry<Integer, Integer>> entries = map.entrySet();
//					  for (Map.Entry<Integer, Integer > entry:entries ) {
//						  Integer key = entry.getKey();
//						  Integer value1 = entry.getValue();
//						  text_temp.applyFont(key, value1, font5);
//					  }


				  }
				  if(value.contains("!@OLb!@")){
					  //value += "\r\n16:00-17:00/Bobo Huang/Economics-!@OLb!@OL!@OLe!@";
					  String[] OLb = value.split("!@OLb!@");
					  if(OLb.length >2 || value.indexOf("\r\n") > 1){
						  if(flagDraft){
							  cell.setCellStyle(cellStyle_1);
						  }else{
							  if(flag){
								  cell.setCellStyle(style3_1);
							  }else{
							  cell.setCellStyle(style2_1);
						  }
						  }

					  }
					  Map<Integer,Integer> map = new HashMap<Integer, Integer>();
					  for (int ik =1 ;ik < OLb.length;ik++){
						  int crb = MathUtils.getFromIndex(value,"!@OLb!@",ik)-14*(ik-1);
						  int cre = MathUtils.getFromIndex(value,"!@OLe!@",ik)-7-14*(ik-1);
						  map.put(crb,cre);

					  }
					  value = value.replace("!@OLb!@", "");
					  value = value.replace("!@OLe!@", "");

					  for (Map.Entry<Integer, Integer> entry:map.entrySet() ) {
						  mapOL.put(1,value.substring(entry.getKey(), entry.getValue()));
					  }

//					  text_temp = new HSSFRichTextString(value);
//					  HSSFFont font5 = workbook.createFont();
//					  font5.setColor(HSSFColor.BLUE.index);
//					  font5.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
//					  Set<Map.Entry<Integer, Integer>> entries = map.entrySet();
//					  for (Map.Entry<Integer, Integer > entry:entries ) {
//						  Integer key = entry.getKey();
//						  Integer value1 = entry.getValue();
//						  text_temp.applyFont(key, value1, font5);
//					  }


				  }
				  text_temp = new HSSFRichTextString(value);
				  mapCR.putAll(mapOL);
				  mapCR.putAll(mapBc);
				  Set<Map.Entry<Integer, String>> entries = mapCR.entrySet();
				  int crCount = 1;
				  int olCount = 1;
				  for (Map.Entry<Integer, String> entry:entries ) {
					  Integer key = entry.getKey();
					  String value1 = entry.getValue();
					  HSSFFont font5 = workbook.createFont();
					  if (key == 0) {
						  //red
						  for (int iii = 0; iii < MathUtils.countOccurrences(value, value1); iii++) {
							  font5.setColor(HSSFColor.RED.index);
							  font5.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
							  int startIndex = MathUtils.findNthOccurrence(value, value1, crCount);
							  text_temp.applyFont(startIndex, startIndex + value1.length(), font5);
							  crCount++;
						  }
					  }
					  if (key == 1) {
						  //blue
						  for (int jjj = 0; jjj < MathUtils.countOccurrences(value, value1); jjj++) {
							  font5.setColor(HSSFColor.BLUE.index);
							  font5.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
							  int startIndex = MathUtils.findNthOccurrence(value, value1, olCount);
							  text_temp.applyFont(startIndex, startIndex + value1.length(), font5);
							  olCount++;
						  }
					  }
					  if (key == 2) {
						  //bkcolor
						  // Convert hex color to RGB
						  int r = Integer.valueOf(value1.substring(1, 3), 16);
						  int g = Integer.valueOf(value1.substring(3, 5), 16);
						  int b = Integer.valueOf(value1.substring(5, 7), 16);

//						  // Get current cell style
						  HSSFCellStyle newStyle = workbook.createCellStyle();
						  newStyle.cloneStyleFrom(style2);
						  // Set custom background color
						  HSSFPalette palette2 = workbook.getCustomPalette();
						  byte red = (byte)(r & 0xFF);
						  byte green = (byte)(g & 0xFF);
						  byte blue = (byte)(b & 0xFF);

						  // Find an unused color index by using the color hex value to generate index
						  // Create a more unique index based on the actual RGB values
						  // Use a prime number for modulo to reduce collisions
						  //int uniqueColorValue = (r << 16) | (g << 8) | b;
						  //short colorIndex = (short)(Math.abs(uniqueColorValue % 47) + 16); // 16-63 range using prime number 47
						  short colorIndex = (short)COLOR_INDEX_MAP.get(value1.toUpperCase());
						  // Create a color key to track which colors we've already set
						 // String colorKey = r + "," + g + "," + b;
						  
						  try {
							  palette2.setColorAtIndex(colorIndex, red, green, blue);
						  } catch (Exception e) {
							  // If this index is already used with a different color, try next available index
//							  for (short i2 = 16; i2 < 64; i2++) {
//								  try {
//									  palette2.setColorAtIndex(i2, red, green, blue);
//									  colorIndex = i2;
//									  break;
//								  } catch (Exception e2) {
//									  continue;
//								  }
//							  }
						  }

						  newStyle.setFillForegroundColor(colorIndex);
						  newStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
						  newStyle.setWrapText(true);
						  cell.setCellStyle(newStyle);
					  }
				  }

				  cell.setCellValue(text_temp);
			  }



		  }
	      index++;
	      row = sheet.createRow(index);
	      //产生表格标题行
	      for (int i = 1; i <=listKeyD.size(); i++) {
		      HSSFCell cell = row.createCell(i);
		      cell.setCellStyle(style);
		      HSSFRichTextString text = new HSSFRichTextString(listKeyD.get(i-1));
		      cell.setCellValue(text);
		  }
	    //遍历集合数据，产生数据行
	      if(listValueD!=null){
	    	  for(Map m:listValueD){
		    	  index++;
			      row = sheet.createRow(index);
			      for(int i = 1; i <= listKeyD.size(); i++){
					  HSSFCell cell = row.createCell(i);
		              cell.setCellStyle(style2);
					  String value =m.get(listKeyD.get(i-1))!=null?m.get(listKeyD.get(i-1)).toString():"";
					  cell.setCellValue(value);
				  }	
			  }
	      }
	      index = 0;
	      //产生表格标题行
	      row = sheet.getRow(index);
	      int k=listKey.size();
	      for (int i = 0; i < listKeyR.size(); i++) {
	    	  int r=i+k;
		      HSSFCell cell = row.createCell(r);
		      cell.setCellStyle(style);
		      HSSFRichTextString text = new HSSFRichTextString(listKeyR.get(i));
		      cell.setCellValue(text);
		  }
	      //遍历集合数据，产生数据行
	      for(Map m:listValueR){
	    	  index++;
		      row = sheet.getRow(index);
		      for(int i = 0; i < listKeyR.size(); i++){
		    	  int r=i+k;
				  HSSFCell cell = row.createCell(r);
	              cell.setCellStyle(style2);
				  String value =m.get(listKeyR.get(i))!=null?m.get(listKeyR.get(i)).toString():"";
				  String newValue = value;
				  if(value!=null && value.contains("bcolorb")){
					      int bkColorIndex = value.indexOf("bcolorb");
					      String colorHex = value.substring(bkColorIndex + 7, bkColorIndex + 14); // Get color value after "bkcolor"
					      value = value.substring(0, bkColorIndex); // Get content before "bkcolor"

					      // Convert hex color to RGB
					      int r2 = Integer.valueOf(colorHex.substring(1, 3), 16);
					      int g = Integer.valueOf(colorHex.substring(3, 5), 16);
					      int b = Integer.valueOf(colorHex.substring(5, 7), 16);

					      // Get current cell style
					      HSSFCellStyle newStyle = workbook.createCellStyle();
					      newStyle.cloneStyleFrom(style2);

					      // Set custom background color
					      HSSFPalette palette2 = workbook.getCustomPalette();
					      byte red = (byte)(r2 & 0xFF);
					      byte green = (byte)(g & 0xFF);
					      byte blue = (byte)(b & 0xFF);

					      // Find an unused color index by using the color hex value to generate index
					      // Create a more unique index based on the actual RGB values
					      // Use a prime number for modulo to reduce collisions
					      //int uniqueColorValue = (r2 << 16) | (g << 8) | b;
					      //short colorIndex = (short)(Math.abs(uniqueColorValue % 47) + 16); // 16-63 range using prime number 47
					      short colorIndex = (short)COLOR_INDEX_MAP.get(colorHex.toUpperCase());
					      // Create a color key to track which colors we've already set
					      String colorKey = r2 + "," + g + "," + b;
					      
					      try {
					          palette2.setColorAtIndex(colorIndex, red, green, blue);
					      } catch (Exception e) {
					          // If this index is already used with a different color, try next available index
//					          for (short i2 = 16; i2 < 64; i2++) {
//					              try {
//					                  palette2.setColorAtIndex(i2, red, green, blue);
//					                  colorIndex = i2;
//					                  break;
//					              } catch (Exception e2) {
//					                  continue;
//					              }
//					          }
					      }

					      newStyle.setFillForegroundColor(colorIndex);
					      newStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);

					      cell.setCellStyle(newStyle);
				  }
				  int startIndex = newValue.indexOf("bcolorb");
				  int endIndex = newValue.indexOf("bcolore", startIndex);

				  if (startIndex != -1 && endIndex != -1) {
					  // endIndex + "bcolore".length() 是结束索引
					  endIndex += "bcolore".length();
					  // 删除中间的部分
					  String result = newValue.substring(0, startIndex) + newValue.substring(endIndex);
					  newValue = result;
				  }
				  cell.setCellValue(newValue);
			  }	
		  }
	      return workbook;
	}

	private static List<Integer> getMaxandMin(List<Map> listValue) {

		Integer[] indexArr  = {0,2,4,6,8,10,12,14,16,18,20,22};
		List<Integer> indexlist = new ArrayList<Integer>();
		for (Integer arr:indexArr) {
			indexlist.add(arr);
		}

		List<Integer> list = new ArrayList<Integer>();
		for(Map m:listValue) {//循环课程数据

			//判断是否存在数据，没有跳过
			  if(m.size() == 8){
				  String h = m.get("H").toString();
				  String Monday = m.get("Monday").toString();
				  String Tuesday = m.get("Tuesday").toString();
				  String Wednesday = m.get("Wednesday").toString();
				  String Thursday = m.get("Thursday").toString();
				  String Friday = m.get("Friday").toString();
				  String Saturday = m.get("Saturday").toString();
				  String Sunday = m.get("Sunday").toString();
				  //没有数据跳过
				  if("".equals(h)&&"".equals(Monday)&&"".equals(Tuesday)&&"".equals(Wednesday)&&"".equals(Thursday)&&"".equals(Friday)&&"".equals(Saturday)&&"".equals(Sunday)){
					  continue;
				  }
				  //是日期行的跳过
				  if(!"".equals(h)){
				  		continue;
				  }

				  if(!"".equals(Monday)){
						list.add(Integer.parseInt(Monday.substring(0,2)));
				  }

				  if(!"".equals(Tuesday)){
					  list.add(Integer.parseInt(Tuesday.substring(0,2)));
				  }
				  if(!"".equals(Wednesday)){
					  list.add(Integer.parseInt(Wednesday.substring(0,2)));
				  }
				  if(!"".equals(Thursday)){
					  list.add(Integer.parseInt(Thursday.substring(0,2)));
				  }
				  if(!"".equals(Friday)){
					  list.add(Integer.parseInt(Friday.substring(0,2)));
				  }
				  if(!"".equals(Saturday)){
					  list.add(Integer.parseInt(Saturday.substring(0,2)));
				  }
				  if(!"".equals(Sunday)){
					  list.add(Integer.parseInt(Sunday.substring(0,2)));
				  }

			  }
		}
		if(CollectionUtils.isEmpty(list)){
			System.out.println("kong");
			return null;
		}
		Collections.sort(list);

		Integer min = list.get(0);
		Integer max = list.get(list.size()-1);

		if(min % 2 !=0){//基数
			min = min- 1;
		}
		if(max %2 != 0){
			max = max- 1;
		}

		Integer indexStart = indexlist.indexOf(min);
		Integer indexEnd = indexlist.indexOf(max);

		Integer count = indexEnd - indexStart + 1;
		list.clear();;
		list.add(indexStart);//头删除几行
		list.add(11-indexEnd);//尾删除几行
		list.add(count);//行数



		return list;
	}


	@SuppressWarnings({"unchecked"})
	public static HSSFWorkbook exportExcelMx(String title, List<String> listKey, List<Map> listValue, String pattern,String title1,
			String title21,String title22,String title31,String title32,String title41,String title42,String end01,String end02,
			String end03,String end1,String end2) {
	      // 声明一个工作薄
	      HSSFWorkbook workbook = new HSSFWorkbook();
	      // 生成一个表格
	      HSSFSheet sheet = workbook.createSheet(title);
	      // 设置表格默认列宽度为20个字节
	      sheet.setDefaultColumnWidth((int) 20);
	      // 生成一个样式
	      HSSFCellStyle style = workbook.createCellStyle();
	      // 设置这些样式
	      style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
	      style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); 
	      // 生成一个字体
	      HSSFFont font = workbook.createFont();
	      font.setColor(HSSFColor.BLACK.index);
	      font.setFontHeightInPoints((short) 12);
	      font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
	      // 把字体应用到当前的样式
	      style.setFont(font);
	      
	   // 生成一个样式
	      HSSFCellStyle style0 = workbook.createCellStyle();
	      // 设置这些样式
	      style0.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      style0.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); 
	      // 生成一个字体
	      HSSFFont font0 = workbook.createFont();
	      font0.setColor(HSSFColor.BLACK.index);
	      font0.setFontHeightInPoints((short) 12);
	      font0.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
	      // 把字体应用到当前的样式
	      style0.setFont(font0);
	      
	      // 生成一个样式
	      HSSFCellStyle style1 = workbook.createCellStyle();
	      // 设置这些样式
	      style1.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      style1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); 
	      // 生成一个字体
	      HSSFFont font1 = workbook.createFont();
	      font1.setColor(HSSFColor.BLACK.index);
	      font1.setFontHeightInPoints((short) 24);
	      font1.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
	      // 把字体应用到当前的样式
	      style1.setFont(font1);
	      // 生成并设置另一个样式
	      HSSFCellStyle style2 = workbook.createCellStyle();
	      style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
	      style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
	      // 生成另一个字体
	      HSSFFont font2 = workbook.createFont();
	      font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
	      // 把字体应用到当前的样式
	      style2.setFont(font2);
	   // 生成并设置另一个样式
	      HSSFCellStyle style3 = workbook.createCellStyle();
	      style3.setAlignment(HSSFCellStyle.ALIGN_LEFT);
	      style3.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
	      // 生成另一个字体
	      HSSFFont font3 = workbook.createFont();
	      font3.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
	      // 把字体应用到当前的样式
	      style3.setFont(font3);
	      
	      HSSFRow row = sheet.createRow(0);
	      Region region = new Region((short)0,(short)0,(short)0,(short)4);//合并从第rowFrom行columnFrom列 
	      sheet.addMergedRegion(region);
	      HSSFCell cell = row.createCell(0);
	      cell.setCellStyle(style1);
	      HSSFRichTextString text1 = new HSSFRichTextString(title1);
	      cell.setCellValue(text1);
	      
	      row = sheet.createRow(2);
	      cell = row.createCell(0);
	      cell.setCellStyle(style0);
	      HSSFRichTextString text21 = new HSSFRichTextString(title21);
	      cell.setCellValue(text21);
	      cell = row.createCell(1);
	      cell.setCellStyle(style3);
	      HSSFRichTextString text22 = new HSSFRichTextString(title22);
	      cell.setCellValue(text22);
	      
	      row = sheet.createRow(3);
	      cell = row.createCell(0);
	      cell.setCellStyle(style0);
	      HSSFRichTextString text31 = new HSSFRichTextString(title31);
	      cell.setCellValue(text31);
	      cell = row.createCell(1);
	      cell.setCellStyle(style3);
	      HSSFRichTextString text32 = new HSSFRichTextString(title32);
	      cell.setCellValue(text32);
	      
	      row = sheet.createRow(4);
	      cell = row.createCell(0);
	      cell.setCellStyle(style0);
	      HSSFRichTextString text41 = new HSSFRichTextString(title41);
	      cell.setCellValue(text41);
	      cell = row.createCell(1);
	      cell.setCellStyle(style3);
	      HSSFRichTextString text42 = new HSSFRichTextString(title42);
	      cell.setCellValue(text42);
	      
	      
	      
	      int index = 6;
	      //产生表格标题行
	      row = sheet.createRow(index);
	      for (int i = 0; i < listKey.size(); i++) {
	         cell = row.createCell(i);
	         cell.setCellStyle(style);
	         HSSFRichTextString text = new HSSFRichTextString(listKey.get(i));
	         cell.setCellValue(text);
	      }
	      
	      //遍历集合数据，产生数据行
	      for(Map m:listValue){
	    	  index++;
			  ZidingyiSon zs=new ZidingyiSon();
			  List<String> list2=new ArrayList<String>();
		      row = sheet.createRow(index);
		      for(int i = 0; i < listKey.size(); i++){
				  cell = row.createCell(i);
	              cell.setCellStyle(style2);
				  String value =m.get(listKey.get(i))!=null?m.get(listKey.get(i)).toString():"";
				  cell.setCellValue(value);
			  }	
		  }
	      index++;
	      row = sheet.createRow(index);
	      region = new Region((short)index,(short)0,(short)index,(short)1);//合并从第rowFrom行columnFrom列 
	      sheet.addMergedRegion(region);
	      cell = row.createCell(0);
	      cell.setCellStyle(style);
	      HSSFRichTextString value01 = new HSSFRichTextString(end01);
	      cell.setCellValue(value01);
	      cell = row.createCell(1);
	      cell.setCellStyle(style);
	      cell = row.createCell(2);
	      cell.setCellStyle(style);
	      HSSFRichTextString value02 = new HSSFRichTextString(end02);
	      cell.setCellValue(value02);
	      cell = row.createCell(3);
	      cell.setCellStyle(style);
	      HSSFRichTextString value = new HSSFRichTextString("");
	      cell.setCellValue(value);
	      cell = row.createCell(4);
	      cell.setCellStyle(style);
	      HSSFRichTextString value03 = new HSSFRichTextString(end03);
	      cell.setCellValue(value03);
	      
	      index++;index++;
	      row = sheet.createRow(index);
	      region = new Region((short)index,(short)0,(short)index,(short)4);//合并从第rowFrom行columnFrom列 
	      sheet.addMergedRegion(region);
	      cell = row.createCell(0);
	      cell.setCellStyle(style3);
	      HSSFRichTextString value1 = new HSSFRichTextString(end1);
	      cell.setCellValue(value1);
	      
	      index++;index++;
	      row = sheet.createRow(index);
	      cell = row.createCell(3);
	      cell.setCellStyle(style0);
	      HSSFRichTextString value2 = new HSSFRichTextString(end2);
	      cell.setCellValue(value2);
	      
	      return workbook;
	}
	
	
	@SuppressWarnings({"unchecked"})
	public static void exportExcelTj(String title, List<String> listKey, 
			List<Map> listValue, OutputStream out, String pattern) {
	      // 声明一个工作薄
	      HSSFWorkbook workbook = new HSSFWorkbook();
	      // 生成一个表格
	      HSSFSheet sheet = workbook.createSheet(title);
	      // 设置表格默认列宽度为15个字节
	      sheet.setDefaultColumnWidth((int) 15);
	      // 生成一个样式
	      HSSFCellStyle style = workbook.createCellStyle();
	      // 设置这些样式
	      style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
	      style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); 
	      // 生成一个字体
	      HSSFFont font = workbook.createFont();
	      font.setColor(HSSFColor.VIOLET.index);
	      font.setFontHeightInPoints((short) 12);
	      font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
	      // 把字体应用到当前的样式
	      style.setFont(font);
	      // 生成并设置另一个样式
	      HSSFCellStyle style2 = workbook.createCellStyle();
	      style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
	      style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
	      // 生成另一个字体
	      HSSFFont font2 = workbook.createFont();
	      font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
	      // 把字体应用到当前的样式
	      style2.setFont(font2);
	      int index = 0;
	      //产生表格标题行
	      HSSFRow row = sheet.createRow(index);
	      for (int i = 0; i < listKey.size(); i++) {
	         HSSFCell cell = row.createCell(i);
	         cell.setCellStyle(style);
	         HSSFRichTextString text = new HSSFRichTextString(listKey.get(i));
	         cell.setCellValue(text);
	      }
	      
	      //遍历集合数据，产生数据行
	      for(Map m:listValue){
	    	  index++;
			  ZidingyiSon zs=new ZidingyiSon();
			  List<String> list2=new ArrayList<String>();
		      row = sheet.createRow(index);
		      for(int i = 0; i < listKey.size(); i++){
				  HSSFCell cell = row.createCell(i);
	              cell.setCellStyle(style2);
				  String value =m.get(listKey.get(i))!=null?m.get(listKey.get(i)).toString():"";
				  cell.setCellValue(value);
			  }	
		  }
        try {
           workbook.write(out);
        } catch (IOException e) {
           e.printStackTrace();
        }
	}
	
	@SuppressWarnings({"unchecked"})
	public static void exportExcel(String title, List<LabelValue> headers, 
			List<?> dataList, OutputStream out, String pattern) {

      // 声明一个工作薄
      HSSFWorkbook workbook = new HSSFWorkbook();
      // 生成一个表格
      HSSFSheet sheet = workbook.createSheet(title);

      // 设置表格默认列宽度为15个字节

      sheet.setDefaultColumnWidth((int) 15);
      // 生成一个样式
      HSSFCellStyle style = workbook.createCellStyle();
      // 设置这些样式
//      style.setFillForegroundColor(HSSFColor.SKY_BLUE.index);
//      style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
      style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
      style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
      style.setBorderRight(HSSFCellStyle.BORDER_THIN);
      style.setBorderTop(HSSFCellStyle.BORDER_THIN);
      style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
      style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
      
      // 生成一个字体
      HSSFFont font = workbook.createFont();
      font.setColor(HSSFColor.VIOLET.index);
      font.setFontHeightInPoints((short) 12);
      font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
      
      
      // 把字体应用到当前的样式
      style.setFont(font);

      // 生成并设置另一个样式
      HSSFCellStyle style2 = workbook.createCellStyle();
//      style2.setFillForegroundColor(HSSFColor.LIGHT_YELLOW.index);
//      style2.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
      style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
      style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
      style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
      style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
      style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
      style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

      // 生成另一个字体
      HSSFFont font2 = workbook.createFont();
      font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
      // 把字体应用到当前的样式
      style2.setFont(font2);

      // 声明一个画图的顶级管理器
      HSSFPatriarch patriarch = sheet.createDrawingPatriarch();

    /*  // 定义注释的大小和位置,详见文档
      HSSFComment comment = patriarch.createComment(new HSSFClientAnchor(0, 0, 0, 0, (short) 4, 2, (short) 6, 5));

      // 设置注释内容
      comment.setString(new HSSFRichTextString("可以在POI中添加注释！"));

      // 设置注释作者，当鼠标移动到单元格上是可以在状态栏中看到该内容.
      comment.setAuthor("lanmo");*/

      //产生表格标题行
      HSSFRow row = sheet.createRow(0);

      for (int i = 0; i < headers.size(); i++) {
         HSSFCell cell = row.createCell(i);
         cell.setCellStyle(style);
         HSSFRichTextString text = new HSSFRichTextString(headers.get(i).getLabel());
         cell.setCellValue(text);
      }

      //遍历集合数据，产生数据行
      Iterator<?> it = dataList.iterator();
      int index = 0;
      while (it.hasNext()) {
         index++;
         row = sheet.createRow(index);
         Object obj = it.next();
         Class tCls = obj.getClass();
         //利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
         Field[] fields = obj.getClass().getDeclaredFields();
         for (int i = 0; i < fields.length; i++) {
        	 Field field = fields[i];
             String fieldName = field.getName();
             for (int j = 0; j < headers.size(); j++) {
            	 if(StringUtils.equals(headers.get(j).getValue(), fieldName)){
            		 String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() 
                    		 + fieldName.substring(1);
            		  HSSFCell cell = row.createCell(j);
                      cell.setCellStyle(style2);
                      try {
                          Method getMethod = tCls.getMethod(getMethodName,
                                new Class[] {});
                          Object value = getMethod.invoke(obj, new Object[] {});
                          if(value ==null)
                          	value = "";
                          //判断值的类型后进行强制类型转换
                          String textValue = null;

//          	              if (value instanceof Integer) {

//          	                 int intValue = (Integer) value;

//          	                 cell.setCellValue(intValue);

//          	              } else if (value instanceof Float) {

//          	                 float fValue = (Float) value;

//          	                 textValue = new HSSFRichTextString(

//          	                       String.valueOf(fValue));

//          	                 cell.setCellValue(textValue);

//          	              } else if (value instanceof Double) {

//          	                 double dValue = (Double) value;

//          	                 textValue = new HSSFRichTextString(

//          	                       String.valueOf(dValue));

//          	                 cell.setCellValue(textValue);

//          	              } else if (value instanceof Long) {

//          	                 long longValue = (Long) value;

//          	                 cell.setCellValue(longValue);

//          	              } 

                          if (value instanceof Boolean) {
                             boolean bValue = (Boolean) value;
                             textValue = "1";
                             if (!bValue) {
                                textValue ="0";
                             }

                          } else if (value instanceof Date) {
                             Date date = (Date) value;
                             SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                              textValue = sdf.format(date);
                          }  else if (value instanceof byte[]) {
                             // 有图片时，设置行高为60px;
                             row.setHeightInPoints(60);
                             // 设置图片所在列宽度为80px,注意这里单位的一个换算
                             sheet.setColumnWidth(i, (short) (35.7 * 80));
                              sheet.autoSizeColumn(i);
                             byte[] bsValue = (byte[]) value;
                             HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0,
                                   1023, 255, (short) 6, index, (short) 6, index);
                             anchor.setAnchorType(2);
                             patriarch.createPicture(anchor, workbook.addPicture(
                                   bsValue, HSSFWorkbook.PICTURE_TYPE_JPEG));
                          } else{
                             //其它数据类型都当作字符串简单处理
                             textValue = value.toString();
                          }
                          //如果不是图片数据，就利用正则表达式判断textValue是否全部由数字组成
                          if(textValue!=null){
                             Pattern p = Pattern.compile("^//d+(//.//d+)?$");   
                             Matcher matcher = p.matcher(textValue);
                             if(matcher.matches()){
                                //是数字当作double处理
                                cell.setCellValue(Double.parseDouble(textValue));
                             }else{
                                HSSFRichTextString richString = new HSSFRichTextString(textValue);
                                HSSFFont font3 = workbook.createFont();
                                font3.setColor(HSSFColor.BLUE.index);
                                richString.applyFont(font3);
                                cell.setCellValue(richString);
                             }
                          }
                      } catch (SecurityException e) {
                          // TODO Auto-generated catch block
                          e.printStackTrace();

                      } catch (NoSuchMethodException e) {
                          // TODO Auto-generated catch block
                          e.printStackTrace();
                      } catch (IllegalArgumentException e) {
                          // TODO Auto-generated catch block
                          e.printStackTrace();

                      } catch (IllegalAccessException e) {
                          // TODO Auto-generated catch block
                          e.printStackTrace();
                      } catch (InvocationTargetException e) {
                          // TODO Auto-generated catch block
                          e.printStackTrace();
                      } finally {
                          //清理资源
                      }
            	 }
              }
         }
      }

      try {
         workbook.write(out);
      } catch (IOException e) {
         // TODO Auto-generated catch block
         e.printStackTrace();
      }
	}
	public static CellStyle generatorHSSFStyle(Workbook workbook){
		CellStyle style = workbook.createCellStyle();
	      // 设置这些样式
//	      style.setFillForegroundColor(HSSFColor.SKY_BLUE.index);
//	      style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
	      style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	      style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	      style.setBorderRight(HSSFCellStyle.BORDER_THIN);
	      style.setBorderTop(HSSFCellStyle.BORDER_THIN);
	      style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		return style;
	}
	
	public static HSSFCellStyle generatorBodyHSSFStyle1(HSSFWorkbook workbook){
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFDataFormat df = workbook.createDataFormat();
	    style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	    style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	    style.setBorderRight(HSSFCellStyle.BORDER_THIN);
	    style.setBorderTop(HSSFCellStyle.BORDER_THIN);
	    style.setDataFormat(df.getFormat("#,##0"));
		return style;
	}
	public static HSSFCellStyle generatorBodyHSSFStyle2(HSSFWorkbook workbook){
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFDataFormat df = workbook.createDataFormat();
	    style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
	    style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	    style.setBorderRight(HSSFCellStyle.BORDER_THIN);
	    style.setBorderTop(HSSFCellStyle.BORDER_THIN);
	    style.setDataFormat(df.getFormat("#,##0.00"));
		return style;
	}
	public static HSSFCellStyle generatorFirstHSSFStyle(HSSFWorkbook workbook){
		 // 生成一个样式
		HSSFCellStyle style = workbook.createCellStyle();
//      style.setFillForegroundColor(HSSFColor.SKY_BLUE.index);
//      style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
      style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
      style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
      style.setBorderRight(HSSFCellStyle.BORDER_THIN);
      style.setBorderTop(HSSFCellStyle.BORDER_THIN);
      style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
      style.setVerticalAlignment(HSSFCellStyle.ALIGN_CENTER);
      
      // 生成一个字体
      HSSFFont font = workbook.createFont();
      font.setColor(HSSFColor.VIOLET.index);
      font.setFontHeightInPoints((short) 12);
      font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
      // 把字体应用到样式
      style.setFont(font);
      return style;
	}
}
