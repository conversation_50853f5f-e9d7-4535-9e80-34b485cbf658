package com.lanmosoft.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import com.lanmosoft.dao.model.Timezone;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class LanDateUtils {
	private static Log log = LogFactory.getLog(LanDateUtils.class);
	public static String currentTimeStr(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String dateStr = sdf.format(new Date());
		return dateStr;
	}
	/**
	 * currentDateTime: 
	 * 		yyyy-MM-dd HH:mm:ss
	 * @return
	 */
	public static String currentTimeStr_web(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateStr = sdf.format(new Date());
		return dateStr;
	}
	/**
	 * currentDateTime: 
	 * 		yyyy-MM-dd
	 * @return
	 */
	public static String currentDateStr(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String dateStr = sdf.format(new Date());
		return dateStr;
	}
	public static String currentDateStr_web(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String dateStr = sdf.format(new Date());
		return dateStr;
	}
	
	public static Date getNowDate(){
		return new Date();
	}
	public static Date getBeforeDate_min(int min){
		return org.apache.commons.lang.time.DateUtils.addMinutes(new Date(), min*-1);
	}
	public static Date getNextDate_min(int min){
		return org.apache.commons.lang.time.DateUtils.addMinutes(new Date(), min);
	}
	public static Date getBeforeDate_month(Date date,int month){
		return org.apache.commons.lang.time.DateUtils.addMonths(date, month*-1);
	}
	/**
	 * 
	 * @param
	 * @param month to be add
	 * @return
	 */
	public static Date getNextDate_month(Date date,int month){
		return org.apache.commons.lang.time.DateUtils.addMonths(date, month);
	}
	
	public static Date getNext_Day(Date date, int day){
		return org.apache.commons.lang.time.DateUtils.addDays(date, day);
	}
	
	public static String format(Date d,String format){
		if(d==null){
			return "";
		}
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		return sdf.format(d);
	}
	
	public static String getFormatDateStr_web(Date d){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(d);
	}

	 /**
     * This method generates a string representation of a date/time
     * in the format you specify on input
     *
     * @param aMask the date pattern the string is in
     * @param strDate a string representation of a date
     * @return a converted Date object
     * @throws ParseException when String doesn't match the expected format
     * @see java.text.SimpleDateFormat
     */
    public static Date convertStringToDate(String aMask, String strDate)
            {
        SimpleDateFormat df;
        Date date=null;
        df = new SimpleDateFormat(aMask);

       /* if (log.isDebugEnabled()) {
            log.debug("converting '" + strDate + "' to date with mask '" + aMask + "'");
        }*/

        try {
            date = df.parse(strDate);
        } catch (ParseException pe) {
            //log.error("ParseException: " + pe);
            
        }

        return (date);
    }
	public static long diffDays(Date date1, Date date2) throws Exception {
		SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd");
		   java.util.Date begin=dfs.parse(dfs.format(date1));
		   java.util.Date end =dfs.parse(dfs.format(date2));
		   long between=(end.getTime()-begin.getTime())/1000;//除以1000是为了转换成秒
		   long day1=between/(24*3600);
		return day1;
	}
	
	public static List<String> getSameDay(String baseDateStr, String endDateStr){
		if(StringUtils.isEmpty(endDateStr)|| StringUtils.isEmpty(baseDateStr)) return null;
		Date baseDate = LanDateUtils.convertStringToDate("yyyy-MM-dd", baseDateStr);
		Date endDate = LanDateUtils.convertStringToDate("yyyy-MM-dd", endDateStr);
		if(baseDate==null ||endDate==null) return null;
		if(endDate.before(baseDate)) return null;
		

		Calendar cal = Calendar.getInstance();
		cal.setTime(baseDate);
		int week = cal.get(Calendar.DAY_OF_WEEK);//获得基准日期的星期数
		int days = (int) ((endDate.getTime()-baseDate.getTime())/(24*60*60*1000));
		List<String> dateLst = new ArrayList<String>();
		
		for(int i=1; i<=days; i++){
			cal.add(Calendar.DATE, 1);
			
			if(cal.get(Calendar.DAY_OF_WEEK)==week){
				System.out.println("date:"+LanDateUtils.format(cal.getTime(), "yyyy-MM-dd"));
				dateLst.add(LanDateUtils.format(cal.getTime(), "yyyy-MM-dd"));
			}
		}
		return dateLst;
	}
	/**
	 * 获取开始baseDateStr到endDateStr中的每一天，返回结果不包括baseDateStr
	 * @param baseDateStr
	 * @param endDateStr
	 * @return
	 */
	public static List<String> getDays(String baseDateStr, String endDateStr){
		if(StringUtils.isEmpty(endDateStr)|| StringUtils.isEmpty(baseDateStr)) return null;
		Date baseDate = LanDateUtils.convertStringToDate("yyyy-MM-dd", baseDateStr);
		Date endDate = LanDateUtils.convertStringToDate("yyyy-MM-dd", endDateStr);
		if(baseDate==null ||endDate==null) return null;
		if(endDate.before(baseDate)) return null;
		

		Calendar cal = Calendar.getInstance();
		cal.setTime(baseDate);
		int week = cal.get(Calendar.DAY_OF_WEEK);//获得基准日期的星期数
		int days = (int) ((endDate.getTime()-baseDate.getTime())/(24*60*60*1000));
		
		List<String> dateLst = new ArrayList<String>();
		System.out.println(days);
		for(int i=1; i<=days; i++){
			cal.add(Calendar.DATE, 1);
			dateLst.add(LanDateUtils.format(cal.getTime(), "yyyy-MM-dd"));
			
		}
		return dateLst;
	}
	
	/**
     * 获取某月第一天
     * @param date the calendar field.
     * @param month the month of date to be added to the field.
     * @return
     */
    public static String getFirstDayOfMonth(Date date, int month) {
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(date);
    	calendar.add(Calendar.MONTH, month);
    	calendar.set(Calendar.DATE, calendar.getActualMinimum(Calendar.DATE));
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(calendar.getTime());
    }
    /**
     * 获取某月最后一天
     * @param date the calendar field.
     * @param month the month of date to be added to the field.
     * @return
     */
    public static String getLastDayOfMonth(Date date, int month) {
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(date);
    	calendar.add(Calendar.MONTH, month);
    	calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(calendar.getTime());
    }
    
    /** 
     * 取得某天所在周的第一天 
     *  
     * @param date 
     * @return 
     */ 
    public static Date getFirstDayOfWeek(Date date) { 
        Calendar c = new GregorianCalendar(); 
        c.setFirstDayOfWeek(Calendar.MONDAY); 
        c.setTime(date); 
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek()); 
        return c.getTime(); 
    }

	/**
	 * 获取每天所在周的第一天(字符串)
	 * @param day
	 * @return
	 */
	public static String getFirstDayOfWeekStr(String day) {
    	Date date = stringToDate(day);
		Calendar c = new GregorianCalendar();
		c.setFirstDayOfWeek(Calendar.MONDAY);
		c.setTime(date);
		c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());
		return format(c.getTime(),"yyyy-MM-dd");
	}

	/**
     * 取得某天所在周的最后一天 
     *  
     * @param date 
     * @return 
     */ 
    public static Date getLastDayOfWeek(Date date) { 
        Calendar c = new GregorianCalendar(); 
        c.setFirstDayOfWeek(Calendar.MONDAY); 
        c.setTime(date); 
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6); 
        return c.getTime(); 
    }

	/**
	 * 取得某天所在周的最后一天 (字符串)
	 *
	 * @param day
	 * @return
	 */
	public static String getLastDayOfWeekStr(String day) {
		Date date = stringToDate(day);
		Calendar c = new GregorianCalendar();
		c.setFirstDayOfWeek(Calendar.MONDAY);
		c.setTime(date);
		c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6);
		return format(c.getTime(),"yyyy-MM-dd");
	}
    
    /**
     * 获取当前日期是星期几<br>
     * 
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date dt) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }
    
    /**
     * 获取当前日期是星期几<br>
     * 
     * @param day
     * @return 当前日期是星期几
     */
    public static int getWeekOfStr(String day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(stringToDate(day));
        if(cal.get(Calendar.DAY_OF_WEEK) - 1==0){
        	return 7;
        }
        return cal.get(Calendar.DAY_OF_WEEK) - 1;    
    }
    
    /**
	 * String 转成Date类型
	 */
	public static Date stringToDate(String dateStr){
		Date date = null;
		try {
			SimpleDateFormat DATE = new SimpleDateFormat("yyyy-MM-dd");
			date = DATE.parse(dateStr);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}		
		return date;
	}

	/**
	 * String按制定格式 ,自'1970-01-01 00:00:00'的到当前时间的秒数差
	 */
	public static long stringToLong(String dateStr,String format){
		long time =0;
		Date date = null;
		try {
			SimpleDateFormat DATE = new SimpleDateFormat(format);
			date = DATE.parse(dateStr);
			time = date.getTime()/1000;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return time;
	}

	/**
	 * String按制定格式 ,自'1970-01-01 00:00:00'的到当前时间的秒数差
	 */
	public static long stringToLongTime(String dateStr,String time){
		long timelong =0;
		Date date = null;
		String format = "yyyy-MM-dd HH:mm";
		//获取开始时间秒数和结束时间秒数
		if(StringUtils.equals(time,"24:00")){
			time = "23:59:59";
			format = "yyyy-MM-dd HH:mm:ss";
		}
		try {
			SimpleDateFormat DATE = new SimpleDateFormat(format);
			date = DATE.parse(dateStr+" "+time);
			timelong = date.getTime()/1000;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return timelong;
	}
	
	 /** 
     * 取得某天所在周的所有天
     *  
     * @param day 
     * @return 
     */ 
    public static String getAllDayOfWeek(String day) { 
    	String week="";
    	for(int i=0;i<7;i++){
    		Calendar c = new GregorianCalendar(); 
    	    c.setFirstDayOfWeek(Calendar.MONDAY); 
    	    c.setTime(stringToDate(day)); 
    	    c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek()+i); 
    	    week+="'"+format(c.getTime(), "yyyy-MM-dd")+"',"; 
    	}
    	if(week.length()>1){
    		week=week.substring(0, week.length()-1);
    	}
        return week;
    }

	/**
	 * 时刻比较
	 * @param time1
	 * @param time2
	 * @return
	 */
	public static int compart_time(String time1 ,String time2){
		SimpleDateFormat sf = new SimpleDateFormat("HH:mm");
		try {
			Date dt1 = sf.parse(time1);
			Date dt2 = sf.parse(time2);
			if (dt1.getTime() > dt2.getTime()) {
				//System.out.println("dt1 在dt2后");
				return 1;
			} else if (dt1.getTime() < dt2.getTime()) {
				//System.out.println("dt1在dt2前");
				return -1;
			} else {
				return 0;
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return 0;
	}
    public static int compare_date(String day1, String day2) {
        try {
            Date dt1 = stringToDate(day1);
            Date dt2 = stringToDate(day2);
            if (dt1.getTime() > dt2.getTime()) {
                //System.out.println("dt1 在dt2后");
                return 1;
            } else if (dt1.getTime() < dt2.getTime()) {
                //System.out.println("dt1在dt2前");
                return -1;
            } else {
                return 0;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
    }
    
    public static String getNext_Day(String d, int day){
    	Date dt1 = stringToDate(d);
		return format(org.apache.commons.lang.time.DateUtils.addDays(dt1, day), "yyyy-MM-dd");
	}
    
    public static String getWeekCount(String day2){
    	String day1=format(new Date(),"yyyy-MM-dd");
    	int week=getWeekOfStr(day1);
    	Calendar calendar1 = Calendar.getInstance();
	    Calendar calendar2 = Calendar.getInstance();
	    int y1=Integer.valueOf(day1.substring(0, 4));
	    int y2=Integer.valueOf(day2.substring(0, 4));
	    int m1=Integer.valueOf(day1.substring(5, 7));
	    int m2=Integer.valueOf(day2.substring(5, 7));
	    int d1=Integer.valueOf(day1.substring(8, 10));
	    int d2=Integer.valueOf(day2.substring(8, 10));
	    if(m1==4||m1==6||m1==9||m1==11){
	    	d1=d1+1;
	    }else if(m1==2){
	    	if(y1%4==0){
	    		d1=d1+2;
	    	}else{
	    		d1=d1+3;
	    	}
	    }
	    if(m2==4||m2==6||m2==9||m2==11){
	    	d2=d2+1;
	    }else if(m2==2){
	    	if(y2%4==0){
	    		d2=d2+2;
	    	}else{
	    		d2=d2+3;
	    	}
	    }
	    calendar1.set(y1, m1, d1);
	    calendar2.set(y2, m2, d2);
	    long milliseconds1 = calendar1.getTimeInMillis();
	    long milliseconds2 = calendar2.getTimeInMillis();
	    long diff = milliseconds2 - milliseconds1;
	    long diffDays = diff / (24 * 60 * 60 * 1000);
	    System.out.println("diffDays----"+diffDays);
	    long count=0l;
	    if(diffDays>=0){
	    	if(week+diffDays>7){
	    		if((week+diffDays-7)%7==0){
	    	    	return String.valueOf((week+diffDays-7)/7);
	    	    }
	    		return String.valueOf((week+diffDays-7)/7+1);
	    	}else{
	    	    return String.valueOf(count);
	    	}
	    }else{
	    	if(week+diffDays<=0){
	    		if((week+diffDays)%7==0){
	    	    	return String.valueOf((week+diffDays)/7-1);
	    	    }
	    		return String.valueOf((week+diffDays)/7-1);
	    	}else{
	    		return String.valueOf(count);
	    	}
	    }
    }
    
    public static Integer getValueByTime(String time){
		int flag=0;
		String[] ss=time.split(":");
		if("0".equals(ss[0].substring(0, 1))){
			flag=Integer.valueOf(ss[0].substring(1, 2)+ss[1]);
		}else{
			flag=Integer.valueOf(ss[0]+ss[1]);
		}
		return flag;
	}
    
	public static void main(String[] args) {


		String format = LanDateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
		String riqi = format.substring(0,10);
		String shijian = format.substring(11,16);
		String miao = format.substring(format.length()-2);
		System.out.println(riqi);
		System.out.println(shijian);
		System.out.println(miao);
		System.out.println(format);

	}
	

}
