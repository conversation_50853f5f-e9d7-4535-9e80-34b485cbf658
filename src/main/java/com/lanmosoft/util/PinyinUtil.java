package com.lanmosoft.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import org.apache.commons.lang.StringUtils;

public class PinyinUtil {

	 /**
     * <p>Description: 获取汉字串拼音首字母小写，英文字符不变 </p>
     * @param chinese 汉字字符
     * @return 汉语拼音首字母 
     */
    public static String getTopSpellingLowerCase(String chinese) {
        if (StringUtils.isEmpty(chinese))
            return null;
        StringBuffer pybf = new StringBuffer(); 
        try { 
            char[] arr = chinese.toCharArray(); 
            HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat(); 
            defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE); 
            defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE); 
            for (int i = 0; i < arr.length; i++) { 
                if (arr[i] > 128) { 
                    String[] _t = PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat); 
                    if (_t != null) { 
                        pybf.append(_t[0].charAt(0)); 
                    } 
                } else { 
                    pybf.append(arr[i]); 
                } 
            } 
        } catch (BadHanyuPinyinOutputFormatCombination e) { 
            e.printStackTrace(); 
        } 
        
        return pybf.toString().replaceAll("\\W", "").trim();
    }
    
    /**
     * <p>Description: 获取汉字串拼音首字母大写，英文字符不变 </p>
     * @param chinese 汉字字符
     * @return 汉语拼音首字母 
     */
    public static String getTopSpellingUpCase(String chinese) {
        if (StringUtils.isEmpty(chinese))
            return null;
        StringBuffer pybf = new StringBuffer(); 
        try { 
            char[] arr = chinese.toCharArray(); 
            HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat(); 
            defaultFormat.setCaseType(HanyuPinyinCaseType.UPPERCASE); 
            defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE); 
            for (int i = 0; i < arr.length; i++) { 
                if (arr[i] > 128) { 
                    String[] _t = PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat); 
                    if (_t != null) { 
                        pybf.append(_t[0].charAt(0)); 
                    } 
                } else { 
                    pybf.append(arr[i]); 
                } 
            } 
        } catch (BadHanyuPinyinOutputFormatCombination e) { 
            e.printStackTrace(); 
        } 
        
        return pybf.toString().replaceAll("\\W", "").trim();
    }
    
    /** 
     * 获取汉字串拼音，英文字符不变 
     * @param chinese 汉字字符 
     * @return 汉语拼音 
     */ 
    public static String getPhoneticSpelling(String chinese) { 
        StringBuffer pybf = new StringBuffer(); 
        try { 
            char[] arr = chinese.toCharArray(); 
            HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat(); 
            defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE); 
            defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE); 
            for (int i = 0; i < arr.length; i++) { 
                if (arr[i] > 128) {
                    pybf.append(PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat)[0]); 
                } else { 
                    pybf.append(arr[i]); 
                } 
            } 
        } catch (BadHanyuPinyinOutputFormatCombination e) { 
            e.printStackTrace(); 
        } 
        
        return pybf.toString(); 
    } 
    
    public static void main(String[] args) {
        System.out.println(getTopSpellingLowerCase("你好weqweq"));
        System.out.println(getTopSpellingUpCase("你好!"));
        System.out.println(getPhoneticSpelling("你好"));
    }

}
