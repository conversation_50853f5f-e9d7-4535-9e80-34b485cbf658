package com.lanmosoft.util;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import net.sf.json.JSONArray;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.apache.commons.beanutils.PropertyUtilsBean;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.beanutils.converters.DateTimeConverter;

/**
 * Map 与 Bean 之间的封装
 */
public class BeanTranUtil {

    /**
     *

     * Description:  Map转对象<BR>

     * <AUTHOR>

     * @date 2017年7月17日 上午11:21:35

     * @param map
     * @param obj
     */
    // Map --> Bean 2: 利用org.apache.commons.beanutils 工具类实现 Map --> Bean
    public static void transMap2Bean1(Map<String, Object> map, Object obj) {
        if (map == null || obj == null) {
            return;
        }
        try {
            BeanUtils.populate(obj, map);
        } catch (Exception e) {
            System.out.println("transMap2Bean2 Error " + e);
        }
    }

    public static void transMap2Bean2(Map<String, Object> map, Object obj) {

        try {
            DateTimeConverter dtConverter = new DateConverter();
            ConvertUtilsBean convertUtilsBean = new ConvertUtilsBean();
            convertUtilsBean.deregister(Date.class);
            convertUtilsBean.register(dtConverter, Date.class);
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(convertUtilsBean,
                    new PropertyUtilsBean());
            beanUtilsBean.populate(obj, map);

        } catch (Exception e) {
            System.out.println("transMap2Bean2 Error " + e);
        }
    }

    // Map --> Bean 1: 利用Introspector,PropertyDescriptor实现 Map --> Bean
    public static void transMap2Bean(Map<String, Object> map, Object obj) {

        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();

            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();

                if (map.containsKey(key)) {
                    Object value = map.get(key);
                    // 得到property对应的setter方法
                    Method setter = property.getWriteMethod();
                    setter.invoke(obj, value);
                }

            }

        } catch (Exception e) {
            System.out.println("transMap2Bean Error " + e);
        }

        return;

    }



    /**
     * Description:  对象转换map 设置KEY的大小写  <BR>
     * <AUTHOR>
     * @date 2017年7月17日 上午11:21:35
     *
     * @param bean
     * @param b		KEY是否大写
     * @return
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     * @throws IntrospectionException
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static Map isKeyUpperCase(Object bean,Boolean b) throws IllegalAccessException,
            IllegalArgumentException, InvocationTargetException, IntrospectionException{

        Map<String,Object> map = convertBean(bean);
        Set<String> se = map.keySet();
        Map returnMap = new HashMap();
        if (b) {
            for (String set : se) {
                returnMap.put(set.toUpperCase(), map.get(set));
            }
        }else {
            for (String set : se) {
                returnMap.put(set.toLowerCase(), map.get(set));
            }
        }
        return returnMap;
    }



    /**
     * Description:  对象转换map<BR>
     * <AUTHOR>
     * @date 2017年7月17日 上午11:21:35

     * @param bean
     * @return
     * @throws IntrospectionException
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static Map convertBean(Object bean) throws IntrospectionException,
            IllegalAccessException, IllegalArgumentException, InvocationTargetException {
        Class type = bean.getClass();
        Map returnMap = new HashMap();
        BeanInfo beanInfo = Introspector.getBeanInfo(type);

        PropertyDescriptor[] propertyDescriptors =  beanInfo.getPropertyDescriptors();
        for (int i = 0; i< propertyDescriptors.length; i++) {
            PropertyDescriptor descriptor = propertyDescriptors[i];
            String propertyName = descriptor.getName();
            if (!propertyName.equals("class")) {
                Method readMethod = descriptor.getReadMethod();
                Object result = readMethod.invoke(bean, new Object[0]);
                if (result != null) {
                    returnMap.put(propertyName, result);
                } else {
                    returnMap.put(propertyName, "");
                }
            }
        }
        return returnMap;
    }

    /**
     * 把JSONArray对象转为集合对象
     * @param array
     * @return
     */
    public static List<String> JSONArrayTOList(JSONArray array){

        //判断是否为空
        if(array.size() == 0){
            return null;
        }
        List<String> list = new ArrayList<String>();
        for (int i =0 ;i <array.size(); i++) {
            String string = array.getString(i);
            list.add(string);
        }
        return  list;
    }
}
