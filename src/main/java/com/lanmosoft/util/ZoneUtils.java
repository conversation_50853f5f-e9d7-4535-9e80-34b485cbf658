package com.lanmosoft.util;

import com.lanmosoft.dao.model.View_ZonePermission;
import com.lanmosoft.webapp.webmodel.LoginModel;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ZoneUtils {
    /**
     * 查询当前用户权限指定的校区
     * @param request
     * @param cityId
     * @return
     */
    public static List<String> findZoneIdByUser(HttpServletRequest request, String cityId) {

        LoginModel login = getLogin(request);
        List<String> list = new ArrayList<String>();
        //非管理员用户
        if(!"0".equals(getLogin(request).getUser().getCategory().trim())){
            String flag = login.getUser().getFlag();//1 所有校区
            if("0".equals(flag)){
                List<View_ZonePermission> zonePermissionList = login.getZonePermissionList();
                if(!zonePermissionList.isEmpty()){
                    for (View_ZonePermission z: zonePermissionList  ) {
                        if(StringUtils.isBlank(cityId)){//无城市id
                            list.add(z.getZone_id());
                        }else {
                            if(cityId.equalsIgnoreCase(z.getCity_id())){
                                list.add(z.getZone_id());
                            }
                        }
                    }
                }
            }
        }
        return list;
    }

    public static LoginModel getLogin(HttpServletRequest request) {
        return (LoginModel) request.getSession().getAttribute("login");
    }

    /**
     * 把list转为对应的字符串,便与查询
     * @param list
     * @return
     */
    public static String changeString(List<String> list) {
        String str = "";
        if(!list.isEmpty()){
            for (String s:list) {
                str += "'"+s+"',";
            }
            str =   str.substring(0,str.length()-1);
        }
        return str;
    }
}
