package com.lanmosoft.util;

import com.google.gson.*;
import com.lanmosoft.web.model.Message;
import jdk.nashorn.internal.parser.JSONParser;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.lanmosoft.util.HttpClient.postUrl;
import static com.lanmosoft.util.HttpClient.postUrlTencent;

public class TencentUtil {
    static String bytesToHex(byte[] bytes) {

        char[] HEX_CHAR = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        char[] buf = new char[bytes.length * 2];
        int index = 0;
        for (byte b : bytes) {
            buf[index++] = HEX_CHAR[b >>> 4 & 0xf];
            buf[index++] = HEX_CHAR[b & 0xf];
        }


        return new String(buf);
    }

    static String sign(String secretId, String secretKey, String httpMethod, String headerNonce, String headerTimestamp, String requestUri, String requestBody)
            throws NoSuchAlgorithmException, InvalidKeyException {

        String HMAC_ALGORITHM = "HmacSHA256";
        String tobeSig =
                httpMethod + "\nX-TC-Key=" + secretId + "&X-TC-Nonce=" + headerNonce + "&X-TC-Timestamp=" + headerTimestamp + "\n" + requestUri + "\n" + requestBody;
        Mac mac = Mac.getInstance(HMAC_ALGORITHM);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), mac.getAlgorithm());
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(tobeSig.getBytes(StandardCharsets.UTF_8));
        String hexHash = bytesToHex(hash);
        return new String(Base64.getEncoder().encode(hexHash.getBytes(StandardCharsets.UTF_8)));
    }

    public static Map createTencentMeeting (String scheduledate, String starttime, Double duration, String host, String invitee, String subject) {
        try{
            String dateString = scheduledate + " " + starttime + ":00";
            String headerNonce = String.valueOf(new Random().nextInt(999999));
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = dateFormat.parse(dateString);
            long timestamp = date.getTime() / 1000; // 将毫秒转换为秒
            System.out.println("Timestamp: " + timestamp);
            String endTime = String.valueOf((int)(timestamp + ((duration * 60) * 60)));
            String timestampString = Long.toString(timestamp);
            JSONObject jsonObject = new JSONObject();
            JsonParser jsonParser = new JsonParser();

            Map result = new HashMap();

            JSONArray hosts = new JSONArray();
            hosts.add(host);
            JSONArray invitees = new JSONArray();
            invitees.add(invitee);

            JSONObject settings = new JSONObject();
            settings.put("auto_in_waiting_room", true);
            settings.put("allow_in_before_host", false);

            String[] array = {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
            List<String> list = Arrays.asList(array);
            if (list.contains(host)) {
                // record off
                settings.put("auto_record_type", "none");
            } else {
                settings.put("auto_record_type", "cloud");
            }

            jsonObject.put("time_zone", "Asia/Shanghai");
            jsonObject.put("settings", settings);
            jsonObject.put("userid", "admin1711967294");
            jsonObject.put("hosts", hosts);
            jsonObject.put("invitees", invitees);
            jsonObject.put("instanceid", "1");
            jsonObject.put("subject", subject);
            jsonObject.put("type", "0");
            jsonObject.put("start_time", timestampString);
            jsonObject.put("end_time", endTime);

            String strNow = String.valueOf(System.currentTimeMillis());
            String sign_tencent = sign("GGt3TNmPXiIrGnLNeOE0jUthDjUVXYmM", "6V2IHAvT1xH2IKdOo3KxUJ4f0zDrT8CaHtKNSpiYcfNYdpya", "POST", headerNonce, strNow, "/v1/meetings", jsonObject.toString());

            String res = postUrlTencent("https://api.meeting.qq.com/v1/meetings", jsonObject.toString(), strNow, sign_tencent, headerNonce, "");
            System.out.println(jsonObject.toString());
            System.out.println(strNow);
            System.out.println(sign_tencent);
            System.out.println(res);
            JsonElement element;
            String url = null;
            String meetingid = null;
            try {
                element = jsonParser.parse(res);
            } catch (JsonSyntaxException jsonSyntaxException) {
                result.put("error", jsonSyntaxException.getMessage());
                return result;
            }
            JsonArray ja = null;
            if (element.isJsonObject()) {
                JsonObject jobj = element.getAsJsonObject();
                ja = jobj.getAsJsonArray("meeting_info_list");
            }
            if (ja != null) {
                for (JsonElement ele : ja) {
                    final JsonObject obj = ele.getAsJsonObject();
                    JsonElement join_url = obj.get("join_url");
                    JsonElement meeting_id = obj.get("meeting_id");
                    JsonElement meeting_code = obj.get("meeting_code");
                    url = join_url.toString();
                    meetingid = meeting_id.toString();
                    result.put("url", url);
                    result.put("meetingid", meetingid);
                    result.put("meeting_code", meeting_code);
                }
            }

            System.out.println("done: " + url);
            if (url == null) {
                url = "error";
            }
            return result;
        }catch(Exception e){
            System.out.println("Wrong!");
            Map res = new HashMap();
            res.put("error", e.getMessage());
            return res;
        }
    }

    public static String cancelTencentMeeting (String meetingid, String userid) {
        try{
            JSONObject jsonObject = new JSONObject();
            JSONObject settings = new JSONObject();
            String strNow = String.valueOf(System.currentTimeMillis());
            String headerNonce = String.valueOf(new Random().nextInt(999999));

            jsonObject.put("userid", "admin1711967294");
            jsonObject.put("reason_code", 1);
            jsonObject.put("instanceid", 1);
            jsonObject.put("reason_detail", "Cancel meeting");

            String sign_tencent = sign("GGt3TNmPXiIrGnLNeOE0jUthDjUVXYmM", "6V2IHAvT1xH2IKdOo3KxUJ4f0zDrT8CaHtKNSpiYcfNYdpya", "POST", headerNonce, strNow, "/v1/meetings/" + meetingid + "/cancel" , jsonObject.toString());
            String res = postUrlTencent("https://api.meeting.qq.com/v1/meetings/" + meetingid + "/cancel", jsonObject.toString(), strNow, sign_tencent, headerNonce, "");
            System.out.println(jsonObject.toString());
            System.out.println(res);

            System.out.println("done: " + res);

            return "succeeded";
        }catch(Exception e){
            System.out.println("Wrong!");
            return "error";
        }
    }

    public static String createTencentAccount (String username, String email, String displayname, String phonenum, String deptid, String type) {
        try{
            JSONObject jsonObject = new JSONObject();

            jsonObject.put("username", username);
            jsonObject.put("displayName", displayname);
            jsonObject.put("name", displayname);
            jsonObject.put("phoneNum", phonenum);
            jsonObject.put("type", type);
            jsonObject.put("deptId", deptid);
            jsonObject.put("primaryMail", email);

            JSONObject values = new JSONObject();
            values.put("values", jsonObject);
            //System.out.printf(values.toString());
            String res = postUrl("https://connect.be.co:4000/tencent", values.toString());
//            System.out.println(jsonObject.toString());
//            System.out.println(res);
//
//            System.out.println("done: " + res);
            return res;
        }catch(Exception e){
            System.out.println("Wrong!");
            return "error";
        }
    }
}

