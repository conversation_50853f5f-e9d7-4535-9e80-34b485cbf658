package com.lanmosoft.thread;


import com.lanmosoft.dao.model.Student;
import com.lanmosoft.service.biz.StudentService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * 多线程执行任务
 */
@Service
public class AsynTaskService {

    @Autowired
    StudentService studentService;

    /**
     * 更新学员的课程状态 Active == 0
     */
    @Async
    public void updateStudentActive() {
       List<Student> students =  studentService.findAllByActive();

       if (CollectionUtils.isNotEmpty(students)){
           for (Student s:students) {

               if(StringUtils.equalsIgnoreCase("0",s.getClassscheduleStatus())){
                   continue;
               }
               s.setClassscheduleStatus("0");
               studentService.update(s);
           }
       }
    }

    /**
     * 更新学员的课程状态 Inactive == 1
     */
    @Async
    public void updateStudentInactive() {
        List<Student> students =  studentService.findAllByInactive();

        if (CollectionUtils.isNotEmpty(students)){
            for (Student s:students) {

                if(StringUtils.equalsIgnoreCase("1",s.getClassscheduleStatus())){
                    continue;
                }
                s.setClassscheduleStatus("1");
                studentService.update(s);
            }
        }
    }

    /**
     * 更新学员的课程状态 HalfClosed == 2
     */
    @Async
    public void updateStudentHalfClosed() {
        List<Student> students =  studentService.findAllByHalfClosed();

        if (CollectionUtils.isNotEmpty(students)){
            for (Student s:students) {

                if(StringUtils.equalsIgnoreCase("2",s.getClassscheduleStatus())){
                    continue;
                }
                s.setClassscheduleStatus("2");
                studentService.update(s);
            }
        }
    }

    /**
     * 更新学员的课程状态 Closed == 3
     */
    @Async
    public void updateStudentClosed() {
        List<Student> students =  studentService.findAllByClosed();

        if (CollectionUtils.isNotEmpty(students)){
            for (Student s:students) {

                if(StringUtils.equalsIgnoreCase("3",s.getClassscheduleStatus())){
                    continue;
                }
                s.setClassscheduleStatus("3");
                studentService.update(s);
            }
        }
    }
}
