package test;

import java.util.Date;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.lanmosoft.model.ContractInfo;
import com.lanmosoft.service.web.erp.ErpServiceImpl;
import com.lanmosoft.util.LanDateUtils;



@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:applicationContext-resources.xml", 
		"classpath*:applicationContext-service.xml"})
public class ERPTest {
	@Autowired
    ApplicationContext ctx;
	
	@Test
	public void testSendMassageDefault(){
		ErpServiceImpl erpServiceImpl = (ErpServiceImpl) ctx.getBean("erpServiceImpl");
		
		Date nowDate = LanDateUtils.getNowDate();
		String firstDay = "2016-01-25";
		String lastDay = "2016-01-25";
			//查询数据
			List<ContractInfo> contractInfos = erpServiceImpl.generateContractInfo(firstDay, lastDay);
//			if(CollectionUtils.isNotEmpty(contractInfos)){
//				//发送数据
//				List<ErpLog> erpLogs = erpServiceImpl.sendContractInfo(contractInfos);
//				//记录日志
//				erpServiceImpl.erpLogger(erpLogs);
//			}
		
	}
}
