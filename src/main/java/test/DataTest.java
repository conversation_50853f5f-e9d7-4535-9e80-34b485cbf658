package test;

import com.lanmosoft.util.TencentUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.lanmosoft.util.HttpClient.postUrl;
import static com.lanmosoft.util.HttpClient.postUrlTencent;

public class DataTest {
	
	private static PreparedStatement ps;
	private static Connection conn;
	
	public void inti_student_freeTime() {
		try{
		        //注册数据库驱动程序为mysql驱动
		    Class.forName("com.mysql.jdbc.Driver");
		}catch (java.lang.ClassNotFoundException e){
		           //这样写是为了方便调试程序，出错打印mydb()就知道在什么地方出错了
		    System.err.println("mydb(): " + e.getMessage());
		}
		try{
			String url="*****************************************************************************************************";
			conn = DriverManager.getConnection(url);
		}catch (SQLException ex){
		    System.err.println("conn:"+ex.getMessage());
		}
		System.out.println(new Date());
		try {
			String hql = "select student_id,freeDate,startTime,count(1) from student_freeTime group by student_id,freeDate,startTime HAVING count(1)>1";
			System.out.println("sql3=="+hql);
			Statement st = conn.createStatement();
            ResultSet set = st.executeQuery(hql);
            while (set.next()) {
            	String student_id = set.getString("student_id");
            	String freeDate = set.getString("freeDate");
            	String startTime = set.getString("startTime");
            	//
            	String sql1="select id from student_freeTime where student_id='"+student_id+"' and freeDate='"+freeDate+"' and startTime='"+startTime+"'";
            	Statement st1 = conn.createStatement();
                ResultSet set1 = st1.executeQuery(sql1);
                List<String> li1=new ArrayList<String>();
                while (set1.next()) {
                	String id = set1.getString("id");
                	li1.add(id);
                }
                if(!li1.isEmpty()&&li1.size()>1){
                	for(int i=0;i<(li1.size()-1);i++){
                		String sql11="delete from student_freeTime where id='"+li1.get(0)+"'";
                		Statement st11 = conn.createStatement();
        	            st11.execute(sql11);
                    }
                } 
            }
			conn.close();	
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println(new Date());			
	}
	
	public void inti_teacher_freeTime() {
		try{
		        //注册数据库驱动程序为mysql驱动
		    Class.forName("com.mysql.jdbc.Driver");
		}catch (java.lang.ClassNotFoundException e){
		           //这样写是为了方便调试程序，出错打印mydb()就知道在什么地方出错了
		    System.err.println("mydb(): " + e.getMessage());
		}
		try{
			String url="*****************************************************************************************************";
			conn = DriverManager.getConnection(url);
		}catch (SQLException ex){
		    System.err.println("conn:"+ex.getMessage());
		}
		System.out.println(new Date());
		try {
			String hql = "select teacher_id,freeDate,startTime,count(1) from teacher_freeTime group by teacher_id,freeDate,startTime HAVING count(1)>1";
			System.out.println("sql3=="+hql);
			Statement st = conn.createStatement();
            ResultSet set = st.executeQuery(hql);
            while (set.next()) {
            	String teacher_id = set.getString("teacher_id");
            	String freeDate = set.getString("freeDate");
            	String startTime = set.getString("startTime");
            	//
            	String sql1="select id from teacher_freeTime where teacher_id='"+teacher_id+"' and freeDate='"+freeDate+"' and startTime='"+startTime+"'";
            	Statement st1 = conn.createStatement();
                ResultSet set1 = st1.executeQuery(sql1);
                List<String> li1=new ArrayList<String>();
                while (set1.next()) {
                	String id = set1.getString("id");
                	li1.add(id);
                }
                if(!li1.isEmpty()&&li1.size()>1){
                	for(int i=0;i<(li1.size()-1);i++){
                		String sql11="delete from teacher_freeTime where id='"+li1.get(0)+"'";
                		Statement st11 = conn.createStatement();
        	            st11.execute(sql11);
                    }
                } 
            }
			conn.close();	
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println(new Date());			
	}
	
	
	public void inti_student_contract() {
		try{
		        //注册数据库驱动程序为mysql驱动
		    Class.forName("com.mysql.jdbc.Driver");
		}catch (java.lang.ClassNotFoundException e){
		           //这样写是为了方便调试程序，出错打印mydb()就知道在什么地方出错了
		    System.err.println("mydb(): " + e.getMessage());
		}
		try{
			String url="*****************************************************************************************************";
			conn = DriverManager.getConnection(url);
		}catch (SQLException ex){
		    System.err.println("conn:"+ex.getMessage());
		}
		System.out.println(new Date());
		try {
			String hql = "select id,student_id,classSchedule_id,consumedClass,givenClass,lockStatus from consumption_student";
			System.out.println("sql3=="+hql);
			Statement st = conn.createStatement();
            ResultSet set = st.executeQuery(hql);
            while (set.next()) {
            	String id = set.getString("id");
            	String student_id = set.getString("student_id");
            	String classSchedule_id = set.getString("classSchedule_id");
            	Double consumedClass = set.getDouble("consumedClass");
            	Double givenClass = set.getDouble("givenClass");
            	String lockStatus = set.getString("lockStatus");
            	
            	//
            	String sql1="select id,consumption_student_id,contract_id,consumedClass,unitPrice,lockStatus from consumption_student_contract where consumption_student_id='"+id+"' order by id asc";
            	Statement st1 = conn.createStatement();
                ResultSet set1 = st1.executeQuery(sql1);
                List<String> li1=new ArrayList<String>();
                double dc=0d;
                while (set1.next()) {
                	String id1 = set1.getString("id");
                	String consumption_student_id1 = set1.getString("consumption_student_id");
                	String contract_id1 = set1.getString("contract_id");
                	Double consumedClass1 = set1.getDouble("consumedClass");
                	Double unitPrice1 = set1.getDouble("unitPrice");
                	String lockStatus1 = set1.getString("lockStatus");
                	dc+=consumedClass1!=null?consumedClass1:0d;
                }
                if(consumedClass!=null&&consumedClass!=dc){
            		if(consumedClass>dc){
            			
            		}else if(consumedClass<dc){
            			
            		}
            	}
                
            }
			conn.close();	
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println(new Date());			
	}

	static String bytesToHex(byte[] bytes) {

		char[] HEX_CHAR = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
		char[] buf = new char[bytes.length * 2];
		int index = 0;
		for (byte b : bytes) {
			buf[index++] = HEX_CHAR[b >>> 4 & 0xf];
			buf[index++] = HEX_CHAR[b & 0xf];
		}


		return new String(buf);
	}

	static String sign(String secretId, String secretKey, String httpMethod, String headerNonce, String headerTimestamp, String requestUri, String requestBody)
			throws NoSuchAlgorithmException, InvalidKeyException {

		String HMAC_ALGORITHM = "HmacSHA256";
		String tobeSig =
				httpMethod + "\nX-TC-Key=" + secretId + "&X-TC-Nonce=" + headerNonce + "&X-TC-Timestamp=" + headerTimestamp + "\n" + requestUri + "\n" + requestBody;
		Mac mac = Mac.getInstance(HMAC_ALGORITHM);
		SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), mac.getAlgorithm());
		mac.init(secretKeySpec);
		byte[] hash = mac.doFinal(tobeSig.getBytes(StandardCharsets.UTF_8));
		String hexHash = bytesToHex(hash);
		return new String(Base64.getEncoder().encode(hexHash.getBytes(StandardCharsets.UTF_8)));
	}


	public static void main(String[] args) {
//		DataTest t=new DataTest();
//		t.inti_student_freeTime();
//		t.inti_teacher_freeTime();
		try{
			//String res = TencentUtil.createTencentMeeting("", ",", "", "", "");
			Map resp = new HashMap();
		 	resp = TencentUtil.createTencentMeeting("2024-05-27", "16:30", 2d, "<EMAIL>", "<EMAIL>", "Test - Meeiting ID");

//			 String[] array = {"abc", "def", "ghi"};
//			 List<String> list = Arrays.asList(array);
//			 if (list.contains("abc")) {
//				 System.out.println("yes");
//			 } else {
//				 System.out.println("no");
//			 }

//			String dateString = "2024-04-19 17:00:00";
//			String headerNonce = String.valueOf(new Random().nextInt(999999));
//			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//			Date date = dateFormat.parse(dateString);
//			long timestamp = date.getTime() / 1000; // 将毫秒转换为秒
//			System.out.println("Timestamp: " + timestamp);
//			String endTime = String.valueOf(timestamp + (120 * 60));
//			String timestampString = Long.toString(timestamp);
//			JSONObject jsonObject = new JSONObject();
//
//			JSONArray hosts = new JSONArray();
//			hosts.add("adela001");
//			JSONArray invitees = new JSONArray();
//			invitees.add("<EMAIL>");
//
//			JSONObject settings = new JSONObject();
//			settings.put("auto_in_waiting_room", true);
//			settings.put("allow_in_before_host", false);
//
//			jsonObject.put("time_zone", "Asia/Shanghai");
//			jsonObject.put("settings", settings);
//			jsonObject.put("userid", "adela001");
//			jsonObject.put("hosts", hosts);
//			jsonObject.put("invitees", invitees);
//			jsonObject.put("instanceid", "1");
//			jsonObject.put("subject", "Test");
//			jsonObject.put("type", "0");
//			jsonObject.put("start_time", timestampString);
//			jsonObject.put("end_time", endTime);

//			String strNow = String.valueOf(System.currentTimeMillis());
//			String sign_tencent = sign("GGt3TNmPXiIrGnLNeOE0jUthDjUVXYmM", "6V2IHAvT1xH2IKdOo3KxUJ4f0zDrT8CaHtKNSpiYcfNYdpya", "POST", headerNonce, strNow, "/v1/meetings", jsonObject.toString());
//
//			String res = postUrlTencent("https://api.meeting.qq.com/v1/meetings", jsonObject.toString(), strNow, sign_tencent, headerNonce, "");
//			System.out.println(jsonObject.toString());
//			System.out.println(strNow);
//			System.out.println(sign_tencent);
//			System.out.println(res);
//			System.out.println("done");
		}catch(Exception e){
			System.out.println("Wrong!");
		}

	}

}
