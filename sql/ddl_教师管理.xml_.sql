CREATE TABLE IF NOT EXISTS `teacherview` (
  `id` varchar(128) DEFAULT NULL comment '用户ID',
  `isTeaching` varchar(128) DEFAULT NULL comment '是否授课',
  `category` varchar(128) DEFAULT NULL comment '用户类别',
  `code` varchar(128) DEFAULT NULL comment 'ERP员工编码',
  `teCode` varchar(128) DEFAULT NULL comment 'TE员工编码',
  `chineseName` varchar(128) DEFAULT NULL comment '中文姓名',
  `englishName` varchar(128) DEFAULT NULL comment '英文姓名',
  `abbr` varchar(128) DEFAULT NULL comment '简称',
  `birthday` datetime DEFAULT NULL comment '出生年月',
  `gender` varchar(128) DEFAULT NULL comment '性别',
  `nationality` varchar(128) DEFAULT NULL comment '国籍',
  `IDNo` varchar(128) DEFAULT NULL comment '省份证号',
  `possportNo` varchar(128) DEFAULT NULL comment '护照号',
  `mobile` varchar(128) DEFAULT NULL comment '手机',
  `email` varchar(128) DEFAULT NULL comment '电子邮件',
  `wechat` varchar(128) DEFAULT NULL comment '微信',
  `address` varchar(128) DEFAULT NULL comment '地址',
  `type` varchar(128) DEFAULT NULL comment '老师性质',
  `zone_id` varchar(128) DEFAULT NULL comment '校区',
  `status` varchar(128) DEFAULT NULL comment '状态',
  `department_id` varchar(128) DEFAULT NULL comment '部门ID',
  `department_name` varchar(128) DEFAULT NULL comment '部门名称',
  `position_id` varchar(128) DEFAULT NULL comment '岗位',
  `position_name` varchar(128) DEFAULT NULL comment '岗位名称',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `teacher_contract` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `teacher_id` varchar(128) DEFAULT NULL comment '教师ID',
  `startDate` datetime DEFAULT NULL comment '合同开始时间',
  `endDate` datetime DEFAULT NULL comment '合同结束时间',
  `postType` varchar(128) DEFAULT NULL comment '岗位类别',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `teacher_zone` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `teacher_id` varchar(128) DEFAULT NULL comment '教师ID',
  `city_id` varchar(128) DEFAULT NULL comment '城市ID',
  `zone_id` varchar(128) DEFAULT NULL comment '校区ID',
  `startDate` datetime DEFAULT NULL comment '开始日期',
  `endDate` datetime DEFAULT NULL comment '结束日期'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `teacher_freeTime` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `teacher_id` varchar(128) DEFAULT NULL comment '教师ID',
  `freeDate` datetime DEFAULT NULL comment '日期',
  `startTime` varchar(128) DEFAULT NULL comment '开始时间',
  `endTime` varchar(128) DEFAULT NULL comment '结束时间',
  `teachingWay` varchar(128) DEFAULT NULL comment '上课方式',
  `isFree` varchar(128) DEFAULT NULL comment '是否可排课',
  `freeTimeCode` varchar(128) DEFAULT NULL comment '可排课时间编码'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `teacher_course` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `teacher_id` varchar(128) DEFAULT NULL comment '教师ID',
  `course_id` varchar(128) DEFAULT NULL comment '课程ID',
  `periodLowerLimit` decimal(15,2) DEFAULT NULL comment '课时数下限',
  `periodUpperLimit` decimal(15,2) DEFAULT NULL comment '课时数上限',
  `intervalType` varchar(128) DEFAULT NULL comment '课时累计间隔',
  `gradeCategory_id` varchar(128) DEFAULT NULL comment '级别分类',
  `grade_id` varchar(128) DEFAULT NULL comment '可教授级别',
  `teachingType` varchar(128) DEFAULT NULL comment '授课类型',
  `startDate` datetime DEFAULT NULL comment '生效开始时间',
  `endDate` datetime DEFAULT NULL comment '生效结束时间',
  `unitPrice` decimal(15,2) DEFAULT NULL comment '课时单价',
  `isActivated` varchar(128) DEFAULT NULL comment '是否生效'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `view_teacher_course` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `teacher_id` varchar(128) DEFAULT NULL comment '教师ID',
  `chineseName` varchar(128) DEFAULT NULL comment 'chineseName',
  `englishName` varchar(128) DEFAULT NULL comment 'englishName',
  `course_id` varchar(128) DEFAULT NULL comment '课程ID',
  `course_name` varchar(128) DEFAULT NULL comment '课程name',
  `periodLowerLimit` int(128) DEFAULT NULL comment '课时数下限',
  `periodUpperLimit` int(128) DEFAULT NULL comment '课时数上限',
  `intervalType` varchar(128) DEFAULT NULL comment '课时累计间隔',
  `gradeCategory_id` varchar(128) DEFAULT NULL comment 'gradeCategory_id',
  `gradeCategory_name` varchar(128) DEFAULT NULL comment 'gradeCategory_name',
  `grade_id` varchar(128) DEFAULT NULL comment '可教授级别',
  `level` varchar(128) DEFAULT NULL comment 'level',
  `grade_name` varchar(128) DEFAULT NULL comment '可教授级别name',
  `teachingType` varchar(128) DEFAULT NULL comment '授课类型',
  `startDate` datetime DEFAULT NULL comment '生效开始时间',
  `endDate` datetime DEFAULT NULL comment '生效结束时间',
  `unitPrice` decimal(15,2) DEFAULT NULL comment '课时单价'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `view_student_paike` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `teacher_id` varchar(128) DEFAULT NULL comment '教师ID',
  `chineseName` varchar(128) DEFAULT NULL comment 'chineseName',
  `englishName` varchar(128) DEFAULT NULL comment 'englishName',
  `abbr` varchar(128) DEFAULT NULL comment 'abbr',
  `course_id` varchar(128) DEFAULT NULL comment '课程ID',
  `course_name` varchar(128) DEFAULT NULL comment '课程name',
  `periodLowerLimit` int(128) DEFAULT NULL comment '课时数下限',
  `periodUpperLimit` int(128) DEFAULT NULL comment '课时数上限',
  `intervalType` varchar(128) DEFAULT NULL comment '课时累计间隔',
  `gradeCategory_id` varchar(128) DEFAULT NULL comment 'gradeCategory_id',
  `gradeCategory_name` varchar(128) DEFAULT NULL comment 'gradeCategory_name',
  `grade_id` varchar(128) DEFAULT NULL comment '可教授级别',
  `grade_name` varchar(128) DEFAULT NULL comment '可教授级别name',
  `level` varchar(128) DEFAULT NULL comment 'level',
  `teachingType` varchar(128) DEFAULT NULL comment '授课类型',
  `startDate` datetime DEFAULT NULL comment '生效开始时间',
  `endDate` datetime DEFAULT NULL comment '生效结束时间',
  `unitPrice` decimal(15,2) DEFAULT NULL comment '课时单价',
  `city_id` varchar(128) DEFAULT NULL comment 'city_id',
  `zone_id` varchar(128) DEFAULT NULL comment 'zone_id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
