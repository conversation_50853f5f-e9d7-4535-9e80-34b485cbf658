CREATE TABLE IF NOT EXISTS `teacher_leave` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `classSchedule_id` varchar(128) DEFAULT NULL comment '课表',
  `teacher_id` varchar(128) DEFAULT NULL comment '教师',
  `handleTime` varchar(128) DEFAULT NULL comment '办理时间',
  `isInadvance` varchar(128) DEFAULT NULL comment '是否提前12小时',
  `leaveDate` datetime DEFAULT NULL comment '请假日期',
  `startTime` varchar(128) DEFAULT NULL comment '请假开始时间',
  `endTime` varchar(128) DEFAULT NULL comment '请假结束时间',
  `reason` varchar(128) DEFAULT NULL comment '请假原因',
  `givenClass` decimal(15,2) DEFAULT NULL comment '补学生课时',
  `deductionClass` decimal(15,2) DEFAULT NULL comment '扣老师课时',
  `deductionWage` decimal(15,2) DEFAULT NULL comment '扣老师工资',
  `contract_id` varchar(128) DEFAULT NULL comment '合同',
  `approvalStatus` varchar(128) DEFAULT NULL comment '审批状态',
  `lockStatus` varchar(128) DEFAULT NULL comment '锁定状态',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `student_leave` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `classSchedule_id` varchar(128) DEFAULT NULL comment '课表',
  `student_id` varchar(128) DEFAULT NULL comment '学生',
  `handleTime` varchar(128) DEFAULT NULL comment '办理时间',
  `isInadvance` varchar(128) DEFAULT NULL comment '是否提前12小时',
  `leaveDate` datetime DEFAULT NULL comment '请假日期',
  `startTime` varchar(128) DEFAULT NULL comment '请假开始时间',
  `endTime` varchar(128) DEFAULT NULL comment '请假结束时间',
  `reason` varchar(128) DEFAULT NULL comment '请假原因',
  `deductionClass` decimal(15,2) DEFAULT NULL comment '扣学生课时',
  `givenClass` decimal(15,2) DEFAULT NULL comment '补老师课时',
  `givenWage` decimal(15,2) DEFAULT NULL comment '补老师工资',
  `contract_id` varchar(128) DEFAULT NULL comment '合同',
  `approvalStatus` varchar(128) DEFAULT NULL comment '审批状态',
  `lockStatus` varchar(128) DEFAULT NULL comment '锁定状态',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `view_student_leave` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `classSchedule_id` varchar(128) DEFAULT NULL comment '课表',
  `student_id` varchar(128) DEFAULT NULL comment '学生',
  `handleTime` varchar(128) DEFAULT NULL comment '办理时间',
  `isInadvance` varchar(128) DEFAULT NULL comment '是否提前12小时',
  `leaveDate` datetime DEFAULT NULL comment '请假日期',
  `startTime` varchar(128) DEFAULT NULL comment '请假开始时间',
  `endTime` varchar(128) DEFAULT NULL comment '请假结束时间',
  `deductionClass` decimal(15,2) DEFAULT NULL comment '扣学生课时',
  `givenClass` decimal(15,2) DEFAULT NULL comment '补老师课时',
  `givenWage` decimal(15,2) DEFAULT NULL comment '补老师工资',
  `contract_id` varchar(128) DEFAULT NULL comment '合同',
  `approvalStatus` varchar(128) DEFAULT NULL comment '审批状态',
  `lockStatus` varchar(128) DEFAULT NULL comment '锁定状态',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间',
  `code` varchar(128) DEFAULT NULL comment '学号',
  `chineseName` varchar(128) DEFAULT NULL comment '中文名称',
  `englishName` varchar(128) DEFAULT NULL comment '英文名称',
  `zone_name` varchar(128) DEFAULT NULL comment '校区',
  `zone_id` varchar(128) DEFAULT NULL comment '校区id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
