CREATE TABLE IF NOT EXISTS `org` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `name` varchar(128) DEFAULT NULL comment '名称',
  `department_id` varchar(128) DEFAULT NULL comment '所属部门id',
  `department_name` varchar(128) DEFAULT NULL comment '所属部门名称',
  `position_id` varchar(128) DEFAULT NULL comment '所属岗位id',
  `position_name` varchar(128) DEFAULT NULL comment '所属岗位名称',
  `description` varchar(128) DEFAULT NULL comment '描述',
  `org_type` varchar(128) DEFAULT NULL comment '组织类型',
  `superOrg_id` varchar(128) DEFAULT NULL comment '顶级组织id',
  `superOrg_name` varchar(128) DEFAULT NULL comment '顶级组织名称',
  `fulldept_id` varchar(128) DEFAULT NULL comment '全部门路径id',
  `fulldept_name` varchar(128) DEFAULT NULL comment '全部门路径名',
  `fullpost_id` varchar(128) DEFAULT NULL comment '全岗位路径id',
  `fullpost_name` varchar(128) DEFAULT NULL comment '全岗位路径名',
  `sortNum` int(11) DEFAULT NULL comment '排序',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间',
  `delStatus` varchar(128) DEFAULT NULL comment '是否禁用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `user` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `userName` varchar(128) DEFAULT NULL comment '登录名',
  `password` varchar(128) DEFAULT NULL comment '密码',
  `isTeaching` varchar(128) DEFAULT NULL comment '是否授课',
  `category` varchar(128) DEFAULT NULL comment '用户类别',
  `code` varchar(128) DEFAULT NULL comment 'ERP员工编码',
  `teCode` varchar(128) DEFAULT NULL comment 'TE员工编码',
  `chineseName` varchar(128) DEFAULT NULL comment '中文姓名',
  `englishName` varchar(128) DEFAULT NULL comment '英文姓名',
  `abbr` varchar(128) DEFAULT NULL comment '简称',
  `birthday` datetime DEFAULT NULL comment '出生年月',
  `gender` varchar(128) DEFAULT NULL comment '性别',
  `nationality` varchar(128) DEFAULT NULL comment '国籍',
  `IDNo` varchar(128) DEFAULT NULL comment '身份证号',
  `possportNo` varchar(128) DEFAULT NULL comment '护照号',
  `mobile` varchar(128) DEFAULT NULL comment '手机',
  `email` varchar(128) DEFAULT NULL comment '电子邮件',
  `wechat` varchar(128) DEFAULT NULL comment '微信',
  `address` varchar(128) DEFAULT NULL comment '地址',
  `type` varchar(128) DEFAULT NULL comment '老师性质',
  `zone_id` varchar(128) DEFAULT NULL comment '校区',
  `status` varchar(128) DEFAULT NULL comment '状态',
  `department_id` varchar(128) DEFAULT NULL comment '部门ID',
  `department_name` varchar(128) DEFAULT NULL comment '部门名称',
  `position_id` varchar(128) DEFAULT NULL comment '岗位ID',
  `position_name` varchar(128) DEFAULT NULL comment '岗位名称',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间',
  `delStatus` varchar(128) DEFAULT NULL comment '是否禁用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `permission` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `position_id` varchar(128) DEFAULT NULL comment '岗位id',
  `position_name` varchar(128) DEFAULT NULL comment '岗位名称',
  `function_id` varchar(128) DEFAULT NULL comment '功能点id',
  `function_name` varchar(128) DEFAULT NULL comment '功能点名称',
  `function_type` varchar(128) DEFAULT NULL comment '功能点类型',
  `firstModule` varchar(128) DEFAULT NULL comment '所属一级模块',
  `secondModule` varchar(128) DEFAULT NULL comment '所二级属模块',
  `data_role` varchar(128) DEFAULT NULL comment '数据角色权限'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `zonePermission` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `user_id` varchar(128) DEFAULT NULL comment '用户',
  `zone_id` varchar(128) DEFAULT NULL comment '校区'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
