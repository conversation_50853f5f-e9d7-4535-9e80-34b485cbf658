/*==============================================================*/
/* DBMS name:      MySQL 5.0                                    */
/* Created on:     2015/12/7 10:15:44                           */
/*==============================================================*/


drop table if exists attendanceBook;

drop table if exists city;

drop table if exists classSchedule;

drop table if exists classroom;

drop table if exists constant;

drop table if exists consumption_student;

drop table if exists consumption_student_contract;

drop table if exists consumption_teacher;

drop index Index_contractId on contract;

drop table if exists contract;

drop index Index_productId on contractProduct;

drop table if exists contractProduct;

drop table if exists course;

drop table if exists followupLog;

drop table if exists grade;

drop table if exists gradeCategory;

drop table if exists holiday;

drop table if exists org;

drop table if exists permission;

drop table if exists product;

drop table if exists refundFee;

drop table if exists requirement;

drop table if exists schedule;

drop index index_crmId on student;

drop table if exists student;

drop table if exists student_course;

drop table if exists student_freeTime;

drop table if exists student_leave;

drop table if exists teacher_contract;

drop table if exists teacher_course;

drop table if exists teacher_freeTime;

drop table if exists teacher_leave;

drop table if exists teacher_zone;

drop table if exists tutor;

drop table if exists user;

drop table if exists zone;

drop table if exists zonePermission;

/*==============================================================*/
/* Table: attendanceBook                                        */
/*==============================================================*/
create table attendanceBook
(
   id                   varchar(50) not null comment 'ID',
   classSchedule_id     varchar(50) not null comment '课表ID',
   attendanceStatus     char(2) not null comment '出勤状态（00：正常出勤，11：学生迟到，12：学生缺勤，21：老师迟到，22：老师缺勤）',
   reason               varchar(1000) comment '修改原因',
   approvalStatus       char(1) comment '审批状态（0：无需审批，1：待审批，2：审批中，3：审批通过，4：驳回）',
   approver             varbinary(100) comment '审批人',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   contract_id          varchar(50) comment '合同',
   lockStatus           char(1) comment '锁定状态（0：未锁定，1：已锁定）',
   primary key (id)
)
engine = InnoDB;

alter table attendanceBook comment '签到表';

/*==============================================================*/
/* Table: city                                                  */
/*==============================================================*/
create table city
(
   id                   varchar(50) not null comment '城市ID',
   name                 varchar(100) not null comment '城市名称',
   code                 varchar(50) comment '城市编码',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (name)
)
engine = InnoDB;

alter table city comment '城市表';

/*==============================================================*/
/* Table: classSchedule                                         */
/*==============================================================*/
create table classSchedule
(
   id                   varchar(50) not null comment 'ID',
   courseType           char(1) not null comment '课程类型（1：正常课程，2：少发需(不上课，发工资，口学时），3：多发（上课，不发工资，不扣学时），4：多发（上课，发工资，不扣学时））',
   student_id           varchar(50) not null comment '学员',
   course_id            varchar(50) comment '课程ID',
   scheduledDate        date comment '上课日期',
   startTime            varchar(50) comment '上课开始时间',
   endTime              varchar(50) comment '上课结束时间',
   time                 char(1) comment '课时数（1：1小时，2：2小时，3：3小时）',
   freeTimeCode         varchar(20) comment '排课时间编码',
   teacher_id           varchar(50) comment '教师ID',
   teachingWay          char(1) comment '上课方式（1：教室授课，2：上门授课，3：远程授课）',
   classroom_id         varchar(50) comment '教室ID',
   status               char(1) not null comment '状态（0：初始化，1：确认，2：删除）',
   description          varchar(1000) comment '删除原因',
   notes                varchar(1000) comment '备注',
   leaveState           char(1) not null comment '请假状态（0：未请假，1：学生请假，2：老师请假）',
   isAuto               char(1) not null comment '是否为自动排课（0：false，1：true）',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   realDate             date comment '实际上课日期',
   realStartTime        varchar(50) comment '实际开始时间',
   realEndTime          varchar(50) comment '实际结束时间',
   realTime             char(1) comment '实际课时数（1：1小时，2：2小时，3：3小时）',
   primary key (id)
)
engine = InnoDB;

alter table classSchedule comment '课表';

/*==============================================================*/
/* Table: classroom                                             */
/*==============================================================*/
create table classroom
(
   id                   varchar(50) not null comment '教室ID',
   name                 varchar(100) not null comment '教室名称',
   zone_id              varchar(50) comment '所属校区ID',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (name, zone_id)
)
engine = InnoDB;

alter table classroom comment '教室表';

/*==============================================================*/
/* Table: constant                                              */
/*==============================================================*/
create table constant
(
   id                   varchar(50) not null comment 'ID',
   name                 varchar(100) not null comment '常量名',
   value                varchar(100) not null comment '常量值',
   code                 varchar(100) comment '代码',
   category             varchar(100) not null comment '类别',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (value, category)
)
engine = InnoDB;

alter table constant comment '常量表';

/*==============================================================*/
/* Table: consumption_student                                   */
/*==============================================================*/
create table consumption_student
(
   id                   varchar(50) not null comment 'ID',
   student_id           varchar(50) comment '学生ID',
   classSchedule_id     varchar(50) comment '课表',
   consumedClass        decimal(7,2) comment '消耗课时（小时计）',
   givenClass           decimal(7,2) comment '赠送课时（小时计）',
   lockStatus           char(1) comment '锁定状态（0：未锁定，1：已锁定）',
   primary key (id)
)
engine = InnoDB;

alter table consumption_student comment '学生耗课信息表';

/*==============================================================*/
/* Table: consumption_student_contract                          */
/*==============================================================*/
create table consumption_student_contract
(
   id                   varchar(50) not null,
   consumption_student_id varchar(50) comment '学生耗课ID',
   contract_id          varchar(50) comment '合同',
   contractProduct_id   varchar(50) comment '合同产品',
   consumedClass        decimal(7,2) comment '消耗课时（小时计）',
   unitPrice            decimal(15,4) comment '单价',
   lockStatus           char(1) comment '锁定状态（0：未锁定，1：已锁定）',
   isUploaded           char(1) comment '是否已上传（0：未上传，1：已上传）',
   primary key (id)
);

alter table consumption_student_contract comment '学生耗课关联合同';

/*==============================================================*/
/* Table: consumption_teacher                                   */
/*==============================================================*/
create table consumption_teacher
(
   id                   varchar(50) not null comment 'ID',
   teacher_id           varchar(50) not null comment '教师',
   classSchedule_id     varchar(50) not null comment '课表',
   wage                 decimal(7,2) comment '工资（小时计）',
   wage2                decimal(15,4) comment '工资（元）',
   wageDeduction        decimal(7,2) comment '扣除工资（小时计）',
   wageDeduction2       decimal(15,4) comment '扣除工资（元）',
   incidental           decimal(15,4) comment '杂费',
   description          varchar(1000) comment '杂费摘要',
   lockStatus           char(1) comment '锁定状态（0：未锁定，1：已锁定）',
   primary key (id)
)
engine = InnoDB;

alter table consumption_teacher comment '老师耗课信息表';

/*==============================================================*/
/* Table: contract                                              */
/*==============================================================*/
create table contract
(
   id                   varchar(50) not null comment '合同ID',
   contractId           varchar(18) comment 'SFcontractId',
   crmId                varchar(18) comment 'SFCRMId',
   studentId            varchar(50) not null comment '学员ID',
   accountId            varchar(18) comment 'SFaccountId',
   contractNo           varchar(18) comment 'SF合同号',
   startDate            date comment '开始日期',
   endDate              date comment '结束日期',
   productId            varchar(50) comment '产品ID',
   contractNoR          varchar(18) comment '关联合同号',
   contractOwnerId      varchar(18) comment '咨询顾问',
   contractType         char(1) comment '学员类型（1：new，2：renew）',
   grade_id             varchar(50) comment '报读级别',
   studyDate_start      date comment '学习起始日期',
   studyDate_end        date comment '学习结束日期',
   company              varchar(100) comment '公司名称',
   status               char(1) comment '当前状态（1：执行中，2：已付费未排课，3：冻结）',
   isAbandoned          char(1) comment '是否弃学（0：否，1：是）',
   abandonedDate        date comment '弃学日期',
   persentType          char(1) comment '赠送类型（1：SA赠送，2，SC赠送，3：TE-推荐赠送，4：TE-老师迟到，5：TE-老师缺勤，6：TE-其他）',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id)
)
engine = InnoDB;

alter table contract comment '合同信息表';

/*==============================================================*/
/* Index: Index_contractId                                      */
/*==============================================================*/
create unique index Index_contractId on contract
(
   contractId
);

/*==============================================================*/
/* Table: contractProduct                                       */
/*==============================================================*/
create table contractProduct
(
   id                   varchar(50) not null comment 'ID',
   contract_id          varchar(50) not null comment 'TE合同',
   contractId           varchar(18) comment 'SF合同',
   product_id           varchar(50) comment 'TE产品',
   productId            varchar(50) comment 'SF Product ID',
   productName          varchar(200) comment '产品名称',
   amount               decimal(7,2) comment '数量',
   units                decimal(7,2) comment '单元数',
   tuitionFee           decimal(15,4) comment '学费',
   pricebookId          varchar(50) comment 'SF Price Book ID',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '删除状态（0：未删，1：已删）',
   productType          char(1) default '1' comment 'Product Type(1:一对一)',
   isUnitPriceChange    char(1) comment '单价是否变化（0：否，1：是）',
   consumedClass        decimal(7,2) comment '已消耗课时',
   amountWithoutVAT     decimal(15,4) comment '不含税学费',
   税率                   decimal(7,2) comment '税率',
   vat                  decimal(15,4) comment 'VAT',
   primary key (id)
)
engine = InnoDB;

alter table contractProduct comment '合同产品表';

/*==============================================================*/
/* Index: Index_productId                                       */
/*==============================================================*/
create index Index_productId on contractProduct
(
   contractId,
   productId
);

/*==============================================================*/
/* Table: course                                                */
/*==============================================================*/
create table course
(
   id                   varchar(50) not null comment '课程ID',
   name                 varchar(100) comment '课程名称',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (name)
)
engine = InnoDB;

alter table course comment '课程表';

/*==============================================================*/
/* Table: followupLog                                           */
/*==============================================================*/
create table followupLog
(
   id                   varchar(50) not null comment 'ID',
   student_id           varchar(50) not null comment '学员ID',
   followupWay          char(1) not null comment '跟进方式（1：电话，2：邮件，3：QQ）',
   followupTime         datetime not null comment '跟进时间',
   nextFollowupTime     datetime not null comment '下次跟进时间',
   followupContent      varchar(1000) comment '跟进内容',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id)
)
engine = InnoDB;

alter table followupLog comment '学员跟进表';

/*==============================================================*/
/* Table: grade                                                 */
/*==============================================================*/
create table grade
(
   id                   varchar(50) not null comment '级别ID',
   gradeCategory_id     varchar(50) not null comment '级别分类',
   name                 varchar(100) not null comment '级别名称',
   level                int comment '级别',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (gradeCategory_id, name)
)
engine = InnoDB;

alter table grade comment '级别表';

/*==============================================================*/
/* Table: gradeCategory                                         */
/*==============================================================*/
create table gradeCategory
(
   id                   varchar(50) not null comment 'ID',
   name                 varchar(100) not null comment '分类名称',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id)
);

alter table gradeCategory comment '级别分类';

/*==============================================================*/
/* Table: holiday                                               */
/*==============================================================*/
create table holiday
(
   id                   varchar(50) not null comment 'ID',
   name                 varchar(100) not null comment '假日名称',
   startDate            date not null comment '假日开始时间',
   endDate              date not null comment '假日结束时间',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id)
)
engine = InnoDB;

alter table holiday comment '节假日表';

/*==============================================================*/
/* Table: org                                                   */
/*==============================================================*/
create table org
(
   id                   varchar(50) not null comment '组织ID',
   name                 varchar(100) not null comment '组织名称',
   department_id        varchar(50) comment '所属部门id',
   department_name      varchar(100) comment '所属部门名称',
   position_id          varchar(50) comment '所属岗位id',
   position_name        varchar(100) comment '所属岗位名称',
   description          varchar(500) comment '描述',
   org_type             varchar(20) not null comment '组织类型(部门：dept，岗位：post)',
   superOrg_id          varchar(50) comment '顶级组织id',
   superOrg_name        varchar(100) comment '顶级组织名称',
   fulldept_id          varchar(500) comment '全部门路径id',
   fulldept_name        varchar(1000) comment '全部门路径名',
   fullpost_id          varchar(500) comment '全岗位路径id',
   fullpost_name        varchar(1000) comment '全岗位路径名',
   sortNum              int comment '排序',
   creator_id           varchar(50) comment '创建者',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用',
   primary key (id)
)
engine = InnoDB;

alter table org comment '组织表';

/*==============================================================*/
/* Table: permission                                            */
/*==============================================================*/
create table permission
(
   id                   varchar(50) not null comment 'ID',
   position_id          varchar(50) not null comment '岗位id',
   position_name        varchar(100) comment '岗位名称',
   function_id          varchar(50) not null comment '功能点id',
   function_name        varchar(100) comment '功能点名称',
   function_type        varchar(100) comment '功能点类型simple:简单功能，complex:含数据权限功能',
   firstModule          varchar(100) comment '所属一级模块',
   secondModule         varchar(100) comment '所二级属模块',
   data_role            varchar(1) comment '数据角色权限',
   primary key (id)
)
engine = InnoDB;

alter table permission comment '授权表';

/*==============================================================*/
/* Table: product                                               */
/*==============================================================*/
create table product
(
   id                   varchar(50) not null comment 'ID',
   productId            varchar(50) comment 'SF Product ID',
   productName          varchar(200) comment 'Product Name',
   price                decimal(15,4) comment 'Price',
   pricebookId          varchar(50) comment 'SF Price Book ID',
   productType          char(1) default '1' comment 'Product Type(1:一对一)',
   productCategory      char(1) comment 'Product Category(1：SA赠送，2，SC赠送，3：TE-推荐赠送，4：TE-老师迟到，5：TE-老师缺勤，6：TE-其他)',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id)
)
engine = InnoDB;

alter table product comment '产品表';

/*==============================================================*/
/* Table: refundFee                                             */
/*==============================================================*/
create table refundFee
(
   id                   varchar(50) not null comment 'ID',
   product_id           varchar(50) not null comment '产品ID',
   handleTime           datetime comment '退费时间',
   refundType           char(1) comment '退费类型（1：实际单价）',
   unitPrice            decimal(15,4) comment '课程单价',
   totalAmount          decimal(15,4) comment '退费金额',
   description          varchar(1000) comment '退费原因',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   period               decimal(7,2) comment '退费课时',
   approvalStatus       char(1) comment '审批状态（0：无需审批，1：待审批，2：审批通过，3，驳回）',
   approver             varchar(50) comment '审批人',
   primary key (id)
)
engine = InnoDB;

alter table refundFee comment '学员退费';

/*==============================================================*/
/* Table: requirement                                           */
/*==============================================================*/
create table requirement
(
   id                   varchar(50) not null comment 'ID',
   student_id           varchar(50) not null comment '学员ID',
   startDate            date comment '开始时间',
   endDate              date comment '结束时间',
   course_id            varchar(50) comment '课程ID',
   totalLowerLimit      decimal(7,2) comment '总课时下限',
   totalUpperLimit      decimal(7,2) comment '总课程上限',
   dayLowerLimit        decimal(7,2) comment '每天课时下限',
   dayUpperLimit        decimal(7,2) comment '每天课时上限',
   weekLowerLimit       decimal(7,2) comment '每周课时下限',
   weekUpperLimit       decimal(7,2) comment '每周课时上限',
   monthLowerLimit      decimal(7,2) comment '每月课时下限',
   monthUpperLimit      decimal(7,2) comment '每月课时上限',
   intervalDay          int comment '排课间隔天数',
   primary key (id)
)
engine = InnoDB;

alter table requirement comment '学员诉求表';

/*==============================================================*/
/* Table: schedule                                              */
/*==============================================================*/
create table schedule
(
   id                   varchar(50) not null comment 'ID',
   startTime            varchar(50) not null,
   endTime              varchar(50) not null,
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id)
)
engine = InnoDB;

alter table schedule comment '上课时间表';

/*==============================================================*/
/* Table: student                                               */
/*==============================================================*/
create table student
(
   id                   varchar(50) not null comment '学员ID',
   code                 varchar(50) comment '学员Code',
   crmId                varchar(18) not null comment 'SFCRMId',
   accountId            varchar(18) comment 'SFaccountId',
   chineseName          varchar(100) comment '中文姓名',
   englishName          varchar(100) comment '英文姓名',
   gender               char(1) comment '性别（1：男，2：女）',
   age                  int comment '年龄',
   birthday             date comment '出生年月',
   nationality          varchar(100) comment '国籍',
   mobile               varchar(50) comment '手机',
   email                varchar(100) comment '电子邮件',
   wechat               varchar(100) comment '微信',
   city_id              varchar(50) comment '城市',
   zone_id              varchar(50) comment '校区',
   school               varchar(100) comment '就读学校',
   grade                varchar(50) comment '年级',
   address              varchar(500) comment '家庭住址',
   teReferral           char(1) comment '0:false,1:true',
   relationshipTypeP    char(50) comment '第一联系人与学生关系（Father/Mother）',
   chineseNameP         varchar(100) comment '第一联系人姓名',
   contractNumberP      varchar(50) comment '第一联系人手机',
   companyP             varchar(100) comment '第一联系人单位',
   occupationP          varchar(100) comment '第一联系人职务',
   wechatP              varchar(100) comment '第一联系人微信',
   IDNoP                varchar(100) comment '第一联系人身份证',
   emailP               varchar(100) comment '第一联系人邮箱',
   relationshipTypeS    char(50) comment '第二联系人与学生关系（Father/Mother）',
   chineseNameS         varchar(100) comment '第二联系人姓名',
   contractNumberS      varchar(50) comment '第二联系人手机',
   companyS             varchar(100) comment '第二联系人单位',
   occupationS          varchar(100) comment '第二联系人职务',
   wechatS              varchar(100) comment '第二联系人微信',
   IDNoS                varchar(100) comment '第二联系人身份证',
   emailS               varchar(100) comment '第二联系人邮箱',
   source               varchar(100) comment '来源',
   referral             varchar(18) comment 'referral',
   contractOwnerId      varchar(18) comment '咨询顾问',
   tppId                varchar(50) comment 'T.P.P',
   tutorId              varchar(50) comment '助教',
   contractType         char(1) comment '学员类型（1：new，2：renew）',
   isOC                 char(1) comment 'OC课（0：否，1：是）',
   startTime            datetime comment 'OC上课开始时间',
   endTime              datetime comment 'OC上课结束时间',
   备注                   varchar(500) comment '备注',
   status               char(1) comment '上课状态（1：欠费，2：待确认，3：执行中，4：已结束，5：冻结）',
   freezeDate           date comment '冻结日期',
   thawedDate           date comment '解冻日期',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (crmId)
)
engine = InnoDB;

alter table student comment '学员信息表';

/*==============================================================*/
/* Index: index_crmId                                           */
/*==============================================================*/
create unique index index_crmId on student
(
   crmId
);

/*==============================================================*/
/* Table: student_course                                        */
/*==============================================================*/
create table student_course
(
   id                   varchar(50) not null comment 'ID',
   student_id           varchar(50) not null comment '学员ID',
   course_id            varchar(50) comment '课程ID',
   gradeCategory_id     varchar(50) comment '级别分类',
   grade_id             varchar(50) comment '报读级别',
   teacher_id           varchar(50) comment '教师ID',
   teachingWay          char(1) comment '上课方式（1：教室授课，2：上门授课，3：远程授课）',
   startDate            date comment '课程开始日期',
   endDate              date comment '课程结束日期',
   units                decimal(7,2) comment '单元数',
   status               char(1) comment '状态（1：有效，2：失效）',
   primary key (id)
)
engine = InnoDB;

alter table student_course comment '学员课程表';

/*==============================================================*/
/* Table: student_freeTime                                      */
/*==============================================================*/
create table student_freeTime
(
   id                   varchar(50) not null comment 'ID',
   student_id           varchar(50) not null comment '学生ID',
   freeDate             date not null comment '日期',
   startTime            varchar(50) not null comment '开始时间',
   endTime              varchar(50) not null comment '结束时间',
   teachingWay          char(1) comment '上课方式（1：教室授课，2：上门授课，3：远程授课）',
   isFree               char(1) not null comment '是否可排课（0：false，1：true）',
   freeTimeCode         varchar(10) not null comment '可排课时间编码（1234，123，234，12，23，34，1，2，3，4）',
   primary key (id)
)
engine = InnoDB;

alter table student_freeTime comment '学生可用时间表';

/*==============================================================*/
/* Table: student_leave                                         */
/*==============================================================*/
create table student_leave
(
   id                   varchar(50) not null comment 'ID',
   classSchedule_id     varchar(50) not null comment '课表ID',
   student_id           varchar(50) not null comment '学生ID',
   handleTime           datetime comment '办理时间',
   isInadvance          char(1) comment '是否提前12小时',
   leaveDate            date comment '请假日期',
   startTime            varchar(50) comment '请假开始时间',
   endTime              varchar(50) comment '请假结束时间',
   deductionClass       decimal(7,2) comment '扣学生课时（小时计）',
   givenClass           decimal(7,2) comment '补老师课时（小时计）',
   givenWage            decimal(10,2) comment '补老师工资',
   contract_id          varchar(50) comment '合同',
   approvalStatus       char(1) comment '审批状态（0：无需审批，1：待审批，2：审批通过，3，驳回）',
   lockStatus           char(1) comment '锁定状态（0：未锁定，1：已锁定）',
   primary key (id)
)
engine = InnoDB;

alter table student_leave comment '学员请假表';

/*==============================================================*/
/* Table: teacher_contract                                      */
/*==============================================================*/
create table teacher_contract
(
   id                   varchar(50) not null comment 'ID',
   teacher_id           varchar(50) comment '教师ID',
   startDate            date comment '合同开始时间',
   endDate              date comment '合同结束时间',
   postType             char(1) comment '岗位类别（1：全职，2：兼职，3：半全职）',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id)
)
engine = InnoDB;

alter table teacher_contract comment '教师合同表';

/*==============================================================*/
/* Table: teacher_course                                        */
/*==============================================================*/
create table teacher_course
(
   id                   varchar(50) not null comment 'ID',
   teacher_id           varchar(50) comment '教师ID',
   course_id            varchar(50) comment '课程ID',
   periodLowerLimit     decimal(7,2) comment '课时数下限',
   periodUpperLimit     decimal(7,2) comment '课时数上限',
   intervalType         char(1) comment '课时累计间隔（1：月，2：年）',
   gradeCategory_id     varchar(50) comment '级别分类',
   grade_id             varchar(50) comment '可教授级别',
   teachingType         char(1) comment '授课类型（1：一对一）',
   startDate            date comment '生效开始时间',
   endDate              date comment '生效结束时间',
   unitPrice            decimal(15,4) comment '课时单价',
   primary key (id)
)
engine = InnoDB;

alter table teacher_course comment '教师可教授课程';

/*==============================================================*/
/* Table: teacher_freeTime                                      */
/*==============================================================*/
create table teacher_freeTime
(
   id                   varchar(50) not null comment 'ID',
   teacher_id           varchar(50) not null comment '教师ID',
   freeDate             date not null comment '日期',
   startTime            varchar(50) not null comment '开始时间',
   endTime              varchar(50) not null comment '结束时间',
   teachingWay          char(1) comment '上课方式（1：教室授课，2：上门授课，3：远程授课）',
   isFree               char(1) not null comment '是否可排课（0：false，1：true）',
   freeTimeCode         varchar(10) not null comment '可排课时间编码（1234，123，234，12，23，34，1，2，3，4）',
   primary key (id)
)
engine = InnoDB;

alter table teacher_freeTime comment '教师可排课时间表';

/*==============================================================*/
/* Table: teacher_leave                                         */
/*==============================================================*/
create table teacher_leave
(
   id                   varchar(50) not null comment 'ID',
   classSchedule_id     varchar(50) not null comment '课表ID',
   student_id           varchar(50) not null comment '教师ID',
   handleTime           datetime comment '办理时间',
   isInadvance          char(1) comment '是否提前12小时',
   leaveDate            date comment '请假日期',
   startTime            varchar(50) comment '请假开始时间',
   endTime              varchar(50) comment '请假结束时间',
   givenClass           decimal(7,2) comment '补学生课时（小时计）',
   deductionClass       decimal(7,2) comment '补老师课时（小时计）',
   deductionWage        decimal(10,2) comment '扣老师工资',
   contract_id          varchar(50) comment '合同',
   approvalStatus       char(1) comment '审批状态（0：无需审批，1：待审批，2：审批通过，3，驳回）',
   lockStatus           char(1) comment '锁定状态（0：未锁定，1：已锁定）',
   primary key (id)
)
engine = InnoDB;

alter table teacher_leave comment '教师请假表';

/*==============================================================*/
/* Table: teacher_zone                                          */
/*==============================================================*/
create table teacher_zone
(
   id                   varchar(50) not null comment 'ID',
   teacher_id           varchar(50) comment '教师ID',
   city_id              varchar(50) comment '城市ID',
   zone_id              varchar(50) comment '校区ID',
   startDate            date comment '开始日期',
   endDate              date comment '结束日期',
   primary key (id)
)
engine = InnoDB;

alter table teacher_zone comment '教师可授课校区';

/*==============================================================*/
/* Table: tutor                                                 */
/*==============================================================*/
create table tutor
(
   id                   varchar(50) not null comment 'ID',
   user_id              varchar(50) comment '助教',
   startDate            date comment '开始日期',
   endDate              date comment '结束日期',
   primary key (id)
);

alter table tutor comment '助教表';

/*==============================================================*/
/* Table: user                                                  */
/*==============================================================*/
create table user
(
   id                   varchar(50) not null comment '用户ID',
   userName             varchar(50) comment '登录名',
   password             varchar(50) comment '密码',
   isTeaching           char(1) not null comment '是否授课',
   category             char(1) not null comment '用户类别（1：管理员，2：普通员工）',
   code                 varchar(50) comment '员工编码',
   chineseName          varchar(100) comment '中文姓名',
   englishName          varchar(100) comment '英文姓名',
   abbr                 varchar(10) comment '简称',
   birthday             date comment '出生年月',
   gender               char(1) comment '性别',
   nationality          varchar(100) comment '国籍',
   IDNo                 varchar(100) comment '身份证号',
   possportNo           varchar(100) comment '护照号',
   mobile               varchar(50) comment '手机',
   email                varchar(100) comment '电子邮件',
   wechat               varchar(100) comment '微信',
   address              varchar(500) comment '地址',
   type                 char(1) comment '老师性质（1：全职，2：兼职，3：半全职）',
   zone_id              varchar(50) comment '校区',
   status               char(1) comment '状态（1：在职，2：离职，3：冻结）',
   department_id        varchar(50) comment '部门ID',
   department_name      varchar(100) comment '部门名称',
   position_id          varchar(50) comment '岗位',
   position_name        varchar(100) comment '岗位名称',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (abbr)
)
engine = InnoDB;

alter table user comment '系统用户表';

/*==============================================================*/
/* Table: zone                                                  */
/*==============================================================*/
create table zone
(
   id                   varchar(50) not null comment '校区ID',
   code                 varchar(50) comment '校区编码',
   name                 varchar(100) not null comment '校区名称',
   city_id              varchar(50) comment '所属城市ID',
   creator_id           varchar(50) comment '创建者ID',
   createTime           datetime comment '创建时间',
   lastModifier_id      varchar(50) comment '最后修改者',
   lastModifiedTime     datetime comment '最后修改时间',
   delStatus            char(1) comment '是否禁用（0：否，1：是）',
   primary key (id),
   unique key AK_Key_2 (name, city_id)
)
engine = InnoDB;

alter table zone comment '校区表';

/*==============================================================*/
/* Table: zonePermission                                        */
/*==============================================================*/
create table zonePermission
(
   id                   varchar(50) not null comment 'ID',
   user_id              varchar(50) not null comment '用户ID',
   zone_id              varchar(50) not null comment '校区ID',
   primary key (id)
)
engine = InnoDB;

alter table zonePermission comment '校区授权表';

alter table attendanceBook add constraint FK_Reference_26 foreign key (classSchedule_id)
      references classSchedule (id) on delete restrict on update restrict;

alter table classSchedule add constraint FK_Reference_24 foreign key (teacher_id)
      references user (id) on delete restrict on update restrict;

alter table classSchedule add constraint FK_Reference_25 foreign key (student_id)
      references student (id) on delete restrict on update restrict;

alter table classroom add constraint FK_Reference_2 foreign key (zone_id)
      references zone (id) on delete restrict on update restrict;

alter table consumption_student add constraint FK_Reference_27 foreign key (classSchedule_id)
      references classSchedule (id) on delete restrict on update restrict;

alter table consumption_student_contract add constraint FK_Reference_33 foreign key (consumption_student_id)
      references consumption_student (id) on delete restrict on update restrict;

alter table consumption_teacher add constraint FK_Reference_28 foreign key (classSchedule_id)
      references classSchedule (id) on delete restrict on update restrict;

alter table contract add constraint FK_Reference_10 foreign key (studentId)
      references student (id) on delete restrict on update restrict;

alter table contractProduct add constraint FK_Reference_16 foreign key (contract_id)
      references contract (id) on delete restrict on update restrict;

alter table followupLog add constraint FK_Reference_20 foreign key (student_id)
      references student (id) on delete restrict on update restrict;

alter table grade add constraint FK_Reference_36 foreign key (gradeCategory_id)
      references gradeCategory (id) on delete restrict on update restrict;

alter table permission add constraint FK_Reference_19 foreign key (position_id)
      references org (id) on delete restrict on update restrict;

alter table refundFee add constraint FK_Reference_22 foreign key (product_id)
      references contractProduct (id) on delete restrict on update restrict;

alter table requirement add constraint FK_Reference_11 foreign key (student_id)
      references student (id) on delete restrict on update restrict;

alter table student add constraint FK_Reference_37 foreign key (tutorId)
      references tutor (id) on delete restrict on update restrict;

alter table student_course add constraint FK_Reference_12 foreign key (student_id)
      references student (id) on delete restrict on update restrict;

alter table student_course add constraint FK_Reference_13 foreign key (course_id)
      references course (id) on delete restrict on update restrict;

alter table student_course add constraint FK_Reference_32 foreign key (teacher_id)
      references user (id) on delete restrict on update restrict;

alter table student_course add constraint FK_Reference_34 foreign key (grade_id)
      references grade (id) on delete restrict on update restrict;

alter table student_freeTime add constraint FK_Reference_15 foreign key (student_id)
      references student (id) on delete restrict on update restrict;

alter table student_leave add constraint FK_Reference_29 foreign key (classSchedule_id)
      references classSchedule (id) on delete restrict on update restrict;

alter table teacher_contract add constraint FK_Reference_5 foreign key (teacher_id)
      references user (id) on delete restrict on update restrict;

alter table teacher_course add constraint FK_Reference_35 foreign key (grade_id)
      references grade (id) on delete restrict on update restrict;

alter table teacher_course add constraint FK_Reference_6 foreign key (course_id)
      references course (id) on delete restrict on update restrict;

alter table teacher_course add constraint FK_Reference_7 foreign key (teacher_id)
      references user (id) on delete restrict on update restrict;

alter table teacher_freeTime add constraint FK_Reference_9 foreign key (teacher_id)
      references user (id) on delete restrict on update restrict;

alter table teacher_leave add constraint FK_Reference_30 foreign key (classSchedule_id)
      references classSchedule (id) on delete restrict on update restrict;

alter table teacher_zone add constraint FK_Reference_21 foreign key (city_id)
      references city (id) on delete restrict on update restrict;

alter table teacher_zone add constraint FK_Reference_3 foreign key (teacher_id)
      references user (id) on delete restrict on update restrict;

alter table teacher_zone add constraint FK_Reference_4 foreign key (zone_id)
      references zone (id) on delete restrict on update restrict;

alter table user add constraint FK_Reference_31 foreign key (position_id)
      references org (id) on delete restrict on update restrict;

alter table zone add constraint FK_Reference_1 foreign key (city_id)
      references city (id) on delete restrict on update restrict;

alter table zonePermission add constraint FK_Reference_17 foreign key (user_id)
      references user (id) on delete restrict on update restrict;

alter table zonePermission add constraint FK_Reference_18 foreign key (zone_id)
      references zone (id) on delete restrict on update restrict;

