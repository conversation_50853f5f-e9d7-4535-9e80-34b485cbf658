-- 签到视图
create or replace view view_attendanceBook as
    select 
      cs.id as classSchedule_id,
      cs.courseType as courseType,
      cs.student_id as student_id,
      cs.course_id as course_id,
      cs.scheduledDate as scheduledDate,
      cs.startTime as startTime,
      cs.endTime as endTime,
      cs.time as time,
      cs.freeTimeCode as freeTimeCode,
      cs.teacher_id as teacher_id,
      cs.teachingWay as teachingWay,
      cs.classroom_id as classroom_id,
      cs.status as status,
      cs.description as description,
      cs.notes as notes,
      cs.leaveState as leaveState,
      cs.isAuto as isAuto,
      cs.realDate as realDate,
      cs.realStartTime as realStartTime,
      cs.realEndTime as realEndTime,
      cs.realTime as realTime,
      ab.id as attendanceBook_id,
      ab.attendanceStatus as attendanceStatus,
      ab.reason as reason,
      ab.approvalStatus as approvalStatus,
      ab.approver as approver,
      ab.creator_id as creator_id,
      ab.createTime as createTime,
      ab.lastModifier_id as lastModifier_id,
      ab.lastModifiedTime as lastModifiedTime,
      ab.delStatus as delStatus,
      ab.contract_id as contract_id,
      ab.lockStatus as lockStatus,
      s.chineseName as s_chineseName,
      s.englishName as s_englishName,
      s.zone_id as zone_id,
      s.tutorId as tutorId,
      t.chineseName as t_chineseName,
      t.englishName as t_englishName,
      c.name as c_name,
      z.name as z_name,
      cr.name as cr_name
    from classSchedule cs 
    left join attendanceBook ab on cs.id = ab.classSchedule_id
    left join student s on cs.student_id = s.id
    left join user t on cs.teacher_id = t.id
    left join course c on cs.course_id = c.id
    left join zone z on s.zone_id = z.id
    left join classroom cr on cs.classroom_id = cr.id;