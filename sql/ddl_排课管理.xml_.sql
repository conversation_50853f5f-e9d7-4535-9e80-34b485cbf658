CREATE TABLE IF NOT EXISTS `classSchedule` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `courseType` varchar(128) DEFAULT NULL comment 'courseType',
  `student_id` varchar(128) DEFAULT NULL comment 'student_id',
  `course_id` varchar(128) DEFAULT NULL comment 'course_id',
  `scheduledDate` datetime DEFAULT NULL comment 'scheduledDate',
  `startTime` varchar(128) DEFAULT NULL comment 'startTime',
  `endTime` varchar(128) DEFAULT NULL comment 'endTime',
  `time` decimal(15,2) DEFAULT NULL comment 'time',
  `freeTimeCode` varchar(128) DEFAULT NULL comment 'freeTimeCode',
  `teacher_id` varchar(128) DEFAULT NULL comment 'teacher_id',
  `teachingWay` varchar(128) DEFAULT NULL comment 'teachingWay',
  `classroom_id` varchar(128) DEFAULT NULL comment 'classroom_id',
  `status` varchar(128) DEFAULT NULL comment 'status',
  `description` varchar(128) DEFAULT NULL comment 'description',
  `notes` varchar(128) DEFAULT NULL comment 'notes',
  `leaveState` varchar(128) DEFAULT NULL comment 'leaveState',
  `isAuto` varchar(128) DEFAULT NULL comment 'isAuto',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus',
  `realDate` datetime DEFAULT NULL comment 'realDate',
  `realStartTime` varchar(128) DEFAULT NULL comment 'realStartTime',
  `realEndTime` varchar(128) DEFAULT NULL comment 'realEndTime',
  `realTime` varchar(128) DEFAULT NULL comment 'realTime'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
