CREATE TABLE IF NOT EXISTS `contract` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `contractId` varchar(128) DEFAULT NULL comment 'contractId',
  `crmId` varchar(128) DEFAULT NULL comment 'crmId',
  `studentId` varchar(128) DEFAULT NULL comment '学员ID',
  `accountId` varchar(128) DEFAULT NULL comment 'accountId',
  `contractNo` varchar(128) DEFAULT NULL comment 'SF合同号',
  `startDate` datetime DEFAULT NULL comment '合同开始日期',
  `endDate` datetime DEFAULT NULL comment '合同结束日期',
  `productId` varchar(128) DEFAULT NULL comment '产品ID',
  `contractNoR` varchar(128) DEFAULT NULL comment '关联合同号',
  `contractOwnerId` varchar(128) DEFAULT NULL comment '咨询顾问',
  `ownerName` varchar(128) DEFAULT NULL comment '咨询顾问名称',
  `contractType` varchar(128) DEFAULT NULL comment '学员类型',
  `grade_id` varchar(128) DEFAULT NULL comment '报读级别',
  `studyDate_start` datetime DEFAULT NULL comment '学习开始日期',
  `studyDate_end` datetime DEFAULT NULL comment '学习结束日期',
  `company` varchar(128) DEFAULT NULL comment '公司名称',
  `status` varchar(128) DEFAULT NULL comment '当前状态',
  `isAbandoned` varchar(128) DEFAULT NULL comment '是否弃学',
  `abandonedDate` datetime DEFAULT NULL comment '弃学日期',
  `persentType` varchar(128) DEFAULT NULL comment '赠送类型',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus',
  `product_id` varchar(128) DEFAULT NULL comment 'TE产品ID',
  `productName` varchar(128) DEFAULT NULL comment '产品名称',
  `amount` decimal(15,2) DEFAULT NULL comment '数量',
  `units` decimal(15,2) DEFAULT NULL comment '单元',
  `tuitionFee` decimal(15,2) DEFAULT NULL comment '学费',
  `pricebookId` varchar(128) DEFAULT NULL comment 'SFPricebookId',
  `productType` varchar(128) DEFAULT NULL comment 'productType',
  `isUnitPriceChange` varchar(128) DEFAULT NULL comment '单价是否变化',
  `amountWithoutVAT` decimal(15,2) DEFAULT NULL comment '不含税学费',
  `vatRate` decimal(15,2) DEFAULT NULL comment '税率',
  `vat` decimal(15,2) DEFAULT NULL comment 'VAT',
  `consumedClass` decimal(15,2) DEFAULT NULL comment '已消耗课时'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `contractProduct` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `contract_id` varchar(128) DEFAULT NULL comment 'TE合同',
  `contractId` varchar(128) DEFAULT NULL comment 'SF合同',
  `product_id` varchar(128) DEFAULT NULL comment 'TE产品',
  `productId` varchar(128) DEFAULT NULL comment 'SF产品',
  `productName` varchar(128) DEFAULT NULL comment '产品名称',
  `amount` decimal(15,2) DEFAULT NULL comment '数量',
  `units` decimal(15,2) DEFAULT NULL comment '单元',
  `tuitionFee` decimal(15,2) DEFAULT NULL comment '学费',
  `pricebookId` varchar(128) DEFAULT NULL comment 'pricebookId',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者ID',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus',
  `productType` varchar(128) DEFAULT NULL comment 'productType',
  `isUnitPriceChange` varchar(128) DEFAULT NULL comment '单价是否变化',
  `consumedClass` decimal(15,2) DEFAULT NULL comment '已消耗课时'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
