CREATE OR REPLACE VIEW `teacherview` AS 
	select `c1`.`id` AS `id`,
	`c1`.`isTeaching` AS `isTeaching`,
	`c1`.`category` AS `category`,
	`c1`.`code` AS `code`,
	`c1`.`teCode` AS `teCode`,
	`c1`.`chineseName` AS `chineseName`,
	`c1`.`englishName` AS `englishName`,
	`c1`.`abbr` AS `abbr`,
	`c1`.`birthday` AS `birthday`,
	`c1`.`gender` AS `gender`,
	`c1`.`nationality` AS `nationality`,
	`c1`.`IDNo` AS `IDNo`,
	`c1`.`possportNo` AS `possportNo`,
	`c1`.`mobile` AS `mobile`,
	`c1`.`email` AS `email`,
	`c1`.`wechat` AS `wechat`,
	`c1`.`address` AS `address`,
	`c1`.`type` AS `type`,
	`c1`.`zone_id` AS `zone_id`,
	`c1`.`status` AS `status`,
	`c1`.`department_id` AS `department_id`,
	`c1`.`department_name` AS `department_name`,
	`c1`.`position_id` AS `position_id`,
	`c1`.`position_name` AS `position_name`,
	
	`c1`.`creator_id` AS `creator_id`,
	`c1`.`createTime` AS `createTime`,
	`c1`.`lastModifier_id` AS `lastModifier_id`,
	`c1`.`lastModifiedTime` AS `lastModifiedTime`,
	`c1`.`delStatus` AS `delStatus`,
`c1`.`sms` AS `sms`
	from `user` as `c1` 
	where  `c1`.`isTeaching` = "1";
 


create or replace view view_teacher_course as  
  select
	te_cou.id 				as   id,
	te_cou.teacher_id 		as   teacher_id,
	us.chineseName    as chineseName,
	us.englishName    as englishName,
  us.`status` as `status`,
 	te_cou.course_id 		as   course_id,
  te_cou.isActivated as isActivated,
 	cou.name   as  course_name,
	te_cou.periodLowerLimit as   periodLowerLimit,
	te_cou.periodUpperLimit as   periodUpperLimit,
	te_cou.intervalType 	as   intervalType,
	
	te_cou.gradeCategory_id as gradeCategory_id,
	grede_y.name as   gradeCategory_name,
	
	te_cou.grade_id 		as   grade_id,
	gra.name   as  grade_name,
	gra.level as level,
	te_cou.teachingType		as   teachingType,
	te_cou.startDate 		as   startDate,
	te_cou.endDate 			as   endDate,
	te_cou.unitPrice		as   unitPrice,
	tz.zone_id as zone_id
	
from  user us,teacher_course te_cou,course cou,grade gra,gradeCategory grede_y,teacher_zone tz
	where te_cou.teacher_id=us.id and 
		te_cou.course_id=cou.id and
		te_cou.grade_id=gra.id  and 
		gra.gradeCategory_id=grede_y.id and
		tz.teacher_id = us.id;
		
-- 学生排课查询可用老师的view
create or replace view view_student_paike as  
  select
	te_cou.id 				as   id,
	te_cou.teacher_id 		as   teacher_id,
	us.chineseName    as chineseName,
	us.englishName    as englishName,
	
	us.abbr  as  abbr,
  us.`status` as teacher_status,
	
 	te_cou.course_id 		as   course_id,
 	cou.name   as  course_name,
	te_cou.periodLowerLimit as   periodLowerLimit,
	te_cou.periodUpperLimit as   periodUpperLimit,
	te_cou.intervalType 	as   intervalType,
	
	te_cou.gradeCategory_id as gradeCategory_id,
	grede_y.name as   gradeCategory_name,
	
	
	te_cou.grade_id 		as   grade_id,
	gra.name   as  grade_name,
	gra.level as  level,
	
	te_cou.teachingType		as   teachingType,
	te_cou.startDate 		as   startDate,
	te_cou.endDate 			as   endDate,
	te_cou.unitPrice		as   unitPrice,
	
	zo.city_id   as  city_id,
	zo.zone_id   as  zone_id
	

		
from teacher_course te_cou  left join  user us    on te_cou.teacher_id=us.id
                  			left join   course cou on te_cou.course_id=cou.id
                 			 left join  grade gra  on  te_cou.grade_id=gra.id
                  			 left join gradeCategory grede_y on  gra.gradeCategory_id=grede_y.id
                  			 left join teacher_zone zo on us.id=zo.teacher_id;




 
 
-- 教师课程视图(teacher_course,course,gradeCategory,grade,user)
create or replace view view_teacher_course_grade as
select a.*, e.englishName as englishName, e.chineseName as chineseName,
	b.`name` as course_name, c.`name` as gradeCategory_name, d.`name` as grade_name 
from teacher_course a  
	left join course b on a.course_id = b.id 
	left join gradeCategory c on a.gradeCategory_id = c.id 
	left join grade d on a.grade_id = d.id 
	left join `user` e on a.teacher_id = e.id;

