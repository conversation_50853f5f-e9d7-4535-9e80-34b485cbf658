CREATE TABLE IF NOT EXISTS `city` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `name` varchar(128) DEFAULT NULL comment 'name',
  `code` varchar(128) DEFAULT NULL comment 'code',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `zone` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `name` varchar(128) DEFAULT NULL comment 'name',
  `code` varchar(128) DEFAULT NULL comment 'code',
  `city_id` varchar(128) DEFAULT NULL comment 'city_id',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `classroom` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `name` varchar(128) DEFAULT NULL comment 'name',
  `zone_id` varchar(128) DEFAULT NULL comment 'zone_id',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `schedule` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `startTime` varchar(128) DEFAULT NULL comment 'startTime',
  `endTime` varchar(128) DEFAULT NULL comment 'endTime',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `holiday` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `name` varchar(128) DEFAULT NULL comment 'name',
  `startDate` datetime DEFAULT NULL comment 'startDate',
  `endDate` datetime DEFAULT NULL comment 'endDate',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `gradeCategory` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `name` varchar(128) DEFAULT NULL comment '级别名称',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `grade` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `gradeCategory_id` varchar(128) DEFAULT NULL comment '级别分类',
  `name` varchar(128) DEFAULT NULL comment '级别名称',
  `level` varchar(128) DEFAULT NULL comment 'level',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `course` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `name` varchar(128) DEFAULT NULL comment '课程名称',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `constant` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `value` varchar(128) DEFAULT NULL comment '常量值',
  `code` varchar(128) DEFAULT NULL comment '代码',
  `category` varchar(128) DEFAULT NULL comment '类别',
  `creator_id` varchar(128) DEFAULT NULL comment 'creator_id',
  `createTime` datetime DEFAULT NULL comment 'createTime',
  `lastModifier_id` varchar(128) DEFAULT NULL comment 'lastModifier_id',
  `lastModifiedTime` datetime DEFAULT NULL comment 'lastModifiedTime',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
