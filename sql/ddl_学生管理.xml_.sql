CREATE TABLE IF NOT EXISTS `student` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `code` varchar(128) DEFAULT NULL comment 'code',
  `crmId` varchar(128) DEFAULT NULL comment 'crmId',
  `accountId` varchar(128) DEFAULT NULL comment 'accountId',
  `chineseName` varchar(128) DEFAULT NULL comment 'chineseName',
  `englishName` varchar(128) DEFAULT NULL comment 'englishName',
  `gender` varchar(128) DEFAULT NULL comment 'gender',
  `age` int(128) DEFAULT NULL comment 'age',
  `birthday` datetime DEFAULT NULL comment 'birthday',
  `nationality` varchar(128) DEFAULT NULL comment 'nationality',
  `mobile` varchar(128) DEFAULT NULL comment 'mobile',
  `email` varchar(128) DEFAULT NULL comment 'email',
  `wechat` varchar(128) DEFAULT NULL comment 'wechat',
  `city_id` varchar(128) DEFAULT NULL comment 'city_id',
  `zone_id` varchar(128) DEFAULT NULL comment 'zone_id',
  `school` varchar(128) DEFAULT NULL comment 'school',
  `grade` varchar(128) DEFAULT NULL comment 'grade',
  `address` varchar(128) DEFAULT NULL comment 'address',
  `teReferral` varchar(128) DEFAULT NULL comment 'teReferral',
  `relationshipTypeP` varchar(128) DEFAULT NULL comment 'relationshipTypeP',
  `chineseNameP` varchar(128) DEFAULT NULL comment 'chineseNameP',
  `contractNumberP` varchar(128) DEFAULT NULL comment 'contractNumberP',
  `companyP` varchar(128) DEFAULT NULL comment 'companyP',
  `occupationP` varchar(128) DEFAULT NULL comment 'occupationP',
  `wechatP` varchar(128) DEFAULT NULL comment 'wechatP',
  `IDNoP` varchar(128) DEFAULT NULL comment 'IDNoP',
  `emailP` varchar(128) DEFAULT NULL comment 'emailP',
  `relationshipTypeS` varchar(128) DEFAULT NULL comment 'relationshipTypeS',
  `chineseNameS` varchar(128) DEFAULT NULL comment 'chineseNameS',
  `contractNumberS` varchar(128) DEFAULT NULL comment 'contractNumberS',
  `companyS` varchar(128) DEFAULT NULL comment 'companyS',
  `occupationS` varchar(128) DEFAULT NULL comment 'occupationS',
  `wechatS` varchar(128) DEFAULT NULL comment 'wechatS',
  `IDNoS` varchar(128) DEFAULT NULL comment 'IDNoS',
  `emailS` varchar(128) DEFAULT NULL comment 'emailS',
  `source` varchar(128) DEFAULT NULL comment 'source',
  `referral` varchar(128) DEFAULT NULL comment 'referral',
  `contractOwnerId` varchar(128) DEFAULT NULL comment 'contractOwnerId',
  `ownerName` varchar(128) DEFAULT NULL comment '咨询顾问名称',
  `tppId` varchar(128) DEFAULT NULL comment 'tppId',
  `tutorId` varchar(128) DEFAULT NULL comment 'tutorId',
  `contractType` varchar(128) DEFAULT NULL comment 'contractType',
  `isOC` varchar(128) DEFAULT NULL comment 'isOC',
  `startTime` datetime DEFAULT NULL comment 'startTime',
  `endTime` datetime DEFAULT NULL comment 'endTime',
  `notes` varchar(128) DEFAULT NULL comment 'notes',
  `status` varchar(128) DEFAULT NULL comment 'status',
  `freezeDate` datetime DEFAULT NULL comment 'freezeDate',
  `thawedDate` datetime DEFAULT NULL comment '解冻日期',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者ID',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间',
  `delStatus` varchar(128) DEFAULT NULL comment 'delStatus'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `student_course` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `student_id` varchar(128) DEFAULT NULL comment 'student_id',
  `course_id` varchar(128) DEFAULT NULL comment 'course_id',
  `gradeCategory_id` varchar(128) DEFAULT NULL comment '级别分类',
  `grade_id` varchar(128) DEFAULT NULL comment 'grade_id',
  `teacher_id` varchar(128) DEFAULT NULL comment 'teacher_id',
  `teachingWay` varchar(128) DEFAULT NULL comment 'teachingWay',
  `startDate` datetime DEFAULT NULL comment 'startDate',
  `endDate` datetime DEFAULT NULL comment 'endDate',
  `units` int(128) DEFAULT NULL comment 'units',
  `status` varchar(128) DEFAULT NULL comment 'status'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `requirement` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `student_id` varchar(128) DEFAULT NULL comment 'student_id',
  `startDate` datetime DEFAULT NULL comment 'startDate',
  `endDate` datetime DEFAULT NULL comment 'endDate',
  `course_id` varchar(128) DEFAULT NULL comment 'course_id',
  `totalLowerLimit` decimal(15,2) DEFAULT NULL comment 'totalLowerLimit',
  `totalUpperLimit` decimal(15,2) DEFAULT NULL comment 'totalUpperLimit',
  `dayLowerLimit` decimal(15,2) DEFAULT NULL comment 'dayLowerLimit',
  `dayUpperLimit` decimal(15,2) DEFAULT NULL comment 'dayUpperLimit',
  `weekLowerLimit` decimal(15,2) DEFAULT NULL comment 'weekLowerLimit',
  `weekUpperLimit` decimal(15,2) DEFAULT NULL comment 'weekUpperLimit',
  `monthLowerLimit` decimal(15,2) DEFAULT NULL comment 'monthLowerLimit',
  `monthUpperLimit` decimal(15,2) DEFAULT NULL comment 'monthUpperLimit',
  `intervalDay` int(128) DEFAULT NULL comment 'intervalDay'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `student_freeTime` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `student_id` varchar(128) DEFAULT NULL comment '学生',
  `freeDate` datetime DEFAULT NULL comment '日期',
  `startTime` varchar(128) DEFAULT NULL comment '开始时间',
  `endTime` varchar(128) DEFAULT NULL comment '结束时间',
  `teachingWay` varchar(128) DEFAULT NULL comment '上课方式',
  `isFree` varchar(128) DEFAULT NULL comment '是否可排课',
  `freeTimeCode` varchar(128) DEFAULT NULL comment '可排课时间编码'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `view_coures_teacher` (
  `id` varchar(128) DEFAULT NULL comment 'id',
  `student_id` varchar(128) DEFAULT NULL comment 'student_id',
  `course_id` varchar(128) DEFAULT NULL comment 'course_id',
  `course_name` varchar(128) DEFAULT NULL comment 'course_name',
  `gradeCategory_id` varchar(128) DEFAULT NULL comment 'gradeCategory_id',
  `gradeCategory_name` varchar(128) DEFAULT NULL comment 'gradeCategory_name',
  `grade_id` varchar(128) DEFAULT NULL comment 'grade_id',
  `grade_name` varchar(128) DEFAULT NULL comment 'grade_name',
  `level` varchar(128) DEFAULT NULL comment 'level',
  `teacher_id` varchar(128) DEFAULT NULL comment 'teacher_id',
  `teacher_chineseName` varchar(128) DEFAULT NULL comment 'teacher_chineseName',
  `teacher_englishName` varchar(128) DEFAULT NULL comment 'teacher_englishName',
  `teachingWay` varchar(128) DEFAULT NULL comment 'teachingWay',
  `startDate` datetime DEFAULT NULL comment 'startDate',
  `endDate` datetime DEFAULT NULL comment 'endDate',
  `units` decimal(15,2) DEFAULT NULL comment 'units',
  `status` varchar(128) DEFAULT NULL comment 'status'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `followupLog` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `student_id` varchar(128) DEFAULT NULL comment '学员',
  `followupWay` varchar(128) DEFAULT NULL comment '跟进方式',
  `followupTime` datetime DEFAULT NULL comment '跟进时间',
  `nextFollowupTime` datetime DEFAULT NULL comment '下次跟进时间',
  `followupContent` varchar(128) DEFAULT NULL comment '跟进内容',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者ID',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间',
  `delStatus` varchar(128) DEFAULT NULL comment '是否禁用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `student_refundFee` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `contract_id` varchar(128) DEFAULT NULL comment 'TE合同ID',
  `handleTime` datetime DEFAULT NULL comment '退费时间',
  `refundType` varchar(128) DEFAULT NULL comment '退费类型',
  `unitPrice` decimal(15,2) DEFAULT NULL comment '课程单价',
  `refundFuitionFee` decimal(15,2) DEFAULT NULL comment '退费金额',
  `refundReason` varchar(128) DEFAULT NULL comment '退费原因',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者ID',
  `createTime` datetime DEFAULT NULL comment '创建时间',
  `lastModifier_id` varchar(128) DEFAULT NULL comment '最后修改者',
  `lastModifiedTime` datetime DEFAULT NULL comment '最后修改时间',
  `delStatus` varchar(128) DEFAULT NULL comment '是否禁用',
  `periods` decimal(15,2) DEFAULT NULL comment '退费课时',
  `approvalStatus` varchar(128) DEFAULT NULL comment '审批状态',
  `approver` varchar(128) DEFAULT NULL comment '审批人',
  `approverTime` datetime DEFAULT NULL comment '审批时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `tutor` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `student_id` varchar(128) DEFAULT NULL comment '学员',
  `tutor_id` varchar(128) DEFAULT NULL comment '助教ID',
  `startDate` datetime DEFAULT NULL comment '开始时间',
  `endDate` datetime DEFAULT NULL comment '结束时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
