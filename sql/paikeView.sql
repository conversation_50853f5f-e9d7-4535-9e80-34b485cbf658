create or replace view view_paike_class as  
	select
	c1.id    as    id,          
	c1.courseType     as    courseType,           
	c1.student_id         as    student_id,    
	c3.chineseName  as   stu_chineseName,
	c3.englishName  as  stu_englishName,
	c3.zone_id  as zone_id,
	c3.tutorId as tutorId,
		
	c1.course_id          as    course_id,    
	c4.`name`  as course_name,
	 
	c1.scheduledDate       as    scheduledDate,      
	c1.startTime            as    startTime,      
	c1.endTime             as    endTime,      
	c1.time                as    time,      
	c1.teacher_id        as    teacher_id,      

	c2.chineseName    as    us_chineseName,
	c2.englishName    as    us_englishName,
	c2.abbr   as  abbr,

	c1.teachingWay         as   teachingWay ,      
	c1.classroom_id     as    classroom_id, 

	c5.`name`          as  classroom_name,

	c1.`status`              as   `status` ,      
	c1.description          as    description,      
	c1.notes               as    notes,      
	c1.leaveState           as   leaveState ,      
	c1.isAuto               as   isAuto ,      
	c1.creator_id           as    creator_id,      
	c1.createTime           as    createTime,      
	c1.lastModifier_id       as   lastModifier_id ,      
	c1.lastModifiedTime      as    lastModifiedTime,      
	c1.delStatus             as    delStatus,      
	c1.realDate              as    realDate,      
	c1.realStartTime        as   realStartTime ,      
	c1.realEndTime          as   realEndTime ,      
	c1.freeTimeCode         as    freeTimeCode,
	c1.realTime   as realTime,
	c6.id as teacher_leave_id,
	c6.approvalStatus as t_approvalStatus,
	c7.id as student_leave_id,
	c7.approvalStatus as s_approvalStatus,
	c8.userName as ctrator_userName,
	c8.chineseName as ctrator_chineseName,
	c8.englishName as ctrator_englishName,
	c9.attendanceStatus as attendanceStatus
from classSchedule c1 
  left join `user` c2 on c1.teacher_id = c2.id 
  left join student c3 on  c1.student_id = c3.id
  left join course c4 on  c1.course_id = c4.id
  left join classroom c5 on  c1.classroom_id = c5.id
  left join teacher_leave c6 on c1.id = c6.classSchedule_id
	left join student_leave c7 on c1.id = c7.classSchedule_id
	left join `user` c8 on c1.creator_id = c8.id
  LEFT JOIN attendanceBook c9 on c1.id=c9.classSchedule_id;

