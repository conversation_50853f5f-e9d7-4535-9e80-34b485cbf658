CREATE TABLE IF NOT EXISTS `filerecord` (
  `id` varchar(128) DEFAULT NULL comment 'ID',
  `originalName` varchar(128) DEFAULT NULL comment '原始名称',
  `displayName` varchar(128) DEFAULT NULL comment '显示名称',
  `suffixName` varchar(128) DEFAULT NULL comment '后缀名',
  `logicName` varchar(128) DEFAULT NULL comment '逻辑名称',
  `logicPath` varchar(128) DEFAULT NULL comment '逻辑存储路径',
  `size` int(128) DEFAULT NULL comment '文件大小',
  `beizhu` varchar(128) DEFAULT NULL comment '备注',
  `status` varchar(128) DEFAULT NULL comment '状态',
  `reference_id` varchar(128) DEFAULT NULL comment '引用',
  `reference_type` varchar(128) DEFAULT NULL comment '引用类别',
  `creator_id` varchar(128) DEFAULT NULL comment '创建者',
  `createTime` datetime DEFAULT NULL comment '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
