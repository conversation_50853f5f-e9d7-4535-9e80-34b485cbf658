drop view if exists view_consumption_student_contract;
create view view_consumption_student_contract as
select c_s_c.id as id, c_s_c.consumption_student_id as consumption_student_id, 
  c_s_c.contract_id as contract_id, c_s_c.consumedClass as consumedClass, c_s_c.unitPrice as unitPrice,
  c_s_c.lockStatus as lockStatus, c_s_c.isUploaded as isUploaded,
  c_s.student_id as student_id, 
  
  cs.scheduledDate as scheduledDate,
  
  s.accountId as accountId, s.crmId as crmId,
  s.chineseName as chineseName, s.englishName as englishName,
  
  c.contractNo as contractNo,c.contractNoR as contractNoR,c.partACode as partACode,
  c.tuitionFee as tuitionFee, c.amount as amount, c.amountWithoutVAT as amountWithoutVAT,
  c.vatRate as vatRate, c.vat as vat,
  c.isUnitPriceChange as isUnitPriceChange, c.diffUnitPrice as diffUnitPrice
from consumption_student_contract c_s_c 
  left join consumption_student c_s on c_s_c. consumption_student_id = c_s.id
  left join classSchedule cs on c_s.classSchedule_id = cs.id
  left join student s on c_s.student_id = s.id
  left join contract c on c_s_c.contract_id = c.id;
  
--	select `c_s_c`.`id` AS `id`,`c_s_c`.`consumption_student_id` AS `consumption_student_id`,
--	`c_s_c`.`contract_id` AS `contract_id`,`c_s_c`.`consumedClass` AS `consumedClass`,`c_s_c`.`unitPrice` AS `unitPrice`,
--	`c_s_c`.`lockStatus` AS `lockStatus`,`c_s_c`.`isUploaded` AS `isUploaded`,
--	`c_s`.`student_id` AS `student_id`,
--	`cs`.`scheduledDate` AS `scheduledDate`,
--	`s`.`accountId` AS `accountId`,`s`.`crmId` AS `crmId`,
--	`s`.`chineseName` AS `chineseName`,`s`.`englishName` AS `englishName`,
--	`c`.`contractNo` AS `contractNo`,`c`.`contractNoR` AS `contractNoR`,`c`.`partACode` AS `partACode`,
--	`c`.`tuitionFee` AS `tuitionFee`,`c`.`amount` AS `amount`,`c`.`amountWithoutVAT` AS `amountWithoutVAT`,
--	`c`.`vatRate` AS `vatRate`,`c`.`vat` AS `vat`,
--	`c`.`isUnitPriceChange` AS `isUnitPriceChange`,`c`.`diffUnitPrice` AS `diffUnitPrice` 
--	from ((((`consumption_student_contract` `c_s_c` left join `consumption_student` `c_s` on((`c_s_c`.`consumption_student_id` = `c_s`.`id`))) 
--	left join `classSchedule` `cs` on((`c_s`.`classSchedule_id` = `cs`.`id`))) 
--	left join `student` `s` on((`c_s`.`student_id` = `s`.`id`))) 
--	left join `contract` `c` on((`c_s_c`.`contract_id` = `c`.`id`)))