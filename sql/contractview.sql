create or replace view `contractview` as 
select 
`c1`.*,
`c2`.`chineseName` as `chineseName`,
`c2`.`code` as `code`,
`c2`.`englishName` as `englishName`,
`c2`.`gender` as `gender`,
`c2`.`age` as `age`,
`c2`.`birthday` as `birthday`,
`c2`.`nationality` as `nationality`,
`c2`.`mobile` as `mobile`,
`c2`.`email` as `email`,
`c2`.`wechat` as `wechat`,
`c2`.`city_id` as `city_id`,
`c2`.`zone_id` as `zone_id`,
`c2`.`school` as `school`,
`c2`.`grade` as `grade`,
`c2`.`address` as `address`,
`c2`.`source` as `source`,
`c2`.`referral` as `referral`,
`c2`.`tppId` as `tppId`,
`c2`.`tutorId` as `tutorId`,
`c2`.`isOC` as `isOC`,
`c2`.`startTime` as `startTime`,
`c2`.`endTime` as `endTime`,
`c2`.`notes` as `notes`,
`c2`.`status` as `studentStatus`,
`c2`.`freezeDate` as `freezeDate`,
`c2`.`thawedDate` as `thawedDate`,
`c3`.`approvalStatus` as `approvalStatus`,
`c4`.`userName` as `creator_name`,
`c4`.`chineseName` as `creator_chineseName`,
`c4`.`englishName` as `creator_englishName`
from `contract` `c1` 
  left join `student` `c2` on `c1`.`studentId` = `c2`.`id` 
  left join `student_refundFee` `c3` on `c1`.id = `c3`.`contract_id`
  left join `user` `c4` on `c1`.creator_id = `c4`.`id`;